{"name": "averum-contracts-monorepo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "preview": "cd frontend && npm run preview", "lint": "cd frontend && npm run lint", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && pip install -r requirements.txt", "install:all": "npm run install:frontend && npm run install:backend", "start:services": "./start-services.sh", "start:production": "./start-production.sh"}, "description": "Averum Contracts - Contract Management System with React frontend and FastAPI backend", "keywords": ["contracts", "legal", "management", "react", "<PERSON><PERSON><PERSON>"], "author": "Averum Team", "license": "MIT"}