-- Default permissions for LegalAI V4
-- Apply these after creating the permissions table

INSERT INTO permissions (id, name, description, category) VALUES
('perm-1', 'contracts.view', 'View contracts', 'contracts'),
('perm-2', 'contracts.create', 'Create contracts', 'contracts'),
('perm-3', 'contracts.edit', 'Edit contracts', 'contracts'),
('perm-4', 'contracts.delete', 'Delete contracts', 'contracts'),
('perm-5', 'contracts.approve', 'Approve contracts', 'contracts'),
('perm-6', 'templates.view', 'View templates', 'templates'),
('perm-7', 'templates.create', 'Create templates', 'templates'),
('perm-8', 'templates.edit', 'Edit templates', 'templates'),
('perm-9', 'templates.delete', 'Delete templates', 'templates'),
('perm-10', 'clauses.view', 'View clauses', 'clauses'),
('perm-11', 'clauses.create', 'Create clauses', 'clauses'),
('perm-12', 'clauses.edit', 'Edit clauses', 'clauses'),
('perm-13', 'clauses.delete', 'Delete clauses', 'clauses'),
('perm-14', 'users.manage', 'Manage users', 'users'),
('perm-15', 'analytics.view', 'View analytics', 'analytics'),
('perm-16', 'workspace.admin', 'Workspace administration', 'workspace')
ON CONFLICT (id) DO NOTHING;
