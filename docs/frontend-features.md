# Frontend Features Documentation

This document outlines the frontend features implemented to make the application more robust, responsive, and accessible.

## Error Handling

We've implemented a global error handling system that provides consistent error handling across the application.

### Key Components:

- **ErrorProvider**: A context provider that manages error state and provides error handling functions.
- **useError**: A hook to access error handling functionality.
- **useNotify**: A hook to display toast notifications for success, error, warning, and info messages.

### Usage:

```tsx
import { useError, useNotify } from "@/lib/error-provider";

const MyComponent = () => {
  const { setError, handleApiError } = useError();
  const notify = useNotify();

  const handleAction = async () => {
    try {
      // Perform action
      notify.success("Action completed successfully!");
    } catch (error) {
      handleApiError(error, "Failed to perform action");
    }
  };

  return (
    // Component JSX
  );
};
```

## Loading States

We've implemented a global loading state system to provide consistent loading indicators across the application.

### Key Components:

- **LoadingProvider**: A context provider that manages loading state and provides loading functions.
- **useLoading**: A hook to access loading functionality.
- **Loading**: A reusable loading component with different sizes and variants.
- **LoadingOverlay**: A component that displays a loading overlay on top of content.
- **LoadingButton**: A button component that displays a loading spinner when loading.

### Usage:

```tsx
import { useLoading } from "@/lib/loading-provider";
import { Loading, LoadingOverlay, LoadingButton } from "@/components/ui/loading";

const MyComponent = () => {
  const { isLoading, startLoading, stopLoading, withLoading } = useLoading();

  const handleAction = async () => {
    try {
      startLoading("Processing...");
      // Perform action
    } finally {
      stopLoading();
    }
  };

  // Alternative using withLoading
  const handleActionWithLoading = async () => {
    await withLoading(
      async () => {
        // Perform action
      },
      "Processing..."
    );
  };

  return (
    <div>
      {/* Simple loading indicator */}
      {isLoading && <Loading size="md" text="Loading..." />}

      {/* Loading overlay */}
      <LoadingOverlay active={isLoading} text="Loading...">
        <div>Content to overlay</div>
      </LoadingOverlay>

      {/* Loading button */}
      <LoadingButton loading={isLoading} onClick={handleAction}>
        Submit
      </LoadingButton>
    </div>
  );
};
```

## Form Validation

We've implemented a form validation system to provide consistent form validation across the application.

### Key Components:

- **useForm**: A hook to manage form state and validation.
- **FormField**: A reusable form field component with validation.
- **form-validation.ts**: Utility functions for form validation.

### Usage:

```tsx
import { useForm } from "@/lib/use-form";
import { FormField } from "@/components/ui/form-field";
import { required, email, minLength } from "@/lib/form-validation";

interface FormValues {
  name: string;
  email: string;
  message: string;
}

const MyForm = () => {
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    isSubmitting,
  } = useForm<FormValues>({
    initialValues: {
      name: "",
      email: "",
      message: "",
    },
    validationSchema: {
      name: [required("Name is required")],
      email: [
        required("Email is required"),
        email("Please enter a valid email address"),
      ],
      message: [
        required("Message is required"),
        minLength(10, "Message must be at least 10 characters"),
      ],
    },
    onSubmit: async (values) => {
      // Submit form
    },
  });

  return (
    <form onSubmit={handleSubmit}>
      <FormField
        id="name"
        name="name"
        label="Name"
        value={values.name}
        onChange={handleChange}
        onBlur={handleBlur}
        error={touched.name ? errors.name : undefined}
        touched={touched.name}
        required
      />

      <FormField
        id="email"
        name="email"
        label="Email"
        type="email"
        value={values.email}
        onChange={handleChange}
        onBlur={handleBlur}
        error={touched.email ? errors.email : undefined}
        touched={touched.email}
        required
      />

      <FormField
        id="message"
        name="message"
        label="Message"
        type="textarea"
        value={values.message}
        onChange={handleChange}
        onBlur={handleBlur}
        error={touched.message ? errors.message : undefined}
        touched={touched.message}
        required
      />

      <button type="submit" disabled={isSubmitting}>
        Submit
      </button>
    </form>
  );
};
```

## Responsive Design

We've implemented responsive design components to make the application more mobile-friendly.

### Key Components:

- **ResponsiveLayout**: A component that provides a responsive container.
- **ResponsiveGrid**: A component that provides a responsive grid layout.
- **ResponsiveStack**: A component that provides a responsive stack layout.
- **ResponsiveSection**: A component that provides a responsive section layout.

### Usage:

```tsx
import {
  ResponsiveLayout,
  ResponsiveGrid,
  ResponsiveStack,
  ResponsiveSection,
} from "@/components/ui/responsive-layout";

const MyComponent = () => {
  return (
    <ResponsiveLayout>
      <ResponsiveSection title="My Section" description="Section description">
        <ResponsiveStack direction="row" responsive spacing="md">
          <div>Item 1</div>
          <div>Item 2</div>
        </ResponsiveStack>

        <ResponsiveGrid
          columns={{
            xs: 1,
            sm: 2,
            md: 3,
            lg: 4,
          }}
          gap="md"
        >
          <div>Grid Item 1</div>
          <div>Grid Item 2</div>
          <div>Grid Item 3</div>
          <div>Grid Item 4</div>
        </ResponsiveGrid>
      </ResponsiveSection>
    </ResponsiveLayout>
  );
};
```

## Accessibility

We've implemented accessibility features to make the application more accessible.

### Key Components:

- **SkipLink**: A component that allows keyboard users to skip to main content.
- **VisuallyHidden**: A component that hides content visually but keeps it accessible to screen readers.
- **FocusTrap**: A component that traps focus within a component.
- **LiveRegion**: A component that announces dynamic content changes to screen readers.

### Usage:

```tsx
import {
  SkipLink,
  VisuallyHidden,
  FocusTrap,
  LiveRegion,
} from "@/components/ui/a11y";

const MyComponent = () => {
  return (
    <div>
      <SkipLink targetId="main-content" />

      <VisuallyHidden>
        This text is hidden visually but accessible to screen readers.
      </VisuallyHidden>

      <FocusTrap active={isModalOpen}>
        <div>Modal content with trapped focus</div>
      </FocusTrap>

      <LiveRegion aria-live="polite">
        {message && `New message: ${message}`}
      </LiveRegion>

      <main id="main-content" tabIndex={-1}>
        Main content
      </main>
    </div>
  );
};
```

## API Service

We've implemented an API service with error handling to provide consistent API calls across the application.

### Key Components:

- **ApiService**: A class that provides methods for making API calls with error handling.
- **useApi**: A hook to access API functionality with loading and error handling.
- **MockApiService**: A class that provides mock API responses for frontend development.

### Usage:

```tsx
import { useApi } from "@/lib/api";

const MyComponent = () => {
  const { fetch, api } = useApi();

  const fetchData = async () => {
    // Using the fetch method with loading and error handling
    const data = await fetch(
      () => api.get("/endpoint"),
      "Loading data...",
      "Failed to fetch data"
    );

    // Using the API directly
    try {
      const response = await api.get("/endpoint");
      // Handle response
    } catch (error) {
      // Handle error
    }
  };

  return (
    // Component JSX
  );
};
```

## Example Implementation

We've created an example form component that demonstrates all of these features working together. You can access it at `/examples/form` in the application.

The example form demonstrates:

- Form validation
- Loading states
- Error handling
- Responsive design
- Accessibility features

You can use this as a reference for implementing these features in your own components.
