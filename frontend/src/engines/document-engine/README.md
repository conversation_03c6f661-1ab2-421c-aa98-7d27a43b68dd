# Document Engine

A powerful, extensible document editing engine built on TipTap that can operate in multiple modes and provides a foundation for collaborative document editing.

## Features

### Current Features
- ✅ **Advanced TipTap Editor** with legal document extensions
- ✅ **Professional Document Styling** with legal document formatting
- ✅ **Enhanced Toolbar** with legal-specific features
- ✅ **Export Capabilities** (PDF, DOCX, HTML, TXT, JSON)
- ✅ **Auto-numbering** for legal document sections
- ✅ **Legal Clause Templates** (WHEREAS, definitions, covenants, etc.)
- ✅ **Document Structure Management** with outline tracking
- ✅ **Multiple Operation Modes** (wizard, standalone, collaborative)
- ✅ **Wizard Integration** with seamless form-to-document sync

### Future Features (Foundation Ready)
- 🔄 **Real-time Collaboration** with WebSocket integration
- 🔄 **Comment System** with threaded discussions
- 🔄 **Track Changes** with operational transformation
- 🔄 **User Presence** indicators and cursor tracking
- 🔄 **Permission-based Editing** with granular controls
- 🔄 **Version Control** with document history

## Architecture

```
src/engines/document-engine/
├── core/
│   ├── DocumentEngine.tsx          # Main engine component
│   ├── DocumentTypes.ts            # TypeScript interfaces
├── editor/
│   ├── TipTapExtensions.ts         # Custom TipTap extensions
├── toolbar/
│   ├── DocumentToolbar.tsx         # Enhanced toolbar component
├── hooks/
│   ├── useDocumentEngine.ts        # Main engine hook
├── export/
│   ├── DocumentExporter.ts         # Export functionality
├── collaboration/
│   ├── CollaborationManager.tsx    # Collaboration features (placeholder)
├── integrations/
│   ├── WizardDocumentEngine.tsx    # Contract wizard integration
└── index.ts                        # Main exports
```

## Usage

### Standalone Mode

```tsx
import { DocumentEngine, createStandaloneConfig } from '@/engines/document-engine';

function MyDocumentEditor() {
  const config = createStandaloneConfig({
    theme: {
      fontFamily: 'Times New Roman',
      fontSize: '12pt'
    }
  });

  return (
    <DocumentEngine
      config={config}
      initialContent="<p>Start typing...</p>"
      onSave={async (document) => {
        // Save document to backend
      }}
      onExport={(format, blob) => {
        // Handle export
      }}
    />
  );
}
```

### Wizard Integration Mode

```tsx
import { WizardDocumentEngine } from '@/engines/document-engine/integrations/WizardDocumentEngine';

function ContractWizardPreview() {
  const { data, setData } = useContractWizard();

  return (
    <WizardDocumentEngine
      wizardData={data}
      onWizardDataChange={setData}
      onError={(error) => console.error(error)}
    />
  );
}
```

### Future Collaborative Mode

```tsx
import { DocumentEngine, createCollaborativeConfig } from '@/engines/document-engine';

function CollaborativeEditor() {
  const config = createCollaborativeConfig('room-123', {
    permissions: {
      userId: 'user-1',
      role: 'editor'
    }
  });

  return (
    <DocumentEngine
      config={config}
      onCollaboratorJoin={(user) => console.log('User joined:', user)}
      onCommentAdd={(comment) => console.log('Comment added:', comment)}
    />
  );
}
```

## Document Preview Components

The document engine includes specialized preview components for read-only document viewing:

### DocumentPreviewEngine

Full-featured preview component with zoom controls, print, download, and fullscreen capabilities:

```tsx
import { DocumentPreviewEngine } from '@/engines/document-engine';

function DocumentPreview() {
  return (
    <DocumentPreviewEngine
      content="<h1>Document Title</h1><p>Content...</p>"
      documentTitle="My Document"
      showZoomControls={true}
      showPrintButton={true}
      showDownloadButton={true}
      showFullscreenButton={true}
      initialZoom={1.0}
      onPrint={() => console.log('Print requested')}
      onDownload={() => console.log('Download requested')}
    />
  );
}
```

### DocumentPreviewSimple

Lightweight preview component for embedded use cases:

```tsx
import { DocumentPreviewSimple } from '@/engines/document-engine';

function EmbeddedPreview() {
  return (
    <DocumentPreviewSimple
      content="<h1>Document Title</h1><p>Content...</p>"
      scale={0.8}
      maxHeight="400px"
      showShadow={true}
    />
  );
}
```

### DocumentPreviewModal

Modal wrapper for document previews:

```tsx
import { DocumentPreviewModal, useDocumentPreview } from '@/engines/document-engine';

function ModalPreview() {
  const { previewState, openPreview, closePreview } = useDocumentPreview();

  return (
    <>
      <button onClick={() => openPreview('<h1>Document</h1>', 'Document Title')}>
        Open Preview
      </button>
      <DocumentPreviewModal
        isOpen={previewState.isOpen}
        onClose={closePreview}
        content={previewState.content}
        title={previewState.title}
        size="xl"
      />
    </>
  );
}
```

### Use Cases

The preview components are designed for:

- **Template Selection**: Preview contract templates before selection
- **Document Approval Workflows**: Review documents in approval processes
- **Repository Browsing**: Quick document previews in file browsers
- **Final Document Review**: Read-only review before signing
- **Embedded Previews**: Small-scale previews in cards and lists

## Configuration

### Document Engine Config

```typescript
interface DocumentEngineConfig {
  mode: 'wizard' | 'standalone' | 'collaborative';
  theme?: DocumentTheme;
  toolbar?: ToolbarConfig;
  collaboration?: CollaborationConfig;
  permissions?: DocumentPermissions;
  export?: ExportConfig;
}
```

### Theme Configuration

```typescript
interface DocumentTheme {
  fontFamily?: string;
  fontSize?: string;
  lineHeight?: number;
  pageWidth?: string;
  margins?: {
    top: string;
    right: string;
    bottom: string;
    left: string;
  };
  colors?: {
    text: string;
    background: string;
    border: string;
    accent: string;
  };
}
```

## Custom Extensions

The document engine includes several custom TipTap extensions:

### AutoNumbering
Automatically numbers legal document sections (1.1, 1.1.1, etc.)

### LegalClause
Provides structured legal content with clause types (whereas, definition, covenant, condition)

### CrossReference
Enables cross-references between document sections

### DocumentStructure
Tracks document outline and section hierarchy

## Export Formats

- **PDF**: High-quality PDF with professional legal document styling
- **DOCX**: Microsoft Word format (RTF-based for compatibility)
- **HTML**: Standalone HTML with embedded CSS
- **TXT**: Plain text version
- **JSON**: Structured data with metadata

## Integration with Contract Wizard

The document engine seamlessly integrates with the existing contract wizard:

1. **Backward Compatibility**: Existing wizard functionality is preserved
2. **Feature Flag**: Can be enabled/disabled via environment variable
3. **Real-time Sync**: Form changes automatically update document preview
4. **Enhanced Features**: Advanced editing capabilities while maintaining wizard workflow

## Development

### Adding New Extensions

```typescript
// Create custom extension
const MyExtension = Extension.create({
  name: 'myExtension',
  // Extension configuration
});

// Add to extensions array in TipTapExtensions.ts
export function createTipTapExtensions(config) {
  return [
    ...baseExtensions,
    MyExtension,
    // other extensions
  ];
}
```

### Adding Export Formats

```typescript
// Extend DocumentExporter class
class DocumentExporter {
  async exportToMyFormat(options: ExportOptions): Promise<Blob> {
    // Implementation
  }
}
```

## Performance Considerations

- **Debounced Updates**: Content changes are debounced to prevent excessive re-renders
- **Memoized Content**: Document content generation is memoized for performance
- **Lazy Loading**: Extensions and features are loaded on-demand
- **Virtual Scrolling**: Ready for large document support

## Future Roadmap

1. **Phase 1**: WebSocket integration for real-time collaboration
2. **Phase 2**: Comment system with threading and resolution
3. **Phase 3**: Track changes with operational transformation
4. **Phase 4**: Advanced workflow and approval systems
5. **Phase 5**: Integration with external legal tools and APIs

## Contributing

When adding new features:

1. Follow the existing architecture patterns
2. Add TypeScript interfaces for new types
3. Include proper error handling
4. Add documentation and examples
5. Consider backward compatibility
6. Test with both wizard and standalone modes
