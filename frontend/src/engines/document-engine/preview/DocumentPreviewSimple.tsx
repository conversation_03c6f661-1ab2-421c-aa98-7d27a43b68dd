import React, { useMemo } from 'react';
import { DocumentTheme } from '../core/DocumentTypes';
import { DocumentStyleGenerator } from '../styles/DocumentStyleGenerator';

export interface DocumentPreviewSimpleProps {
  /** Document content to display (HTML string) */
  content: string;
  /** Theme configuration for document styling */
  theme?: Partial<DocumentTheme>;
  /** Additional CSS classes */
  className?: string;
  /** Maximum height for the preview container */
  maxHeight?: string;
  /** Whether to show document shadow */
  showShadow?: boolean;
  /** Scale factor for the preview (0.5 = 50%, 1.0 = 100%) */
  scale?: number;
}

/**
 * DocumentPreviewSimple - Lightweight read-only document preview
 * 
 * A simplified version of DocumentPreviewEngine optimized for:
 * - Modal previews
 * - Embedded previews in lists/cards
 * - Template selection interfaces
 * - Quick document overviews
 * 
 * Features:
 * - Minimal UI overhead
 * - Configurable scaling
 * - Consistent document styling
 * - Optimized for performance
 */
export const DocumentPreviewSimple: React.FC<DocumentPreviewSimpleProps> = ({
  content,
  theme,
  className = '',
  maxHeight = '600px',
  showShadow = true,
  scale = 0.8
}) => {
  // Default theme configuration matching DocumentEngine
  const defaultTheme: DocumentTheme = {
    fontFamily: 'Times New Roman',
    fontSize: '12pt',
    lineHeight: 1.6,
    pageWidth: '8.5in',
    margins: {
      top: '1in',
      right: '1in',
      bottom: '1in',
      left: '1in'
    },
    colors: {
      text: '#1a1a1a',
      background: '#ffffff',
      border: '#e5e7eb',
      accent: '#3b82f6'
    }
  };

  const appliedTheme = { ...defaultTheme, ...theme };

  // Create style generator for unified styling
  const styleGenerator = useMemo(() => new DocumentStyleGenerator(appliedTheme), [appliedTheme]);

  // Generate document styles using unified system
  const documentStyles = useMemo(() => {
    const baseStyles = styleGenerator.generatePreviewStyles(scale);
    return {
      ...baseStyles,
      boxShadow: showShadow ? '0 10px 40px rgba(0, 0, 0, 0.15)' : 'none'
    };
  }, [styleGenerator, scale, showShadow]);

  // Generate base styles for injection into preview content
  const baseStyles = useMemo(() => styleGenerator.generateBaseStyles(), [styleGenerator]);

  const containerStyles = useMemo(() => ({
    maxHeight,
    // Adjust container size based on scale to prevent clipping
    paddingTop: scale < 1 ? `${(1 - scale) * 50}px` : '0',
    paddingBottom: scale < 1 ? `${(1 - scale) * 50}px` : '0'
  }), [maxHeight, scale]);

  return (
    <div
      className={`document-preview-simple overflow-auto bg-gray-50 ${className}`}
      style={containerStyles}
    >
      <div
        className="document-preview-content"
        style={documentStyles}
      >
        {/* Inject unified styles for consistent typography */}
        <style dangerouslySetInnerHTML={{ __html: baseStyles }} />
        <div dangerouslySetInnerHTML={{ __html: content }} />
      </div>
    </div>
  );
};

export default DocumentPreviewSimple;
