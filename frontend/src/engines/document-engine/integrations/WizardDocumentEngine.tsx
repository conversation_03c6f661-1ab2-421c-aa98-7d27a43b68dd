import React, { useMemo, useCallback } from 'react';
import { DocumentEngine } from '../core/DocumentEngine';
import { DocumentEngineConfig, DocumentTheme } from '../core/DocumentTypes';
import { generateContractContent } from '@/components/contracts/contract-wizard/contractUtils';
import { DocumentEngineErrorBoundary } from '../components/DocumentEngineErrorBoundary';

// Local wizard config creator to avoid circular imports
function createWizardConfig(overrides?: Partial<DocumentEngineConfig>): DocumentEngineConfig {
  return {
    mode: 'wizard',
    theme: {
      fontFamily: 'Times New Roman',
      fontSize: '12pt',
      lineHeight: 1.6,
      pageWidth: '8.5in',
      margins: {
        top: '1in',
        right: '1in',
        bottom: '1in',
        left: '1in'
      },
      colors: {
        text: '#1a1a1a',
        background: '#ffffff',
        border: '#e5e7eb',
        accent: '#3b82f6'
      }
    },
    toolbar: {
      simplified: true,
      sections: [],
      hiddenFeatures: []
    },
    export: {
      formats: ['pdf', 'docx', 'html'],
      defaultFormat: 'pdf',
      customOptions: {}
    },
    collaboration: {
      enabled: false
    },
    ...overrides
  };
}

interface WizardDocumentEngineProps {
  // Contract wizard specific props
  wizardData: any;
  onWizardDataChange: (data: any) => void;
  
  // Optional customization
  theme?: Partial<DocumentTheme>;
  config?: Partial<DocumentEngineConfig>;
  className?: string;
  
  // Event handlers
  onSave?: () => void;
  onExport?: (format: string, blob: Blob) => void;
  onError?: (error: any) => void;
}

/**
 * Wizard Document Engine Integration
 * 
 * This component wraps the standalone DocumentEngine to provide
 * seamless integration with the contract wizard workflow.
 * 
 * Features:
 * - Automatic content generation from wizard data
 * - Real-time synchronization with form inputs
 * - Simplified toolbar for wizard context
 * - Backward compatibility with existing wizard
 */
export const WizardDocumentEngine: React.FC<WizardDocumentEngineProps> = ({
  wizardData,
  onWizardDataChange,
  theme,
  config,
  className = '',
  onSave,
  onExport,
  onError
}) => {
  // Generate document content from wizard data
  const documentContent = useMemo(() => {
    console.log('WizardDocumentEngine: Regenerating content from wizard data', {
      hasImportedContent: !!wizardData.importedContent,
      title: wizardData.title,
      partiesCount: wizardData.parties?.length || 0,
      jurisdiction: wizardData.jurisdiction,
      contractType: wizardData.contractType
    });

    if (wizardData.importedContent) {
      console.log('WizardDocumentEngine: Using imported content');
      return wizardData.importedContent;
    }

    const generatedContent = generateContractContent(wizardData);
    console.log('WizardDocumentEngine: Generated new content', {
      contentLength: generatedContent.length,
      preview: generatedContent.substring(0, 100) + '...'
    });

    return generatedContent;
  }, [
    wizardData.importedContent,
    wizardData.title,
    wizardData.parties,
    wizardData.jurisdiction,
    wizardData.contractType,
    wizardData.effectiveDate,
    wizardData.description,
    wizardData.standardClauses,
    wizardData.customClauses,
    wizardData.libraryClauses,
    wizardData.paymentTerms,
    wizardData.contractValue,
    wizardData.currency,
    wizardData.scope,
    wizardData.deliverables
  ]);

  // Create wizard-specific configuration
  const engineConfig = useMemo(() => {
    return createWizardConfig({
      toolbar: {
        simplified: true,
        sections: [
          {
            id: 'file',
            label: 'File',
            items: [
              { id: 'save', type: 'button' },
              { id: 'export', type: 'dropdown' }
            ]
          },
          {
            id: 'format',
            label: 'Format',
            items: [
              { id: 'bold', type: 'button' },
              { id: 'italic', type: 'button' },
              { id: 'underline', type: 'button' },
              { id: 'separator', type: 'separator' },
              { id: 'heading1', type: 'button' },
              { id: 'heading2', type: 'button' },
              { id: 'heading3', type: 'button' }
            ]
          },
          {
            id: 'legal',
            label: 'Legal',
            items: [
              { id: 'legal-clauses', type: 'dropdown' },
              { id: 'numbering', type: 'button' }
            ]
          }
        ],
        hiddenFeatures: ['collaboration', 'comments', 'track-changes']
      },
      ...config
    });
  }, [config]);

  // Apply wizard-specific theme
  const engineTheme = useMemo(() => {
    return {
      fontFamily: 'Times New Roman',
      fontSize: '12pt',
      lineHeight: 1.6,
      pageWidth: '8.5in',
      margins: {
        top: '1in',
        right: '1in',
        bottom: '1in',
        left: '1in'
      },
      colors: {
        text: '#1a1a1a',
        background: '#ffffff',
        border: '#e5e7eb',
        accent: '#3b82f6'
      },
      ...theme
    };
  }, [theme]);

  // Handle content changes from the editor
  const handleContentChange = useCallback((content: string) => {
    console.log('WizardDocumentEngine: Content changed in editor', {
      contentLength: content.length,
      hasWizardData: !!wizardData,
      preview: content.substring(0, 100) + '...'
    });

    onWizardDataChange({
      ...wizardData,
      importedContent: content
    });
  }, [wizardData, onWizardDataChange]);

  // Handle save operation
  const handleSave = useCallback(async (documentData: any) => {
    try {
      // Update wizard data with the latest content
      onWizardDataChange({
        ...wizardData,
        importedContent: documentData.content
      });
      
      // Call the wizard's save handler if provided
      onSave?.();
    } catch (error) {
      onError?.(error);
    }
  }, [wizardData, onWizardDataChange, onSave, onError]);

  // Handle export operation
  const handleExport = useCallback((format: string, blob: Blob) => {
    try {
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${wizardData.title || 'contract'}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      // Call the wizard's export handler if provided
      onExport?.(format, blob);
    } catch (error) {
      onError?.(error);
    }
  }, [wizardData.title, onExport, onError]);

  // Handle errors
  const handleError = useCallback((error: any) => {
    console.error('Wizard Document Engine Error:', error);
    onError?.(error);
  }, [onError]);

  return (
    <DocumentEngineErrorBoundary
      onError={(error) => {
        console.error('Wizard Document Engine Error:', error);
        onError?.(error);
      }}
    >
      <div className={`wizard-document-engine ${className}`}>
        {/* Debug info in development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="text-xs text-gray-500 p-2 border-b bg-gray-50">
            Content Length: {documentContent.length} |
            Title: {wizardData.title || 'No title'} |
            Parties: {wizardData.parties?.length || 0} |
            Has Imported: {wizardData.importedContent ? 'Yes' : 'No'}
          </div>
        )}

        <DocumentEngine
          initialContent={documentContent}
          config={engineConfig}
          theme={engineTheme}
          wizardData={wizardData}
          onWizardDataChange={onWizardDataChange}
          onContentChange={handleContentChange}
          onSave={handleSave}
          onExport={handleExport}
          onError={handleError}
          className="h-full"
        />
      </div>
    </DocumentEngineErrorBoundary>
  );
};

export default WizardDocumentEngine;

/**
 * Hook for using the wizard document engine
 */
export function useWizardDocumentEngine(wizardData: any) {
  // Generate content when wizard data changes
  const content = useMemo(() => {
    return wizardData.importedContent || generateContractContent(wizardData);
  }, [wizardData]);

  // Check if content has been manually edited
  const isManuallyEdited = useMemo(() => {
    if (!wizardData.importedContent) return false;
    
    const generatedContent = generateContractContent(wizardData);
    return wizardData.importedContent !== generatedContent;
  }, [wizardData]);

  // Get document statistics
  const statistics = useMemo(() => {
    const text = content.replace(/<[^>]*>/g, ''); // Strip HTML tags
    const words = text.split(/\s+/).filter((word: string) => word.length > 0).length;
    const characters = text.length;
    const paragraphs = content.split('</p>').length - 1;
    
    return {
      words,
      characters,
      paragraphs,
      isManuallyEdited
    };
  }, [content, isManuallyEdited]);

  return {
    content,
    isManuallyEdited,
    statistics
  };
}

/**
 * Utility function to check if wizard data is ready for document generation
 */
export function isWizardDataReady(wizardData: any): boolean {
  const requiredFields = [
    'title',
    'jurisdiction',
    'contractType',
    'effectiveDate',
    'parties'
  ];

  return requiredFields.every(field => {
    const value = wizardData[field];
    if (field === 'parties') {
      return Array.isArray(value) && value.length > 0 && value.every(party => party.name);
    }
    return value && value.trim().length > 0;
  });
}

/**
 * Utility function to validate wizard document content
 */
export function validateWizardDocument(wizardData: any): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required fields
  if (!wizardData.title?.trim()) {
    errors.push('Document title is required');
  }

  if (!wizardData.jurisdiction?.trim()) {
    errors.push('Jurisdiction is required');
  }

  if (!wizardData.contractType?.trim()) {
    errors.push('Contract type is required');
  }

  if (!wizardData.effectiveDate?.trim()) {
    errors.push('Effective date is required');
  }

  // Check parties
  if (!Array.isArray(wizardData.parties) || wizardData.parties.length === 0) {
    errors.push('At least one party is required');
  } else {
    wizardData.parties.forEach((party: any, index: number) => {
      if (!party.name?.trim()) {
        errors.push(`Party ${index + 1} name is required`);
      }
      if (!party.address?.trim()) {
        warnings.push(`Party ${index + 1} address is missing`);
      }
    });
  }

  // Check clauses
  if (!wizardData.standardClauses?.length && !wizardData.customClauses?.length) {
    warnings.push('No legal clauses have been added');
  }

  // Check payment terms for service contracts
  if (wizardData.contractType?.toLowerCase().includes('service')) {
    if (!wizardData.paymentTerms?.trim()) {
      warnings.push('Payment terms are recommended for service contracts');
    }
    if (!wizardData.contractValue?.trim()) {
      warnings.push('Contract value is recommended for service contracts');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
