import { DocumentTheme } from '../core/DocumentTypes';

/**
 * Unified document style generator for consistent styling across preview and export
 * Ensures WYSIWYG experience between preview and exported documents
 */
export class DocumentStyleGenerator {
  constructor(private theme: DocumentTheme) {}

  /**
   * Generate base document styles that are shared between preview and export
   */
  generateBaseStyles(): string {
    return `
      /* Base Typography */
      body {
        font-family: "${this.theme.fontFamily || 'Times New Roman'}", serif;
        font-size: ${this.theme.fontSize || '12pt'};
        line-height: ${this.theme.lineHeight || 1.6};
        color: ${this.theme.colors?.text || '#1a1a1a'};
        background: ${this.theme.colors?.background || '#ffffff'};
        margin: 0;
        padding: 0;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
      }

      /* Document Container */
      .document-content {
        max-width: 100%;
        margin: 0 auto;
        position: relative;
      }

      /* Professional Content Area with Enhanced Padding */
      .document-content-area {
        padding: 0 0.75in;
        margin: 0 auto;
        max-width: 100%;
        position: relative;
        box-sizing: border-box;
      }

      /* Letterhead and Header Areas - Full Width */
      .document-full-width {
        margin-left: -0.75in;
        margin-right: -0.75in;
        padding-left: 0.75in;
        padding-right: 0.75in;
        box-sizing: border-box;
      }

      /* Responsive Content Padding for Different Zoom Levels */
      @media screen and (max-width: 768px) {
        .document-content-area {
          padding: 0 0.5in;
        }
        .document-full-width {
          margin-left: -0.5in;
          margin-right: -0.5in;
          padding-left: 0.5in;
          padding-right: 0.5in;
        }
      }

      /* Enhanced Text Readability */
      .document-content-area p,
      .document-content-area div,
      .document-content-area li {
        text-align: justify;
        hyphens: auto;
        word-wrap: break-word;
      }

      /* Professional Table Spacing within Content Area */
      .document-content-area .contract-table {
        margin: 1.5em 0;
        width: 100%;
      }

      /* Professional Merge Field Styling */
      .merge-field {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        padding: 0.1em 0.3em;
        border-radius: 3px;
        border: 1px solid #f59e0b;
        position: relative;
        cursor: help;
        transition: all 0.2s ease;
      }

      .merge-field:hover {
        background: linear-gradient(135deg, #fde68a 0%, #fbbf24 100%);
        box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
        transform: translateY(-1px);
      }

      .merge-field::after {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: #1f2937;
        color: white;
        padding: 0.5em 0.75em;
        border-radius: 4px;
        font-size: 10pt;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease;
        z-index: 1000;
        margin-bottom: 5px;
      }

      .merge-field:hover::after {
        opacity: 1;
      }

      .merge-field.validation-error {
        background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
        border-color: #dc2626;
        color: #dc2626;
      }

      /* Headings */
      h1, h2, h3, h4, h5, h6 {
        font-family: "${this.theme.fontFamily || 'Times New Roman'}", serif;
        font-weight: bold;
        margin-top: 1.5em;
        margin-bottom: 0.5em;
        page-break-after: avoid;
        color: ${this.theme.colors?.text || '#1a1a1a'};
      }

      h1 { 
        font-size: 18pt; 
        margin-top: 0;
        text-align: center;
        margin-bottom: 1em;
      }
      h2 { 
        font-size: 16pt; 
        margin-top: 1.5em;
      }
      h3 { 
        font-size: 14pt; 
      }
      h4 { 
        font-size: 12pt; 
        font-weight: bold;
      }
      h5 { 
        font-size: 11pt; 
        font-weight: bold;
      }
      h6 { 
        font-size: 10pt; 
        font-weight: bold;
      }

      /* Paragraphs */
      p {
        margin-bottom: 1em;
        margin-top: 0;
        text-align: justify;
        orphans: 2;
        widows: 2;
      }

      /* Lists */
      ul, ol {
        margin: 1em 0;
        padding-left: 2em;
        page-break-inside: avoid;
      }

      li {
        margin-bottom: 0.5em;
        page-break-inside: avoid;
      }

      /* Tables */
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 1em 0;
        page-break-inside: avoid;
        font-size: inherit;
      }

      th, td {
        border: 1px solid ${this.theme.colors?.text || '#1a1a1a'};
        padding: 8px 12px;
        text-align: left;
        vertical-align: top;
      }

      th {
        background-color: #f5f5f5;
        font-weight: bold;
      }

      /* Legal Document Specific Styles */
      .legal-clause {
        margin: 1.5em 0;
        padding: 0.5em 0;
        page-break-inside: avoid;
      }

      .signature-block {
        margin-top: 3em;
        page-break-inside: avoid;
        border-top: 2px solid ${this.theme.colors?.text || '#1a1a1a'};
        padding-top: 2em;
      }

      .signature-line {
        border-bottom: 1px solid ${this.theme.colors?.text || '#1a1a1a'};
        margin: 2em 0 0.5em 0;
        min-height: 1.5em;
      }

      /* Strong and Emphasis */
      strong, b {
        font-weight: bold;
      }

      em, i {
        font-style: italic;
      }

      /* Links */
      a {
        color: ${this.theme.colors?.accent || '#3b82f6'};
        text-decoration: underline;
      }

      /* Blockquotes */
      blockquote {
        margin: 1em 2em;
        padding-left: 1em;
        border-left: 3px solid ${this.theme.colors?.border || '#e5e7eb'};
        font-style: italic;
      }

      /* Code */
      code {
        font-family: 'Courier New', monospace;
        background-color: #f5f5f5;
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-size: 0.9em;
      }

      pre {
        font-family: 'Courier New', monospace;
        background-color: #f5f5f5;
        padding: 1em;
        border-radius: 5px;
        overflow-x: auto;
        white-space: pre-wrap;
      }

      /* Professional Contract Styling */
      .letterhead {
        position: relative;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-bottom: 3px solid #1f2937;
        margin-bottom: 2em;
      }

      .contract-signature-block {
        border: 2px solid #374151;
        padding: 1.5em;
        background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        position: relative;
      }

      .contract-signature-block::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #374151 0%, #6b7280 50%, #374151 100%);
      }

      .contract-table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5em 0;
        border: 2px solid #374151;
        background: white;
      }

      .contract-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-size: 11pt;
        padding: 0.75em;
        border-bottom: 2px solid #374151;
        text-align: left;
      }

      .contract-table td {
        padding: 0.75em;
        border-bottom: 1px solid #e5e7eb;
        vertical-align: top;
      }

      .contract-table tr:last-child td {
        border-bottom: none;
      }

      /* Legal Numbering */
      .contract-section-header {
        font-size: 14pt;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        margin: 2em 0 1em 0;
        padding-bottom: 0.5em;
        border-bottom: 2px solid #374151;
        color: #1f2937;
      }

      .contract-subsection-header {
        font-size: 12pt;
        font-weight: bold;
        margin: 1.5em 0 0.75em 0;
        color: #374151;
      }

      /* Professional Lists */
      .contract-list {
        counter-reset: section;
        padding-left: 0;
      }

      .contract-list > li {
        counter-increment: section;
        margin-bottom: 2em;
        position: relative;
        padding-left: 2em;
      }

      .contract-list > li::before {
        content: counter(section) ".";
        position: absolute;
        left: 0;
        font-weight: bold;
        color: #374151;
      }

      /* Horizontal Rules */
      hr {
        border: none;
        border-top: 1px solid ${this.theme.colors?.border || '#e5e7eb'};
        margin: 2em 0;
      }
    `;
  }

  /**
   * Generate print-specific styles for export
   */
  generatePrintStyles(): string {
    return `
      @page {
        size: ${this.theme.pageWidth || '8.5in'} 11in;
        margin: ${this.theme.margins?.top || '1in'} ${this.theme.margins?.right || '1in'} 
                ${this.theme.margins?.bottom || '1in'} ${this.theme.margins?.left || '1in'};
      }

      /* Print-specific rules */
      @media print {
        body {
          print-color-adjust: exact;
          -webkit-print-color-adjust: exact;
        }

        h1, h2, h3, h4, h5, h6 {
          page-break-after: avoid;
        }

        p, li {
          orphans: 2;
          widows: 2;
        }

        table, blockquote, pre {
          page-break-inside: avoid;
        }

        img {
          max-width: 100%;
          page-break-inside: avoid;
        }
      }
    `;
  }

  /**
   * Generate preview-specific styles (includes container styling)
   */
  generatePreviewStyles(zoomLevel: number = 1): React.CSSProperties {
    return {
      fontFamily: `"${this.theme.fontFamily || 'Times New Roman'}", serif`,
      fontSize: this.theme.fontSize || '12pt',
      lineHeight: this.theme.lineHeight || 1.6,
      color: this.theme.colors?.text || '#1a1a1a',
      background: this.theme.colors?.background || '#ffffff',
      minHeight: '11in',
      padding: `${this.theme.margins?.top || '1in'} ${this.theme.margins?.right || '1in'} ${this.theme.margins?.bottom || '1in'} ${this.theme.margins?.left || '1in'}`,
      maxWidth: this.theme.pageWidth || '8.5in',
      // Ensure content doesn't overflow with enhanced padding
      overflow: 'hidden',
      margin: '0 auto',
      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.1)',
      borderRadius: '4px',
      transform: `scale(${zoomLevel})`,
      transformOrigin: 'top center',
      transition: 'transform 0.2s ease-in-out',
      WebkitFontSmoothing: 'antialiased',
      MozOsxFontSmoothing: 'grayscale',
      textRendering: 'optimizeLegibility',
      position: 'relative',
      // Professional page styling with visible boundaries
      border: '1px solid #e5e7eb'
    };
  }

  /**
   * Generate complete export HTML with unified styling
   */
  generateExportHTML(content: string, title: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <title>${title}</title>
          <style>
            ${this.generateBaseStyles()}
            ${this.generatePrintStyles()}
          </style>
        </head>
        <body>
          <div class="document-content">
            ${content}
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Generate print HTML with unified styling
   */
  generatePrintHTML(content: string, title: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="utf-8">
          <title>${title}</title>
          <style>
            ${this.generateBaseStyles()}
            ${this.generatePrintStyles()}
          </style>
        </head>
        <body>
          <div class="document-content">
            ${content}
          </div>
        </body>
      </html>
    `;
  }
}

export default DocumentStyleGenerator;
