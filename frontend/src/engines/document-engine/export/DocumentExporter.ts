import { Editor } from '@tiptap/react';
import html2pdf from 'html2pdf.js';
import { DocumentTheme, ExportOptions, ExportFormat } from '../core/DocumentTypes';
import { DocumentStyleGenerator } from '../styles/DocumentStyleGenerator';

/**
 * Document Export Engine
 * Handles exporting documents to various formats (PDF, DOCX, HTML, etc.)
 */
export class DocumentExporter {
  private editor: Editor;
  private theme: DocumentTheme;
  private styleGenerator: DocumentStyleGenerator;

  constructor(editor: Editor, theme: DocumentTheme) {
    this.editor = editor;
    this.theme = theme;
    this.styleGenerator = new DocumentStyleGenerator(theme);
  }

  /**
   * Export document to specified format
   */
  async export(options: ExportOptions): Promise<Blob> {
    switch (options.format) {
      case 'pdf':
        return this.exportToPDF(options);
      case 'docx':
        return this.exportToDocx(options);
      case 'html':
        return this.exportToHTML(options);
      case 'txt':
        return this.exportToText(options);
      case 'json':
        return this.exportToJSON(options);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * Export to PDF using html2pdf
   */
  private async exportToPDF(options: ExportOptions): Promise<Blob> {
    const htmlContent = this.prepareHTMLForExport(options);
    
    const pdfOptions = {
      margin: this.getPDFMargins(),
      filename: 'document.pdf',
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { 
        scale: 2,
        useCORS: true,
        letterRendering: true
      },
      jsPDF: { 
        unit: 'in', 
        format: options.pageSize?.toLowerCase() || 'letter', 
        orientation: options.orientation || 'portrait'
      }
    };

    try {
      const pdf = await (html2pdf as any)().set(pdfOptions).from(htmlContent).outputPdf('blob');
      return new Blob([pdf], { type: 'application/pdf' });
    } catch (error) {
      throw new Error(`PDF export failed: ${error}`);
    }
  }

  /**
   * Export to DOCX format
   * Note: This is a simplified implementation. For production, consider using libraries like docx or mammoth.js
   */
  private async exportToDocx(options: ExportOptions): Promise<Blob> {
    // For now, we'll create a simple RTF format that can be opened by Word
    const htmlContent = this.editor.getHTML();
    const rtfContent = this.convertHTMLToRTF(htmlContent);
    
    return new Blob([rtfContent], { 
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
    });
  }

  /**
   * Export to HTML
   */
  private async exportToHTML(options: ExportOptions): Promise<Blob> {
    const htmlContent = this.prepareHTMLForExport(options);
    return new Blob([htmlContent], { type: 'text/html' });
  }

  /**
   * Export to plain text
   */
  private async exportToText(options: ExportOptions): Promise<Blob> {
    const textContent = this.editor.getText();
    return new Blob([textContent], { type: 'text/plain' });
  }

  /**
   * Export to JSON format (structured data)
   */
  private async exportToJSON(options: ExportOptions): Promise<Blob> {
    const jsonData = {
      content: this.editor.getHTML(),
      text: this.editor.getText(),
      structure: this.extractDocumentStructure(),
      metadata: {
        exportedAt: new Date().toISOString(),
        format: 'json',
        options
      }
    };

    return new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
  }

  /**
   * Prepare HTML content for export with proper styling using unified system
   */
  private prepareHTMLForExport(options: ExportOptions): string {
    const content = this.editor.getHTML();
    const title = 'Document Export';

    // Use unified style generator for consistent styling
    return this.styleGenerator.generateExportHTML(content, title);
  }



  /**
   * Generate metadata HTML for export
   */
  private generateMetadataHTML(): string {
    const now = new Date();
    return `
      <div class="document-metadata" style="border-bottom: 1px solid #ccc; padding-bottom: 1em; margin-bottom: 2em;">
        <h4>Document Information</h4>
        <p><strong>Exported:</strong> ${now.toLocaleString()}</p>
        <p><strong>Source:</strong> LegalAI Document Engine</p>
      </div>
    `;
  }

  /**
   * Get PDF margins based on theme
   */
  private getPDFMargins(): number[] {
    const margins = this.theme.margins;
    if (!margins) return [1, 1, 1, 1]; // Default 1 inch margins
    
    // Convert string margins to numbers (assuming inches)
    const parseMargin = (margin: string) => parseFloat(margin.replace('in', '')) || 1;
    
    return [
      parseMargin(margins.top),
      parseMargin(margins.right),
      parseMargin(margins.bottom),
      parseMargin(margins.left)
    ];
  }

  /**
   * Convert HTML to RTF format (simplified)
   */
  private convertHTMLToRTF(html: string): string {
    // This is a very basic HTML to RTF conversion
    // For production use, consider using a proper HTML to RTF library
    
    let rtf = '{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}}';
    rtf += '\\f0\\fs24 '; // Font 0, size 12pt (24 half-points)
    
    // Basic HTML tag replacements
    let content = html
      .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\\b\\fs32 $1\\b0\\fs24\\par ')
      .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\\b\\fs28 $1\\b0\\fs24\\par ')
      .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\\b\\fs26 $1\\b0\\fs24\\par ')
      .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\\par ')
      .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '\\b $1\\b0 ')
      .replace(/<em[^>]*>(.*?)<\/em>/gi, '\\i $1\\i0 ')
      .replace(/<br\s*\/?>/gi, '\\par ')
      .replace(/<[^>]+>/g, ''); // Remove remaining HTML tags
    
    rtf += content + '}';
    return rtf;
  }

  /**
   * Extract document structure for JSON export
   */
  private extractDocumentStructure(): any {
    const structure = {
      headings: [] as any[],
      sections: [] as any[],
      tables: [] as any[],
      lists: [] as any[]
    };

    this.editor.state.doc.descendants((node, pos) => {
      switch (node.type.name) {
        case 'heading':
          structure.headings.push({
            level: node.attrs.level,
            text: node.textContent,
            position: pos
          });
          break;
        case 'table':
          structure.tables.push({
            position: pos,
            rows: node.childCount
          });
          break;
        case 'bulletList':
        case 'orderedList':
          structure.lists.push({
            type: node.type.name,
            position: pos,
            items: node.childCount
          });
          break;
      }
    });

    return structure;
  }
}

export default DocumentExporter;
