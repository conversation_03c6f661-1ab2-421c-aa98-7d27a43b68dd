import React, { useState } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Download,
  Save,
  Undo,
  Redo,
  Table as TableIcon,
  Highlighter,
  Palette,
  FileText,
  Hash,
  Quote,
  Indent,
  Outdent,
  Minus as LineHeight,
  Strikethrough,
  Settings
} from 'lucide-react';
import { ToolbarConfig, DocumentTheme, ExportOptions } from '../core/DocumentTypes';

interface DocumentToolbarProps {
  editor: Editor | null;
  config?: ToolbarConfig;
  theme: DocumentTheme;
  onExport: (options: ExportOptions) => void;
  onSave: () => void;
  isExporting?: boolean;
  isLoading?: boolean;
}

export const DocumentToolbar: React.FC<DocumentToolbarProps> = ({
  editor,
  config,
  theme,
  onExport,
  onSave,
  isExporting = false,
  isLoading = false
}) => {
  const [selectedFont, setSelectedFont] = useState(theme.fontFamily || 'Times New Roman');
  const [selectedFontSize, setSelectedFontSize] = useState(theme.fontSize || '12pt');
  const [selectedLineHeight, setSelectedLineHeight] = useState('1.6');
  const [selectedTextColor, setSelectedTextColor] = useState('#000000');

  if (!editor) return null;

  const isActive = (name: string, attributes?: Record<string, any>) => {
    return editor.isActive(name, attributes);
  };

  const canUndo = editor.can().undo();
  const canRedo = editor.can().redo();

  // Font families for legal documents
  const fontFamilies = [
    'Times New Roman',
    'Arial',
    'Calibri',
    'Georgia',
    'Garamond',
    'Book Antiqua'
  ];

  // Font sizes
  const fontSizes = [
    '8pt', '9pt', '10pt', '11pt', '12pt', '14pt', '16pt', '18pt', '20pt', '24pt'
  ];

  // Line height options
  const lineHeights = [
    { value: '1.0', label: 'Single' },
    { value: '1.15', label: '1.15' },
    { value: '1.5', label: '1.5' },
    { value: '1.6', label: '1.6' },
    { value: '2.0', label: 'Double' },
    { value: '2.5', label: '2.5' },
    { value: '3.0', label: 'Triple' }
  ];

  // Handle font family change
  const handleFontChange = (font: string) => {
    setSelectedFont(font);
    editor.chain().focus().setFontFamily(font).run();
  };

  // Handle font size change
  const handleFontSizeChange = (size: string) => {
    setSelectedFontSize(size);
    // Apply font size via CSS custom property or direct styling
    const numericSize = parseInt(size);
    editor.chain().focus().setMark('textStyle', { fontSize: `${numericSize}pt` }).run();
  };

  // Handle line height change
  const handleLineHeightChange = (lineHeight: string) => {
    setSelectedLineHeight(lineHeight);
    editor.chain().focus().setMark('textStyle', { lineHeight }).run();
  };

  // Handle text color change
  const handleTextColorChange = (color: string) => {
    setSelectedTextColor(color);
    editor.chain().focus().setColor(color).run();
  };

  // Handle export with different formats
  const handleExport = (format: 'pdf' | 'docx' | 'html') => {
    onExport({
      format,
      pageSize: 'Letter',
      orientation: 'portrait',
      includeComments: false,
      includeMetadata: true
    });
  };

  // Insert table
  const insertTable = () => {
    editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
  };

  // Insert legal clause
  const insertLegalClause = (type: string) => {
    const templates = {
      whereas: 'WHEREAS, [insert recital text];',
      definition: '"[Term]" means [definition];',
      covenant: 'The [Party] covenants and agrees that [covenant text];',
      condition: 'This Agreement is subject to the condition that [condition text];'
    };
    
    const template = templates[type as keyof typeof templates] || '';
    editor.chain().focus().insertContent(`<p data-clause-type="${type}">${template}</p>`).run();
  };

  // Auto-number sections
  const updateNumbering = () => {
    // TODO: Implement custom numbering logic
    console.log('Auto-numbering feature to be implemented');
  };

  return (
    <div className="document-toolbar border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-950 p-2 sm:p-3">
      <div className="flex flex-wrap items-center gap-1 sm:gap-2">

        {/* File Operations */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onSave}
            disabled={isLoading}
            className="h-10 px-2 sm:px-3 touch-manipulation"
          >
            <Save className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Save</span>
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                disabled={isExporting}
                className="h-8 px-2"
              >
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleExport('pdf')}>
                <FileText className="h-4 w-4 mr-2" />
                Export as PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('docx')}>
                <FileText className="h-4 w-4 mr-2" />
                Export as DOCX
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('html')}>
                <FileText className="h-4 w-4 mr-2" />
                Export as HTML
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Undo/Redo */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!canUndo}
            className="h-10 w-10 p-0 touch-manipulation"
            aria-label="Undo"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!canRedo}
            className="h-10 w-10 p-0 touch-manipulation"
            aria-label="Redo"
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Font Controls */}
        <div className="flex items-center gap-1 mr-2">
          <Select value={selectedFont} onValueChange={handleFontChange}>
            <SelectTrigger className="w-32 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {fontFamilies.map(font => (
                <SelectItem key={font} value={font} style={{ fontFamily: font }}>
                  {font}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedFontSize} onValueChange={handleFontSizeChange}>
            <SelectTrigger className="w-16 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {fontSizes.map(size => (
                <SelectItem key={size} value={size}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Text Formatting */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBold().run()}
            data-active={isActive('bold')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            data-active={isActive('italic')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            data-active={isActive('underline')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <UnderlineIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleStrike().run()}
            data-active={isActive('strike')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Strikethrough className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHighlight().run()}
            data-active={isActive('highlight')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Highlighter className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Advanced Text Formatting */}
        <div className="flex items-center gap-1 mr-2">
          {/* Text Color */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Palette className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <div className="p-2">
                <div className="text-sm font-medium mb-2">Text Color</div>
                <input
                  type="color"
                  value={selectedTextColor}
                  onChange={(e) => handleTextColorChange(e.target.value)}
                  className="w-full h-8 border rounded cursor-pointer"
                />
                <div className="grid grid-cols-6 gap-1 mt-2">
                  {['#000000', '#374151', '#6b7280', '#ef4444', '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6'].map(color => (
                    <button
                      key={color}
                      onClick={() => handleTextColorChange(color)}
                      className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Line Height */}
          <Select value={selectedLineHeight} onValueChange={handleLineHeightChange}>
            <SelectTrigger className="w-20 h-8">
              <LineHeight className="h-4 w-4" />
            </SelectTrigger>
            <SelectContent>
              {lineHeights.map(({ value, label }) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Future: Superscript/Subscript - requires compatible extensions */}
          {/*
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleSuperscript().run()}
            data-active={isActive('superscript')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Superscript className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleSubscript().run()}
            data-active={isActive('subscript')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Subscript className="h-4 w-4" />
          </Button>
          */}
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Headings */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            data-active={isActive('heading', { level: 1 })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Heading1 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            data-active={isActive('heading', { level: 2 })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Heading2 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
            data-active={isActive('heading', { level: 3 })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Heading3 className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Alignment */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            data-active={isActive('textAlign', { textAlign: 'left' })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            data-active={isActive('textAlign', { textAlign: 'center' })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            data-active={isActive('textAlign', { textAlign: 'right' })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <AlignRight className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            data-active={isActive('textAlign', { textAlign: 'justify' })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <AlignJustify className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Indentation */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              // Increase indentation by adding margin-left
              const selection = editor.state.selection;
              const { from, to } = selection;
              editor.chain().focus().command(({ tr, state }) => {
                const doc = state.doc;
                doc.nodesBetween(from, to, (node, pos) => {
                  if (node.type.name === 'paragraph') {
                    const currentMargin = node.attrs.style?.marginLeft || '0px';
                    const currentValue = parseInt(currentMargin) || 0;
                    const newMargin = `${currentValue + 20}px`;
                    tr.setNodeMarkup(pos, undefined, {
                      ...node.attrs,
                      style: { ...node.attrs.style, marginLeft: newMargin }
                    });
                  }
                });
                return true;
              }).run();
            }}
            className="h-8 w-8 p-0"
            title="Increase Indent"
          >
            <Indent className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              // Decrease indentation by reducing margin-left
              const selection = editor.state.selection;
              const { from, to } = selection;
              editor.chain().focus().command(({ tr, state }) => {
                const doc = state.doc;
                doc.nodesBetween(from, to, (node, pos) => {
                  if (node.type.name === 'paragraph') {
                    const currentMargin = node.attrs.style?.marginLeft || '0px';
                    const currentValue = parseInt(currentMargin) || 0;
                    const newMargin = `${Math.max(0, currentValue - 20)}px`;
                    tr.setNodeMarkup(pos, undefined, {
                      ...node.attrs,
                      style: { ...node.attrs.style, marginLeft: newMargin }
                    });
                  }
                });
                return true;
              }).run();
            }}
            className="h-8 w-8 p-0"
            title="Decrease Indent"
          >
            <Outdent className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Lists and Tables */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            data-active={isActive('bulletList')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            data-active={isActive('orderedList')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <ListOrdered className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={insertTable}
            className="h-8 w-8 p-0"
          >
            <TableIcon className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Page Layout Settings */}
        <div className="flex items-center gap-1 mr-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <Settings className="h-4 w-4 mr-1" />
                Layout
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <div className="p-3">
                <div className="text-sm font-medium mb-2">Page Settings</div>

                {/* Page Size */}
                <div className="mb-3">
                  <label className="text-xs text-gray-600 mb-1 block">Page Size</label>
                  <Select defaultValue="Letter">
                    <SelectTrigger className="w-full h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Letter">Letter (8.5" × 11")</SelectItem>
                      <SelectItem value="A4">A4 (210 × 297 mm)</SelectItem>
                      <SelectItem value="Legal">Legal (8.5" × 14")</SelectItem>
                      <SelectItem value="A3">A3 (297 × 420 mm)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Margins */}
                <div className="mb-3">
                  <label className="text-xs text-gray-600 mb-1 block">Margins</label>
                  <Select defaultValue="normal">
                    <SelectTrigger className="w-full h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="narrow">Narrow (0.5")</SelectItem>
                      <SelectItem value="normal">Normal (1")</SelectItem>
                      <SelectItem value="wide">Wide (1.5")</SelectItem>
                      <SelectItem value="custom">Custom...</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Orientation */}
                <div className="mb-3">
                  <label className="text-xs text-gray-600 mb-1 block">Orientation</label>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1 h-8">Portrait</Button>
                    <Button variant="ghost" size="sm" className="flex-1 h-8">Landscape</Button>
                  </div>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Legal Document Features */}
        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <Quote className="h-4 w-4 mr-1" />
                Legal
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => insertLegalClause('whereas')}>
                Insert WHEREAS Clause
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertLegalClause('definition')}>
                Insert Definition
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertLegalClause('covenant')}>
                Insert Covenant
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertLegalClause('condition')}>
                Insert Condition
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={updateNumbering}>
                <Hash className="h-4 w-4 mr-2" />
                Update Numbering
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

export default DocumentToolbar;
