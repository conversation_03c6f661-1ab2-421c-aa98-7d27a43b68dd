@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&display=swap');
@import url('https://fonts.googleapis.com/css2?family=EB+Garamond:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500&display=swap');

/* Tailwind CSS directives - IDE warnings are normal and expected */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Document Engine Styles */
.document-engine {
  @apply flex flex-col h-full;
}

.document-content {
  @apply max-w-none;
  /* Typography styles for document content */
  line-height: 1.7;
  color: hsl(var(--foreground));
}

.document-content h1,
.document-content h2,
.document-content h3,
.document-content h4,
.document-content h5,
.document-content h6 {
  @apply font-bold text-slate-900 dark:text-slate-100;
}

.document-content p {
  @apply text-slate-700 dark:text-slate-300 leading-relaxed;
}

.document-content table {
  @apply border-collapse border border-slate-300 dark:border-slate-600;
}

.document-content th,
.document-content td {
  @apply border border-slate-300 dark:border-slate-600 px-3 py-2;
}

.document-content th {
  @apply bg-slate-100 dark:bg-slate-800 font-semibold;
}



/* Unified Document Styling - Applied to both TipTap Editor and Preview */
.tiptap-editor-content,
.document-preview-content,
.document-content {
  /* Ensure consistent typography across all views */
  font-family: "Times New Roman", serif !important;
  font-size: 12pt !important;
  line-height: 1.6 !important;
  color: #1a1a1a !important;
}

/* Unified heading styles */
.tiptap-editor-content h1,
.document-preview-content h1,
.document-content h1 {
  font-family: "Times New Roman", serif !important;
  font-size: 18pt !important;
  font-weight: bold !important;
  text-align: center !important;
  margin-bottom: 0.5em !important;
  color: #1f2937 !important;
}

.tiptap-editor-content h2,
.document-preview-content h2,
.document-content h2 {
  font-family: "Times New Roman", serif !important;
  font-size: 14pt !important;
  font-weight: bold !important;
  margin: 1.5em 0 0.75em 0 !important;
  color: #1f2937 !important;
  border-bottom: 2px solid #374151 !important;
  padding-bottom: 0.25em !important;
}

.tiptap-editor-content h3,
.document-preview-content h3,
.document-content h3 {
  font-family: "Times New Roman", serif !important;
  font-size: 12pt !important;
  font-weight: bold !important;
  margin: 1em 0 0.5em 0 !important;
  color: #374151 !important;
}

/* Unified paragraph styles */
.tiptap-editor-content p,
.document-preview-content p,
.document-content p {
  font-family: "Times New Roman", serif !important;
  font-size: 12pt !important;
  line-height: 1.6 !important;
  margin-bottom: 1em !important;
  color: #1a1a1a !important;
}

/* Unified table styles */
.tiptap-editor-content table,
.document-preview-content table,
.document-content table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 1.5em 0 !important;
  font-family: "Times New Roman", serif !important;
  border: 1px solid #374151 !important;
}

.tiptap-editor-content th,
.document-preview-content th,
.document-content th {
  background: #f8f9fa !important;
  font-weight: bold !important;
  text-align: left !important;
  padding: 0.75em !important;
  border: 1px solid #374151 !important;
  font-family: "Times New Roman", serif !important;
}

.tiptap-editor-content td,
.document-preview-content td,
.document-content td {
  padding: 0.75em !important;
  border: 1px solid #374151 !important;
  vertical-align: top !important;
  font-family: "Times New Roman", serif !important;
}

/* Unified strong and emphasis */
.tiptap-editor-content strong,
.document-preview-content strong,
.document-content strong {
  font-weight: bold !important;
  color: #1f2937 !important;
}

.tiptap-editor-content em,
.document-preview-content em,
.document-content em {
  font-style: italic !important;
  color: #374151 !important;
}

/* Unified list styles */
.tiptap-editor-content ol,
.document-preview-content ol,
.document-content ol {
  margin: 1em 0 !important;
  padding-left: 2em !important;
}

.tiptap-editor-content ul,
.document-preview-content ul,
.document-content ul {
  margin: 1em 0 !important;
  padding-left: 2em !important;
}

.tiptap-editor-content li,
.document-preview-content li,
.document-content li {
  margin-bottom: 0.5em !important;
  line-height: 1.6 !important;
}

/* Unified merge field styling */
.tiptap-editor-content .merge-field,
.document-preview-content .merge-field,
.document-content .merge-field {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%) !important;
  padding: 0.1em 0.3em !important;
  border-radius: 3px !important;
  border: 1px solid #f59e0b !important;
  position: relative !important;
  cursor: help !important;
  transition: all 0.2s ease !important;
}

.tiptap-editor-content .merge-field:hover,
.document-preview-content .merge-field:hover,
.document-content .merge-field:hover {
  background: linear-gradient(135deg, #fde68a 0%, #fbbf24 100%) !important;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3) !important;
  transform: translateY(-1px) !important;
}

/* Legal Document Specific Styles */
.legal-clause {
  @apply my-4 p-2 border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20;
}

.legal-clause--whereas {
  @apply border-purple-500 bg-purple-50 dark:bg-purple-900/20 italic;
}

.legal-clause--definition {
  @apply border-green-500 bg-green-50 dark:bg-green-900/20 font-semibold;
}

.legal-clause--covenant {
  @apply border-orange-500 bg-orange-50 dark:bg-orange-900/20;
}

.legal-clause--condition {
  @apply border-red-500 bg-red-50 dark:bg-red-900/20;
}

.cross-reference {
  @apply text-blue-600 dark:text-blue-400 underline cursor-pointer hover:text-blue-800 dark:hover:text-blue-300;
}

/* TipTap Editor Styles */
.ProseMirror {
  @apply outline-none;
}

.ProseMirror p.is-editor-empty:first-child::before {
  @apply text-slate-400 dark:text-slate-500;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.has-focus {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}



@layer base {
  :root {
    /* Smooth theme transitions */
    transition: background-color 0.3s ease, color 0.3s ease;

    /* Softened background - warm off-white instead of harsh pure white */
    --background: 30 15% 97%;
    --foreground: 222.2 84% 4.9%;

    --card: 30 15% 98%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 30 15% 98%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 30%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Adding success and warning states while maintaining the color scheme */
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* Subtle shadow values for clean, minimal appearance */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);

    /* Enhanced border and background colors for better light mode contrast */
    --card-border: 214.3 31.8% 85%;
    --card-item-border: 214.3 31.8% 80%;
    --card-item-bg: 220 9% 85%;

    /* Enhanced tab list border for subtle visual definition */
    --tab-list-border: 214.3 31.8% 82%;

    /* Enhanced border colors for all UI components */
    --input-border: 214.3 31.8% 80%;
    --select-border: 214.3 31.8% 80%;
    --textarea-border: 214.3 31.8% 80%;
    --button-outline-border: 214.3 31.8% 78%;
    --dialog-border: 214.3 31.8% 85%;
    --popover-border: 214.3 31.8% 85%;
    --dropdown-border: 214.3 31.8% 85%;

    /* Enhanced navigation active state colors */
    --nav-active: 222.2 84% 4.9%;
    --nav-active-foreground: 210 40% 98%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 222.2 84% 4.9%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Keeping the original dark mode colors */
    --background: 222 18% 11%;
    --foreground: 210 40% 98%;

    --card: 222 16% 13%;
    --card-foreground: 210 40% 98%;

    --popover: 222 16% 13%;
    --popover-foreground: 210 40% 98%;

    --primary: 220 13% 91%;
    --primary-foreground: 222 18% 11%;

    --secondary: 222 14% 18%;
    --secondary-foreground: 210 40% 98%;

    --muted: 223 14% 18%;
    --muted-foreground: 215 20.2% 75%;

    --accent: 220 13% 91%;
    --accent-foreground: 222 18% 11%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    /* Adding success and warning states for dark mode */
    --success: 142 70% 45%;
    --success-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 222 47% 11%;

    --border: 216 15% 25%;
    --input: 216 15% 25%;
    --ring: 220 13% 91%;

    /* Subtle shadow values for dark mode */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.2);
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.15);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.2), 0 2px 4px rgba(0, 0, 0, 0.15);

    /* Dark mode card colors */
    --card-border: 216 15% 25%;
    --card-item-border: 216 15% 25%;
    --card-item-bg: 220 9% 20%;

    /* Enhanced tab list border for dark mode */
    --tab-list-border: 216 15% 28%;

    /* Enhanced border colors for all UI components - dark mode */
    --input-border: 216 15% 30%;
    --select-border: 216 15% 30%;
    --textarea-border: 216 15% 30%;
    --button-outline-border: 216 15% 32%;
    --dialog-border: 216 15% 25%;
    --popover-border: 216 15% 25%;
    --dropdown-border: 216 15% 25%;

    /* Enhanced navigation active state colors for dark mode */
    --nav-active: 210 40% 98%;
    --nav-active-foreground: 222.2 84% 4.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 98%;
    --sidebar-accent-foreground: 222.2 84% 4.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    /* Smooth theme transitions for body */
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Enhanced Typography Hierarchy */
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
    line-height: 1.2;
  }
  h1 {
    @apply text-2xl font-medium tracking-tight;
    letter-spacing: -0.025em;
  }
  h2 {
    @apply text-xl font-medium tracking-tight;
    letter-spacing: -0.02em;
  }
  h3 {
    @apply text-lg font-medium tracking-tight;
    letter-spacing: -0.015em;
  }
  h4 {
    @apply text-base font-medium;
    letter-spacing: -0.01em;
  }
  h5 {
    @apply text-sm font-medium;
  }
  h6 {
    @apply text-xs font-medium uppercase tracking-wide;
    letter-spacing: 0.05em;
  }

  /* Enhanced Body Text */
  p {
    @apply text-sm leading-relaxed;
    line-height: 1.6;
  }

  /* Enhanced Links */
  a {
    @apply transition-all duration-200 ease-out;
  }

  /* Text Hierarchy Utilities */
  .text-primary-content {
    @apply text-foreground;
  }
  .text-secondary-content {
    @apply text-muted-foreground;
  }
  .text-tertiary-content {
    @apply text-muted-foreground/80;
  }
  .text-subtle {
    @apply text-muted-foreground/60;
  }
}

@layer components {
  /* Enhanced Spacing Utilities */
  .spacing-micro {
    @apply gap-1.5;
  }
  .spacing-tight {
    @apply gap-2.5;
  }
  .spacing-comfortable {
    @apply gap-5;
  }
  .spacing-relaxed {
    @apply gap-6;
  }

  /* Enhanced Button Styles */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-all duration-200 ease-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-sm active:scale-[0.98];
  }
  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:shadow-sm active:scale-[0.98];
  }
  .btn-outline {
    @apply hover:bg-accent hover:text-accent-foreground hover:shadow-sm active:scale-[0.98];
    border: 1.5px solid hsl(var(--input));
  }
  .btn-outline:hover {
    border: 1.5px solid hsl(var(--primary) / 0.3);
  }
  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground hover:shadow-sm active:scale-[0.98];
  }
  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:shadow-sm active:scale-[0.98];
  }
  .btn-success {
    @apply bg-success text-success-foreground hover:bg-success/90 hover:shadow-sm active:scale-[0.98];
  }
  .btn-warning {
    @apply bg-warning text-warning-foreground hover:bg-warning/90 hover:shadow-sm active:scale-[0.98];
  }

  /* Enhanced Card Styles with Better Light Mode Contrast */
  .card {
    @apply rounded-xl bg-card text-card-foreground shadow-sm;
    border: 1.5px solid hsl(var(--card-border));
  }
  .card-hover {
    /* Hover effects removed */
  }
  .card-interactive {
    @apply cursor-pointer select-none;
  }
  .card-enhanced {
    @apply p-5 space-y-4;
  }

  /* Enhanced Card Item Styles for Better Visual Separation */
  .card-item {
    @apply rounded-lg p-3 cursor-pointer;
    background-color: hsl(var(--card-item-bg)) !important;
    border: 1.5px solid hsl(var(--card-item-border)) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .card-item-static {
    @apply rounded-lg p-3;
    background-color: hsl(var(--card-item-bg)) !important;
    border: 1.5px solid hsl(var(--card-item-border)) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* Enhanced Text Contrast for Better Readability */
  .text-enhanced-muted {
    color: hsl(215.4 16.3% 25%);
  }

  .dark .text-enhanced-muted {
    color: hsl(215 20.2% 75%);
  }

  .text-card-title {
    @apply font-medium text-foreground;
    color: hsl(222.2 84% 4.9%);
  }

  .dark .text-card-title {
    color: hsl(210 40% 98%);
  }

  /* Enhanced Tab List Container - Dark grey background */
  .tabs-list-enhanced {
    @apply inline-flex h-9 items-center justify-center rounded-lg text-muted-foreground;
    background: hsl(220 9% 85%);
    border: 1.5px solid hsl(var(--tab-list-border));
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    padding: 2px;
  }

  .dark .tabs-list-enhanced {
    background: hsl(220 9% 20%);
    border: 1.5px solid hsl(var(--tab-list-border));
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  }

  /* Enhanced Navigation Active State */
  .nav-item-active {
    background-color: hsl(var(--nav-active)) !important;
    color: hsl(var(--nav-active-foreground)) !important;
    @apply font-semibold shadow-sm;
    border: 1.5px solid hsl(var(--primary) / 0.2);
  }

  .nav-item-active .nav-icon {
    color: hsl(var(--nav-active-foreground)) !important;
    opacity: 1 !important;
  }

  /* Enhanced sidebar active state styles */
  [data-sidebar="menu-button"][data-active="true"] {
    background-color: hsl(var(--sidebar-accent)) !important;
    color: hsl(var(--sidebar-accent-foreground)) !important;
    font-weight: 600 !important;
  }

  [data-sidebar="menu-button"][data-active="true"] svg {
    color: hsl(var(--sidebar-accent-foreground)) !important;
  }

  /* Enhanced Form Element Styles */
  .input {
    @apply flex h-10 w-full rounded-md bg-background px-3 py-2.5 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground/80 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2 focus-visible:border-primary/50 focus-visible:shadow-sm disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
    border: 1.5px solid hsl(var(--input-border));
  }

  /* Enhanced Status Indicators */
  .status-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium border;
  }
  .status-pending {
    @apply bg-warning/10 text-warning border-warning/20;
  }
  .status-success {
    @apply bg-success/10 text-success border-success/20;
  }
  .status-error {
    @apply bg-destructive/10 text-destructive border-destructive/20;
  }
  .status-info {
    @apply bg-primary/10 text-primary border-primary/20;
  }

  /* Enhanced Layout Utilities */
  .page-container {
    @apply w-full h-full bg-background p-5 overflow-auto;
  }
  .page-header {
    @apply flex items-center justify-between mb-6;
  }
  .section-spacing {
    @apply space-y-6;
  }
  .grid-comfortable {
    @apply grid gap-5;
  }
  .flex-comfortable {
    @apply flex gap-5;
  }

  /* Enhanced Typography Components */
  .heading-primary {
    @apply text-2xl font-medium tracking-tight text-foreground;
    letter-spacing: -0.025em;
  }
  .heading-secondary {
    @apply text-xl font-medium tracking-tight text-foreground;
    letter-spacing: -0.02em;
  }
  .heading-tertiary {
    @apply text-lg font-medium tracking-tight text-foreground;
    letter-spacing: -0.015em;
  }
  .heading-section {
    @apply text-base font-medium text-foreground;
    letter-spacing: -0.01em;
  }
  .heading-card {
    @apply text-sm font-medium text-foreground;
  }
  .heading-label {
    @apply text-xs font-medium uppercase tracking-wide text-muted-foreground;
    letter-spacing: 0.05em;
  }

  .body-text {
    @apply text-sm leading-relaxed text-foreground;
    line-height: 1.6;
  }
  .body-text-secondary {
    @apply text-sm leading-relaxed text-muted-foreground;
    line-height: 1.6;
  }
  .caption-text {
    @apply text-xs leading-normal text-muted-foreground/80;
    line-height: 1.4;
  }
  .helper-text {
    @apply text-xs leading-normal text-muted-foreground/60;
    line-height: 1.4;
  }

  /* Enhanced Focus States */
  .focus-enhanced {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2 focus-visible:ring-offset-background;
    transition: all 0.2s ease-out;
  }

  .focus-enhanced-strong {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/40 focus-visible:ring-offset-2 focus-visible:ring-offset-background;
    transition: all 0.2s ease-out;
  }

  /* Enhanced Hover States */
  .hover-lift {
    @apply transition-all duration-200 ease-out hover:-translate-y-0.5 hover:shadow-sm;
  }

  .hover-glow {
    @apply transition-all duration-200 ease-out hover:shadow-md hover:shadow-primary/10;
  }

  .hover-subtle {
    @apply transition-all duration-200 ease-out hover:bg-muted/50;
  }

  /* Enhanced Border Utilities */
  .border-subtle {
    @apply border border-border/30;
  }

  .border-soft {
    @apply border border-border/50;
  }

  .border-medium {
    @apply border border-border/70;
  }

  .border-strong {
    @apply border border-border;
  }

  /* Interactive States */
  .interactive-element {
    @apply transition-all duration-200 ease-out cursor-pointer;
    @apply hover:bg-muted/30 hover:border-primary/30;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2;
    @apply active:scale-[0.98] active:bg-muted/50;
  }

  .interactive-card {
    @apply transition-all duration-300 ease-out cursor-pointer;
    @apply hover:shadow-sm hover:border-primary/20 hover:-translate-y-0.5;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2;
    @apply active:translate-y-0 active:shadow-sm;
  }
}

.font-handwriting {
  font-family: 'Dancing Script', cursive;
}

/* Focus Indicators for Accessibility */
:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Scrollbars */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

/* Enhanced scrollbar for dropdowns */
.scrollbar-track-transparent {
  scrollbar-color: hsl(var(--border)) transparent;
}

.scrollbar-thumb-border {
  scrollbar-color: hsl(var(--border)) transparent;
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thumb-border::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border) / 0.5);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thumb-border::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--border) / 0.8);
}

/* Transitions and Animations */
.transition-all-fast {
  transition: all 0.15s ease;
}

.transition-transform-fast {
  transition: transform 0.15s ease;
}

.transition-opacity-fast {
  transition: opacity 0.15s ease;
}

/* Tag Colors - Light & Dark Mode */
.tag-red {
  background-color: #fef2f2;
  color: #b91c1c;
  border-color: #fee2e2;
}

.dark .tag-red {
  background-color: rgba(185, 28, 28, 0.2);
  color: #f87171;
  border-color: rgba(185, 28, 28, 0.3);
}

.tag-green {
  background-color: #f0fdf4;
  color: #166534;
  border-color: #dcfce7;
}

.dark .tag-green {
  background-color: rgba(22, 101, 52, 0.2);
  color: #4ade80;
  border-color: rgba(22, 101, 52, 0.3);
}

.tag-blue {
  background-color: #eff6ff;
  color: #1e40af;
  border-color: #dbeafe;
}

.dark .tag-blue {
  background-color: rgba(30, 64, 175, 0.2);
  color: #60a5fa;
  border-color: rgba(30, 64, 175, 0.3);
}

.tag-yellow {
  background-color: #fefce8;
  color: #854d0e;
  border-color: #fef9c3;
}

.dark .tag-yellow {
  background-color: rgba(133, 77, 14, 0.2);
  color: #facc15;
  border-color: rgba(133, 77, 14, 0.3);
}

.tag-purple {
  background-color: #faf5ff;
  color: #7e22ce;
  border-color: #f3e8ff;
}

.dark .tag-purple {
  background-color: rgba(126, 34, 206, 0.2);
  color: #c084fc;
  border-color: rgba(126, 34, 206, 0.3);
}

.tag-pink {
  background-color: #fdf2f8;
  color: #be185d;
  border-color: #fce7f3;
}

.dark .tag-pink {
  background-color: rgba(190, 24, 93, 0.2);
  color: #f472b6;
  border-color: rgba(190, 24, 93, 0.3);
}

.tag-orange {
  background-color: #fff7ed;
  color: #c2410c;
  border-color: #ffedd5;
}

.dark .tag-orange {
  background-color: rgba(194, 65, 12, 0.2);
  color: #fb923c;
  border-color: rgba(194, 65, 12, 0.3);
}

.tag-gray {
  background-color: #f9fafb;
  color: #374151;
  border-color: #f3f4f6;
}

.dark .tag-gray {
  background-color: rgba(55, 65, 81, 0.2);
  color: #9ca3af;
  border-color: rgba(55, 65, 81, 0.3);
}

/* Responsive Tables */
@media (max-width: 640px) {
  .responsive-table {
    display: block;
  }

  .responsive-table thead {
    display: none;
  }

  .responsive-table tbody {
    display: block;
  }

  .responsive-table tr {
    display: block;
    margin-bottom: 1rem;
    border: 1px solid hsl(var(--border));
    border-radius: 0.5rem;
    padding: 0.5rem;
  }

  .responsive-table td {
    display: flex;
    justify-content: space-between;
    text-align: right;
    padding: 0.5rem;
    border-bottom: 1px solid hsl(var(--border));
  }

  .responsive-table td:last-child {
    border-bottom: none;
  }

  .responsive-table td::before {
    content: attr(data-label);
    font-weight: 500;
    text-align: left;
    color: hsl(var(--muted-foreground));
  }
}

/* Safe Area Insets */
.pt-safe {
  padding-top: env(safe-area-inset-top);
}

.pb-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

.pl-safe {
  padding-left: env(safe-area-inset-left);
}

.pr-safe {
  padding-right: env(safe-area-inset-right);
}

/* Enhanced Animation keyframes */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Glass Cards Floating Animations */
@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(-20px) rotate(var(--rotation, 0deg));
  }
}

@keyframes float-medium {
  0%, 100% {
    transform: translateY(0px) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(-15px) rotate(var(--rotation, 0deg));
  }
}

@keyframes float-fast {
  0%, 100% {
    transform: translateY(0px) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(-10px) rotate(var(--rotation, 0deg));
  }
}

@keyframes float-slow-reverse {
  0%, 100% {
    transform: translateY(-20px) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(0px) rotate(var(--rotation, 0deg));
  }
}

@keyframes float-medium-reverse {
  0%, 100% {
    transform: translateY(-15px) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(0px) rotate(var(--rotation, 0deg));
  }
}

@keyframes float-fast-reverse {
  0%, 100% {
    transform: translateY(-10px) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(0px) rotate(var(--rotation, 0deg));
  }
}

/* Glass Cards Animation Classes */
.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 6s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 4s ease-in-out infinite;
}

.animate-float-slow-reverse {
  animation: float-slow-reverse 8s ease-in-out infinite;
}

.animate-float-medium-reverse {
  animation: float-medium-reverse 6s ease-in-out infinite;
}

.animate-float-fast-reverse {
  animation: float-fast-reverse 4s ease-in-out infinite;
}

/* Gradient Shift Animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradient-shift-reverse {
  0% {
    background-position: 100% 50%;
  }
  50% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

.animate-gradient-shift {
  animation: gradient-shift 15s ease-in-out infinite;
}

.animate-gradient-shift-reverse {
  animation: gradient-shift-reverse 20s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce-subtle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes loading-bar {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes loading-progress {
  0% {
    transform: translateX(-100%);
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    transform: translateX(100%);
    width: 100%;
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 0%,
    hsl(var(--muted-foreground) / 0.1) 50%,
    hsl(var(--muted)) 100%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-bounce-subtle {
  animation: bounce-subtle 0.6s ease-in-out;
}

/* Enhanced Button Effects */
.btn-enhanced-primary {
  @apply bg-gradient-to-r from-primary to-primary/90 text-primary-foreground;
  @apply hover:from-primary/90 hover:to-primary/80 hover:shadow-sm hover:scale-[1.02];
  @apply active:scale-[0.98] active:shadow-sm;
  @apply focus-visible:ring-2 focus-visible:ring-primary/30 focus-visible:ring-offset-2;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced-secondary {
  @apply bg-secondary text-secondary-foreground;
  border: 1.5px solid hsl(var(--border) / 0.5);
  @apply hover:bg-secondary/80 hover:shadow-sm hover:scale-[1.01];
  @apply active:scale-[0.99] active:bg-secondary/90;
  @apply focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced-secondary:hover {
  border: 1.5px solid hsl(var(--primary) / 0.3);
}

.btn-enhanced-outline {
  @apply bg-background text-foreground;
  border: 1.5px solid hsl(var(--input) / 0.5);
  @apply hover:bg-accent hover:text-accent-foreground hover:shadow-sm;
  @apply active:bg-accent/80 active:scale-[0.98];
  @apply focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced-outline:hover {
  border: 1.5px solid hsl(var(--primary) / 0.4);
}

.btn-enhanced-ghost {
  @apply text-foreground;
  @apply hover:bg-accent hover:text-accent-foreground hover:shadow-sm;
  @apply active:bg-accent/80 active:scale-[0.98];
  @apply focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Card Effects - Hover effects removed */
.card-enhanced-hover {
  /* Hover effects removed */
}

.card-enhanced-interactive {
  @apply cursor-pointer;
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2;
}

.card-enhanced-glow {
  /* Hover effects removed */
}

/* Micro-interaction Effects - Hover effects removed */
.micro-bounce {
  /* Hover effects removed */
}

.micro-lift {
  /* Hover effects removed */
}

.micro-glow {
  /* Hover effects removed */
}

.micro-pulse {
  /* Hover effects removed */
}

/* Enhanced Table Styles */
.enhanced-table {
  @apply w-full caption-bottom text-sm;
}

.enhanced-table thead th {
  @apply h-10 px-2 text-left align-middle font-medium text-muted-foreground;
  @apply transition-colors duration-200;
}

.enhanced-table thead th.sortable {
  @apply cursor-pointer select-none hover:text-foreground;
}

.enhanced-table thead th.sortable:hover {
  @apply bg-muted/30;
}

.enhanced-table tbody tr {
  @apply border-b transition-all duration-200;
}

.enhanced-table tbody tr:hover {
  @apply bg-muted/50;
}

.enhanced-table tbody tr.selected {
  @apply bg-muted/50 border-primary/20;
}

.enhanced-table tbody tr.focused {
  @apply ring-2 ring-primary ring-inset;
}

.enhanced-table tbody tr:last-child {
  @apply border-0;
}

.enhanced-table tbody td {
  @apply p-2 align-middle;
}

/* Table row actions */
.table-row-actions {
  @apply opacity-0 transition-opacity duration-200;
}

.enhanced-table tbody tr:hover .table-row-actions {
  @apply opacity-100;
}

.enhanced-table tbody tr.focused .table-row-actions {
  @apply opacity-100;
}

/* Staggered Animation Utilities */
.stagger-children > * {
  animation: fadeIn 0.3s ease-out;
}

.stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-children > *:nth-child(5) { animation-delay: 0.5s; }

/* Professional Legal Document Styling */
.legal-document {
  font-family: 'Times New Roman', 'Libre Baskerville', serif;
  line-height: 1.6;
  color: #1a1a1a;
  text-rendering: optimizeLegibility;
  font-size: 12pt;
}

.dark .legal-document {
  color: #e5e7eb;
}

/* Professional Document Container */
.professional-contract-document {
  position: relative;
  background-color: #ffffff;
  padding: 1in 1in 1.25in 1in;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #d1d5db;
  margin: 1.5rem auto;
  max-width: 8.5in;
  min-height: 11in;
  font-family: 'Times New Roman', serif;
  font-size: 12pt;
  line-height: 1.6;
  color: #1a1a1a;
}

.dark .professional-contract-document {
  background-color: #1e293b;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  border: 1px solid #334155;
  color: #e5e7eb;
}

/* Letterhead Area */
.contract-letterhead {
  position: relative;
  text-align: center;
  padding: 0.75in 0 0.5in 0;
  border-bottom: 2px solid #1f2937;
  margin-bottom: 1.5em;
}

.dark .contract-letterhead {
  border-bottom-color: #64748b;
}

.contract-letterhead::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.75in;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  z-index: -1;
}

.dark .contract-letterhead::before {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  border-bottom-color: #475569;
}

/* Document Title Styling */
.contract-title {
  font-family: 'Times New Roman', serif;
  font-size: 18pt;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 0.5em;
  color: #1f2937;
}

.dark .contract-title {
  color: #f1f5f9;
}

.contract-subtitle {
  font-size: 14pt;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.25em;
}

.dark .contract-subtitle {
  color: #9ca3af;
}

/* Section Headers */
.contract-section-header {
  font-family: 'Times New Roman', serif;
  font-size: 14pt;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-align: center;
  margin: 2em 0 1em 0;
  padding: 0.5em 0;
  border-top: 1px solid #d1d5db;
  border-bottom: 1px solid #d1d5db;
  color: #1f2937;
}

.dark .contract-section-header {
  border-color: #4b5563;
  color: #f1f5f9;
}

/* Subsection Headers */
.contract-subsection-header {
  font-family: 'Times New Roman', serif;
  font-size: 12pt;
  font-weight: bold;
  margin: 1.5em 0 0.75em 0;
  color: #374151;
}

.dark .contract-subsection-header {
  color: #d1d5db;
}

/* Party Information Blocks */
.contract-party-block {
  border: 1px solid #d1d5db;
  padding: 1em 1.25em;
  margin: 1em 0;
  background-color: #f9fafb;
  border-radius: 0;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.dark .contract-party-block {
  border-color: #4b5563;
  background-color: #374151;
}

.contract-party-label {
  font-weight: bold;
  text-transform: uppercase;
  font-size: 11pt;
  letter-spacing: 0.05em;
  margin-bottom: 0.5em;
  padding-bottom: 0.25em;
  border-bottom: 1px solid #d1d5db;
  color: #1f2937;
}

.dark .contract-party-label {
  border-bottom-color: #6b7280;
  color: #f3f4f6;
}

/* Signature Blocks */
.contract-signature-section {
  margin-top: 3em;
  page-break-inside: avoid;
}

.contract-signature-block {
  border: 1px solid #d1d5db;
  padding: 1.5em;
  margin: 1em 0;
  background-color: #f9fafb;
  min-height: 4in;
  position: relative;
}

.dark .contract-signature-block {
  border-color: #4b5563;
  background-color: #374151;
}

.signature-line {
  border-bottom: 1px solid #000000;
  height: 1.5em;
  margin: 2em 0 0.5em 0;
  position: relative;
}

.signature-line::after {
  content: "Signature";
  position: absolute;
  bottom: -1.5em;
  left: 0;
  font-size: 10pt;
  color: #6b7280;
  font-style: italic;
}

/* Document Metadata */
.contract-metadata {
  text-align: center;
  font-size: 11pt;
  color: #6b7280;
  margin-bottom: 1.5em;
  font-style: italic;
}

.dark .contract-metadata {
  color: #9ca3af;
}

/* Page Numbers and Headers */
.contract-page-header {
  position: absolute;
  top: 0.5in;
  left: 1in;
  right: 1in;
  height: 0.25in;
  font-size: 10pt;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.125in;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark .contract-page-header {
  color: #9ca3af;
  border-bottom-color: #4b5563;
}

.contract-page-footer {
  position: absolute;
  bottom: 0.5in;
  left: 1in;
  right: 1in;
  height: 0.25in;
  font-size: 10pt;
  color: #6b7280;
  border-top: 1px solid #e5e7eb;
  padding-top: 0.125in;
  text-align: center;
}

.dark .contract-page-footer {
  color: #9ca3af;
  border-top-color: #4b5563;
}

/* Professional Table Styling for Contracts */
.contract-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
  font-size: 11pt;
  border: 1px solid #374151;
}

.contract-table th {
  background-color: #f8fafc;
  padding: 0.75em;
  text-align: left;
  font-weight: bold;
  border: 1px solid #d1d5db;
  font-size: 11pt;
}

.dark .contract-table th {
  background-color: #374151;
  border-color: #4b5563;
}

.contract-table td {
  padding: 0.75em;
  border: 1px solid #d1d5db;
  vertical-align: top;
}

.dark .contract-table td {
  border-color: #4b5563;
}

/* Clause Styling */
.contract-clause {
  margin: 1.5em 0;
  padding-left: 1em;
  border-left: 3px solid #d1d5db;
  text-align: justify;
}

.dark .contract-clause {
  border-left-color: #4b5563;
}

.contract-clause-number {
  font-weight: bold;
  margin-bottom: 0.5em;
  color: #374151;
}

.dark .contract-clause-number {
  color: #d1d5db;
}

/* Recitals Styling */
.contract-recitals {
  margin: 2em 0;
  padding: 1em 1.5em;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  font-style: italic;
  text-align: justify;
}

.dark .contract-recitals {
  background-color: #374151;
  border-color: #4b5563;
}

/* Witness Section */
.contract-witness-section {
  margin-top: 2em;
  text-align: center;
  font-weight: bold;
  font-size: 13pt;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Professional List Styling */
.contract-list {
  margin: 1em 0;
  padding-left: 2em;
}

.contract-list li {
  margin-bottom: 0.75em;
  text-align: justify;
  line-height: 1.6;
}

/* Document Watermark */
.contract-watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 4rem;
  color: rgba(209, 213, 219, 0.15);
  pointer-events: none;
  z-index: 0;
  font-family: 'Times New Roman', serif;
  white-space: nowrap;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.2em;
}

.dark .contract-watermark {
  color: rgba(75, 85, 99, 0.2);
}

/* Print Styles */
@media print {
  .professional-contract-document {
    box-shadow: none;
    border: none;
    margin: 0;
    padding: 1in;
    max-width: none;
    width: 100%;
    background: white !important;
    color: black !important;
  }

  .contract-letterhead::before {
    background: #f8fafc !important;
  }

  .contract-party-block,
  .contract-signature-block {
    background: #f9fafb !important;
    border-color: #d1d5db !important;
  }

  .contract-table th {
    background: #f8fafc !important;
  }

  .contract-recitals {
    background: #f9fafb !important;
  }

  .contract-watermark {
    color: rgba(209, 213, 219, 0.1) !important;
  }

  /* Ensure proper page breaks */
  .contract-signature-section {
    page-break-before: auto;
    page-break-inside: avoid;
  }

  .contract-section-header {
    page-break-after: avoid;
  }
}

/* TipTap Editor Overrides for Professional Documents */
.ProseMirror {
  outline: none !important;
  font-family: 'Times New Roman', serif !important;
  font-size: 12pt !important;
  line-height: 1.6 !important;
  color: #1a1a1a !important;
  max-width: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.dark .ProseMirror {
  color: #e5e7eb !important;
}

/* Override Tailwind prose styles completely */
.ProseMirror.prose,
.ProseMirror.prose-sm,
.ProseMirror.prose-lg,
.ProseMirror.prose-xl,
.ProseMirror.prose-2xl {
  max-width: none !important;
  font-size: 12pt !important;
  line-height: 1.6 !important;
  color: #1a1a1a !important;
}

.ProseMirror h1 {
  font-family: 'Times New Roman', serif !important;
  font-size: 18pt !important;
  font-weight: bold !important;
  text-align: center !important;
  text-transform: uppercase !important;
  letter-spacing: 0.1em !important;
  margin: 1em 0 0.5em 0 !important;
  color: #1a1a1a !important;
}

.ProseMirror h2 {
  font-family: 'Times New Roman', serif !important;
  font-size: 14pt !important;
  font-weight: bold !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  text-align: center !important;
  margin: 2em 0 1em 0 !important;
  padding: 0.5em 0 !important;
  border-top: 1px solid #d1d5db !important;
  border-bottom: 1px solid #d1d5db !important;
  color: #1a1a1a !important;
}

.dark .ProseMirror h2 {
  border-color: #4b5563 !important;
}

.ProseMirror h3 {
  font-family: 'Times New Roman', serif !important;
  font-size: 12pt !important;
  font-weight: bold !important;
  margin: 1.5em 0 0.75em 0 !important;
  color: #1a1a1a !important;
}

.ProseMirror p {
  margin: 0.75em 0 !important;
  text-align: justify !important;
  text-indent: 0 !important;
  font-size: 12pt !important;
  line-height: 1.6 !important;
  color: #1a1a1a !important;
}

.ProseMirror ul, .ProseMirror ol {
  margin: 1em 0 !important;
  padding-left: 2em !important;
}

.ProseMirror li {
  margin-bottom: 0.5em !important;
  text-align: justify !important;
  font-size: 12pt !important;
  line-height: 1.6 !important;
  color: #1a1a1a !important;
}

/* Remove all prose styling that conflicts */
.ProseMirror * {
  font-family: 'Times New Roman', serif !important;
}

/* Ensure professional document container styling */
.ProseMirror[style*="max-width: 8.5in"] {
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
  margin: 0 auto !important;
}

/* Dark mode adjustments for professional documents */
.dark .ProseMirror[style*="max-width: 8.5in"] {
  background: #1f2937 !important;
  color: #e5e7eb !important;
}

.dark .ProseMirror h1,
.dark .ProseMirror h2,
.dark .ProseMirror h3,
.dark .ProseMirror p,
.dark .ProseMirror li {
  color: #e5e7eb !important;
}

/* Ensure proper text rendering */
.ProseMirror {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

/* Enhanced Professional Document Features */
.contract-document-wrapper {
  background: #f8fafc;
  min-height: 100vh;
  padding: 2rem 1rem;
}

.contract-document-container {
  max-width: 8.5in;
  margin: 0 auto;
  background: white;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border-radius: 0;
}

/* Professional numbering for sections */
.contract-section-number {
  display: inline-block;
  width: 2em;
  font-weight: bold;
  color: #374151;
}

/* Enhanced clause styling */
.contract-clause-indent {
  margin-left: 2em;
  text-indent: -1em;
}

/* Professional table enhancements */
.contract-table-wrapper {
  margin: 1.5em 0;
  border: 2px solid #374151;
  overflow: hidden;
}

.contract-table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 11pt;
}

/* Document status indicators */
.contract-status-draft {
  position: relative;
}

.contract-status-draft::before {
  content: "DRAFT";
  position: absolute;
  top: 2in;
  right: 1in;
  transform: rotate(45deg);
  font-size: 2rem;
  color: rgba(239, 68, 68, 0.2);
  font-weight: bold;
  letter-spacing: 0.2em;
  pointer-events: none;
  z-index: 1;
}

/* Enhanced signature block styling */
.signature-block-enhanced {
  border: 2px solid #374151;
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  position: relative;
}

.signature-block-enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #374151 0%, #6b7280 50%, #374151 100%);
}

/* Professional footer styling */
.contract-footer-enhanced {
  border-top: 2px solid #374151;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 1em;
  text-align: center;
  font-size: 10pt;
  color: #6b7280;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .professional-contract-document {
    padding: 0.5in;
    margin: 1rem;
    max-width: none;
  }

  .contract-signature-block {
    min-width: 100%;
    margin-bottom: 2em;
  }

  .contract-table {
    font-size: 10pt;
  }

  .contract-title {
    font-size: 16pt;
  }

  .contract-section-header {
    font-size: 12pt;
  }
}

.dark .legal-document-watermark {
  color: rgba(30, 41, 59, 0.3);
}

.legal-clause {
  margin-bottom: 1.5rem;
  text-align: justify;
  text-justify: inter-word;
  hyphens: auto;
}

.legal-signature-block {
  display: flex;
  flex-direction: column;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.dark .legal-signature-block {
  border-top: 1px solid #334155;
}

.legal-signature-line {
  display: block;
  width: 80%;
  max-width: 250px;
  border-bottom: 1px solid #111827;
  margin: 3rem 0 0.5rem;
}

.dark .legal-signature-line {
  border-bottom: 1px solid #e5e7eb;
}

.legal-document-number {
  position: absolute;
  top: 0.75in;
  right: 1in;
  font-size: 0.75rem;
  color: #6b7280;
}

.dark .legal-document-number {
  color: #9ca3af;
}

.legal-page-number {
  position: absolute;
  bottom: 0.5in;
  right: 1in;
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
}

.dark .legal-page-number {
  color: #9ca3af;
}

/* Custom modal styling for dark mode */
.dark .modal-content {
  background-color: hsl(222 16% 13%);
  color: hsl(210 40% 98%);
}

.dark .modal-content textarea,
.dark .modal-content input,
.dark .modal-content select {
  background-color: hsl(222 18% 11%);
  color: hsl(210 40% 98%);
}



/* Mobile-specific enhancements and optimizations */

/* iOS Safe Area Support */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .safe-area-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .safe-area-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }

  .safe-area-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Mobile viewport optimizations */
@media (max-width: 640px) {
  /* Mobile-specific optimizations */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Optimize form inputs for mobile */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  textarea,
  select {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Mobile-friendly modal sizing */
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  /* Enhanced responsive table improvements */
  .responsive-table {
    font-size: 14px;
  }

  .responsive-table th,
  .responsive-table td {
    padding: 12px 8px;
  }
}

/* Landscape orientation optimizations */
@media (max-height: 500px) and (orientation: landscape) {
  .mobile-landscape-compact {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .mobile-landscape-compact .page-header {
    padding: 0.75rem 0;
  }

  /* Compact navigation for landscape */
  .mobile-landscape-compact .sidebar {
    width: 200px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-border {
    border-width: 0.5px;
  }

  /* Sharper icons and graphics */
  .high-dpi-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Mobile gesture support */
.swipe-container {
  touch-action: pan-x pan-y;
  -webkit-overflow-scrolling: touch;
}

.pinch-zoom {
  touch-action: pinch-zoom;
}

/* Mobile-specific animations */
@media (prefers-reduced-motion: no-preference) {
  .mobile-slide-in {
    animation: mobileSlideIn 0.3s ease-out;
  }

  @keyframes mobileSlideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}

/* Enhanced mobile scrolling */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Mobile-specific focus states */
@media (hover: none) and (pointer: coarse) {
  .mobile-focus:focus {
    outline: 3px solid hsl(var(--primary));
    outline-offset: 2px;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}