import { Suspense } from "react";
import { useRoutes } from "react-router-dom";
import appRoutes from "./routes";
import { ErrorProvider } from "./lib/error-provider";
import { LoadingProvider } from "./lib/loading-provider";
import { SkipLink } from "./components/ui/a11y";
import ErrorBoundary from "./components/error-boundary";
import { ApiErrorFallback } from "./components/ui/api-error-fallback";

function App() {
  // Use the app routes
  const routeElements = useRoutes(appRoutes);

  // Custom fallback for API errors
  const apiErrorFallback = ({ error, resetError }: { error: Error; resetError: () => void }) => (
    <div className="flex items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md">
        <ApiErrorFallback
          error={{
            message: error.message,
            details: error.stack,
            code: "APP_ERROR"
          }}
          title="Application Error"
          description="The application encountered an unexpected error. Please try refreshing the page."
          showDetails={true}
          retry={() => window.location.reload()}
          resetError={resetError}
        />
      </div>
    </div>
  );

  return (
    <ErrorProvider>
      <LoadingProvider>
        <SkipLink targetId="main-content" />
        <ErrorBoundary fallback={apiErrorFallback}>
          <Suspense fallback={
            <div className="flex items-center justify-center h-screen">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          }>
            {routeElements}
          </Suspense>
        </ErrorBoundary>
      </LoadingProvider>
    </ErrorProvider>
  );
}

export default App;
