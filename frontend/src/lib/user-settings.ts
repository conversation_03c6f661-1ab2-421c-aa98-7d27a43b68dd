import { useState, useEffect } from 'react';

// Define types for different settings
export interface ProfileSettings {
  firstName: string;
  lastName: string;
  email: string;
  title: string;
  company: string;
  timezone: string;
  bio: string;
}

export interface AccountSettings {
  username: string;
  language: string;
  // Add other account settings as needed
}

export interface NotificationSettings {
  emailApprovals: boolean;
  emailUpdates: boolean;
  emailReminders: boolean;
  emailComments: boolean;
  systemApprovals: boolean;
  browserNotifications: boolean;
  emailDigestFrequency: string;
}

export interface SecuritySettings {
  // For password change
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface TeamSettings {
  allowInvites: boolean;
  allowContractCreation: boolean;
  requireApproval: boolean;
}

// Mock function to simulate API call
const saveToAPI = async <T>(endpoint: string, data: T): Promise<T> => {
  console.log(`Saving to ${endpoint}:`, data);
  
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, this would be an actual API call
  // return await fetch(`/api/${endpoint}`, {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(data)
  // }).then(res => res.json());
  
  return data;
};

// Hook for managing profile settings
export const useProfileSettings = () => {
  const [settings, setSettings] = useState<ProfileSettings>({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    title: 'Legal Counsel',
    company: 'Acme Corporation',
    timezone: 'america_new_york',
    bio: 'Legal professional with 8+ years of experience in contract law and compliance.'
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const saveSettings = async (newSettings: ProfileSettings) => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      const savedSettings = await saveToAPI('profile', newSettings);
      setSettings(savedSettings);
      setSuccess(true);
      
      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      setError('Failed to save profile settings');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return { settings, setSettings, saveSettings, isLoading, error, success };
};

// Hook for managing account settings
export const useAccountSettings = () => {
  const [settings, setSettings] = useState<AccountSettings>({
    username: 'johndoe',
    language: 'en',
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const saveSettings = async (newSettings: AccountSettings) => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      const savedSettings = await saveToAPI('account', newSettings);
      setSettings(savedSettings);
      setSuccess(true);
      
      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      setError('Failed to save account settings');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return { settings, setSettings, saveSettings, isLoading, error, success };
};

// Hook for managing notification settings
export const useNotificationSettings = () => {
  const [settings, setSettings] = useState<NotificationSettings>({
    emailApprovals: true,
    emailUpdates: true,
    emailReminders: false,
    emailComments: true,
    systemApprovals: true,
    browserNotifications: false,
    emailDigestFrequency: 'daily',
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const saveSettings = async (newSettings: NotificationSettings) => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      const savedSettings = await saveToAPI('notifications', newSettings);
      setSettings(savedSettings);
      setSuccess(true);
      
      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      setError('Failed to save notification settings');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return { settings, setSettings, saveSettings, isLoading, error, success };
};

// Hook for managing security settings (password change)
export const useSecuritySettings = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const changePassword = async (passwordData: SecuritySettings) => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);
    
    // Validate passwords match
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('New passwords do not match');
      setIsLoading(false);
      return;
    }
    
    try {
      await saveToAPI('security/password', passwordData);
      setSuccess(true);
      
      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      setError('Failed to change password');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return { changePassword, isLoading, error, success };
};

// Hook for managing team settings
export const useTeamSettings = () => {
  const [settings, setSettings] = useState<TeamSettings>({
    allowInvites: true,
    allowContractCreation: true,
    requireApproval: true,
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const saveSettings = async (newSettings: TeamSettings) => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);
    
    try {
      const savedSettings = await saveToAPI('team', newSettings);
      setSettings(savedSettings);
      setSuccess(true);
      
      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      setError('Failed to save team settings');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return { settings, setSettings, saveSettings, isLoading, error, success };
};
