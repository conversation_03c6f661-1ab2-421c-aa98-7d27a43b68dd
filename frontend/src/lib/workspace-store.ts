import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { Workspace } from '../types/workspace';

interface WorkspaceState {
  // Data
  workspaces: Workspace[];
  currentWorkspace: Workspace | null;
  
  // Loading states
  isLoading: boolean;
  isRefreshing: boolean;
  
  // Error state
  error: string | null;
  
  // Request tracking
  activeRequests: Set<string>;
  lastFetchTime: number | null;
  
  // Actions
  setWorkspaces: (workspaces: Workspace[]) => void;
  setCurrentWorkspace: (workspace: Workspace | null) => void;
  setLoading: (loading: boolean) => void;
  setRefreshing: (refreshing: boolean) => void;
  setError: (error: string | null) => void;
  addActiveRequest: (requestId: string) => void;
  removeActiveRequest: (requestId: string) => void;
  isRequestActive: (requestId: string) => boolean;
  shouldFetch: () => boolean;
  reset: () => void;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const REQUEST_DEBOUNCE = 1000; // 1 second

export const useWorkspaceStore = create<WorkspaceState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    workspaces: [],
    currentWorkspace: null,
    isLoading: false,
    isRefreshing: false,
    error: null,
    activeRequests: new Set(),
    lastFetchTime: null,

    // Actions
    setWorkspaces: (workspaces) => {
      set({ 
        workspaces, 
        lastFetchTime: Date.now(),
        error: null 
      });
    },

    setCurrentWorkspace: (workspace) => {
      set({ currentWorkspace: workspace });
    },

    setLoading: (loading) => {
      set({ isLoading: loading });
    },

    setRefreshing: (refreshing) => {
      set({ isRefreshing: refreshing });
    },

    setError: (error) => {
      set({ error, isLoading: false, isRefreshing: false });
    },

    addActiveRequest: (requestId) => {
      const { activeRequests } = get();
      const newRequests = new Set(activeRequests);
      newRequests.add(requestId);
      set({ activeRequests: newRequests });
    },

    removeActiveRequest: (requestId) => {
      const { activeRequests } = get();
      const newRequests = new Set(activeRequests);
      newRequests.delete(requestId);
      set({ activeRequests: newRequests });
    },

    isRequestActive: (requestId) => {
      return get().activeRequests.has(requestId);
    },

    shouldFetch: () => {
      const { lastFetchTime, activeRequests, isLoading } = get();
      
      // Don't fetch if already loading
      if (isLoading || activeRequests.size > 0) {
        return false;
      }
      
      // Don't fetch if we have recent data
      if (lastFetchTime && Date.now() - lastFetchTime < CACHE_DURATION) {
        return false;
      }
      
      return true;
    },

    reset: () => {
      set({
        workspaces: [],
        currentWorkspace: null,
        isLoading: false,
        isRefreshing: false,
        error: null,
        activeRequests: new Set(),
        lastFetchTime: null,
      });
    },
  }))
);

// Selectors for better performance
export const useWorkspaces = () => useWorkspaceStore((state) => state.workspaces);
export const useCurrentWorkspace = () => useWorkspaceStore((state) => state.currentWorkspace);
export const useWorkspaceLoading = () => useWorkspaceStore((state) => state.isLoading);
export const useWorkspaceError = () => useWorkspaceStore((state) => state.error);
