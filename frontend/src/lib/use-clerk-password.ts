import { useState } from 'react';
import { useUser, useClerk } from '@clerk/clerk-react';

export interface PasswordChangeData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export const useClerkPassword = () => {
  const { user } = useUser();
  const { client } = useClerk();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Change password using Clerk's API
  const changePassword = async (data: PasswordChangeData) => {
    if (!user) {
      setError('User not authenticated');
      return;
    }

    // Validate passwords match
    if (data.newPassword !== data.confirmPassword) {
      setError('New passwords do not match');
      return;
    }

    // Validate password complexity
    if (data.newPassword.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Update password using Clerk's API
      await user.updatePassword({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });

      setSuccess(true);

      // Reset success message after 3 seconds
      setTimeout(() => setSuccess(false), 3000);
    } catch (err: any) {
      console.error('Error changing password:', err);
      
      // Handle specific Clerk error messages
      if (err.errors && err.errors.length > 0) {
        const clerkError = err.errors[0];
        
        if (clerkError.code === 'form_password_incorrect') {
          setError('Current password is incorrect');
        } else if (clerkError.code === 'form_password_pwned') {
          setError('This password has been found in a data breach. Please choose a more secure password.');
        } else if (clerkError.code === 'form_password_validation_failed') {
          setError('Password does not meet security requirements');
        } else {
          setError(clerkError.message || 'Failed to change password');
        }
      } else {
        setError('Failed to change password');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return {
    changePassword,
    isLoading,
    error,
    success,
  };
};
