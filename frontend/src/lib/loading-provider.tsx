import React, { createContext, useContext, useState, ReactNode } from "react";

interface LoadingContextType {
  isLoading: boolean;
  loadingMessage: string;
  startLoading: (message?: string) => void;
  stopLoading: () => void;
  withLoading: <T>(promise: Promise<T>, message?: string) => Promise<T>;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error("useLoading must be used within a LoadingProvider");
  }
  return context;
};

interface LoadingProviderProps {
  children: ReactNode;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState("Loading...");

  const startLoading = (message = "Loading...") => {
    setLoadingMessage(message);
    setIsLoading(true);
  };

  const stopLoading = () => {
    setIsLoading(false);
  };

  // Helper function to wrap promises with loading state
  const withLoading = async <T,>(promise: Promise<T>, message?: string): Promise<T> => {
    try {
      startLoading(message);
      const result = await promise;
      return result;
    } finally {
      stopLoading();
    }
  };

  return (
    <LoadingContext.Provider value={{ isLoading, loadingMessage, startLoading, stopLoading, withLoading }}>
      {children}
      {isLoading && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-background border rounded-lg shadow-lg p-6 max-w-md w-full mx-4">
            <div className="flex flex-col items-center space-y-4">
              <div className="relative h-10 w-10">
                <div className="absolute h-10 w-10 rounded-full border-2 border-primary animate-ping opacity-75"></div>
                <div className="absolute h-10 w-10 rounded-full border-2 border-primary border-t-transparent animate-spin"></div>
              </div>
              <p className="text-sm text-center">{loadingMessage}</p>
            </div>
          </div>
        </div>
      )}
    </LoadingContext.Provider>
  );
};
