import React, { createContext, useContext, useEffect, useState } from "react";

type Theme = "dark" | "light" | "system";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  forcedTheme?: Theme; // New prop to force a specific theme
};

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  effectiveTheme: "dark" | "light"; // The actual theme being applied
};

const initialState: ThemeProviderState = {
  theme: "system",
  setTheme: () => null,
  effectiveTheme: "light",
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "ui-theme",
  forcedTheme,
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme,
  );

  // Calculate the effective theme
  const getEffectiveTheme = (): "dark" | "light" => {
    if (forcedTheme) {
      return forcedTheme === "system"
        ? (window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light")
        : forcedTheme;
    }

    if (theme === "system") {
      return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }

    return theme;
  };

  const [effectiveTheme, setEffectiveTheme] = useState<"dark" | "light">(getEffectiveTheme);

  useEffect(() => {
    const root = window.document.documentElement;
    const newEffectiveTheme = getEffectiveTheme();

    root.classList.remove("light", "dark");
    root.classList.add(newEffectiveTheme);

    setEffectiveTheme(newEffectiveTheme);

    // Listen for system theme changes if using system theme
    const currentTheme = forcedTheme || theme;
    if (currentTheme === "system") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = () => {
        const updatedTheme = mediaQuery.matches ? "dark" : "light";
        root.classList.remove("light", "dark");
        root.classList.add(updatedTheme);
        setEffectiveTheme(updatedTheme);
      };

      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    }
  }, [theme, forcedTheme]);

  const value = {
    theme: forcedTheme || theme,
    setTheme: (newTheme: Theme) => {
      if (!forcedTheme) {
        localStorage.setItem(storageKey, newTheme);
        setTheme(newTheme);
      }
    },
    effectiveTheme,
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider");

  return context;
};
