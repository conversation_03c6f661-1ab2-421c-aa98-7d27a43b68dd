/**
 * Workspace Cache Service
 * Provides intelligent caching for workspace-specific data with optimistic updates
 */

import { ContractService, TemplateService } from '../services/api-services';

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const MAX_CACHE_SIZE = 10; // Maximum number of workspaces to cache
const PRELOAD_DELAY = 100; // Delay before starting background preload

// Cache entry interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  isLoading: boolean;
  error: string | null;
}

// Workspace data cache
interface WorkspaceDataCache {
  contracts: CacheEntry<any[]>;
  templates: CacheEntry<any[]>;
  lastAccessed: number;
}

// Debounce utility
class Debouncer {
  private timeouts = new Map<string, NodeJS.Timeout>();

  debounce<T extends (...args: any[]) => any>(
    key: string,
    fn: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      const existingTimeout = this.timeouts.get(key);
      if (existingTimeout) {
        clearTimeout(existingTimeout);
      }

      const timeout = setTimeout(() => {
        fn(...args);
        this.timeouts.delete(key);
      }, delay);

      this.timeouts.set(key, timeout);
    };
  }

  cancel(key: string): void {
    const timeout = this.timeouts.get(key);
    if (timeout) {
      clearTimeout(timeout);
      this.timeouts.delete(key);
    }
  }

  cancelAll(): void {
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts.clear();
  }
}

// Workspace cache manager
class WorkspaceCacheManager {
  private cache = new Map<string, WorkspaceDataCache>();
  private debouncer = new Debouncer();
  private accessOrder: string[] = [];
  private abortControllers = new Map<string, AbortController>();

  // Initialize cache entry for a workspace
  private initializeCacheEntry<T>(data: T[] = []): CacheEntry<T[]> {
    return {
      data,
      timestamp: Date.now(),
      isLoading: false,
      error: null,
    };
  }

  // Get or create cache for workspace
  private getWorkspaceCache(workspaceId: string): WorkspaceDataCache {
    if (!this.cache.has(workspaceId)) {
      this.cache.set(workspaceId, {
        contracts: this.initializeCacheEntry(),
        templates: this.initializeCacheEntry(),
        lastAccessed: Date.now(),
      });
    }

    // Update access order for LRU eviction
    this.updateAccessOrder(workspaceId);
    return this.cache.get(workspaceId)!;
  }

  // Update access order for LRU cache
  private updateAccessOrder(workspaceId: string): void {
    const index = this.accessOrder.indexOf(workspaceId);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
    this.accessOrder.push(workspaceId);

    // Evict least recently used entries if cache is full
    while (this.accessOrder.length > MAX_CACHE_SIZE) {
      const lruWorkspaceId = this.accessOrder.shift()!;
      this.cache.delete(lruWorkspaceId);
      this.abortControllers.get(lruWorkspaceId)?.abort();
      this.abortControllers.delete(lruWorkspaceId);
    }
  }

  // Check if cache entry is valid
  private isCacheValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < CACHE_DURATION && !entry.error;
  }

  // Cancel ongoing requests for workspace
  private cancelWorkspaceRequests(workspaceId: string): void {
    const controller = this.abortControllers.get(workspaceId);
    if (controller) {
      controller.abort();
      this.abortControllers.delete(workspaceId);
    }
  }

  // Fetch contracts with caching and workspace validation
  async getContracts(workspaceId: string, token?: string, force = false): Promise<any[]> {
    if (!workspaceId) {
      throw new Error('Workspace ID is required for fetching contracts');
    }

    const cache = this.getWorkspaceCache(workspaceId);

    // Return cached data if valid and not forcing refresh
    if (!force && this.isCacheValid(cache.contracts) && !cache.contracts.isLoading) {
      return cache.contracts.data;
    }

    // Return cached data immediately if loading (optimistic)
    if (cache.contracts.isLoading) {
      return cache.contracts.data;
    }

    // Start loading
    cache.contracts.isLoading = true;
    cache.contracts.error = null;

    try {
      // Cancel any existing request
      this.cancelWorkspaceRequests(`${workspaceId}-contracts`);

      // Create new abort controller
      const controller = new AbortController();
      this.abortControllers.set(`${workspaceId}-contracts`, controller);

      // Always include workspace_id as required parameter for security
      const response = await ContractService.getContracts(
        { workspace_id: workspaceId },
        token
      );

      if (response.data) {
        cache.contracts.data = response.data;
        cache.contracts.timestamp = Date.now();
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        // Handle 403 Forbidden errors specifically
        if (error.status === 403 || error.message?.includes('Access denied')) {
          cache.contracts.error = 'Access denied: You don\'t have permission to access this workspace';
          console.warn(`Access denied to workspace ${workspaceId}:`, error);
        } else {
          cache.contracts.error = error.message || 'Failed to fetch contracts';
          console.error('Failed to fetch contracts:', error);
        }
      }
    } finally {
      cache.contracts.isLoading = false;
      this.abortControllers.delete(`${workspaceId}-contracts`);
    }

    return cache.contracts.data;
  }

  // Fetch templates with caching and workspace validation
  async getTemplates(workspaceId: string, force = false): Promise<any[]> {
    if (!workspaceId) {
      throw new Error('Workspace ID is required for fetching templates');
    }

    const cache = this.getWorkspaceCache(workspaceId);

    // Return cached data if valid and not forcing refresh
    if (!force && this.isCacheValid(cache.templates) && !cache.templates.isLoading) {
      return cache.templates.data;
    }

    // Return cached data immediately if loading (optimistic)
    if (cache.templates.isLoading) {
      return cache.templates.data;
    }

    // Start loading
    cache.templates.isLoading = true;
    cache.templates.error = null;

    try {
      // Cancel any existing request
      this.cancelWorkspaceRequests(`${workspaceId}-templates`);

      // Create new abort controller
      const controller = new AbortController();
      this.abortControllers.set(`${workspaceId}-templates`, controller);

      // Always include workspace_id as required parameter for security
      const response = await TemplateService.getTemplates({
        workspace_id: workspaceId,
      });

      if (response.data) {
        cache.templates.data = response.data;
        cache.templates.timestamp = Date.now();
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        // Handle 403 Forbidden errors specifically
        if (error.status === 403 || error.message?.includes('Access denied')) {
          cache.templates.error = 'Access denied: You don\'t have permission to access this workspace';
          console.warn(`Access denied to workspace ${workspaceId}:`, error);
        } else {
          cache.templates.error = error.message || 'Failed to fetch templates';
          console.error('Failed to fetch templates:', error);
        }
      }
    } finally {
      cache.templates.isLoading = false;
      this.abortControllers.delete(`${workspaceId}-templates`);
    }

    return cache.templates.data;
  }

  // Preload workspace data in background
  preloadWorkspaceData = this.debouncer.debounce(
    'preload',
    async (workspaceId: string, token?: string) => {
      console.log(`🔄 Preloading data for workspace: ${workspaceId}`);
      
      // Start both requests in parallel
      const contractsPromise = this.getContracts(workspaceId, token);
      const templatesPromise = this.getTemplates(workspaceId);

      try {
        await Promise.all([contractsPromise, templatesPromise]);
        console.log(`✅ Preloaded data for workspace: ${workspaceId}`);
      } catch (error) {
        console.warn(`⚠️ Failed to preload some data for workspace: ${workspaceId}`, error);
      }
    },
    PRELOAD_DELAY
  );

  // Get loading states
  getLoadingStates(workspaceId: string): { contracts: boolean; templates: boolean } {
    const cache = this.cache.get(workspaceId);
    return {
      contracts: cache?.contracts.isLoading || false,
      templates: cache?.templates.isLoading || false,
    };
  }

  // Get error states
  getErrorStates(workspaceId: string): { contracts: string | null; templates: string | null } {
    const cache = this.cache.get(workspaceId);
    return {
      contracts: cache?.contracts.error || null,
      templates: cache?.templates.error || null,
    };
  }

  // Clear cache for specific workspace
  clearWorkspaceCache(workspaceId: string): void {
    this.cache.delete(workspaceId);
    this.cancelWorkspaceRequests(workspaceId);
    this.cancelWorkspaceRequests(`${workspaceId}-contracts`);
    this.cancelWorkspaceRequests(`${workspaceId}-templates`);
    
    const index = this.accessOrder.indexOf(workspaceId);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  // Clear all cache
  clearAllCache(): void {
    this.cache.clear();
    this.accessOrder = [];
    this.abortControllers.forEach(controller => controller.abort());
    this.abortControllers.clear();
    this.debouncer.cancelAll();
  }

  // Get cache statistics
  getCacheStats(): { size: number; workspaces: string[] } {
    return {
      size: this.cache.size,
      workspaces: Array.from(this.cache.keys()),
    };
  }
}

// Export singleton instance
export const workspaceCache = new WorkspaceCacheManager();
