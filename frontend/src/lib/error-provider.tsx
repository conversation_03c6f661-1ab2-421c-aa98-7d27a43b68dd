import React, { createContext, useContext, useState, ReactNode } from "react";
import { useToast } from "@/components/ui/use-toast";
import { AlertCircle, XCircle, Info } from "lucide-react";

export type ErrorSeverity = "error" | "warning" | "info";

export interface ErrorState {
  message: string;
  severity: ErrorSeverity;
  details?: string;
  code?: string;
}

interface ErrorContextType {
  error: ErrorState | null;
  setError: (error: ErrorState | null) => void;
  clearError: () => void;
  handleApiError: (error: unknown, fallbackMessage?: string) => void;
}

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

export const useError = () => {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error("useError must be used within an ErrorProvider");
  }
  return context;
};

interface ErrorProviderProps {
  children: ReactNode;
}

export const ErrorProvider: React.FC<ErrorProviderProps> = ({ children }) => {
  const [error, setError] = useState<ErrorState | null>(null);
  const { toast } = useToast();

  const clearError = () => setError(null);

  // Helper function to handle API errors
  const handleApiError = (error: unknown, fallbackMessage = "An unexpected error occurred") => {
    console.error("API Error:", error);

    let errorMessage = fallbackMessage;
    let errorDetails = "";
    let errorCode = "";

    // Handle different error types
    if (error instanceof Error) {
      // Check for specific error patterns
      if (error.message.includes("did not match the expected pattern")) {
        errorMessage = "The server returned an invalid response format";
        errorDetails = "The application received data in an unexpected format. This might be due to API changes or server issues.";
        errorCode = "FORMAT_ERROR";
      } else {
        errorMessage = error.message;
        errorDetails = error.stack || "";
      }
    } else if (typeof error === "object" && error !== null) {
      // Handle API response errors
      const apiError = error as any;
      if (apiError.message) {
        // Check for specific error messages in API responses
        if (typeof apiError.message === 'string' && apiError.message.includes("did not match the expected pattern")) {
          errorMessage = "The server returned an invalid response format";
          errorDetails = "The application received data in an unexpected format. This might be due to API changes or server issues.";
          errorCode = "FORMAT_ERROR";
        } else {
          errorMessage = apiError.message;
        }
      }
      if (apiError.details) errorDetails = apiError.details;
      if (apiError.code) errorCode = apiError.code;
      if (apiError.status) errorCode = String(apiError.status);
    }

    const errorState: ErrorState = {
      message: errorMessage,
      severity: "error",
      details: errorDetails,
      code: errorCode,
    };

    setError(errorState);

    // Also show a toast notification for immediate feedback
    toast({
      title: errorCode ? `Error ${errorCode}` : "Error",
      description: errorMessage,
      variant: "destructive",
    });
  };

  return (
    <ErrorContext.Provider value={{ error, setError, clearError, handleApiError }}>
      {children}
      {/* You could add a global error display component here if needed */}
    </ErrorContext.Provider>
  );
};

// Helper function to show toast notifications for different severities
export const useNotify = () => {
  const { toast } = useToast();

  return {
    success: (message: string, title = "Success") => {
      toast({
        title,
        description: message,
        variant: "default",
        className: "bg-green-50 border-green-200 text-green-800 dark:bg-green-900/30 dark:border-green-800 dark:text-green-300",
      });
    },
    error: (message: string, title = "Error") => {
      toast({
        title,
        description: message,
        variant: "destructive",

      });
    },
    warning: (message: string, title = "Warning") => {
      toast({
        title,
        description: message,
        variant: "default",
        className: "bg-amber-50 border-amber-200 text-amber-800 dark:bg-amber-900/30 dark:border-amber-800 dark:text-amber-300",

      });
    },
    info: (message: string, title = "Information") => {
      toast({
        title,
        description: message,
        variant: "default",
        className: "bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/30 dark:border-blue-800 dark:text-blue-300",

      });
    },
  };
};
