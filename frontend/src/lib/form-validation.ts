// Form validation utility functions

export type ValidationRule = {
  validate: (value: any, formValues?: any) => boolean;
  message: string;
};

export type FieldValidation = {
  [key: string]: ValidationRule[];
};

export type ValidationErrors = {
  [key: string]: string[];
};

// Validate a single field against its rules
export const validateField = (value: any, rules: ValidationRule[]): string[] => {
  return rules
    .filter((rule) => !rule.validate(value))
    .map((rule) => rule.message);
};

// Validate an entire form
export const validateForm = (values: Record<string, any>, validations: FieldValidation): ValidationErrors => {
  const errors: ValidationErrors = {};

  Object.keys(validations).forEach((fieldName) => {
    const value = values[fieldName];
    const fieldErrors = validateField(value, validations[fieldName]);

    if (fieldErrors.length > 0) {
      errors[fieldName] = fieldErrors;
    }
  });

  return errors;
};

// Check if form has any errors
export const hasErrors = (errors: ValidationErrors): boolean => {
  return Object.keys(errors).length > 0;
};

// Common validation rules
export const required = (message = "This field is required"): ValidationRule => ({
  validate: (value) => {
    if (value === null || value === undefined) return false;
    if (typeof value === "string") return value.trim() !== "";
    if (Array.isArray(value)) return value.length > 0;
    return true;
  },
  message,
});

export const minLength = (min: number, message = `Must be at least ${min} characters`): ValidationRule => ({
  validate: (value) => {
    if (typeof value !== "string") return false;
    return value.length >= min;
  },
  message,
});

export const maxLength = (max: number, message = `Must be no more than ${max} characters`): ValidationRule => ({
  validate: (value) => {
    if (typeof value !== "string") return false;
    return value.length <= max;
  },
  message,
});

export const email = (message = "Must be a valid email address"): ValidationRule => ({
  validate: (value) => {
    if (typeof value !== "string") return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  },
  message,
});

export const numeric = (message = "Must be a number"): ValidationRule => ({
  validate: (value) => {
    if (value === "" || value === null || value === undefined) return true; // Allow empty
    return !isNaN(Number(value));
  },
  message,
});

export const date = (message = "Must be a valid date"): ValidationRule => ({
  validate: (value) => {
    if (!value) return true; // Allow empty
    const date = new Date(value);
    return !isNaN(date.getTime());
  },
  message,
});

export const pattern = (regex: RegExp, message = "Invalid format"): ValidationRule => ({
  validate: (value) => {
    if (typeof value !== "string") return false;
    return regex.test(value);
  },
  message,
});

export const match = (field: string, message = "Fields must match"): ValidationRule => ({
  validate: (value, formValues) => {
    return value === formValues[field];
  },
  message,
});
