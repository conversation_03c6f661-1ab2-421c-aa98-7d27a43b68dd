import React, { useState, useMemo, useEffect } from "react";
import { formatDistanceToNow } from "date-fns";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialog<PERSON><PERSON>le,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Bell,
  Check,
  CheckCheck,
  Clock,
  FileText,
  MessageSquare,
  MoreHorizontal,
  Settings,
  Trash,
  User,
  X,
  RefreshCw,
  AlertCircle,
} from "lucide-react";
import { useNotifications } from "@/hooks/useNotifications";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { useToast } from "@/components/ui/use-toast";
import type { Notification, NotificationType } from "@/services/api-types";

interface EnhancedNotificationsCenterProps {
  onSettingsClick?: () => void;
  className?: string;
  onUnreadCountChange?: (count: number) => void;
}

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'approval':
      return <CheckCheck className="h-4 w-4 text-orange-500" />;
    case 'contract':
      return <FileText className="h-4 w-4 text-blue-500" />;
    case 'mention':
      return <MessageSquare className="h-4 w-4 text-green-500" />;
    case 'system':
      return <AlertCircle className="h-4 w-4 text-purple-500" />;
    default:
      return <Bell className="h-4 w-4 text-gray-500" />;
  }
};

const getNotificationTypeLabel = (type: NotificationType) => {
  switch (type) {
    case 'approval':
      return 'Approvals';
    case 'contract':
      return 'Contracts';
    case 'mention':
      return 'Mentions';
    case 'system':
      return 'System';
    default:
      return 'All';
  }
};

const EnhancedNotificationsCenter: React.FC<EnhancedNotificationsCenterProps> = ({
  onSettingsClick,
  className = "",
  onUnreadCountChange
}) => {
  const { currentWorkspace } = useClerkWorkspace();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>("all");
  const [showDeleteAllDialog, setShowDeleteAllDialog] = useState(false);
  
  const {
    notifications,
    summary,
    loading,
    error,
    refreshNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications,
    clearError
  } = useNotifications({
    workspaceId: currentWorkspace?.id || '',
    autoRefresh: true,
    refreshInterval: 30000
  });

  // Filter notifications based on active tab
  const filteredNotifications = useMemo(() => {
    if (activeTab === "all") return notifications;
    if (activeTab === "unread") return notifications.filter(n => n.status === "unread");
    return notifications.filter(n => n.type === activeTab);
  }, [notifications, activeTab]);

  const unreadCount = summary?.unread_count || 0;

  // Update parent component with unread count changes
  useEffect(() => {
    if (onUnreadCountChange) {
      onUnreadCountChange(unreadCount);
    }
  }, [unreadCount, onUnreadCountChange]);

  // Handle notification click
  const handleNotificationClick = async (notification: Notification) => {
    if (notification.status === "unread") {
      await markAsRead(notification.id);
    }
    
    if (notification.action_url) {
      window.location.href = notification.action_url;
    }
  };

  // Handle delete notification
  const handleDeleteNotification = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    await deleteNotification(notificationId);
  };

  // Handle mark as read
  const handleMarkAsRead = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    await markAsRead(notificationId);
  };

  // Handle delete all
  const handleDeleteAll = async () => {
    await deleteAllNotifications();
    setShowDeleteAllDialog(false);
  };

  // Loading skeleton
  const LoadingSkeleton = () => (
    <div className="space-y-3 p-3">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-start gap-3">
          <Skeleton className="h-8 w-8 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
      ))}
    </div>
  );

  // Error state
  if (error) {
    return (
      <Card className={`w-full ${className}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notifications
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                clearError();
                refreshNotifications();
              }}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => {
              clearError();
              refreshNotifications();
            }}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {unreadCount} new
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshNotifications}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="text-xs"
              >
                <CheckCheck className="h-4 w-4 mr-1" />
                Mark All Read
              </Button>
            )}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onSettingsClick}>
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteAllDialog(true)}
                  className="text-destructive"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Delete All
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5 h-8 mx-4 mb-2">
          <TabsTrigger value="all" className="text-xs py-1">
            All
            {notifications.length > 0 && (
              <Badge variant="outline" className="ml-1 text-[10px] px-1 py-0">
                {notifications.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="unread" className="text-xs py-1">
            Unread
            {unreadCount > 0 && (
              <Badge variant="outline" className="ml-1 text-[10px] px-1 py-0">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="approval" className="text-xs py-1">Approvals</TabsTrigger>
          <TabsTrigger value="contract" className="text-xs py-1">Contracts</TabsTrigger>
          <TabsTrigger value="mention" className="text-xs py-1">Mentions</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-2">
          <Card className="border-0 shadow-none">
            <CardContent className="p-0">
              {loading ? (
                <LoadingSkeleton />
              ) : filteredNotifications.length > 0 ? (
                <ScrollArea className="h-[400px]">
                  <div className="divide-y">
                    {filteredNotifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-3 hover:bg-muted/50 cursor-pointer transition-colors ${
                          notification.status === "unread" ? "bg-muted/30" : ""
                        }`}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex items-start gap-3">
                          <div className="mt-0.5">
                            {notification.sender ? (
                              <Avatar className="h-7 w-7">
                                {notification.sender.avatar ? (
                                  <AvatarImage src={notification.sender.avatar} alt={notification.sender.name} />
                                ) : (
                                  <AvatarFallback className="text-xs">
                                    {notification.sender.initials}
                                  </AvatarFallback>
                                )}
                              </Avatar>
                            ) : (
                              <div className="h-7 w-7 rounded-full bg-muted flex items-center justify-center">
                                {getNotificationIcon(notification.type)}
                              </div>
                            )}
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-2">
                              <div className="flex-1 min-w-0">
                                <h4 className="text-sm font-medium text-foreground truncate">
                                  {notification.title}
                                </h4>
                                <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                  {notification.message}
                                </p>
                              </div>
                              
                              <div className="flex items-center gap-1 flex-shrink-0">
                                <span className="text-xs text-muted-foreground">
                                  {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                                </span>
                                {notification.status === "unread" && (
                                  <div className="h-2 w-2 bg-blue-500 rounded-full ml-1" />
                                )}
                              </div>
                            </div>
                            
                            <div className="flex items-center justify-between mt-2">
                              <Badge variant="outline" className="text-xs">
                                {getNotificationTypeLabel(notification.type)}
                              </Badge>
                              
                              <div className="flex items-center gap-1">
                                {notification.status === "unread" && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={(e) => handleMarkAsRead(notification.id, e)}
                                  >
                                    <Check className="h-3 w-3" />
                                  </Button>
                                )}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                                  onClick={(e) => handleDeleteNotification(notification.id, e)}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center py-8">
                  <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-sm text-muted-foreground">
                    {activeTab === "unread" ? "No unread notifications" : "No notifications"}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete All Confirmation Dialog */}
      <AlertDialog open={showDeleteAllDialog} onOpenChange={setShowDeleteAllDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete All Notifications</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete all notifications? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteAll} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete All
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

export default EnhancedNotificationsCenter;
