import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Bell,
  Settings,
  Filter,
  RefreshCw,
  CheckCheck,
  Trash2,
  Volume2,
  VolumeX,
  Mail,
  Smartphone,
  AlertCircle,
  Info,
  CheckCircle,
  Clock
} from "lucide-react";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import EnhancedNotificationsCenter from "./EnhancedNotificationsCenter";
import { useNavigate } from "react-router-dom";

const NotificationsPage = () => {
  const navigate = useNavigate();
  const { currentWorkspace } = useClerkWorkspace();
  const [activeTab, setActiveTab] = useState("notifications");
  
  // Mock notification preferences (in real app, these would come from API)
  const [preferences, setPreferences] = useState({
    email_enabled: true,
    push_enabled: true,
    sound_enabled: true,
    contract_expiry: true,
    approval_required: true,
    contract_updates: true,
    renewal_reminders: true,
    ai_insights: true,
    system_updates: false,
    digest_frequency: "daily",
    quiet_hours_enabled: true,
    quiet_hours_start: "22:00",
    quiet_hours_end: "08:00"
  });

  const handlePreferenceChange = (key: string, value: boolean | string) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
    // In real app, this would save to API
    console.log(`Updated preference ${key} to ${value}`);
  };

  const notificationTypes = [
    {
      key: "contract_expiry",
      label: "Contract Expiry Alerts",
      description: "Get notified when contracts are about to expire",
      icon: Clock,
      color: "text-orange-500"
    },
    {
      key: "approval_required",
      label: "Approval Required",
      description: "Notifications when your approval is needed",
      icon: AlertCircle,
      color: "text-red-500"
    },
    {
      key: "contract_updates",
      label: "Contract Updates",
      description: "When contracts are modified or updated",
      icon: CheckCircle,
      color: "text-blue-500"
    },
    {
      key: "renewal_reminders",
      label: "Renewal Reminders",
      description: "Reminders for upcoming contract renewals",
      icon: RefreshCw,
      color: "text-green-500"
    },
    {
      key: "ai_insights",
      label: "AI Insights",
      description: "AI-powered contract analysis and recommendations",
      icon: Info,
      color: "text-purple-500"
    },
    {
      key: "system_updates",
      label: "System Updates",
      description: "Platform updates and maintenance notifications",
      icon: Settings,
      color: "text-gray-500"
    }
  ];

  return (
    <div className="w-full h-full bg-background overflow-auto">
      <div className="page-container">
        <div className="page-header">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Bell className="h-6 w-6" />
              Notifications
            </h1>
            <p className="text-muted-foreground">
              Manage your notifications and preferences
            </p>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 w-fit">
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="notifications" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>All Notifications</CardTitle>
                <CardDescription>
                  View and manage all your notifications for {currentWorkspace?.name || "your workspace"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!currentWorkspace ? (
                  <div className="h-[400px] flex items-center justify-center">
                    <p className="text-muted-foreground">
                      Please select a workspace to view notifications
                    </p>
                  </div>
                ) : (
                  <EnhancedNotificationsCenter
                    onSettingsClick={() => setActiveTab("settings")}
                    className="border-0 shadow-none"
                  />
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="mt-6 space-y-6">
            {/* Delivery Methods */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Delivery Methods
                </CardTitle>
                <CardDescription>
                  Choose how you want to receive notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <Label htmlFor="email-notifications">Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications via email
                      </p>
                    </div>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={preferences.email_enabled}
                    onCheckedChange={(checked) => handlePreferenceChange("email_enabled", checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Smartphone className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <Label htmlFor="push-notifications">Push Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive browser push notifications
                      </p>
                    </div>
                  </div>
                  <Switch
                    id="push-notifications"
                    checked={preferences.push_enabled}
                    onCheckedChange={(checked) => handlePreferenceChange("push_enabled", checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {preferences.sound_enabled ? (
                      <Volume2 className="h-5 w-5 text-muted-foreground" />
                    ) : (
                      <VolumeX className="h-5 w-5 text-muted-foreground" />
                    )}
                    <div>
                      <Label htmlFor="sound-notifications">Sound Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Play sounds for important notifications
                      </p>
                    </div>
                  </div>
                  <Switch
                    id="sound-notifications"
                    checked={preferences.sound_enabled}
                    onCheckedChange={(checked) => handlePreferenceChange("sound_enabled", checked)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Notification Types */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Notification Types
                </CardTitle>
                <CardDescription>
                  Choose which types of notifications you want to receive
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {notificationTypes.map((type, index) => {
                  const IconComponent = type.icon;
                  return (
                    <div key={type.key}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <IconComponent className={`h-5 w-5 ${type.color}`} />
                          <div>
                            <Label htmlFor={`notification-${type.key}`}>{type.label}</Label>
                            <p className="text-sm text-muted-foreground">
                              {type.description}
                            </p>
                          </div>
                        </div>
                        <Switch
                          id={`notification-${type.key}`}
                          checked={preferences[type.key as keyof typeof preferences] as boolean}
                          onCheckedChange={(checked) => handlePreferenceChange(type.key, checked)}
                        />
                      </div>
                      {index < notificationTypes.length - 1 && <Separator className="mt-4" />}
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            {/* Digest Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Digest Settings
                </CardTitle>
                <CardDescription>
                  Configure notification digest frequency and quiet hours
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="digest-frequency">Email Digest Frequency</Label>
                  <Select
                    value={preferences.digest_frequency}
                    onValueChange={(value) => handlePreferenceChange("digest_frequency", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="immediate">Immediate</SelectItem>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="never">Never</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="quiet-hours">Quiet Hours</Label>
                    <p className="text-sm text-muted-foreground">
                      Disable notifications during specified hours
                    </p>
                  </div>
                  <Switch
                    id="quiet-hours"
                    checked={preferences.quiet_hours_enabled}
                    onCheckedChange={(checked) => handlePreferenceChange("quiet_hours_enabled", checked)}
                  />
                </div>

                {preferences.quiet_hours_enabled && (
                  <div className="grid grid-cols-2 gap-4 pt-2">
                    <div className="space-y-2">
                      <Label htmlFor="quiet-start">Start Time</Label>
                      <Select
                        value={preferences.quiet_hours_start}
                        onValueChange={(value) => handlePreferenceChange("quiet_hours_start", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => {
                            const hour = i.toString().padStart(2, '0');
                            return (
                              <SelectItem key={hour} value={`${hour}:00`}>
                                {hour}:00
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="quiet-end">End Time</Label>
                      <Select
                        value={preferences.quiet_hours_end}
                        onValueChange={(value) => handlePreferenceChange("quiet_hours_end", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 24 }, (_, i) => {
                            const hour = i.toString().padStart(2, '0');
                            return (
                              <SelectItem key={hour} value={`${hour}:00`}>
                                {hour}:00
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Save Settings */}
            <div className="flex justify-end">
              <Button onClick={() => {
                // In real app, this would save preferences to API
                console.log("Saving preferences:", preferences);
                // Show success message
              }}>
                Save Preferences
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default NotificationsPage;
