import React, { ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface StatisticsCardProps {
  title: string;
  value: string;
  description: string;
  icon: ReactNode;
  showProgress?: boolean;
  progressValue?: number;
}

const StatisticsCard = ({
  title,
  value,
  description,
  icon,
  showProgress = false,
  progressValue = 0,
}: StatisticsCardProps) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
          <div className="p-1">{icon}</div>
        </div>
        <div className="space-y-1">
          <p className="text-2xl font-bold">{value}</p>
          <p className="text-xs text-muted-foreground">{description}</p>
          {showProgress && (
            <div className="mt-3">
              <Progress value={progressValue} className="h-1" />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default StatisticsCard; 