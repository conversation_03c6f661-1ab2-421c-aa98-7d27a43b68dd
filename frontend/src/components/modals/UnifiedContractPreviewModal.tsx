import React, { useState, useEffect } from 'react';
import UnifiedModal from '@/components/ui/unified-modal';
import { DocumentPreviewEngine } from '@/engines/document-engine';
import { useApi } from '@/lib/api';
import { ContractService } from '@/services/api-services';
import { FileText, Download, Edit, ExternalLink } from 'lucide-react';

interface UnifiedContractPreviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contractId: string | null;
  contractTitle?: string;
  readOnly?: boolean;
  showActions?: boolean;
  onEdit?: (contractId: string) => void;
  onExport?: (contractId: string) => void;
  onOpen?: (contractId: string) => void;
}

const UnifiedContractPreviewModal: React.FC<UnifiedContractPreviewModalProps> = ({
  open,
  onOpenChange,
  contractId,
  contractTitle,
  readOnly = true,
  showActions = true,
  onEdit,
  onExport,
  onOpen,
}) => {
  const { fetch } = useApi();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [contract, setContract] = useState<any>(null);

  // Fetch contract data when modal opens
  useEffect(() => {
    if (open && contractId) {
      fetchContract();
    }
  }, [open, contractId]);

  const fetchContract = async () => {
    if (!contractId) return;

    setLoading(true);
    setError(null);

    try {
      const result = await fetch(
        () => ContractService.getContract(contractId),
        "Loading contract...",
        "Failed to load contract"
      );

      if (result) {
        setContract(result);
      }
    } catch (err) {
      console.error('Error fetching contract:', err);
      setError('Failed to load contract. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    if (contractId && onEdit) {
      onEdit(contractId);
      onOpenChange(false);
    }
  };

  const handleExport = () => {
    if (contractId && onExport) {
      onExport(contractId);
    }
  };

  const handleOpenContract = () => {
    if (contractId && onOpen) {
      onOpen(contractId);
      onOpenChange(false);
    }
  };

  // Build actions array
  const actions = [];
  
  if (showActions && contractId) {
    if (onExport) {
      actions.push({
        label: 'Export',
        variant: 'outline' as const,
        icon: <Download className="h-4 w-4" />,
        onClick: handleExport,
      });
    }
    
    if (onEdit && !readOnly) {
      actions.push({
        label: 'Edit',
        variant: 'outline' as const,
        icon: <Edit className="h-4 w-4" />,
        onClick: handleEdit,
      });
    }
    
    if (onOpen) {
      actions.push({
        label: 'Open',
        variant: 'default' as const,
        icon: <ExternalLink className="h-4 w-4" />,
        onClick: handleOpenContract,
      });
    }
  }

  const renderContent = () => {
    if (error) {
      return (
        <div className="flex flex-col items-center justify-center py-8">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-destructive mb-2">Error: {error}</p>
        </div>
      );
    }

    if (!contractId) {
      return (
        <div className="flex flex-col items-center justify-center py-8">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <p className="text-muted-foreground">No contract selected</p>
        </div>
      );
    }

    return (
      <div className="h-full">
        <DocumentPreviewEngine
          content={`
            <h1>${contractTitle || contract?.title || 'Contract Preview'}</h1>
            <p><strong>Contract ID:</strong> ${contractId}</p>
            <h2>Contract Content</h2>
            <p>This is a sample contract document for preview purposes.</p>
            <p>In a real application, the contract content would be loaded from the backend API.</p>
          `}
          documentTitle={contractTitle || contract?.title || 'Contract Preview'}
          showZoomControls={true}
          showPrintButton={true}
          showDownloadButton={true}
          showFullscreenButton={false}
          className="h-full"
        />
      </div>
    );
  };

  return (
    <UnifiedModal
      open={open}
      onOpenChange={onOpenChange}
      type="dialog"
      size="5xl"
      title="Contract Preview"
      description={contractTitle || contract?.title}
      loading={loading}
      loadingText="Loading contract..."
      actions={actions}
      className="h-[90vh]"
      contentClassName="h-[90vh] p-0"
      headerClassName="px-6 py-4 border-b"
      footerClassName="px-6 py-4 border-t"
    >
      {renderContent()}
    </UnifiedModal>
  );
};

export default UnifiedContractPreviewModal;
