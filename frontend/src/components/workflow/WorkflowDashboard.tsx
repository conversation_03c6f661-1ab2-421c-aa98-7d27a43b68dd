import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  GitBranch, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp, 
  Users, 
  FileText,
  Settings,
  BarChart3,
  Zap,
  Plus,
  ArrowRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { WorkflowTemplateManager } from './WorkflowTemplateManager';
import { WorkflowPerformanceAnalytics } from './WorkflowPerformanceAnalytics';
import { WorkflowEscalationManager } from './WorkflowEscalationManager';

interface DashboardStats {
  active_workflows: number;
  pending_approvals: number;
  escalations_today: number;
  templates_count: number;
  avg_completion_time: number;
  success_rate: number;
  recent_activity: Array<{
    id: string;
    type: 'workflow_created' | 'approval_completed' | 'escalation_created' | 'template_used';
    title: string;
    timestamp: string;
    status: string;
  }>;
  urgent_items: Array<{
    id: string;
    type: 'timeout_warning' | 'escalation_pending' | 'approval_overdue';
    title: string;
    description: string;
    urgency: 'high' | 'medium' | 'low';
  }>;
}

export const WorkflowDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      
      // Mock data for now - replace with actual API calls
      const mockStats: DashboardStats = {
        active_workflows: 23,
        pending_approvals: 8,
        escalations_today: 2,
        templates_count: 5,
        avg_completion_time: 18.5,
        success_rate: 0.89,
        recent_activity: [
          {
            id: '1',
            type: 'workflow_created',
            title: 'Service Agreement workflow started',
            timestamp: '2024-01-15T10:30:00Z',
            status: 'active'
          },
          {
            id: '2',
            type: 'approval_completed',
            title: 'Legal review completed for Contract #123',
            timestamp: '2024-01-15T09:15:00Z',
            status: 'completed'
          },
          {
            id: '3',
            type: 'escalation_created',
            title: 'Timeout escalation for Contract #456',
            timestamp: '2024-01-15T08:45:00Z',
            status: 'escalated'
          }
        ],
        urgent_items: [
          {
            id: '1',
            type: 'timeout_warning',
            title: 'Contract #789 approval timeout in 2 hours',
            description: 'Legal review step will timeout soon',
            urgency: 'high'
          },
          {
            id: '2',
            type: 'escalation_pending',
            title: 'Escalation requires resolution',
            description: 'Manual escalation created 3 days ago',
            urgency: 'medium'
          }
        ]
      };

      setStats(mockStats);
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'workflow_created': return <GitBranch className="h-4 w-4 text-blue-500" />;
      case 'approval_completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'escalation_created': return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'template_used': return <FileText className="h-4 w-4 text-purple-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'border-l-red-500 bg-red-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-green-500 bg-green-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b bg-background">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">Workflow Management</h1>
          <p className="text-sm text-muted-foreground">Manage approval workflows, templates, and escalations</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Workflow
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="px-6 pt-4 border-b">
            <TabsList className="grid w-full grid-cols-4 max-w-md">
              <TabsTrigger value="overview" className="text-xs">Overview</TabsTrigger>
              <TabsTrigger value="templates" className="text-xs">Templates</TabsTrigger>
              <TabsTrigger value="analytics" className="text-xs">Analytics</TabsTrigger>
              <TabsTrigger value="escalations" className="text-xs">Escalations</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="overview" className="flex-1 p-6 space-y-6 overflow-auto">
          {/* Key Metrics */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Active Workflows</p>
                      <p className="text-2xl font-semibold text-foreground mt-1">{stats.active_workflows}</p>
                    </div>
                    <div className="h-8 w-8 rounded-md bg-blue-100 flex items-center justify-center">
                      <GitBranch className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Pending Approvals</p>
                      <p className="text-2xl font-semibold text-foreground mt-1">{stats.pending_approvals}</p>
                    </div>
                    <div className="h-8 w-8 rounded-md bg-orange-100 flex items-center justify-center">
                      <Clock className="h-4 w-4 text-orange-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Success Rate</p>
                      <p className="text-2xl font-semibold text-foreground mt-1">{Math.round(stats.success_rate * 100)}%</p>
                    </div>
                    <div className="h-8 w-8 rounded-md bg-green-100 flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="border-0 shadow-sm">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Avg Completion</p>
                      <p className="text-2xl font-semibold text-foreground mt-1">{stats.avg_completion_time}h</p>
                    </div>
                    <div className="h-8 w-8 rounded-md bg-purple-100 flex items-center justify-center">
                      <TrendingUp className="h-4 w-4 text-purple-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Recent Activity */}
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm font-medium">
                  <Clock className="h-4 w-4" />
                  <span>Recent Activity</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {stats?.recent_activity.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3 p-2 rounded-md hover:bg-muted/50 transition-colors">
                      {getActivityIcon(activity.type)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground truncate">{activity.title}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                      <Badge variant="secondary" className="text-xs shrink-0">
                        {activity.status}
                      </Badge>
                    </div>
                  ))}
                </div>
                <Button variant="ghost" className="w-full mt-3 text-xs h-8">
                  View All Activity
                  <ArrowRight className="h-3 w-3 ml-2" />
                </Button>
              </CardContent>
            </Card>

            {/* Urgent Items */}
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm font-medium">
                  <AlertTriangle className="h-4 w-4" />
                  <span>Urgent Items</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  {stats?.urgent_items.map((item) => (
                    <div
                      key={item.id}
                      className={cn(
                        "p-3 border-l-2 rounded-r-md",
                        getUrgencyColor(item.urgency)
                      )}
                    >
                      <h4 className="font-medium text-foreground text-sm">{item.title}</h4>
                      <p className="text-xs text-muted-foreground mt-1">{item.description}</p>
                      <div className="flex items-center justify-between mt-2">
                        <Badge
                          variant="outline"
                          className={cn(
                            "text-xs h-5",
                            item.urgency === 'high' ? 'border-red-200 text-red-700 bg-red-50' :
                            item.urgency === 'medium' ? 'border-yellow-200 text-yellow-700 bg-yellow-50' :
                            'border-green-200 text-green-700 bg-green-50'
                          )}
                        >
                          {item.urgency} priority
                        </Badge>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <ArrowRight className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                {stats?.urgent_items.length === 0 && (
                  <div className="text-center py-6 text-muted-foreground">
                    <CheckCircle className="h-6 w-6 mx-auto mb-2 text-green-500" />
                    <p className="text-xs">No urgent items</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <Button variant="outline" className="h-16 flex-col gap-1 text-xs">
                  <FileText className="h-4 w-4" />
                  <span>Create Template</span>
                </Button>
                <Button variant="outline" className="h-16 flex-col gap-1 text-xs">
                  <BarChart3 className="h-4 w-4" />
                  <span>View Analytics</span>
                </Button>
                <Button variant="outline" className="h-16 flex-col gap-1 text-xs">
                  <Zap className="h-4 w-4" />
                  <span>Check Timeouts</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

          <TabsContent value="templates" className="flex-1 p-6 overflow-auto">
            <WorkflowTemplateManager />
          </TabsContent>

          <TabsContent value="analytics" className="flex-1 p-6 overflow-auto">
            <WorkflowPerformanceAnalytics />
          </TabsContent>

          <TabsContent value="escalations" className="flex-1 p-6 overflow-auto">
            <WorkflowEscalationManager />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
