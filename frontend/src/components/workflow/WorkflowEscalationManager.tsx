import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Plus, 
  Search, 
  Filter,
  TrendingUp,
  Users,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface Escalation {
  id: string;
  workflow_id: string;
  escalation_type: 'timeout' | 'manual' | 'automatic' | 'conditional';
  reason: string;
  escalated_at: string;
  escalated_by: string;
  resolved_at?: string;
  resolved_by?: string;
  resolution_notes?: string;
  contract_title?: string;
  original_approvers: string[];
  new_approvers: string[];
  status: 'pending' | 'resolved';
}

interface EscalationAnalytics {
  total_escalations: number;
  escalation_rate_percent: number;
  resolved_escalations: number;
  resolution_rate_percent: number;
  average_resolution_time_hours: number;
  escalation_types: Record<string, number>;
}

export const WorkflowEscalationManager: React.FC = () => {
  const [escalations, setEscalations] = useState<Escalation[]>([]);
  const [analytics, setAnalytics] = useState<EscalationAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedEscalation, setSelectedEscalation] = useState<Escalation | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isResolveDialogOpen, setIsResolveDialogOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [timePeriod, setTimePeriod] = useState(30);

  useEffect(() => {
    loadEscalations();
    loadAnalytics();
  }, [filterStatus, filterType, timePeriod]);

  const loadEscalations = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        days: timePeriod.toString(),
        ...(filterStatus !== 'all' && { status: filterStatus }),
        ...(filterType !== 'all' && { escalation_type: filterType })
      });

      const response = await fetch(`/api/v1/workflow-escalations/?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setEscalations(data.data || []);
      } else {
        throw new Error('Failed to load escalations');
      }
    } catch (error) {
      console.error('Error loading escalations:', error);
      toast.error('Failed to load escalations');
    } finally {
      setLoading(false);
    }
  };

  const loadAnalytics = async () => {
    try {
      const response = await fetch(`/api/v1/workflow-escalations/analytics?days=${timePeriod}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAnalytics(data.data);
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
    }
  };

  const handleCreateEscalation = async (escalationData: any) => {
    try {
      const response = await fetch('/api/v1/workflow-escalations/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(escalationData)
      });

      if (response.ok) {
        toast.success('Escalation created successfully');
        setIsCreateDialogOpen(false);
        loadEscalations();
      } else {
        throw new Error('Failed to create escalation');
      }
    } catch (error) {
      console.error('Error creating escalation:', error);
      toast.error('Failed to create escalation');
    }
  };

  const handleResolveEscalation = async (escalationId: string, resolutionNotes: string) => {
    try {
      const response = await fetch(`/api/v1/workflow-escalations/${escalationId}/resolve`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({ resolution_notes: resolutionNotes })
      });

      if (response.ok) {
        toast.success('Escalation resolved successfully');
        setIsResolveDialogOpen(false);
        setSelectedEscalation(null);
        loadEscalations();
        loadAnalytics();
      } else {
        throw new Error('Failed to resolve escalation');
      }
    } catch (error) {
      console.error('Error resolving escalation:', error);
      toast.error('Failed to resolve escalation');
    }
  };

  const getEscalationTypeColor = (type: string) => {
    switch (type) {
      case 'timeout': return 'bg-red-100 text-red-800';
      case 'manual': return 'bg-blue-100 text-blue-800';
      case 'automatic': return 'bg-yellow-100 text-yellow-800';
      case 'conditional': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'resolved' 
      ? 'bg-green-100 text-green-800' 
      : 'bg-orange-100 text-orange-800';
  };

  const filteredEscalations = escalations.filter(escalation =>
    escalation.contract_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    escalation.reason?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Workflow Escalations</h2>
          <p className="text-gray-600">Manage and track workflow escalations</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Escalation
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Manual Escalation</DialogTitle>
              <DialogDescription>
                Manually escalate a workflow that requires immediate attention
              </DialogDescription>
            </DialogHeader>
            <EscalationForm onSubmit={handleCreateEscalation} onCancel={() => setIsCreateDialogOpen(false)} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Analytics Cards */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Escalations</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.total_escalations}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Escalation Rate</p>
                  <p className="text-2xl font-bold text-orange-600">{analytics.escalation_rate_percent}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Resolution Rate</p>
                  <p className="text-2xl font-bold text-green-600">{analytics.resolution_rate_percent}%</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Resolution Time</p>
                  <p className="text-2xl font-bold text-blue-600">{analytics.average_resolution_time_hours.toFixed(1)}h</p>
                </div>
                <Clock className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Search escalations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="resolved">Resolved</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="timeout">Timeout</SelectItem>
            <SelectItem value="manual">Manual</SelectItem>
            <SelectItem value="automatic">Automatic</SelectItem>
            <SelectItem value="conditional">Conditional</SelectItem>
          </SelectContent>
        </Select>
        <Select value={timePeriod.toString()} onValueChange={(value) => setTimePeriod(Number(value))}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Escalations List */}
      <div className="space-y-4">
        {filteredEscalations.map((escalation) => (
          <Card key={escalation.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="font-semibold text-gray-900">
                      {escalation.contract_title || `Workflow ${escalation.workflow_id.slice(0, 8)}`}
                    </h3>
                    <Badge className={getEscalationTypeColor(escalation.escalation_type)}>
                      {escalation.escalation_type}
                    </Badge>
                    <Badge className={getStatusColor(escalation.status)}>
                      {escalation.status}
                    </Badge>
                  </div>
                  <p className="text-gray-600 mb-2">{escalation.reason}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>Escalated: {new Date(escalation.escalated_at).toLocaleDateString()}</span>
                    <span>By: {escalation.escalated_by}</span>
                    {escalation.resolved_at && (
                      <span>Resolved: {new Date(escalation.resolved_at).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {escalation.status === 'pending' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedEscalation(escalation);
                        setIsResolveDialogOpen(true);
                      }}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Resolve
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredEscalations.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No escalations found</h3>
            <p className="text-gray-600">
              {searchTerm || filterStatus !== 'all' || filterType !== 'all'
                ? 'No escalations match your current filters.'
                : 'No escalations have been created yet.'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Resolve Dialog */}
      <Dialog open={isResolveDialogOpen} onOpenChange={setIsResolveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Resolve Escalation</DialogTitle>
            <DialogDescription>
              Add resolution notes and mark this escalation as resolved
            </DialogDescription>
          </DialogHeader>
          {selectedEscalation && (
            <ResolveEscalationForm
              escalation={selectedEscalation}
              onSubmit={(notes) => handleResolveEscalation(selectedEscalation.id, notes)}
              onCancel={() => setIsResolveDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Helper Components
const EscalationForm: React.FC<{ onSubmit: (data: any) => void; onCancel: () => void }> = ({
  onSubmit,
  onCancel
}) => {
  const [formData, setFormData] = useState({
    workflow_id: '',
    escalation_type: 'manual',
    reason: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="workflow_id">Workflow ID</Label>
        <Input
          id="workflow_id"
          value={formData.workflow_id}
          onChange={(e) => setFormData({ ...formData, workflow_id: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="reason">Reason for Escalation</Label>
        <Textarea
          id="reason"
          value={formData.reason}
          onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
          required
          rows={3}
        />
      </div>
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">Create Escalation</Button>
      </div>
    </form>
  );
};

const ResolveEscalationForm: React.FC<{
  escalation: Escalation;
  onSubmit: (notes: string) => void;
  onCancel: () => void;
}> = ({ escalation, onSubmit, onCancel }) => {
  const [resolutionNotes, setResolutionNotes] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(resolutionNotes);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-2">Escalation Details</h4>
        <p className="text-sm text-gray-600 mb-1"><strong>Type:</strong> {escalation.escalation_type}</p>
        <p className="text-sm text-gray-600 mb-1"><strong>Reason:</strong> {escalation.reason}</p>
        <p className="text-sm text-gray-600"><strong>Escalated:</strong> {new Date(escalation.escalated_at).toLocaleString()}</p>
      </div>
      <div>
        <Label htmlFor="resolution_notes">Resolution Notes</Label>
        <Textarea
          id="resolution_notes"
          value={resolutionNotes}
          onChange={(e) => setResolutionNotes(e.target.value)}
          placeholder="Describe how this escalation was resolved..."
          rows={4}
          required
        />
      </div>
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">Resolve Escalation</Button>
      </div>
    </form>
  );
};
