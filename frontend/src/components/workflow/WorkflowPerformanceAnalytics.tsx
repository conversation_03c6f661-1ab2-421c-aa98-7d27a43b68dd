import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  CheckCircle, 
  AlertTriangle, 
  Users, 
  BarChart3,
  <PERSON><PERSON>hart,
  Activity,
  Target,
  Zap,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PerformanceMetrics {
  template_id: string;
  template_name: string;
  period_days: number;
  total_workflows: number;
  success_rate: number;
  average_duration_hours: number;
  escalation_rate: number;
  timeout_rate: number;
  user_satisfaction: number;
  trends: {
    success_rate_trend: number;
    duration_trend: number;
    volume_trend: number;
  };
  breakdown: {
    by_status: Record<string, number>;
    by_department: Record<string, number>;
    by_contract_type: Record<string, number>;
  };
}

interface WorkflowPerformanceAnalyticsProps {
  templateId?: string;
  workspaceId?: string;
  className?: string;
}

export const WorkflowPerformanceAnalytics: React.FC<WorkflowPerformanceAnalyticsProps> = ({
  templateId,
  workspaceId,
  className
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>(templateId || 'all');
  const [timePeriod, setTimePeriod] = useState<number>(30);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'trends' | 'breakdown'>('overview');

  useEffect(() => {
    loadPerformanceMetrics();
  }, [selectedTemplate, timePeriod]);

  const loadPerformanceMetrics = async () => {
    try {
      setLoading(true);
      
      // Mock data for now - replace with actual API call
      const mockMetrics: PerformanceMetrics[] = [
        {
          template_id: 'template-1',
          template_name: 'Standard Contract Approval',
          period_days: timePeriod,
          total_workflows: 156,
          success_rate: 0.89,
          average_duration_hours: 18.5,
          escalation_rate: 0.12,
          timeout_rate: 0.08,
          user_satisfaction: 4.2,
          trends: {
            success_rate_trend: 0.05,
            duration_trend: -0.15,
            volume_trend: 0.23
          },
          breakdown: {
            by_status: {
              completed: 139,
              rejected: 8,
              cancelled: 5,
              escalated: 4
            },
            by_department: {
              legal: 45,
              finance: 38,
              operations: 42,
              sales: 31
            },
            by_contract_type: {
              service_agreement: 67,
              nda: 34,
              employment: 28,
              vendor: 27
            }
          }
        },
        {
          template_id: 'template-2',
          template_name: 'High-Value Contract Approval',
          period_days: timePeriod,
          total_workflows: 43,
          success_rate: 0.95,
          average_duration_hours: 32.1,
          escalation_rate: 0.07,
          timeout_rate: 0.05,
          user_satisfaction: 4.6,
          trends: {
            success_rate_trend: 0.02,
            duration_trend: -0.08,
            volume_trend: 0.15
          },
          breakdown: {
            by_status: {
              completed: 41,
              rejected: 1,
              cancelled: 0,
              escalated: 1
            },
            by_department: {
              legal: 18,
              finance: 15,
              operations: 6,
              sales: 4
            },
            by_contract_type: {
              service_agreement: 23,
              vendor: 12,
              partnership: 8
            }
          }
        }
      ];

      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Error loading performance metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSelectedMetrics = () => {
    if (selectedTemplate === 'all') {
      // Aggregate metrics for all templates
      const totalWorkflows = metrics.reduce((sum, m) => sum + m.total_workflows, 0);
      const avgSuccessRate = metrics.reduce((sum, m) => sum + (m.success_rate * m.total_workflows), 0) / totalWorkflows;
      const avgDuration = metrics.reduce((sum, m) => sum + (m.average_duration_hours * m.total_workflows), 0) / totalWorkflows;
      
      return {
        template_name: 'All Templates',
        total_workflows: totalWorkflows,
        success_rate: avgSuccessRate,
        average_duration_hours: avgDuration,
        escalation_rate: metrics.reduce((sum, m) => sum + (m.escalation_rate * m.total_workflows), 0) / totalWorkflows,
        timeout_rate: metrics.reduce((sum, m) => sum + (m.timeout_rate * m.total_workflows), 0) / totalWorkflows,
        user_satisfaction: metrics.reduce((sum, m) => sum + (m.user_satisfaction * m.total_workflows), 0) / totalWorkflows,
        trends: {
          success_rate_trend: 0.04,
          duration_trend: -0.12,
          volume_trend: 0.19
        }
      };
    }
    return metrics.find(m => m.template_id === selectedTemplate) || metrics[0];
  };

  const selectedMetrics = getSelectedMetrics();

  const MetricCard = ({ 
    title, 
    value, 
    trend, 
    icon: Icon, 
    format = 'number',
    trendFormat = 'percentage'
  }: any) => {
    const formatValue = (val: number) => {
      switch (format) {
        case 'percentage':
          return `${Math.round(val * 100)}%`;
        case 'hours':
          return `${val.toFixed(1)}h`;
        case 'rating':
          return `${val.toFixed(1)}/5`;
        default:
          return val.toString();
      }
    };

    const formatTrend = (val: number) => {
      const prefix = val > 0 ? '+' : '';
      switch (trendFormat) {
        case 'percentage':
          return `${prefix}${Math.round(val * 100)}%`;
        case 'hours':
          return `${prefix}${val.toFixed(1)}h`;
        default:
          return `${prefix}${val}`;
      }
    };

    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold text-gray-900">{formatValue(value)}</p>
              {trend !== undefined && (
                <div className={cn(
                  "flex items-center text-sm mt-1",
                  trend > 0 ? "text-green-600" : trend < 0 ? "text-red-600" : "text-gray-600"
                )}>
                  {trend > 0 ? (
                    <TrendingUp className="h-4 w-4 mr-1" />
                  ) : trend < 0 ? (
                    <TrendingDown className="h-4 w-4 mr-1" />
                  ) : null}
                  <span>{formatTrend(trend)} vs last period</span>
                </div>
              )}
            </div>
            <Icon className="h-8 w-8 text-gray-400" />
          </div>
        </CardContent>
      </Card>
    );
  };

  const BreakdownChart = ({ data, title, colorClass }: any) => (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {Object.entries(data).map(([key, value]: [string, any]) => {
            const total: number = (Object.values(data) as any[]).reduce((a: number, b: any) => a + (typeof b === 'number' ? b : Number(b) || 0), 0) || 1;
            const numericValue: number = typeof value === 'number' ? value : Number(value) || 0;
            const percentage = (numericValue / total) * 100;
            return (
              <div key={key} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={cn("w-3 h-3 rounded-full", colorClass)} />
                  <span className="text-sm font-medium capitalize">{key.replace('_', ' ')}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{value}</div>
                  <div className="text-xs text-gray-500">{percentage.toFixed(1)}%</div>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Workflow Performance</h2>
          <p className="text-gray-600">Analytics and insights for approval workflows</p>
        </div>
        <div className="flex items-center space-x-4">
          <Select value={timePeriod.toString()} onValueChange={(value) => setTimePeriod(Number(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Templates</SelectItem>
              {metrics.map(metric => (
                <SelectItem key={metric.template_id} value={metric.template_id}>
                  {metric.template_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Total Workflows"
              value={selectedMetrics.total_workflows}
              trend={selectedMetrics.trends?.volume_trend}
              icon={Activity}
            />
            <MetricCard
              title="Success Rate"
              value={selectedMetrics.success_rate}
              trend={selectedMetrics.trends?.success_rate_trend}
              icon={CheckCircle}
              format="percentage"
              trendFormat="percentage"
            />
            <MetricCard
              title="Avg Duration"
              value={selectedMetrics.average_duration_hours}
              trend={selectedMetrics.trends?.duration_trend}
              icon={Clock}
              format="hours"
              trendFormat="hours"
            />
            <MetricCard
              title="User Satisfaction"
              value={selectedMetrics.user_satisfaction}
              icon={Users}
              format="rating"
            />
          </div>

          {/* Secondary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Escalation Rate</p>
                    <p className="text-xl font-bold text-orange-600">
                      {Math.round(selectedMetrics.escalation_rate * 100)}%
                    </p>
                  </div>
                  <AlertTriangle className="h-6 w-6 text-orange-400" />
                </div>
                <Progress 
                  value={selectedMetrics.escalation_rate * 100} 
                  className="mt-2 h-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Timeout Rate</p>
                    <p className="text-xl font-bold text-red-600">
                      {Math.round(selectedMetrics.timeout_rate * 100)}%
                    </p>
                  </div>
                  <Clock className="h-6 w-6 text-red-400" />
                </div>
                <Progress 
                  value={selectedMetrics.timeout_rate * 100} 
                  className="mt-2 h-2"
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Efficiency Score</p>
                    <p className="text-xl font-bold text-green-600">
                      {Math.round((selectedMetrics.success_rate * 0.6 + (1 - selectedMetrics.escalation_rate) * 0.4) * 100)}%
                    </p>
                  </div>
                  <Target className="h-6 w-6 text-green-400" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <span className="text-sm font-medium">Success Rate</span>
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                      <span className="text-green-600 font-medium">
                        +{Math.round(selectedMetrics.trends?.success_rate_trend * 100)}%
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <span className="text-sm font-medium">Average Duration</span>
                    <div className="flex items-center space-x-2">
                      <TrendingDown className="h-4 w-4 text-blue-600" />
                      <span className="text-blue-600 font-medium">
                        {selectedMetrics.trends?.duration_trend.toFixed(1)}h faster
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                    <span className="text-sm font-medium">Workflow Volume</span>
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="h-4 w-4 text-purple-600" />
                      <span className="text-purple-600 font-medium">
                        +{Math.round(selectedMetrics.trends?.volume_trend * 100)}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start space-x-2">
                    <Zap className="h-4 w-4 text-yellow-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Optimize timeout settings</p>
                      <p className="text-xs text-gray-600">Reduce timeout rate by adjusting step timeouts</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Zap className="h-4 w-4 text-yellow-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Add parallel approvals</p>
                      <p className="text-xs text-gray-600">Speed up process with concurrent approvals</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Zap className="h-4 w-4 text-yellow-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium">Implement auto-routing</p>
                      <p className="text-xs text-gray-600">Use AI to optimize approval routing</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="breakdown" className="space-y-6">
          {selectedTemplate !== 'all' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <BreakdownChart
                data={metrics.find(m => m.template_id === selectedTemplate)?.breakdown.by_status || {}}
                title="By Status"
                colorClass="bg-blue-500"
              />
              <BreakdownChart
                data={metrics.find(m => m.template_id === selectedTemplate)?.breakdown.by_department || {}}
                title="By Department"
                colorClass="bg-green-500"
              />
              <BreakdownChart
                data={metrics.find(m => m.template_id === selectedTemplate)?.breakdown.by_contract_type || {}}
                title="By Contract Type"
                colorClass="bg-purple-500"
              />
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
