import React, { useState } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  <PERSON>alogD<PERSON><PERSON>,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alog<PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import {
  CheckCircle,
  Clock,
  Info,
  ThumbsDown,
  ThumbsUp,
  User,
  Users,
  X,
  ArrowRight,
  Eye,
  Calendar
} from "lucide-react";

// Types for our component
interface Approver {
  id: string;
  name: string;
  role: string;
  status: "pending" | "approved" | "rejected" | "current";
  order: number;
  avatar?: string;
  completedAt?: string;
  comments?: string;
}

interface ApprovalWorkflowProps {
  contractId: string;
  contractTitle: string;
  approvers: Approver[];
  approvalProcess: "sequential" | "parallel";
  dueDate?: string;
  onApprove?: (contractId: string, comments?: string) => void;
  onReject?: (contractId: string, reason: string) => void;
  onAddApprover?: (approver: Omit<Approver, "id" | "status" | "completedAt">) => void;
  onRemoveApprover?: (approverId: string) => void;
  onReorderApprover?: (approverId: string, newOrder: number) => void;
  onViewContract?: (contractId: string) => void;
}

const ApprovalWorkflow: React.FC<ApprovalWorkflowProps> = ({
  contractId,
  contractTitle,
  approvers = [],
  approvalProcess = "sequential",
  dueDate,
  onApprove,
  onReject,
  onAddApprover,
  onViewContract,
}) => {
  // State
  const [activeTab, setActiveTab] = useState<string>("workflow");
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState<boolean>(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState<boolean>(false);
  const [isAddApproverDialogOpen, setIsAddApproverDialogOpen] = useState<boolean>(false);
  const [approvalComments, setApprovalComments] = useState<string>("");
  const [rejectionReason, setRejectionReason] = useState<string>("");
  const [newApprover, setNewApprover] = useState<{name: string; role: string; order: number}>({
    name: "",
    role: "",
    order: approvers.length + 1,
  });

  // Calculate approval progress
  const completedApprovals = approvers.filter(a => a.status === "approved" || a.status === "rejected").length;
  const approvalProgress = approvers.length > 0 ? (completedApprovals / approvers.length) * 100 : 0;

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Get current approver (for sequential workflow)
  const getCurrentApprover = () => {
    if (approvalProcess === "sequential") {
      return approvers.find(a => a.status === "current") || approvers.find(a => a.status === "pending");
    }
    return null;
  };

  // Check if user is current approver (in a real app, this would check against the logged-in user)
  const isCurrentUserApprover = () => {
    // For demo purposes, we'll assume the current user is the current approver
    return true;
  };

  // Handle approve action
  const handleApprove = () => {
    if (onApprove) {
      onApprove(contractId, approvalComments);
    }
    setApprovalComments("");
    setIsApproveDialogOpen(false);
  };

  // Handle reject action
  const handleReject = () => {
    if (onReject && rejectionReason.trim()) {
      onReject(contractId, rejectionReason);
    }
    setRejectionReason("");
    setIsRejectDialogOpen(false);
  };

  // Handle add approver
  const handleAddApprover = () => {
    if (onAddApprover && newApprover.name.trim() && newApprover.role.trim()) {
      onAddApprover({
        name: newApprover.name,
        role: newApprover.role,
        order: newApprover.order,
      });
      setNewApprover({
        name: "",
        role: "",
        order: approvers.length + 2,
      });
      setIsAddApproverDialogOpen(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: Approver["status"]) => {
    switch (status) {
      case "approved":
        return <Badge variant="default" className="bg-green-500">Approved</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      case "current":
        return <Badge variant="secondary" className="bg-blue-500 text-white">Current</Badge>;
      case "pending":
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">{contractTitle}</CardTitle>
              <CardDescription>Approval Workflow</CardDescription>
            </div>
            <Button
              variant="outline"
              onClick={() => onViewContract && onViewContract(contractId)}
              className="bg-primary/10 hover:bg-primary/20 border-primary/20"
            >
              <Eye className="mr-2 h-4 w-4" />
              Review Contract
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pb-0">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <div className="flex items-center gap-4">
              <div className="flex flex-col">
                <span className="text-sm font-medium">Approval Process</span>
                <span className="text-sm text-muted-foreground">
                  {approvalProcess === "sequential" ? "Sequential Approval" : "Parallel Approval"}
                </span>
              </div>

              {dueDate && (
                <>
                  <Separator orientation="vertical" className="h-8" />
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">Due Date</span>
                    <span className="text-sm text-muted-foreground flex items-center">
                      <Calendar className="mr-1 h-3 w-3" />
                      {formatDate(dueDate)}
                    </span>
                  </div>
                </>
              )}
            </div>

            <div className="w-full md:w-64">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium">Approval Progress</span>
                <span className="text-sm font-medium">{Math.round(approvalProgress)}%</span>
              </div>
              <Progress value={approvalProgress} className="h-2" />
            </div>
          </div>

          <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="workflow">
                <Users className="mr-2 h-4 w-4" />
                Workflow
              </TabsTrigger>
              <TabsTrigger value="history">
                <Clock className="mr-2 h-4 w-4" />
                History
              </TabsTrigger>
            </TabsList>

            <TabsContent value="workflow" className="mt-6">
              <div className="space-y-4">
                {approvers.length > 0 ? (
                  <div className="relative pl-6 border-l-2 border-muted">
                    {approvers.map((approver) => (
                      <div key={approver.id} className="mb-6 relative">
                        {/* Timeline dot */}
                        <div
                          className={`absolute -left-[13px] top-0 h-6 w-6 rounded-full flex items-center justify-center ${
                            approver.status === "approved"
                              ? "bg-green-100 border-2 border-green-500"
                              : approver.status === "rejected"
                              ? "bg-red-100 border-2 border-red-500"
                              : approver.status === "current"
                              ? "bg-blue-100 border-2 border-blue-500"
                              : "bg-muted border-2 border-muted-foreground"
                          }`}
                        >
                          {approver.status === "approved" ? (
                            <CheckCircle className="h-3 w-3 text-green-500" />
                          ) : approver.status === "rejected" ? (
                            <X className="h-3 w-3 text-red-500" />
                          ) : approver.status === "current" ? (
                            <ArrowRight className="h-3 w-3 text-blue-500" />
                          ) : (
                            <Clock className="h-3 w-3 text-muted-foreground" />
                          )}
                        </div>

                        <div className="p-4 border rounded-lg">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-8 w-8">
                                {approver.avatar ? (
                                  <AvatarImage src={approver.avatar} alt={approver.name} />
                                ) : (
                                  <AvatarFallback>
                                    {approver.name.split(" ").map(n => n[0]).join("").toUpperCase()}
                                  </AvatarFallback>
                                )}
                              </Avatar>
                              <div>
                                <div className="font-medium">{approver.name}</div>
                                <div className="text-sm text-muted-foreground">{approver.role}</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(approver.status)}
                              {approver.completedAt && (
                                <span className="text-xs text-muted-foreground">
                                  {formatDate(approver.completedAt)}
                                </span>
                              )}
                            </div>
                          </div>

                          {approver.comments && (
                            <div className="mt-3 pl-4 border-l-2 border-muted">
                              <div className="text-sm italic text-muted-foreground">
                                &quot;{approver.comments}&quot;
                              </div>
                            </div>
                          )}

                          {approver.status === "current" && isCurrentUserApprover() && (
                            <div className="mt-4 flex justify-end gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onViewContract && onViewContract(contractId)}
                                className="mr-auto"
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                Review Contract
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setIsRejectDialogOpen(true)}
                              >
                                <ThumbsDown className="mr-2 h-4 w-4" />
                                Reject
                              </Button>
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => setIsApproveDialogOpen(true)}
                              >
                                <ThumbsUp className="mr-2 h-4 w-4" />
                                Approve
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-3">
                      <Users className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-1">No approvers</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      There are no approvers assigned to this contract.
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => setIsAddApproverDialogOpen(true)}
                    >
                      Add Approver
                    </Button>
                  </div>
                )}

                {approvers.length > 0 && (
                  <div className="flex justify-end">
                    <Button
                      variant="outline"
                      onClick={() => setIsAddApproverDialogOpen(true)}
                    >
                      <User className="mr-2 h-4 w-4" />
                      Add Approver
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="history" className="mt-6">
              <div className="space-y-4">
                {approvers
                  .filter(a => a.status === "approved" || a.status === "rejected")
                  .sort((a, b) => new Date(b.completedAt || "").getTime() - new Date(a.completedAt || "").getTime())
                  .map((approver) => (
                    <div key={approver.id} className="p-4 border rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            {approver.avatar ? (
                              <AvatarImage src={approver.avatar} alt={approver.name} />
                            ) : (
                              <AvatarFallback>
                                {approver.name.split(" ").map(n => n[0]).join("").toUpperCase()}
                              </AvatarFallback>
                            )}
                          </Avatar>
                          <div>
                            <div className="font-medium">{approver.name}</div>
                            <div className="text-sm text-muted-foreground">{approver.role}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusBadge(approver.status)}
                          {approver.completedAt && (
                            <span className="text-xs text-muted-foreground">
                              {formatDate(approver.completedAt)}
                            </span>
                          )}
                        </div>
                      </div>

                      {approver.comments && (
                        <div className="mt-3 pl-4 border-l-2 border-muted">
                          <div className="text-sm italic text-muted-foreground">
                            &quot;{approver.comments}&quot;
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                {approvers.filter(a => a.status === "approved" || a.status === "rejected").length === 0 && (
                  <div className="text-center py-8">
                    <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-3">
                      <Clock className="h-6 w-6 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-1">No approval history</h3>
                    <p className="text-sm text-muted-foreground">
                      There is no approval history for this contract yet.
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="pt-6 pb-4">
          <div className="w-full flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                {approvalProcess === "sequential"
                  ? "Approvers must approve in sequence"
                  : "Approvers can approve in any order"}
              </span>
            </div>

            {getCurrentApprover() && (
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Current Approver:</span>
                <span className="text-sm">{getCurrentApprover()?.name}</span>
              </div>
            )}
          </div>
        </CardFooter>
      </Card>

      {/* Approve Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Contract</DialogTitle>
            <DialogDescription>
              Add optional comments with your approval.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <Textarea
              placeholder="Add your comments (optional)"
              value={approvalComments}
              onChange={(e) => setApprovalComments(e.target.value)}
              rows={4}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApproveDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApprove}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Approve
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <AlertDialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reject Contract</AlertDialogTitle>
            <AlertDialogDescription>
              Please provide a reason for rejecting this contract.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4 py-2">
            <Textarea
              placeholder="Reason for rejection"
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              rows={4}
              className={!rejectionReason.trim() ? "border-destructive" : ""}
            />
            {!rejectionReason.trim() && (
              <p className="text-xs text-destructive">A reason is required for rejection</p>
            )}
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsRejectDialogOpen(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleReject}
              disabled={!rejectionReason.trim()}
            >
              <X className="mr-2 h-4 w-4" />
              Reject
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Add Approver Dialog */}
      <Dialog open={isAddApproverDialogOpen} onOpenChange={setIsAddApproverDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Approver</DialogTitle>
            <DialogDescription>
              Add a new approver to the workflow.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <label className="text-sm font-medium">Name</label>
              <Input
                placeholder="Enter approver name"
                value={newApprover.name}
                onChange={(e) => setNewApprover({...newApprover, name: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Role</label>
              <Input
                placeholder="Enter approver role"
                value={newApprover.role}
                onChange={(e) => setNewApprover({...newApprover, role: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Order</label>
              <Input
                type="number"
                min={1}
                placeholder="Enter approval order"
                value={newApprover.order}
                onChange={(e) => setNewApprover({...newApprover, order: parseInt(e.target.value) || 1})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddApproverDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleAddApprover}
              disabled={!newApprover.name.trim() || !newApprover.role.trim()}
            >
              Add Approver
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export type { Approver };
export default ApprovalWorkflow;
