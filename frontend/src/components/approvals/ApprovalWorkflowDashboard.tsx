/**
 * Enhanced Approval Workflow Dashboard
 * 
 * Features:
 * - Real-time workflow status tracking
 * - AI-powered routing recommendations
 * - Bulk approval actions
 * - Workflow templates management
 * - Performance analytics
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Users, 
  TrendingUp,
  Brain,
  Zap,
  Filter,
  Search,
  MoreHorizontal,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';

interface WorkflowSummary {
  total_workflows: number;
  active_workflows: number;
  completed_workflows: number;
  rejected_workflows: number;
  overdue_workflows: number;
  average_completion_time_hours: number;
}

interface ApprovalWorkflow {
  id: string;
  contract_id: string;
  workflow_type: 'sequential' | 'parallel' | 'conditional' | 'hybrid';
  status: 'draft' | 'active' | 'completed' | 'rejected' | 'cancelled' | 'escalated';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  due_date?: string;
  contract: {
    title: string;
    type: string;
    value?: string;
  };
  approvers: Array<{
    id: string;
    user_id: string;
    status: string;
    order_index: number;
    user?: {
      name: string;
      email: string;
    };
  }>;
  progress?: {
    total_approvals: number;
    completed_approvals: number;
    progress_percentage: number;
  };
}

interface ApprovalWorkflowDashboardProps {
  workspaceId: string;
}

export const ApprovalWorkflowDashboard: React.FC<ApprovalWorkflowDashboardProps> = ({
  workspaceId
}) => {
  const [workflows, setWorkflows] = useState<ApprovalWorkflow[]>([]);
  const [summary, setSummary] = useState<WorkflowSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    workflow_type: '',
    search: ''
  });
  const [activeTab, setActiveTab] = useState('overview');
  const { toast } = useToast();

  useEffect(() => {
    loadWorkflows();
    loadSummary();
  }, [workspaceId, filters]);

  const loadWorkflows = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        workspace_id: workspaceId,
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v))
      });

      const response = await fetch(`/api/approval-workflows?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setWorkflows(data);
      }
    } catch (error) {
      console.error('Failed to load workflows:', error);
      toast({
        title: "Error",
        description: "Failed to load approval workflows",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const loadSummary = async () => {
    try {
      const response = await fetch(`/api/approval-workflows/summary?workspace_id=${workspaceId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSummary(data);
      }
    } catch (error) {
      console.error('Failed to load summary:', error);
    }
  };

  const handleBulkAction = async (action: 'approve' | 'reject', comments?: string) => {
    if (selectedWorkflows.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select workflows to perform bulk actions",
        variant: "destructive"
      });
      return;
    }

    try {
      const response = await fetch('/api/approval-workflows/bulk-approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          approval_ids: selectedWorkflows,
          action,
          comments
        })
      });

      if (response.ok) {
        const result = await response.json();
        toast({
          title: "Bulk Action Completed",
          description: `${result.summary.successful} workflows processed successfully`,
        });
        setSelectedWorkflows([]);
        loadWorkflows();
      }
    } catch (error) {
      console.error('Bulk action failed:', error);
      toast({
        title: "Error",
        description: "Failed to perform bulk action",
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'active': return 'bg-blue-500';
      case 'rejected': return 'bg-red-500';
      case 'escalated': return 'bg-orange-500';
      case 'draft': return 'bg-gray-500';
      default: return 'bg-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const formatDuration = (hours: number) => {
    if (hours < 24) return `${Math.round(hours)}h`;
    return `${Math.round(hours / 24)}d`;
  };

  return (
    <div className="approval-workflow-dashboard space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Approval Workflows</h1>
          <p className="text-muted-foreground">Manage contract approval processes</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Brain className="h-4 w-4 mr-2" />
            AI Insights
          </Button>
          <Button size="sm">
            <Zap className="h-4 w-4 mr-2" />
            New Workflow
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.total_workflows}</div>
              <p className="text-xs text-muted-foreground">All time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active</CardTitle>
              <Clock className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{summary.active_workflows}</div>
              <p className="text-xs text-muted-foreground">In progress</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{summary.completed_workflows}</div>
              <p className="text-xs text-muted-foreground">Approved</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Time</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatDuration(summary.average_completion_time_hours)}</div>
              <p className="text-xs text-muted-foreground">To complete</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Input
                placeholder="Search workflows..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="w-full"
              />
            </div>
            
            <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.priority} onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value }))}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Priority</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.workflow_type} onValueChange={(value) => setFilters(prev => ({ ...prev, workflow_type: value }))}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Types</SelectItem>
                <SelectItem value="sequential">Sequential</SelectItem>
                <SelectItem value="parallel">Parallel</SelectItem>
                <SelectItem value="conditional">Conditional</SelectItem>
                <SelectItem value="hybrid">Hybrid</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedWorkflows.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {selectedWorkflows.length} workflow(s) selected
              </span>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={() => handleBulkAction('approve')}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Bulk Approve
                </Button>
                <Button size="sm" variant="outline" onClick={() => handleBulkAction('reject', 'Bulk rejection')}>
                  <XCircle className="h-4 w-4 mr-2" />
                  Bulk Reject
                </Button>
                <Button size="sm" variant="ghost" onClick={() => setSelectedWorkflows([])}>
                  Clear Selection
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Workflows List */}
      <Card>
        <CardHeader>
          <CardTitle>Workflows</CardTitle>
          <CardDescription>Manage approval workflows for contracts</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-20 bg-gray-100 rounded animate-pulse" />
              ))}
            </div>
          ) : workflows.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No workflows found</h3>
              <p className="text-gray-500">Create your first approval workflow to get started.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {workflows.map((workflow) => (
                <div key={workflow.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        checked={selectedWorkflows.includes(workflow.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedWorkflows(prev => [...prev, workflow.id]);
                          } else {
                            setSelectedWorkflows(prev => prev.filter(id => id !== workflow.id));
                          }
                        }}
                      />
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-medium">{workflow.contract.title}</h3>
                          <Badge variant="outline" className={`${getStatusColor(workflow.status)} text-white`}>
                            {workflow.status}
                          </Badge>
                          <Badge variant="outline" className={`${getPriorityColor(workflow.priority)} text-white`}>
                            {workflow.priority}
                          </Badge>
                        </div>
                        
                        <div className="text-sm text-gray-600 mb-2">
                          {workflow.contract.type} • {workflow.workflow_type} workflow
                        </div>
                        
                        {workflow.progress && (
                          <div className="flex items-center gap-2 mb-2">
                            <Progress value={workflow.progress.progress_percentage} className="flex-1" />
                            <span className="text-sm text-gray-500">
                              {workflow.progress.completed_approvals}/{workflow.progress.total_approvals}
                            </span>
                          </div>
                        )}
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Created {new Date(workflow.created_at).toLocaleDateString()}</span>
                          {workflow.due_date && (
                            <span>Due {new Date(workflow.due_date).toLocaleDateString()}</span>
                          )}
                          <span>{workflow.approvers.length} approvers</span>
                        </div>
                      </div>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Play className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Restart Workflow
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Pause className="h-4 w-4 mr-2" />
                          Cancel Workflow
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
