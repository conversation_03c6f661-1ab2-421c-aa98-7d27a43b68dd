import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Brain,
  CheckCircle,
  Clock,
  AlertTriangle,
  Zap,
  Loader2,
  Eye,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AnalysisResult {
  risk_score?: number;
  contract_type?: string;
  key_clauses?: Array<{
    type: string;
    content: string;
    risk_level: string;
  }>;
  recommendations?: Array<{
    type: string;
    description: string;
    priority: string;
  }>;
}

interface AnalysisStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  duration?: number;
  result?: AnalysisResult;
  error?: string;
}

interface RealTimeAnalysisProgressProps {
  contractId: string;
  analysisId?: string;
  onComplete?: (results: AnalysisResult) => void;
  onError?: (error: string) => void;
  autoStart?: boolean;
  showDetails?: boolean;
}

export const RealTimeAnalysisProgress: React.FC<RealTimeAnalysisProgressProps> = ({
  contractId,
  analysisId,
  onComplete,
  onError,
  autoStart = false,
  showDetails = true
}) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | null>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const [steps, setSteps] = useState<AnalysisStep[]>([
    {
      id: 'preprocessing',
      name: 'Document Preprocessing',
      description: 'Extracting and cleaning contract text',
      status: 'pending',
      progress: 0
    },
    {
      id: 'classification',
      name: 'Contract Classification',
      description: 'Identifying contract type and structure',
      status: 'pending',
      progress: 0
    },
    {
      id: 'clause_extraction',
      name: 'Clause Analysis',
      description: 'Extracting and categorizing key clauses',
      status: 'pending',
      progress: 0
    },
    {
      id: 'risk_assessment',
      name: 'Risk Assessment',
      description: 'Analyzing potential risks and compliance issues',
      status: 'pending',
      progress: 0
    },
    {
      id: 'recommendations',
      name: 'Generating Recommendations',
      description: 'Creating actionable insights and suggestions',
      status: 'pending',
      progress: 0
    },
    {
      id: 'finalization',
      name: 'Finalizing Results',
      description: 'Compiling and formatting analysis results',
      status: 'pending',
      progress: 0
    }
  ]);

  useEffect(() => {
    if (autoStart) {
      startAnalysis();
    }
    return () => {
      cleanup();
    };
  }, [autoStart]);

  const cleanup = () => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  const startAnalysis = async () => {
    try {
      setIsAnalyzing(true);
      setError(null);
      setStartTime(new Date());
      setCurrentStep(0);
      setOverallProgress(0);

      // Reset all steps
      setSteps(prev => prev.map(step => ({
        ...step,
        status: 'pending',
        progress: 0,
        result: undefined,
        error: undefined
      })));

      // Start the analysis
      const response = await fetch(`/api/v1/contracts/${contractId}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          analysis_type: 'comprehensive',
          include_recommendations: true,
          real_time_updates: true
        })
      });

      if (!response.ok) {
        throw new Error('Failed to start analysis');
      }

      const data = await response.json();
      
      // Set up WebSocket for real-time updates
      setupWebSocket(data.analysis_id || analysisId);
      
      // Start progress simulation
      simulateProgress();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Analysis failed';
      setError(errorMessage);
      setIsAnalyzing(false);
      onError?.(errorMessage);
    }
  };

  const setupWebSocket = (analysisId: string) => {
    const wsUrl = `ws://localhost:8000/ws/analysis/${analysisId}`;
    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleAnalysisUpdate(data);
    };

    wsRef.current.onclose = () => {
      console.log('WebSocket connection closed');
    };

    wsRef.current.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  };

  const handleAnalysisUpdate = (data: {
    step: string;
    progress: number;
    status: 'pending' | 'running' | 'completed' | 'error';
    result?: AnalysisResult;
    error?: string;
  }) => {
    const { step, progress, status, result, error: stepError } = data;

    if (stepError) {
      setError(stepError);
      setIsAnalyzing(false);
      onError?.(stepError);
      return;
    }

    // Update specific step
    setSteps(prev => prev.map((s, index) => {
      if (s.id === step || index === currentStep) {
        return {
          ...s,
          status,
          progress,
          result,
          error: stepError
        };
      }
      return s;
    }));

    // Update overall progress
    if (status === 'completed') {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      
      if (nextStep >= steps.length) {
        // Analysis complete
        setIsAnalyzing(false);
        setOverallProgress(100);
        setAnalysisResults(result);
        onComplete?.(result);
        cleanup();
      } else {
        setOverallProgress((nextStep / steps.length) * 100);
      }
    }
  };

  const simulateProgress = () => {
    // Simulate realistic progress for each step
    intervalRef.current = setInterval(() => {
      setSteps(prev => prev.map((step, index) => {
        if (index === currentStep && step.status === 'pending') {
          return { ...step, status: 'running' };
        }
        if (index === currentStep && step.status === 'running' && step.progress < 90) {
          return { ...step, progress: Math.min(step.progress + Math.random() * 15, 90) };
        }
        return step;
      }));

      // Update estimated time remaining
      if (startTime) {
        const elapsed = (new Date().getTime() - startTime.getTime()) / 1000;
        const avgTimePerStep = elapsed / (currentStep + 1);
        const remaining = avgTimePerStep * (steps.length - currentStep - 1);
        setEstimatedTimeRemaining(remaining);
      }
    }, 1000);
  };

  const getStepIcon = (step: AnalysisStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'running':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStepColor = (step: AnalysisStep) => {
    switch (step.status) {
      case 'completed':
        return 'border-green-200 bg-green-50';
      case 'running':
        return 'border-blue-200 bg-blue-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="h-6 w-6 text-blue-600" />
            <CardTitle>AI Contract Analysis</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            {estimatedTimeRemaining && (
              <Badge variant="outline">
                <Clock className="h-3 w-3 mr-1" />
                {formatTime(estimatedTimeRemaining)} remaining
              </Badge>
            )}
            {!isAnalyzing && !analysisResults && (
              <Button onClick={startAnalysis} size="sm">
                <Zap className="h-4 w-4 mr-2" />
                Start Analysis
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Overall Progress</span>
            <span>{Math.round(overallProgress)}%</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span className="text-red-700 font-medium">Analysis Error</span>
            </div>
            <p className="text-red-600 mt-1">{error}</p>
          </div>
        )}

        {/* Analysis Steps */}
        {showDetails && (
          <div className="space-y-3">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={cn(
                  "p-4 border rounded-lg transition-all duration-200",
                  getStepColor(step),
                  index === currentStep && isAnalyzing ? "ring-2 ring-blue-200" : ""
                )}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStepIcon(step)}
                    <div>
                      <h4 className="font-medium text-gray-900">{step.name}</h4>
                      <p className="text-sm text-gray-600">{step.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {step.progress > 0 ? `${Math.round(step.progress)}%` : ''}
                    </div>
                    {step.duration && (
                      <div className="text-xs text-gray-500">
                        {formatTime(step.duration)}
                      </div>
                    )}
                  </div>
                </div>
                {step.status === 'running' && step.progress > 0 && (
                  <div className="mt-2">
                    <Progress value={step.progress} className="h-1" />
                  </div>
                )}
                {step.error && (
                  <div className="mt-2 text-sm text-red-600">
                    Error: {step.error}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Results Summary */}
        {analysisResults && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="font-medium text-green-700">Analysis Complete</span>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  View Results
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
            <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Risk Score:</span>
                <span className="ml-2 font-medium">
                  {analysisResults.risk_score ? `${Math.round(analysisResults.risk_score * 100)}%` : 'N/A'}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Contract Type:</span>
                <span className="ml-2 font-medium">{analysisResults.contract_type || 'Unknown'}</span>
              </div>
              <div>
                <span className="text-gray-600">Key Clauses:</span>
                <span className="ml-2 font-medium">
                  {analysisResults.key_clauses?.length || 0} found
                </span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
