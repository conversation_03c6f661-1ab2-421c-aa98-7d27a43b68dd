import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Search, 
  Filter, 
  Calendar as CalendarIcon, 
  FileText, 
  Download, 
  Eye, 
  Bookmark, 
  Clock, 
  Sparkles,
  Brain,
  Type,
  Loader2,
  X
} from "lucide-react";
import { useAuth } from "@clerk/clerk-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface SearchResult {
  id: string;
  title: string;
  filename: string;
  content?: string;
  file_url: string;
  folder?: string;
  created_at: string;
  created_by: any;
  file_info?: any;
  relevance_score: number;
  search_type: string;
  highlights?: string[];
  explanation?: string;
  matching_concepts?: string[];
}

interface SearchFilters {
  file_types: string[];
  folder?: string;
  date_from?: Date;
  date_to?: Date;
}

interface AdvancedDocumentSearchProps {
  workspaceId: string;
  onResultSelect?: (result: SearchResult) => void;
}

const AdvancedDocumentSearch = ({ workspaceId, onResultSelect }: AdvancedDocumentSearchProps) => {
  const { getToken } = useAuth();
  const [query, setQuery] = useState("");
  const [searchType, setSearchType] = useState<"text" | "semantic" | "hybrid">("hybrid");
  const [filters, setFilters] = useState<SearchFilters>({
    file_types: [],
    folder: undefined,
    date_from: undefined,
    date_to: undefined,
  });
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [savedSearches, setSavedSearches] = useState<any[]>([]);
  const [totalCount, setTotalCount] = useState(0);

  const fileTypes = [
    { value: "pdf", label: "PDF" },
    { value: "doc", label: "Word" },
    { value: "docx", label: "Word" },
    { value: "txt", label: "Text" },
    { value: "xlsx", label: "Excel" },
    { value: "pptx", label: "PowerPoint" },
  ];

  const folders = [
    { value: "contracts", label: "Contracts" },
    { value: "templates", label: "Templates" },
    { value: "legal", label: "Legal Documents" },
    { value: "drafts", label: "Drafts" },
    { value: "archive", label: "Archive" },
  ];

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (searchQuery.trim().length < 2) {
        setResults([]);
        return;
      }
      await performSearch(searchQuery);
    }, 300),
    [workspaceId, searchType, filters]
  );

  // Perform the actual search
  const performSearch = async (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;

    try {
      setLoading(true);
      const token = await getToken();

      // Build query parameters
      const params = new URLSearchParams({
        workspace_id: workspaceId,
        query: searchQuery,
        search_type: searchType,
        limit: "50",
        offset: "0",
      });

      if (filters.file_types.length > 0) {
        params.append("file_types", filters.file_types.join(","));
      }
      if (filters.folder) {
        params.append("folder", filters.folder);
      }
      if (filters.date_from) {
        params.append("date_from", filters.date_from.toISOString());
      }
      if (filters.date_to) {
        params.append("date_to", filters.date_to.toISOString());
      }

      const response = await fetch(`/api/documents/search/advanced?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Search failed');
      }

      const data = await response.json();
      setResults(data.results || []);
      setTotalCount(data.total_count || 0);
      setSuggestions(data.suggestions || []);

    } catch (error) {
      console.error('Search error:', error);
      toast.error('Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Load saved searches
  const loadSavedSearches = async () => {
    try {
      const token = await getToken();
      const response = await fetch(`/api/documents/search/saved?workspace_id=${workspaceId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSavedSearches(data.saved_searches || []);
      }
    } catch (error) {
      console.error('Failed to load saved searches:', error);
    }
  };

  // Save current search
  const saveCurrentSearch = async () => {
    if (!query.trim()) {
      toast.error('Please enter a search query first');
      return;
    }

    const name = prompt('Enter a name for this search:');
    if (!name) return;

    try {
      const token = await getToken();
      const response = await fetch(`/api/documents/search/save-query?workspace_id=${workspaceId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          query,
          filters,
          search_type: searchType,
        }),
      });

      if (response.ok) {
        toast.success('Search saved successfully');
        loadSavedSearches();
      } else {
        throw new Error('Failed to save search');
      }
    } catch (error) {
      console.error('Save search error:', error);
      toast.error('Failed to save search');
    }
  };

  // Load a saved search
  const loadSavedSearch = (savedSearch: any) => {
    setQuery(savedSearch.query);
    setSearchType(savedSearch.search_type || 'hybrid');
    setFilters(savedSearch.filters || {});
    performSearch(savedSearch.query);
  };

  // Handle filter changes
  const handleFileTypeChange = (fileType: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      file_types: checked 
        ? [...prev.file_types, fileType]
        : prev.file_types.filter(ft => ft !== fileType)
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      file_types: [],
      folder: undefined,
      date_from: undefined,
      date_to: undefined,
    });
  };

  // Format relevance score
  const formatRelevanceScore = (score: number) => {
    return Math.round(score * 100);
  };

  // Get search type icon
  const getSearchTypeIcon = (type: string) => {
    switch (type) {
      case 'semantic': return <Brain className="h-4 w-4" />;
      case 'text': return <Type className="h-4 w-4" />;
      case 'hybrid': return <Sparkles className="h-4 w-4" />;
      default: return <Search className="h-4 w-4" />;
    }
  };

  useEffect(() => {
    loadSavedSearches();
  }, [workspaceId]);

  useEffect(() => {
    if (query.trim().length >= 2) {
      debouncedSearch(query);
    } else {
      setResults([]);
    }
  }, [query, debouncedSearch]);

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Advanced Document Search
          </CardTitle>
          <CardDescription>
            Search across all documents using AI-powered semantic search and advanced filters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Search Input */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search documents, contracts, or content..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="pl-10"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    performSearch();
                  }
                }}
              />
              {loading && (
                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin" />
              )}
            </div>
            <Button onClick={() => performSearch()} disabled={loading}>
              Search
            </Button>
          </div>

          {/* Search Type Selection */}
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium">Search Type:</span>
            <Select value={searchType} onValueChange={(value: any) => setSearchType(value)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hybrid">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    Hybrid
                  </div>
                </SelectItem>
                <SelectItem value="semantic">
                  <div className="flex items-center gap-2">
                    <Brain className="h-4 w-4" />
                    AI Semantic
                  </div>
                </SelectItem>
                <SelectItem value="text">
                  <div className="flex items-center gap-2">
                    <Type className="h-4 w-4" />
                    Text Match
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={saveCurrentSearch}
              disabled={!query.trim()}
            >
              <Bookmark className="h-4 w-4 mr-2" />
              Save Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Filters Panel */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Search Filters
              </span>
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                <X className="h-4 w-4 mr-2" />
                Clear All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* File Types */}
              <div className="space-y-2">
                <label className="text-sm font-medium">File Types</label>
                <div className="space-y-2">
                  {fileTypes.map((type) => (
                    <div key={type.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={type.value}
                        checked={filters.file_types.includes(type.value)}
                        onCheckedChange={(checked) =>
                          handleFileTypeChange(type.value, checked as boolean)
                        }
                      />
                      <label htmlFor={type.value} className="text-sm">
                        {type.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Folder */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Folder</label>
                <Select value={filters.folder || ""} onValueChange={(value) =>
                  setFilters(prev => ({ ...prev, folder: value || undefined }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="All folders" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All folders</SelectItem>
                    {folders.map((folder) => (
                      <SelectItem key={folder.value} value={folder.value}>
                        {folder.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Date From */}
              <div className="space-y-2">
                <label className="text-sm font-medium">From Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !filters.date_from && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.date_from ? format(filters.date_from, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.date_from}
                      onSelect={(date) => setFilters(prev => ({ ...prev, date_from: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Date To */}
              <div className="space-y-2">
                <label className="text-sm font-medium">To Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !filters.date_to && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.date_to ? format(filters.date_to, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={filters.date_to}
                      onSelect={(date) => setFilters(prev => ({ ...prev, date_to: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Saved Searches */}
      {savedSearches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bookmark className="h-4 w-4" />
              Saved Searches
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {savedSearches.map((savedSearch) => (
                <Button
                  key={savedSearch.id}
                  variant="outline"
                  size="sm"
                  onClick={() => loadSavedSearch(savedSearch)}
                  className="flex items-center gap-2"
                >
                  <Clock className="h-3 w-3" />
                  {savedSearch.name}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Suggestions */}
      {suggestions.length > 0 && query.trim() && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Suggestions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {suggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  onClick={() => setQuery(suggestion)}
                  className="text-xs"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Results */}
      {(results.length > 0 || loading) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Search Results</span>
              {totalCount > 0 && (
                <Badge variant="secondary">
                  {totalCount} result{totalCount !== 1 ? 's' : ''}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Searching...</span>
              </div>
            ) : (
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {results.map((result) => (
                    <div
                      key={result.id}
                      className="border rounded-lg p-4 hover:bg-muted/50 cursor-pointer transition-colors"
                      onClick={() => onResultSelect?.(result)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <h3 className="font-medium">{result.title || result.filename}</h3>
                            <Badge variant="outline" className="text-xs">
                              {getSearchTypeIcon(result.search_type)}
                              <span className="ml-1">{formatRelevanceScore(result.relevance_score)}%</span>
                            </Badge>
                          </div>

                          <p className="text-sm text-muted-foreground mb-2">
                            {result.filename} • {result.folder || 'Root'} • {format(new Date(result.created_at), 'MMM d, yyyy')}
                          </p>

                          {/* Highlights */}
                          {result.highlights && result.highlights.length > 0 && (
                            <div className="space-y-1 mb-2">
                              {result.highlights.map((highlight, index) => (
                                <div
                                  key={index}
                                  className="text-sm bg-yellow-50 dark:bg-yellow-900/20 p-2 rounded border-l-2 border-yellow-400"
                                  dangerouslySetInnerHTML={{ __html: highlight }}
                                />
                              ))}
                            </div>
                          )}

                          {/* AI Explanation for semantic search */}
                          {result.explanation && (
                            <div className="text-sm bg-blue-50 dark:bg-blue-900/20 p-2 rounded border-l-2 border-blue-400 mb-2">
                              <div className="flex items-center gap-1 mb-1">
                                <Brain className="h-3 w-3" />
                                <span className="font-medium text-xs">AI Analysis:</span>
                              </div>
                              <p>{result.explanation}</p>
                            </div>
                          )}

                          {/* Matching Concepts */}
                          {result.matching_concepts && result.matching_concepts.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-2">
                              {result.matching_concepts.map((concept, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {concept}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>

                        <div className="flex items-center gap-2 ml-4">
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export default AdvancedDocumentSearch;
