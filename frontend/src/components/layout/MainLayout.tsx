import { useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import Header from "./Header";
import Sidebar from "./Sidebar";
import ThemeToggle from "../theme/ThemeToggle";
import PermissionCheck from "../permissions/PermissionCheck";
import { Toaster } from "@/components/ui/toaster";
import { Workspace } from "@/types/workspace";
import { ClerkWorkspaceProvider, useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { LiveRegion } from "@/components/ui/a11y";
import { useError } from "@/lib/error-provider";
import NoWorkspacesScreen from "../workspace/NoWorkspacesScreen";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";

const MainLayoutContent = () => {
  const location = useLocation();
  // Use Clerk workspace provider instead of the mock one
  const { currentWorkspace, setCurrentWorkspace, currentUser, isLoading, userWorkspaces, getUserWorkspaces } = useClerkWorkspace();

  // Check if we're on the contract wizard page
  const isContractWizard = location.pathname.includes('/contracts/wizard');
  const { error } = useError();

  // Handlers
  const handleWorkspaceChange = (workspace: Workspace) => {
    setCurrentWorkspace(workspace);
  };

  // Update document title based on location
  useEffect(() => {
    // Map routes to page titles for browser tab
    const routeTitles: Record<string, string> = {
      '/app/dashboard': 'Dashboard',
      '/app/contracts': 'Contracts',
      '/app/contracts/wizard': 'Contract Wizard',
      '/app/contracts/templates': 'Contract Templates',
      '/app/contracts/create': 'Create Contract',
      '/app/contracts/new': 'New Contract',
      '/app/contracts/import': 'Import Contract',
      '/app/repository': 'Document Repository',
      '/app/approvals': 'Approvals',
      '/app/analytics': 'Analytics',
      '/app/settings': 'Settings',
      '/app/workspaces': 'Workspaces',
      '/app/activity': 'Activity History',
      '/app/clause-library': 'Clause Library',
      '/app/profile': 'Profile',
    };

    // Find the matching route or use a default
    const matchingRoute = Object.keys(routeTitles).find(route =>
      location.pathname.startsWith(route)
    );

    const newTitle = matchingRoute ? routeTitles[matchingRoute] : 'Averum';

    // Update document title for browser tab
    document.title = `${newTitle} | Averum Contracts`;
  }, [location.pathname]);

  // Show loading state while Clerk is initializing
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show NoWorkspacesScreen if user has no workspaces and is not on workspace management pages
  const userWorkspacesList = getUserWorkspaces();
  const isOnWorkspacePages = location.pathname.startsWith('/workspaces') || location.pathname.startsWith('/test');

  // Only show NoWorkspacesScreen if we're absolutely sure the user has no workspaces
  // We need to be very careful here to avoid showing this screen when data is still loading
  // or when the user actually has workspaces
  const shouldShowNoWorkspacesScreen = !isLoading &&
                                       currentUser &&
                                       userWorkspacesList.length === 0 &&
                                       userWorkspaces.length === 0 &&
                                       !isOnWorkspacePages;

  // For now, let's disable the NoWorkspacesScreen to ensure users with workspaces can access the app
  // This can be re-enabled once we're sure the workspace detection is working correctly
  const DISABLE_NO_WORKSPACES_SCREEN = true;

  if (shouldShowNoWorkspacesScreen && !DISABLE_NO_WORKSPACES_SCREEN) {
    return <NoWorkspacesScreen autoRedirect={false} />;
  }

  return (
    <SidebarProvider defaultOpen={true}>
      {/* Accessibility announcements */}
      <LiveRegion aria-live="polite">
        {error && `Error: ${error.message}`}
      </LiveRegion>

      {/* Sidebar */}
      <Sidebar
        activePath={location.pathname}
        userName={currentUser?.name || ""}
        userRole={currentUser?.role || "Member"}
        currentWorkspace={currentWorkspace || undefined}
        onWorkspaceChange={handleWorkspaceChange}
      />

      {/* Main content */}
      <SidebarInset>
        <Header />

        <main id="main-content" className={`flex-1 ${isContractWizard ? 'overflow-hidden' : 'overflow-auto'}`} tabIndex={-1}>
          <div className={`${isContractWizard ? 'h-full' : 'min-h-full'} pb-safe`} style={{
            paddingBottom: 'env(safe-area-inset-bottom)',
            paddingRight: 'env(safe-area-inset-right)'
          }}>
            <PermissionCheck>
              <Outlet />
            </PermissionCheck>
          </div>
        </main>

        {/* Theme toggle for mobile (fixed position) */}
        <div className="md:hidden fixed bottom-4 right-4 z-50" style={{
          bottom: 'calc(1rem + env(safe-area-inset-bottom))',
          right: 'calc(1rem + env(safe-area-inset-right))'
        }}>
          <ThemeToggle variant="outline" size="default" />
        </div>

        {/* Toast notifications */}
        <Toaster />
      </SidebarInset>
    </SidebarProvider>
  );
};

const MainLayout = () => {
  return (
    <ClerkWorkspaceProvider>
      <MainLayoutContent />
    </ClerkWorkspaceProvider>
  );
};

export default MainLayout;
