import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Search, LogOut, Settings } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarTrigger } from "@/components/ui/sidebar";
import ThemeToggle from "../theme/ThemeToggle";
import NotificationsDropdown from "../notifications/NotificationsDropdown";
import BreadcrumbNavigation from "./BreadcrumbNavigation";
import GlobalSearch from "./GlobalSearch";
import { useUser, useClerk } from "@clerk/clerk-react";


interface HeaderProps {
  onToggleSidebar?: () => void; // Made optional since we're using SidebarTrigger
}

const Header = ({
  onToggleSidebar,
}: HeaderProps) => {
  const navigate = useNavigate();
  const { user } = useUser();
  const { signOut } = useClerk();
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);

  return (
    <header
      className="sticky top-0 z-30 flex h-16 items-center gap-2 sm:gap-4 border-b bg-background/95 backdrop-blur-sm px-3 sm:px-4 md:px-6 shadow-sm"
      style={{ paddingTop: 'env(safe-area-inset-top)' }}
    >
      {/* Left section: Sidebar trigger + Breadcrumbs */}
      <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-shrink-0">
        {/* Mobile sidebar trigger */}
        <SidebarTrigger className="md:hidden max-sm:min-h-[44px] max-sm:min-w-[44px] touch-manipulation" />

        {/* Desktop sidebar trigger */}
        <SidebarTrigger className="hidden md:flex" />

        {/* Breadcrumb navigation */}
        <BreadcrumbNavigation className="min-w-0 max-w-[180px] sm:max-w-[220px] lg:max-w-[280px]" />
      </div>

      {/* Center section: Global Search - hidden on mobile */}
      <div className="hidden md:flex flex-1 justify-center px-4 max-w-[400px] mx-auto">
        <GlobalSearch
          className="w-full"
          placeholder="Search contracts, templates, clauses..."
          size="md"
        />
      </div>

      {/* Right section: Controls */}
      <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
        {/* Mobile search button */}
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden max-sm:min-h-[44px] max-sm:min-w-[44px] touch-manipulation"
          onClick={() => setIsMobileSearchOpen(!isMobileSearchOpen)}
          aria-label="Search"
        >
          <Search className="h-5 w-5" />
        </Button>

        <NotificationsDropdown
          onSettingsClick={() => navigate("/app/settings")}
        />

        <div className="hidden md:block">
          <ThemeToggle variant="ghost" />
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="relative h-10 w-10 max-sm:min-h-[44px] max-sm:min-w-[44px] rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center text-sm font-medium text-primary hover:bg-primary/20 transition-colors touch-manipulation"
            >
              {user?.firstName?.[0]}{user?.lastName?.[0] || ''}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <div className="flex items-center gap-2 p-2">
              <div className="h-10 w-10 rounded-full bg-primary/10 border border-primary/20 flex items-center justify-center text-sm font-medium text-primary">
                {user?.firstName?.[0]}{user?.lastName?.[0] || ''}
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-medium">{user?.firstName} {user?.lastName}</span>
                <span className="text-xs text-muted-foreground">{user?.primaryEmailAddress?.emailAddress}</span>
              </div>
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => navigate("/app/profile")}
              className="cursor-pointer flex items-center gap-2 max-sm:min-h-[44px] touch-manipulation max-sm:py-3"
            >
              <Settings className="h-4 w-4" />
              <span>Profile</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => navigate("/app/settings")}
              className="cursor-pointer flex items-center gap-2 max-sm:min-h-[44px] touch-manipulation max-sm:py-3"
            >
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => signOut()}
              className="cursor-pointer text-destructive focus:text-destructive flex items-center gap-2 max-sm:min-h-[44px] touch-manipulation max-sm:py-3"
            >
              <LogOut className="h-4 w-4" />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Mobile search overlay */}
      {isMobileSearchOpen && (
        <div className="absolute top-full left-0 right-0 z-50 md:hidden">
          <div className="bg-background border-b shadow-lg p-4 min-h-[60px] flex items-center">
            <GlobalSearch
              className="w-full"
              placeholder="Search contracts, templates, clauses..."
              size="md"
              onResultSelect={() => setIsMobileSearchOpen(false)}
            />
          </div>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-background/80 backdrop-blur-sm -z-10 touch-manipulation"
            onClick={() => setIsMobileSearchOpen(false)}
            onTouchStart={() => setIsMobileSearchOpen(false)}
          />
        </div>
      )}
    </header>
  );
};

export default Header;
