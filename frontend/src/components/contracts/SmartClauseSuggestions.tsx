import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  AlertCircle,
  ArrowRight,
  Check,
  Copy,
  FileText,
  Info,
  Lightbulb,
  Plus,
  Shield,
  ThumbsUp,
  Zap,
} from "lucide-react";

// Types
interface Clause {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  riskLevel?: "low" | "medium" | "high";
  recommendation?: string;
  confidence: number; // 0-100
  reason?: string;
}

interface ContractContext {
  contractType: string;
  jurisdiction: string;
  industry?: string;
  parties?: {
    type: string;
    name: string;
  }[];
  value?: number;
  currency?: string;
  selectedClauses?: string[];
}

interface SmartClauseSuggestionsProps {
  contractContext: ContractContext;
  onSelectClause?: (clause: Clause) => void;
}

const SmartClauseSuggestions: React.FC<SmartClauseSuggestionsProps> = ({
  contractContext,
  onSelectClause,
}) => {
  // State
  const [activeTab, setActiveTab] = useState<string>("recommended");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Mock data for suggested clauses based on contract context
  const suggestedClauses: Clause[] = [
    {
      id: "sc-1",
      title: "GDPR Compliance",
      content: "Each party shall comply with all applicable data protection and privacy laws, including the General Data Protection Regulation (GDPR), when processing personal data in connection with this Agreement.",
      category: "compliance",
      tags: ["gdpr", "data-protection", "regulatory"],
      riskLevel: "high",
      recommendation: "Required for EU-based contracts involving personal data",
      confidence: 95,
      reason: "Contract involves EU jurisdiction and data processing",
    },
    {
      id: "sc-2",
      title: "Enhanced Limitation of Liability",
      content: "In no event shall either party be liable for any indirect, special, incidental, consequential, or punitive damages, including lost profits, arising out of or relating to this Agreement, regardless of the legal or equitable theory upon which the claim is based, even if advised of the possibility of such damages. Each party's total cumulative liability shall not exceed the greater of $100,000 or the total amount paid under this Agreement in the 12 months preceding the event giving rise to the claim.",
      category: "liability",
      tags: ["limitation", "enhanced", "risk-mitigation"],
      riskLevel: "medium",
      recommendation: "Recommended for high-value contracts",
      confidence: 88,
      reason: "Contract value exceeds standard thresholds",
    },
    {
      id: "sc-3",
      title: "Industry-Specific Compliance",
      content: "Service Provider shall comply with all industry standards and regulations applicable to the Services, including but not limited to [Industry Standards].",
      category: "compliance",
      tags: ["regulatory", "industry-specific"],
      riskLevel: "medium",
      recommendation: "Customize with specific industry standards",
      confidence: 82,
      reason: "Based on selected industry and contract type",
    },
    {
      id: "sc-4",
      title: "Force Majeure with Pandemic Provisions",
      content: "Neither party shall be liable for any failure or delay in performance under this Agreement due to circumstances beyond its reasonable control, including acts of God, natural disasters, pandemic or epidemic, government restrictions, wars, terrorist acts, riots, or labor strikes. In the event of a pandemic or epidemic, the parties shall work together in good faith to mitigate impacts and may temporarily suspend performance if necessary.",
      category: "force-majeure",
      tags: ["pandemic", "risk-mitigation"],
      riskLevel: "medium",
      recommendation: "Modern force majeure clause with pandemic provisions",
      confidence: 78,
      reason: "Recent global events suggest including pandemic provisions",
    },
    {
      id: "sc-5",
      title: "Intellectual Property Rights",
      content: "All intellectual property created by Service Provider in the course of providing the Services shall be owned by Client upon full payment of all fees due under this Agreement. Service Provider hereby assigns all such intellectual property rights to Client.",
      category: "intellectual-property",
      tags: ["ip-rights", "standard"],
      riskLevel: "low",
      confidence: 90,
      reason: "Standard clause for service agreements",
    },
  ];

  // Mock data for missing clauses
  const missingClauses: Clause[] = [
    {
      id: "mc-1",
      title: "Dispute Resolution",
      content: "Any dispute arising out of or in connection with this Agreement shall be resolved by arbitration under the rules of the [Arbitration Association] by one or more arbitrators appointed in accordance with said rules. The place of arbitration shall be [City, Country]. The language of arbitration shall be English.",
      category: "dispute-resolution",
      tags: ["arbitration", "standard"],
      riskLevel: "medium",
      recommendation: "Recommended for international contracts",
      confidence: 85,
      reason: "No dispute resolution mechanism detected in contract",
    },
    {
      id: "mc-2",
      title: "Termination for Convenience",
      content: "Either party may terminate this Agreement for convenience upon thirty (30) days' prior written notice to the other party.",
      category: "termination",
      tags: ["standard", "early-termination"],
      riskLevel: "low",
      confidence: 75,
      reason: "Only termination for cause detected, missing convenience option",
    },
  ];

  // Mock data for risk mitigation clauses
  const riskMitigationClauses: Clause[] = [
    {
      id: "rm-1",
      title: "Enhanced Indemnification",
      content: "Service Provider shall defend, indemnify, and hold harmless Client from and against any and all claims, damages, losses, liabilities, costs, and expenses (including reasonable attorneys' fees) arising out of or relating to any claim or action by a third party that the Services provided by Service Provider infringe or misappropriate any intellectual property right of such third party.",
      category: "indemnification",
      tags: ["enhanced", "ip-protection", "risk-mitigation"],
      riskLevel: "medium",
      recommendation: "Stronger protection for client against IP claims",
      confidence: 82,
      reason: "Current indemnification clause is limited in scope",
    },
    {
      id: "rm-2",
      title: "Data Breach Notification",
      content: "In the event of a data breach affecting Client's data, Service Provider shall notify Client within 24 hours of discovery and take immediate steps to remedy the breach and prevent further unauthorized access. Service Provider shall cooperate fully with Client's investigation of the breach and comply with all applicable data breach notification laws.",
      category: "data-protection",
      tags: ["data-breach", "notification", "compliance"],
      riskLevel: "high",
      recommendation: "Critical for contracts involving sensitive data",
      confidence: 90,
      reason: "Contract involves data processing without breach provisions",
    },
  ];

  // Get risk level badge
  const getRiskLevelBadge = (riskLevel?: Clause["riskLevel"]) => {
    switch (riskLevel) {
      case "high":
        return <Badge variant="destructive">High Risk</Badge>;
      case "medium":
        return <Badge variant="secondary" className="bg-amber-500 text-white">Medium Risk</Badge>;
      case "low":
        return <Badge variant="outline" className="border-green-500 text-green-600">Low Risk</Badge>;
      default:
        return null;
    }
  };

  // Get confidence indicator
  const getConfidenceIndicator = (confidence: number) => {
    let color = "";
    if (confidence >= 90) color = "text-green-500";
    else if (confidence >= 70) color = "text-amber-500";
    else color = "text-red-500";

    return (
      <div className="flex items-center gap-1">
        <ThumbsUp className={`h-3 w-3 ${color}`} />
        <span className={`text-xs ${color}`}>{confidence}% match</span>
      </div>
    );
  };

  // Render clause card
  const renderClauseCard = (clause: Clause) => (
    <Card key={clause.id} className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-start gap-2">
            <div>
              <CardTitle className="text-base">{clause.title}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">{clause.category}</Badge>
                {getRiskLevelBadge(clause.riskLevel)}
                {getConfidenceIndicator(clause.confidence)}
              </div>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="h-8"
            onClick={() => onSelectClause && onSelectClause(clause)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground mb-3 line-clamp-3">
          {clause.content}
        </div>

        {clause.reason && (
          <div className="flex items-start gap-2 mt-3 text-xs bg-muted/50 p-2 rounded-md">
            <Lightbulb className="h-4 w-4 text-amber-500 flex-shrink-0 mt-0.5" />
            <div>
              <span className="font-medium">Why suggested: </span>
              {clause.reason}
            </div>
          </div>
        )}

        {clause.recommendation && (
          <div className="flex items-start gap-2 mt-2 text-xs bg-blue-50 dark:bg-blue-950/30 p-2 rounded-md">
            <Info className="h-4 w-4 text-blue-500 flex-shrink-0 mt-0.5" />
            <div>
              <span className="font-medium">Recommendation: </span>
              {clause.recommendation}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="w-full space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-amber-500" />
          <h2 className="text-lg font-medium">Smart Clause Suggestions</h2>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="h-8"
          onClick={() => {
            setIsRefreshing(true);
            // Simulate API call with timeout
            setTimeout(() => {
              setIsRefreshing(false);
            }, 1500);
          }}
          disabled={isRefreshing}
        >
          <Shield className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Analyzing...' : 'Refresh Analysis'}
        </Button>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base">Contract Context</CardTitle>
          <CardDescription>Suggestions based on your contract details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Contract Type:</span>{" "}
              {contractContext.contractType}
            </div>
            <div>
              <span className="font-medium">Jurisdiction:</span>{" "}
              {contractContext.jurisdiction}
            </div>
            {contractContext.industry && (
              <div>
                <span className="font-medium">Industry:</span>{" "}
                {contractContext.industry}
              </div>
            )}
            {contractContext.value && contractContext.currency && (
              <div>
                <span className="font-medium">Value:</span>{" "}
                {contractContext.currency} {contractContext.value.toLocaleString()}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="recommended">
            <ThumbsUp className="h-4 w-4 mr-2" />
            Recommended
          </TabsTrigger>
          <TabsTrigger value="missing">
            <AlertCircle className="h-4 w-4 mr-2" />
            Missing
          </TabsTrigger>
          <TabsTrigger value="risk">
            <Shield className="h-4 w-4 mr-2" />
            Risk Mitigation
          </TabsTrigger>
        </TabsList>

        <TabsContent value="recommended" className="mt-4">
          <ScrollArea className="h-[500px] pr-4">
            {suggestedClauses.map(renderClauseCard)}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="missing" className="mt-4">
          <ScrollArea className="h-[500px] pr-4">
            {missingClauses.length > 0 ? (
              missingClauses.map(renderClauseCard)
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Check className="h-12 w-12 text-green-500 mb-3" />
                <h3 className="text-lg font-medium mb-1">No missing clauses detected</h3>
                <p className="text-sm text-muted-foreground">
                  Your contract appears to have all essential clauses
                </p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="risk" className="mt-4">
          <ScrollArea className="h-[500px] pr-4">
            {riskMitigationClauses.map(renderClauseCard)}
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SmartClauseSuggestions;
