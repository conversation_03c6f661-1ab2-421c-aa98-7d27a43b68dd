import React, { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  File,
  Upload,
  FileText,
  Image,
  FileSpreadsheet,
  Download,
  Trash2
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

export interface Attachment {
  id: string;
  name: string;
  size: number;
  type: string;
  uploadDate: string;
  url?: string;
  file?: File;
  progress?: number;
}

interface AttachmentsManagerProps {
  attachments: Attachment[];
  onAttachmentAdd: (attachment: Attachment) => void;
  onAttachmentRemove: (attachmentId: string) => void;
  maxFileSize?: number; // in MB
  allowedFileTypes?: string[];
  maxFiles?: number;
}

const AttachmentsManager: React.FC<AttachmentsManagerProps> = ({
  attachments = [],
  onAttachmentAdd,
  onAttachmentRemove,
  maxFileSize = 10, // Default 10MB
  allowedFileTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.txt'],
  maxFiles = 10,
}) => {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [uploading, setUploading] = useState<Attachment[]>([]);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      handleFiles(Array.from(files));
    }
    // Reset the input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle drag events
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  };

  // Process files
  const handleFiles = (files: File[]) => {
    // Check if adding these files would exceed the max files limit
    if (attachments.length + files.length > maxFiles) {
      toast({
        title: "Too many files",
        description: `You can only upload a maximum of ${maxFiles} files.`,
        variant: "destructive",
      });
      return;
    }

    // Process each file
    files.forEach(file => {
      // Check file size
      if (file.size > maxFileSize * 1024 * 1024) {
        toast({
          title: "File too large",
          description: `${file.name} exceeds the maximum file size of ${maxFileSize}MB.`,
          variant: "destructive",
        });
        return;
      }

      // Check file type
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!allowedFileTypes.includes(fileExtension)) {
        toast({
          title: "File type not allowed",
          description: `${file.name} is not an allowed file type.`,
          variant: "destructive",
        });
        return;
      }

      // Create attachment object
      const newAttachment: Attachment = {
        id: `attachment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: file.name,
        size: file.size,
        type: file.type,
        uploadDate: new Date().toISOString(),
        file: file,
        progress: 0,
      };

      // Add to uploading state
      setUploading(prev => [...prev, newAttachment]);

      // Simulate upload progress
      simulateUpload(newAttachment);
    });
  };

  // Simulate file upload with progress
  const simulateUpload = (attachment: Attachment) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.floor(Math.random() * 10) + 5;

      if (progress >= 100) {
        clearInterval(interval);
        progress = 100;

        // Remove from uploading state
        setUploading(prev => prev.filter(a => a.id !== attachment.id));

        // Add to attachments via callback
        onAttachmentAdd({
          ...attachment,
          progress: undefined,
          url: URL.createObjectURL(attachment.file as File),
        });

        toast({
          title: "File uploaded",
          description: `${attachment.name} has been uploaded successfully.`,
        });
      } else {
        // Update progress
        setUploading(prev =>
          prev.map(a =>
            a.id === attachment.id ? { ...a, progress } : a
          )
        );
      }
    }, 300);
  };

  // Get appropriate icon based on file type
  const getFileIcon = (type: string) => {
    if (type.includes('image')) return <Image className="h-4 w-4" />;
    if (type.includes('pdf')) return <FileText className="h-4 w-4" />;
    if (type.includes('spreadsheet') || type.includes('excel')) return <FileSpreadsheet className="h-4 w-4" />;
    if (type.includes('word') || type.includes('document')) return <FileText className="h-4 w-4" />;
    return <File className="h-4 w-4" />;
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  return (
    <div className="space-y-4">
      {/* Drag and drop area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragging ? 'border-primary bg-primary/5' : 'border-muted-foreground/20'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Upload className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <h3 className="font-medium mb-1">Drag and drop files here</h3>
        <p className="text-sm text-muted-foreground mb-4">or click to browse</p>
        <Input
          type="file"
          multiple
          className="hidden"
          id="file-upload"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept={allowedFileTypes.join(',')}
        />
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
        >
          Select Files
        </Button>
        <div className="mt-2 text-xs text-muted-foreground">
          Maximum {maxFiles} files, up to {maxFileSize}MB each
        </div>
      </div>

      {/* Files currently uploading */}
      {uploading.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Uploading</h4>
          {uploading.map((file) => (
            <div key={file.id} className="bg-muted/50 p-3 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  {getFileIcon(file.type)}
                  <span className="text-sm font-medium">{file.name}</span>
                </div>
                <Badge variant="outline">{formatFileSize(file.size)}</Badge>
              </div>
              <Progress value={file.progress} className="h-1.5" />
              <div className="text-xs text-right mt-1 text-muted-foreground">
                {file.progress}%
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Uploaded files */}
      {attachments.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Attachments ({attachments.length})</h4>
          {attachments.map((file) => (
            <div key={file.id} className="bg-muted/50 p-3 rounded-md">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getFileIcon(file.type)}
                  <span className="text-sm font-medium">{file.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{formatFileSize(file.size)}</Badge>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => file.url && window.open(file.url, '_blank')}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-muted-foreground hover:text-destructive"
                    onClick={() => onAttachmentRemove(file.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AttachmentsManager;
