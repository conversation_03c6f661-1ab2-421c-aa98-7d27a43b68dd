import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  AlertTriangle, 
  CheckCircle, 
  Shield, 
  TrendingUp, 
  FileText, 
  ArrowLeft,
  BarChart3,
  PieChart,
  Activity,
  Zap
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useApi } from '@/lib/api';
import { ContractService } from '@/services/api-services';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';

const AIInsightsPage: React.FC = () => {
  const navigate = useNavigate();
  const { fetch } = useApi();
  const { currentWorkspace } = useClerkWorkspace();
  
  const [contracts, setContracts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [insights, setInsights] = useState({
    totalContracts: 0,
    averageRiskScore: 0,
    averageComplianceScore: 0,
    highRiskContracts: 0,
    contractsWithAnalysis: 0,
    commonRisks: [],
    recentAnalyses: []
  });

  useEffect(() => {
    fetchContracts();
  }, [currentWorkspace]);

  const fetchContracts = async () => {
    if (!currentWorkspace) return;

    setLoading(true);
    try {
      const result = await fetch(
        () => ContractService.getContracts({ workspace_id: currentWorkspace.id }),
        "Loading contracts...",
        "Failed to load contracts"
      );

      if (result) {
        setContracts(result);
        calculateInsights(result);
      }
    } catch (error) {
      console.error("Error fetching contracts:", error);
    } finally {
      setLoading(false);
    }
  };

  const calculateInsights = (contractsData: any[]) => {
    // Mock insights calculation - in real implementation, this would aggregate AI analysis data
    const totalContracts = contractsData.length;
    const contractsWithAnalysis = Math.floor(totalContracts * 0.7); // 70% have analysis
    const averageRiskScore = 0.35; // Mock average
    const averageComplianceScore = 0.82; // Mock average
    const highRiskContracts = Math.floor(totalContracts * 0.15); // 15% high risk

    const commonRisks = [
      { type: 'Liability Exposure', count: Math.floor(totalContracts * 0.4), severity: 'high' },
      { type: 'Missing Termination Clause', count: Math.floor(totalContracts * 0.3), severity: 'medium' },
      { type: 'Unclear Payment Terms', count: Math.floor(totalContracts * 0.25), severity: 'medium' },
      { type: 'Intellectual Property Gaps', count: Math.floor(totalContracts * 0.2), severity: 'low' }
    ];

    const recentAnalyses = contractsData.slice(0, 5).map(contract => ({
      id: contract.id,
      title: contract.title,
      riskScore: Math.random() * 0.6 + 0.2, // Random between 0.2-0.8
      complianceScore: Math.random() * 0.4 + 0.6, // Random between 0.6-1.0
      analysisDate: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // Random within last week
    }));

    setInsights({
      totalContracts,
      averageRiskScore,
      averageComplianceScore,
      highRiskContracts,
      contractsWithAnalysis,
      commonRisks,
      recentAnalyses
    });
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 0.8) return 'default';
    if (score >= 0.6) return 'secondary';
    return 'destructive';
  };

  const getRiskLevel = (score: number) => {
    if (score <= 0.3) return 'Low';
    if (score <= 0.6) return 'Medium';
    return 'High';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Brain className="h-8 w-8 animate-pulse mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading AI insights...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button variant="ghost" onClick={() => navigate('/app/contracts')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Contracts
          </Button>
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Brain className="h-6 w-6 text-blue-600" />
              AI Contract Insights
            </h1>
            <p className="text-muted-foreground">
              AI-powered analysis across your contract portfolio
            </p>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Total Contracts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{insights.totalContracts}</div>
            <p className="text-xs text-muted-foreground">
              {insights.contractsWithAnalysis} analyzed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Average Risk
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(1 - insights.averageRiskScore)}`}>
              {Math.round(insights.averageRiskScore * 100)}%
            </div>
            <Badge variant={getScoreBadgeVariant(1 - insights.averageRiskScore)} className="text-xs">
              {getRiskLevel(insights.averageRiskScore)}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Compliance Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(insights.averageComplianceScore)}`}>
              {Math.round(insights.averageComplianceScore * 100)}%
            </div>
            <Progress value={insights.averageComplianceScore * 100} className="h-2 mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              High Risk
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{insights.highRiskContracts}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((insights.highRiskContracts / insights.totalContracts) * 100)}% of total
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="risks" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="risks">Common Risks</TabsTrigger>
          <TabsTrigger value="recent">Recent Analysis</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="risks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Most Common Risk Types
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {insights.commonRisks.map((risk: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{risk.type}</span>
                        <Badge 
                          variant={risk.severity === 'high' ? 'destructive' : risk.severity === 'medium' ? 'secondary' : 'outline'}
                          className="text-xs"
                        >
                          {risk.severity}
                        </Badge>
                      </div>
                      <Progress value={(risk.count / insights.totalContracts) * 100} className="h-2 mt-1" />
                    </div>
                    <span className="text-sm text-muted-foreground ml-4">
                      {risk.count} contracts
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent AI Analyses
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {insights.recentAnalyses.map((analysis: any) => (
                  <div key={analysis.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{analysis.title}</h4>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => navigate(`/app/contracts/analysis/${analysis.id}`)}
                      >
                        <Zap className="h-4 w-4 mr-1" />
                        View Analysis
                      </Button>
                    </div>
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <span>Risk:</span>
                        <Badge variant={getScoreBadgeVariant(1 - analysis.riskScore)}>
                          {Math.round(analysis.riskScore * 100)}%
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <span>Compliance:</span>
                        <Badge variant={getScoreBadgeVariant(analysis.complianceScore)}>
                          {Math.round(analysis.complianceScore * 100)}%
                        </Badge>
                      </div>
                      <span className="text-muted-foreground">
                        {analysis.analysisDate.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Portfolio Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <PieChart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Trend Analysis Coming Soon</h3>
                <p className="text-muted-foreground">
                  Advanced trend analysis and predictive insights will be available in future updates.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIInsightsPage;
