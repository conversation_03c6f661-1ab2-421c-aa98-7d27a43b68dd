import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Brain, 
  AlertTriangle, 
  CheckCircle, 
  Shield, 
  TrendingUp, 
  FileText, 
  Lightbulb,
  RefreshCw,
  Download,
  Clock,
  Zap,
  Info,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { AIAnalysisService } from '@/services/api-services';
import { useToast } from '@/components/ui/use-toast';
import { useApi } from '@/lib/api';
import type { AIAnalysisResult } from '@/services/api-types';

interface EnhancedAIAnalysisProps {
  contractId: string;
  contractTitle?: string;
}

const EnhancedAIAnalysis: React.FC<EnhancedAIAnalysisProps> = ({
  contractId,
  contractTitle = "Contract"
}) => {
  const { fetch } = useApi();
  const { toast } = useToast();
  
  const [analysis, setAnalysis] = useState<AIAnalysisResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [isRunningAnalysis, setIsRunningAnalysis] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch analysis on component mount
  useEffect(() => {
    fetchAnalysis();
  }, [contractId]);

  const fetchAnalysis = async () => {
    if (!contractId) return;

    setLoading(true);
    setError(null);

    try {
      const result = await fetch(
        () => AIAnalysisService.getAnalysis(contractId),
        "Loading AI analysis...",
        "Failed to load analysis"
      );

      if (result) {
        setAnalysis(result);
      }
    } catch (err) {
      console.error("Error fetching analysis:", err);
      setError("Failed to load analysis. Click 'Run Analysis' to generate a new one.");
    } finally {
      setLoading(false);
    }
  };

  const runFreshAnalysis = async () => {
    setIsRunningAnalysis(true);
    setError(null);

    try {
      const result = await fetch(
        () => AIAnalysisService.runAnalysis(contractId, 'contract_analysis'),
        "Running AI analysis...",
        "Failed to run analysis"
      );

      if (result) {
        setAnalysis(result);
        toast({
          title: "Analysis Complete",
          description: "AI analysis has been completed successfully.",
        });
      }
    } catch (err) {
      console.error("Error running analysis:", err);
      setError("Failed to run analysis. Please try again.");
      toast({
        title: "Analysis Failed",
        description: "Failed to run AI analysis. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRunningAnalysis(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 0.8) return 'default';
    if (score >= 0.6) return 'secondary';
    return 'destructive';
  };

  const getRiskLevel = (score: number) => {
    if (score <= 0.3) return 'Low';
    if (score <= 0.6) return 'Medium';
    return 'High';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading AI analysis...</p>
        </div>
      </div>
    );
  }

  if (error && !analysis) {
    return (
      <div className="space-y-4">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="text-center">
          <Button onClick={runFreshAnalysis} disabled={isRunningAnalysis}>
            {isRunningAnalysis ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Brain className="h-4 w-4 mr-2" />
                Run AI Analysis
              </>
            )}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Brain className="h-6 w-6 text-blue-600" />
          <div>
            <h2 className="text-xl font-semibold">AI Analysis</h2>
            <p className="text-sm text-muted-foreground">{contractTitle}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={runFreshAnalysis} disabled={isRunningAnalysis}>
            {isRunningAnalysis ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Run Fresh Analysis
              </>
            )}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {analysis && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="risks">Risks</TabsTrigger>
            <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
            <TabsTrigger value="clauses">Clauses</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Score Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Risk Score
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={`text-2xl font-bold ${getScoreColor(analysis.risk_score)}`}>
                        {Math.round(analysis.risk_score * 100)}%
                      </span>
                      <Badge variant={getScoreBadgeVariant(1 - analysis.risk_score)}>
                        {getRiskLevel(analysis.risk_score)}
                      </Badge>
                    </div>
                    <Progress value={analysis.risk_score * 100} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Compliance Score
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={`text-2xl font-bold ${getScoreColor(analysis.compliance_score)}`}>
                        {Math.round(analysis.compliance_score * 100)}%
                      </span>
                      <Badge variant={getScoreBadgeVariant(analysis.compliance_score)}>
                        {analysis.compliance_score >= 0.8 ? 'Good' : analysis.compliance_score >= 0.6 ? 'Fair' : 'Poor'}
                      </Badge>
                    </div>
                    <Progress value={analysis.compliance_score * 100} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Language Clarity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={`text-2xl font-bold ${getScoreColor(analysis.language_clarity)}`}>
                        {Math.round(analysis.language_clarity * 100)}%
                      </span>
                      <Badge variant={getScoreBadgeVariant(analysis.language_clarity)}>
                        {analysis.language_clarity >= 0.8 ? 'Clear' : analysis.language_clarity >= 0.6 ? 'Fair' : 'Complex'}
                      </Badge>
                    </div>
                    <Progress value={analysis.language_clarity * 100} className="h-2" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Analysis Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-2">Key Findings</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• {analysis.key_risks.length} potential risks identified</li>
                      <li>• {analysis.suggestions.length} improvement suggestions</li>
                      <li>• {analysis.extracted_clauses.length} key clauses analyzed</li>
                      <li>• Analysis confidence: {Math.round(analysis.confidence * 100)}%</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Analysis Details</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Provider: {'AI Service'}</li>
                      <li>• Processing time: {'N/A'}s</li>
                      <li>• Created: {new Date(analysis.created_at).toLocaleDateString()}</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="risks" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Identified Risks ({analysis.key_risks.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {analysis.key_risks.length > 0 ? (
                      analysis.key_risks.map((risk: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium">{risk.description || `Risk ${index + 1}`}</h4>
                            <Badge variant={risk.severity === 'high' ? 'destructive' : risk.severity === 'medium' ? 'secondary' : 'outline'}>
                              {risk.severity || 'Medium'}
                            </Badge>
                          </div>
                          {risk.recommendation && (
                            <p className="text-sm text-muted-foreground">{risk.recommendation}</p>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">No Major Risks Detected</h3>
                        <p className="text-muted-foreground">The AI analysis found no significant risks in this contract.</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="suggestions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  AI Suggestions ({analysis.suggestions.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {analysis.suggestions.length > 0 ? (
                      analysis.suggestions.map((suggestion: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium">{suggestion.title || `Suggestion ${index + 1}`}</h4>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{suggestion.priority || 'Medium'}</Badge>
                              <Button variant="ghost" size="sm">
                                <ThumbsUp className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <ThumbsDown className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">{suggestion.description}</p>
                          {suggestion.content && (
                            <div className="bg-muted p-3 rounded text-sm">
                              <strong>Suggested content:</strong> {suggestion.content}
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">No Suggestions</h3>
                        <p className="text-muted-foreground">The contract appears to be well-structured with no immediate suggestions.</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="clauses" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-500" />
                  Extracted Clauses ({analysis.extracted_clauses.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {analysis.extracted_clauses.length > 0 ? (
                      analysis.extracted_clauses.map((clause: any, index: number) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium">{clause.title || `Clause ${index + 1}`}</h4>
                            <Badge variant="outline">{clause.type || 'General'}</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{clause.content || clause.description}</p>
                          {clause.importance && (
                            <div className="mt-2">
                              <Badge variant={clause.importance === 'high' ? 'default' : 'secondary'}>
                                {clause.importance} importance
                              </Badge>
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <Info className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">No Clauses Extracted</h3>
                        <p className="text-muted-foreground">No specific clauses were identified in this analysis.</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default EnhancedAIAnalysis;
