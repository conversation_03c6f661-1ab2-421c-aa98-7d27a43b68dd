import React, { useState, useEffect, useMemo } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { contractTemplates, ContractTemplate, ContractField } from './contractTemplates';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Zap, 
  Settings, 
  Layers, 
  Target,
  Sparkles,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface TemplateAdaptation {
  reason: string;
  changes: string[];
  confidence: number;
  impact: 'low' | 'medium' | 'high';
}

interface DynamicTemplate extends ContractTemplate {
  adaptations: TemplateAdaptation[];
  dynamicFields: ContractField[];
  contextualClauses: string[];
  isAdapted: boolean;
}

/**
 * Dynamic Template Engine
 * - Adapts contract templates based on user selections
 * - Provides contextual field suggestions
 * - Intelligent template recommendations
 * - Real-time template optimization
 */
const DynamicTemplateEngine: React.FC = () => {
  const { data, selectedTemplate, setSelectedTemplate } = useContractWizard();
  const [dynamicTemplate, setDynamicTemplate] = useState<DynamicTemplate | null>(null);
  const [isAdapting, setIsAdapting] = useState(false);
  const [adaptationHistory, setAdaptationHistory] = useState<TemplateAdaptation[]>([]);
  const [showAdaptations, setShowAdaptations] = useState(true);

  // Generate dynamic template based on context
  const generateDynamicTemplate = useMemo(() => {
    if (!selectedTemplate) return null;

    const adaptations: TemplateAdaptation[] = [];
    let dynamicFields: ContractField[] = [...selectedTemplate.fields];
    let contextualClauses: string[] = [];

    // Jurisdiction-based adaptations
    if (data.jurisdiction) {
      const jurisdictionAdaptation = adaptForJurisdiction(data.jurisdiction, dynamicFields);
      if (jurisdictionAdaptation) {
        adaptations.push(jurisdictionAdaptation.adaptation);
        dynamicFields = jurisdictionAdaptation.fields;
      }
    }

    // Industry-based adaptations
    if (data.industry) {
      const industryAdaptation = adaptForIndustry(data.industry, dynamicFields);
      if (industryAdaptation) {
        adaptations.push(industryAdaptation.adaptation);
        dynamicFields = industryAdaptation.fields;
        contextualClauses = industryAdaptation.clauses;
      }
    }

    // Contract type specific adaptations
    if (data.contractType) {
      const typeAdaptation = adaptForContractType(data.contractType, dynamicFields);
      if (typeAdaptation) {
        adaptations.push(typeAdaptation.adaptation);
        dynamicFields = typeAdaptation.fields;
      }
    }

    // Party type adaptations
    if (data.parties.length > 0) {
      const partyAdaptation = adaptForPartyTypes(data.parties, dynamicFields);
      if (partyAdaptation) {
        adaptations.push(partyAdaptation.adaptation);
        dynamicFields = partyAdaptation.fields;
      }
    }

    return {
      ...selectedTemplate,
      adaptations,
      dynamicFields,
      contextualClauses,
      isAdapted: adaptations.length > 0
    } as DynamicTemplate;
  }, [selectedTemplate, data.jurisdiction, data.industry, data.contractType, data.parties]);

  const adaptForJurisdiction = (jurisdiction: string, fields: ContractField[]) => {
    const newFields = [...fields];
    const changes: string[] = [];

    switch (jurisdiction) {
      case 'us':
        // Add US-specific fields
        if (!newFields.find(f => f.key === 'stateSpecific')) {
          newFields.push({
            key: 'stateSpecific',
            label: 'State-Specific Provisions',
            type: 'textarea',
            placeholder: 'Enter state-specific legal requirements...',
            helpText: 'Include any state-specific legal provisions required for this contract'
          });
          changes.push('Added state-specific provisions field');
        }
        
        if (!newFields.find(f => f.key === 'governingLaw')) {
          newFields.push({
            key: 'governingLaw',
            label: 'Governing Law State',
            type: 'select',
            options: ['California', 'New York', 'Texas', 'Florida', 'Delaware'],
            required: true,
            helpText: 'Select the state whose laws will govern this contract'
          });
          changes.push('Added governing law selection');
        }
        break;

      case 'uk':
        if (!newFields.find(f => f.key === 'ukCompliance')) {
          newFields.push({
            key: 'ukCompliance',
            label: 'UK Regulatory Compliance',
            type: 'multiselect',
            options: ['GDPR', 'Companies Act 2006', 'Consumer Rights Act 2015'],
            helpText: 'Select applicable UK regulations'
          });
          changes.push('Added UK regulatory compliance options');
        }
        break;

      case 'eu':
        if (!newFields.find(f => f.key === 'gdprCompliance')) {
          newFields.push({
            key: 'gdprCompliance',
            label: 'GDPR Compliance Requirements',
            type: 'checkbox',
            required: true,
            helpText: 'Confirm GDPR compliance requirements are met'
          });
          changes.push('Added GDPR compliance requirement');
        }
        break;
    }

    if (changes.length === 0) return null;

    return {
      adaptation: {
        reason: `Adapted for ${jurisdiction.toUpperCase()} jurisdiction requirements`,
        changes,
        confidence: 95,
        impact: 'high' as const
      },
      fields: newFields
    };
  };

  const adaptForIndustry = (industry: string, fields: ContractField[]) => {
    const newFields = [...fields];
    const changes: string[] = [];
    let clauses: string[] = [];

    switch (industry) {
      case 'technology':
        if (!newFields.find(f => f.key === 'intellectualProperty')) {
          newFields.push({
            key: 'intellectualProperty',
            label: 'Intellectual Property Rights',
            type: 'textarea',
            required: true,
            placeholder: 'Define IP ownership, licensing, and protection terms...',
            helpText: 'Critical for technology contracts to protect innovations'
          });
          changes.push('Added IP rights field');
        }

        if (!newFields.find(f => f.key === 'dataProtection')) {
          newFields.push({
            key: 'dataProtection',
            label: 'Data Protection & Privacy',
            type: 'textarea',
            required: true,
            placeholder: 'Specify data handling, storage, and privacy requirements...',
            helpText: 'Essential for compliance with privacy regulations'
          });
          changes.push('Added data protection requirements');
        }

        clauses = ['Software License Terms', 'Source Code Escrow', 'Technical Support', 'SLA Requirements'];
        break;

      case 'healthcare':
        if (!newFields.find(f => f.key === 'hipaaCompliance')) {
          newFields.push({
            key: 'hipaaCompliance',
            label: 'HIPAA Compliance',
            type: 'checkbox',
            required: true,
            helpText: 'Required for healthcare-related contracts'
          });
          changes.push('Added HIPAA compliance requirement');
        }

        clauses = ['Patient Privacy Protection', 'Medical Records Handling', 'Healthcare Regulations'];
        break;

      case 'finance':
        if (!newFields.find(f => f.key === 'financialRegulations')) {
          newFields.push({
            key: 'financialRegulations',
            label: 'Financial Regulatory Compliance',
            type: 'multiselect',
            options: ['SOX', 'PCI DSS', 'Basel III', 'MiFID II'],
            helpText: 'Select applicable financial regulations'
          });
          changes.push('Added financial regulatory compliance');
        }

        clauses = ['Anti-Money Laundering', 'Know Your Customer', 'Financial Reporting'];
        break;

      case 'manufacturing':
        if (!newFields.find(f => f.key === 'qualityStandards')) {
          newFields.push({
            key: 'qualityStandards',
            label: 'Quality Standards & Certifications',
            type: 'multiselect',
            options: ['ISO 9001', 'ISO 14001', 'OHSAS 18001', 'Six Sigma'],
            helpText: 'Specify required quality standards and certifications'
          });
          changes.push('Added quality standards requirements');
        }

        clauses = ['Product Liability', 'Quality Assurance', 'Supply Chain Management'];
        break;
    }

    if (changes.length === 0) return null;

    return {
      adaptation: {
        reason: `Customized for ${industry} industry requirements`,
        changes,
        confidence: 90,
        impact: 'high' as const
      },
      fields: newFields,
      clauses
    };
  };

  const adaptForContractType = (contractType: string, fields: ContractField[]) => {
    const newFields = [...fields];
    const changes: string[] = [];

    // Add contract-type specific fields based on the template
    const template = contractTemplates.find(t => t.id === contractType);
    if (!template) return null;

    // Service agreements need SLA fields
    if (contractType.includes('service')) {
      if (!newFields.find(f => f.key === 'serviceLevel')) {
        newFields.push({
          key: 'serviceLevel',
          label: 'Service Level Agreement',
          type: 'textarea',
          placeholder: 'Define service levels, uptime requirements, response times...',
          helpText: 'Specify measurable service performance criteria'
        });
        changes.push('Added service level agreement field');
      }
    }

    // Employment contracts need specific fields
    if (contractType.includes('employment')) {
      if (!newFields.find(f => f.key === 'benefits')) {
        newFields.push({
          key: 'benefits',
          label: 'Employee Benefits',
          type: 'textarea',
          placeholder: 'List health insurance, retirement, vacation, and other benefits...',
          helpText: 'Detail all employee benefits and compensation'
        });
        changes.push('Added employee benefits field');
      }
    }

    if (changes.length === 0) return null;

    return {
      adaptation: {
        reason: `Enhanced for ${template.label} specific requirements`,
        changes,
        confidence: 85,
        impact: 'medium' as const
      },
      fields: newFields
    };
  };

  const adaptForPartyTypes = (parties: any[], fields: ContractField[]) => {
    const newFields = [...fields];
    const changes: string[] = [];

    // Check if we have government entities
    const hasGovernment = parties.some(p => p.type === 'government');
    if (hasGovernment && !newFields.find(f => f.key === 'governmentCompliance')) {
      newFields.push({
        key: 'governmentCompliance',
        label: 'Government Compliance Requirements',
        type: 'textarea',
        placeholder: 'Specify government compliance requirements, certifications, security clearances...',
        helpText: 'Required when contracting with government entities'
      });
      changes.push('Added government compliance requirements');
    }

    // Check if we have international parties
    const hasInternational = parties.some(p => p.address && !p.address.toLowerCase().includes('united states'));
    if (hasInternational && !newFields.find(f => f.key === 'internationalTerms')) {
      newFields.push({
        key: 'internationalTerms',
        label: 'International Contract Terms',
        type: 'textarea',
        placeholder: 'Address currency, exchange rates, international law, dispute resolution...',
        helpText: 'Important considerations for international contracts'
      });
      changes.push('Added international contract terms');
    }

    if (changes.length === 0) return null;

    return {
      adaptation: {
        reason: 'Adapted for specific party types and requirements',
        changes,
        confidence: 80,
        impact: 'medium' as const
      },
      fields: newFields
    };
  };

  // Update dynamic template when context changes
  useEffect(() => {
    if (generateDynamicTemplate) {
      setIsAdapting(true);
      
      // Simulate processing time
      const timer = setTimeout(() => {
        setDynamicTemplate(generateDynamicTemplate);
        
        if (generateDynamicTemplate.adaptations.length > 0) {
          setAdaptationHistory(prev => [...prev, ...generateDynamicTemplate.adaptations]);
        }
        
        setIsAdapting(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [generateDynamicTemplate]);

  const applyDynamicTemplate = () => {
    if (dynamicTemplate) {
      setSelectedTemplate(dynamicTemplate);
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!selectedTemplate) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-600" />
            Dynamic Template Engine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Select a contract type to see intelligent template adaptations.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Template Engine Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">Dynamic Template Engine</CardTitle>
              {isAdapting && <RefreshCw className="h-4 w-4 animate-spin text-blue-600" />}
            </div>
            <Badge variant={dynamicTemplate?.isAdapted ? 'default' : 'outline'}>
              {dynamicTemplate?.isAdapted ? 'Adapted' : 'Standard'}
            </Badge>
          </div>
        </CardHeader>
        
        {dynamicTemplate && (
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">{dynamicTemplate.label}</h3>
                <p className="text-sm text-muted-foreground">
                  {dynamicTemplate.description}
                </p>
              </div>
              {dynamicTemplate.isAdapted && (
                <Button onClick={applyDynamicTemplate} size="sm">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Apply Adaptations
                </Button>
              )}
            </div>

            {/* Adaptation Summary */}
            {dynamicTemplate.adaptations.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Smart Adaptations Applied</span>
                </div>
                
                {dynamicTemplate.adaptations.map((adaptation, index) => (
                  <div key={index} className="border rounded-lg p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">{adaptation.reason}</span>
                      <div className="flex items-center gap-2">
                        <Badge className={getImpactColor(adaptation.impact)}>
                          {adaptation.impact} impact
                        </Badge>
                        <Badge variant="outline">
                          {adaptation.confidence}% confidence
                        </Badge>
                      </div>
                    </div>
                    
                    <ul className="text-sm text-muted-foreground space-y-1">
                      {adaptation.changes.map((change, changeIndex) => (
                        <li key={changeIndex} className="flex items-center gap-2">
                          <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                          {change}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            )}

            {/* Contextual Clauses */}
            {dynamicTemplate.contextualClauses.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Layers className="h-4 w-4 text-purple-600" />
                  <span className="font-medium">Recommended Clauses</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {dynamicTemplate.contextualClauses.map((clause, index) => (
                    <Badge key={index} variant="outline" className="border-purple-200">
                      {clause}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Dynamic Fields Preview */}
            {dynamicTemplate.dynamicFields.length > selectedTemplate.fields.length && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-orange-600" />
                  <span className="font-medium">Additional Fields</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  {dynamicTemplate.dynamicFields.length - selectedTemplate.fields.length} new fields added based on your selections
                </div>
              </div>
            )}
          </CardContent>
        )}
      </Card>

      {/* Adaptation History */}
      {adaptationHistory.length > 0 && showAdaptations && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Adaptation History</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdaptations(false)}
              >
                Hide
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {adaptationHistory.slice(-3).map((adaptation, index) => (
                <div key={index} className="flex items-start gap-3 text-sm">
                  <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <span className="font-medium">{adaptation.reason}</span>
                    <div className="text-muted-foreground">
                      {adaptation.changes.length} changes applied
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DynamicTemplateEngine;
