import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { GripVertical, Trash2 } from 'lucide-react';

interface ClauseOrderListProps {
  clauses: { title: string; content: string }[];
  onMove: (from: number, to: number) => void;
  onRemove: (idx: number) => void;
}

const ClauseOrderList: React.FC<ClauseOrderListProps> = ({ clauses, onMove, onRemove }) => {
  return (
    <div className="space-y-2">
      {clauses.map((clause, idx) => (
        <div key={idx} className="flex items-center gap-2">
          <Button type="button" size="icon" variant="ghost" aria-label="Move up" disabled={idx === 0} onClick={() => onMove(idx, idx - 1)}>
            <GripVertical className="h-4 w-4" />
          </Button>
          <Button type="button" size="icon" variant="ghost" aria-label="Move down" disabled={idx === clauses.length - 1} onClick={() => onMove(idx, idx + 1)}>
            <GripVertical className="h-4 w-4 rotate-180" />
          </Button>
          <div className="flex-1 border rounded px-2 py-1 bg-muted">
            <span className="font-semibold">{clause.title}</span>
            <div className="text-xs text-muted-foreground">{clause.content}</div>
          </div>
          <Button type="button" size="icon" variant="ghost" className="text-destructive" onClick={() => onRemove(idx)} aria-label="Remove clause">
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ))}
    </div>
  );
};

export default ClauseOrderList;
