import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ContractDocumentPreview from './ContractDocumentPreview';
import WizardStepper from './WizardStepper';
import { ContractWizardProvider, useContractWizard } from './ContractWizardContext';
import { useContractWizardValidation } from './useContractWizardValidation';
import UnifiedContractPreviewModal from '@/components/modals/UnifiedContractPreviewModal';
import SaveAsTemplateModal from './SaveAsTemplateModal';
import { DocumentPreviewModal } from '@/engines/document-engine/preview/DocumentPreviewModal';
import { useDocumentPreview } from '@/engines/document-engine/hooks/useDocumentPreview';
import Step1JurisdictionType from './Step1JurisdictionType';
import { default as Step2PartiesInfo } from './Step2PartiesInfo';
import Step3ContractTerms from './Step3ContractTerms';
import Step4LegalClauses from './Step4LegalClauses';
import Step5IndustrySpecific from './Step5IndustrySpecific';
import Step6Attachments from './Step6Attachments';
import Step7ReviewApproval from './Step7ReviewApproval';
import AIAnalysisInterface from './AIAnalysisInterface';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CheckCircle, Save, ArrowLeft, FileText, Brain, BookTemplate, Edit3 } from 'lucide-react';
import { SimpleLoadingOverlay } from '@/components/ui/loading';
import { useToast } from '@/components/ui/use-toast';
import { generateContractContent } from './contractUtils';

// Interface for imported party data
interface ImportedParty {
  type?: string;
  name?: string;
  address?: string;
  representative?: string;
  title?: string;
  role?: string;
}

// Step names for better UX (imported from WizardStepper)
const stepNames = [
  'Jurisdiction',
  'Parties',
  'Terms',
  'Clauses',
  'Industry',
  'Attachments',
  'Review & Approval'
];

const StepPanel: React.FC = () => {
  const { currentStep } = useContractWizard();
  switch (currentStep) {
    case 0:
      return <Step1JurisdictionType />;
    case 1:
      return <Step2PartiesInfo />;
    case 2:
      return <Step3ContractTerms />;
    case 3:
      return <Step4LegalClauses />;
    case 4:
      return <Step5IndustrySpecific />;
    case 5:
      return <Step6Attachments />;
    case 6:
      return <Step7ReviewApproval />;
    // ... add more cases for other steps
    default:
      return null;
  }
};

const DraftPrompt: React.FC = () => {
  const { loadDraft } = useContractWizard();
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (localStorage.getItem('contract-wizard-draft')) {
      setShow(true);
    }
  }, []);

  if (!show) return null;
  return (
    <Alert className="mb-2 bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800 py-1.5 px-3">
      <AlertDescription className="flex items-center justify-between text-xs">
        <span className="text-amber-800 dark:text-amber-200">Resume your saved draft?</span>
        <div className="flex gap-1.5">
          <Button
            variant="outline"
            size="sm"
            className="h-6 px-2 text-xs bg-amber-100 border-amber-200 text-amber-800 hover:bg-amber-200 hover:text-amber-900 dark:bg-amber-900 dark:border-amber-700 dark:text-amber-200 dark:hover:bg-amber-800"
            onClick={() => { loadDraft(); setShow(false); }}
          >
            Load Draft
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs text-amber-700 hover:text-amber-900 hover:bg-amber-100 dark:text-amber-300 dark:hover:bg-amber-900"
            onClick={() => setShow(false)}
          >
            Dismiss
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
};

// New shell component that consumes the context
const ContractWizardShell: React.FC = () => {
  const navigate = useNavigate();
  const { data, setData, saveDraft } = useContractWizard();
  const { currentStep, isLoading, error } = useContractWizardValidation();
  const { toast } = useToast();
  const [showSaved, setShowSaved] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const { previewState, openPreview, closePreview } = useDocumentPreview();
  const [templateModalOpen, setTemplateModalOpen] = useState(false);
  const [mobileView, setMobileView] = useState<number>(-1); // -1 for preview, 0+ for wizard steps

  const [showAIModal, setShowAIModal] = useState(false);

  // Handle new document preview
  const handleDocumentPreview = () => {
    try {
      const contractContent = data.importedContent || generateContractContent(data);
      const contractTitle = data.title || `${data.contractType || 'Contract'} Preview`;

      if (!contractContent || contractContent.trim() === '') {
        toast({
          title: "No Content",
          description: "Please fill in some contract details before previewing.",
          variant: "destructive"
        });
        return;
      }

      openPreview(contractContent, contractTitle);
    } catch (error) {
      console.error('Error generating preview:', error);
      toast({
        title: "Preview Error",
        description: "Unable to generate contract preview. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Sync mobile view with current step when it changes
  useEffect(() => {
    if (mobileView !== -1) {
      setMobileView(currentStep);
    }
  }, [currentStep]);

  // Check for imported document data
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const isImported = searchParams.get('imported') === 'true';

    if (isImported) {
      try {
        const importedData = sessionStorage.getItem('importedContractData');
        if (importedData) {
          const parsedData = JSON.parse(importedData);

          // Update contract data with imported information
          setData(prevData => ({
            ...prevData,
            title: parsedData.title || 'Imported Contract',
            description: parsedData.content?.substring(0, 200) + '...' || '',
            parties: parsedData.parties?.length ?
              parsedData.parties.map((party: ImportedParty) => ({
                type: party.type || 'organization',
                name: party.name || '',
                address: party.address || '',
                representative: party.representative || '',
                title: party.title || '',
                role: party.role || ''
              })) :
              prevData.parties,
            effectiveDate: parsedData.effectiveDate || new Date().toISOString().split('T')[0],
            contractType: parsedData.type || 'agreement',
            // Store the full content for the document preview
            importedContent: parsedData.content
          }));

          // Clear the session storage
          sessionStorage.removeItem('importedContractData');
        }
      } catch (error) {
        console.error('Error loading imported contract data:', error);
      }
    }
  }, [setData]);

  // Listen for AI Assistant events from document preview
  useEffect(() => {
    const handleOpenAIAssistant = () => {
      setShowAIModal(true);
    };

    window.addEventListener('openAIAssistant', handleOpenAIAssistant);
    return () => {
      window.removeEventListener('openAIAssistant', handleOpenAIAssistant);
    };
  }, []);

  const handleSaveDraft = () => {
    saveDraft();
    setShowSaved(true);
    setTimeout(() => setShowSaved(false), 2000);
  };



  return (
    <div className="h-screen w-full bg-background overflow-hidden flex flex-col">
      {/* Loading Overlay */}
      {isLoading && <SimpleLoadingOverlay text="Processing contract..." />}

      {/* AI Assistant Modal */}
      <Dialog open={showAIModal} onOpenChange={setShowAIModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-600" />
              AI-Powered Document Analysis
            </DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto">
            <AIAnalysisInterface />
          </div>
        </DialogContent>
      </Dialog>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Contract Preview Modal */}
      <UnifiedContractPreviewModal
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        contractId={null}
        showActions={false}
      />

      {/* Save as Template Modal */}
      <SaveAsTemplateModal open={templateModalOpen} onOpenChange={setTemplateModalOpen} />

      {/* New Document Preview Modal */}
      <DocumentPreviewModal
        isOpen={previewState.isOpen}
        onClose={closePreview}
        content={previewState.content}
        title={previewState.title}
        size="xl"
        previewProps={{
          showZoomControls: true,
          showPrintButton: true,
          showDownloadButton: true,
          showFullscreenButton: true,
          documentTitle: previewState.title
        }}
      />

      {/* Mobile Tabs for switching between preview and wizard */}
      <div className="flex lg:hidden border-b px-3 sm:px-4 md:px-5 shrink-0">
        <Button
          variant={mobileView === -1 ? "default" : "ghost"}
          className="flex-1 rounded-none border-b-2 border-transparent data-[state=active]:border-primary min-h-[48px] touch-manipulation"
          data-state={mobileView === -1 ? "active" : "inactive"}
          onClick={() => setMobileView(-1)}
        >
          <FileText className="h-4 w-4 mr-2" />
          Preview
        </Button>
        <Button
          variant={mobileView !== -1 ? "default" : "ghost"}
          className="flex-1 rounded-none border-b-2 border-transparent data-[state=active]:border-primary min-h-[48px] touch-manipulation"
          data-state={mobileView !== -1 ? "active" : "inactive"}
          onClick={() => mobileView === -1 && setMobileView(currentStep)}
        >
          <Edit3 className="h-4 w-4 mr-2" />
          Wizard
        </Button>
      </div>

      {/* Main content area with proper padding and overflow handling - 70:30 split layout */}
      <div className="flex flex-col lg:grid lg:grid-cols-[7fr_3fr] w-full gap-4 sm:gap-5 flex-1 overflow-hidden p-3 sm:p-4 md:p-5 pb-0">
        {/* Left: Contract Preview/Editor (70% width via grid) - Hidden on mobile when wizard is active */}
        <div className={`w-full ${mobileView !== -1 ? "hidden lg:block" : ""} flex flex-col overflow-hidden lg:min-w-0`}>
          <Card className="h-full shadow-sm flex flex-col overflow-hidden">
            <CardHeader className="pb-2 pt-3 px-3 sm:px-4 flex flex-row items-center justify-between shrink-0">
              <CardTitle>Contract Preview</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileView(currentStep)}
                className="h-8 lg:hidden"
              >
                Edit
              </Button>
            </CardHeader>
            <CardContent className="pt-0 px-3 sm:px-4 pb-4 flex-1 overflow-auto">
              <ContractDocumentPreview />
            </CardContent>
          </Card>
        </div>

        {/* Right: Wizard Steps (30% width via grid) - Full width on mobile when wizard is active */}
        <div className={`w-full lg:min-w-[320px] ${mobileView === -1 ? "hidden lg:block" : ""} flex flex-col overflow-hidden`}>
          <Card className="h-full shadow-sm flex flex-col border-slate-200 dark:border-slate-700 overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 pt-3 sm:pt-4 px-3 sm:px-5 border-b dark:border-slate-700">
              <div className="flex flex-col space-y-0">
                <CardTitle className="text-lg sm:text-xl font-semibold">{stepNames[currentStep]}</CardTitle>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setMobileView(-1)}
                  className="min-h-[44px] lg:hidden hover:bg-slate-100 dark:hover:bg-slate-800 touch-manipulation"
                >
                  <FileText className="h-3.5 w-3.5 mr-1.5" />
                  Preview
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDocumentPreview}
                  className="h-8 hover:bg-slate-100 dark:hover:bg-slate-800"
                  title="Quick Preview"
                >
                  <FileText className="h-3.5 w-3.5" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/contracts')}
                  className="h-8 hover:bg-slate-100 dark:hover:bg-slate-800"
                >
                  <ArrowLeft className="h-3.5 w-3.5 mr-1.5" />
                  Back
                </Button>
              </div>
            </CardHeader>

            <div className="px-3 sm:px-5 py-3 border-b bg-slate-50/50 dark:bg-slate-900/50 flex flex-wrap items-center justify-between gap-2">
              <div className="flex items-center gap-2 flex-wrap">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSaveDraft}
                  className="min-h-[44px] px-3 text-xs bg-white dark:bg-slate-950 hover:bg-slate-100 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700 touch-manipulation"
                >
                  <Save className="h-3 w-3 mr-1" />
                  <span className="hidden xs:inline">Save</span> Draft
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDocumentPreview}
                  className="min-h-[44px] px-3 text-xs bg-white dark:bg-slate-950 hover:bg-slate-100 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700 touch-manipulation"
                >
                  <FileText className="h-3 w-3 mr-1" />
                  Preview
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setTemplateModalOpen(true)}
                  className="min-h-[44px] px-3 text-xs bg-white dark:bg-slate-950 hover:bg-slate-100 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700 touch-manipulation"
                >
                  <BookTemplate className="h-3 w-3 mr-1" />
                  <span className="hidden sm:inline">Save as</span> Template
                </Button>


              </div>

              {showSaved && (
                <span className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1 bg-green-50 dark:bg-green-900/30 px-2 py-1 rounded-full">
                  <CheckCircle className="h-3 w-3" />
                  Saved
                </span>
              )}
            </div>

            <DraftPrompt />

            <CardContent className="px-3 sm:px-5 pt-4 pb-4 flex-1 overflow-y-auto">
              <WizardStepper />

              <div className="mt-6 pb-4">
                <StepPanel />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>


    </div>
  );
};

// Top-level component only provides the context
const ContractWizard: React.FC = () => {
  return (
    <ContractWizardProvider>
      <ContractWizardShell />
    </ContractWizardProvider>
  );
};

export default ContractWizard;