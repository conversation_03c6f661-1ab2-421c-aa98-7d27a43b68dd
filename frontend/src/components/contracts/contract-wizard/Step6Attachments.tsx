import React, { useState, useRef, useCallback } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FileText, X, Upload, File, Paperclip, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Attachment {
  name: string;
  type: string;
  file?: File;
  size?: number;
  preview?: string;
}

const Step6Attachments: React.FC = () => {
  const { data, setData, nextStep, prevStep } = useContractWizard();
  const [attachmentName, setAttachmentName] = useState('');
  const [attachmentType, setAttachmentType] = useState('');
  const [touched, setTouched] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize attachments array if it doesn't exist
  React.useEffect(() => {
    if (!data.attachments) {
      setData(d => ({ ...d, attachments: [] }));
    }
  }, [data.attachments, setData]);

  const isValid =
    data.attachments &&
    data.attachments.length > 0 &&
    data.attachments.every((a: Attachment) => a.name && a.name.trim() !== '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);
    if (isValid) nextStep();
  };

  const addAttachment = () => {
    if (attachmentName.trim()) {
      setData(d => ({
        ...d,
        attachments: [...(d.attachments || []), { name: attachmentName.trim(), type: attachmentType.trim() }],
      }));
      setAttachmentName('');
      setAttachmentType('');
    }
  };

  const removeAttachment = (idx: number) => {
    setData(d => ({
      ...d,
      attachments: (d.attachments || []).filter((_: any, i: number) => i !== idx)
    }));
  };

  // Handle drag events
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) {
      setIsDragging(true);
    }
  };

  // Process dropped or selected files
  const processFiles = useCallback((files: FileList | null) => {
    if (!files) return;

    const newAttachments: Attachment[] = Array.from(files).map(file => {
      // Generate a preview URL for image files
      let preview = undefined;
      if (file.type.startsWith('image/')) {
        preview = URL.createObjectURL(file);
      }

      return {
        name: file.name,
        type: file.type || getFileExtension(file.name),
        file: file,
        size: file.size,
        preview
      };
    });

    setData(d => ({
      ...d,
      attachments: [...(d.attachments || []), ...newAttachments],
    }));
    setIsDragging(false);
  }, [setData]);

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    processFiles(e.dataTransfer.files);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    processFiles(e.target.files);
    // Reset the input value so the same file can be uploaded again if needed
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Helper function to get file extension
  const getFileExtension = (filename: string): string => {
    const parts = filename.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toUpperCase() : '';
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // Get icon based on file type
  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return '🖼️';
    if (type.startsWith('application/pdf')) return '📄';
    if (type.startsWith('application/msword') || type.includes('document')) return '📝';
    if (type.startsWith('application/vnd.ms-excel') || type.includes('spreadsheet')) return '📊';
    if (type.startsWith('application/vnd.ms-powerpoint') || type.includes('presentation')) return '📊';
    return '📎';
  };

  return (
    <form className="space-y-3" autoComplete="off" onSubmit={handleSubmit}>
      <div>
        <Label className="text-sm font-medium mb-1 block">Attachments <span className="text-destructive">*</span></Label>

        {/* Drag and drop area */}
        <div
          className={cn(
            "border-2 border-dashed rounded-md p-4 transition-colors text-center cursor-pointer mb-3",
            isDragging
              ? "border-primary bg-primary/5 dark:bg-primary/10"
              : "border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600 bg-slate-50/50 dark:bg-slate-800/20"
          )}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileInputChange}
            className="hidden"
            multiple
            accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
          />
          <div className="flex flex-col items-center justify-center py-2">
            <Upload className={cn("h-8 w-8 mb-2", isDragging ? "text-primary" : "text-slate-400 dark:text-slate-500")} />
            <p className="text-sm font-medium mb-1">
              {isDragging ? "Drop files here" : "Drag & drop files here"}
            </p>
            <p className="text-xs text-slate-500 dark:text-slate-400">or click to browse</p>
          </div>
        </div>

        {/* Attachments list */}
        <div className="space-y-2 max-h-[300px] overflow-y-auto pr-1">
          {(data.attachments || []).map((att: Attachment, idx: number) => (
            <div key={idx} className="flex items-start gap-2 bg-slate-50 dark:bg-slate-800/50 p-2 rounded-md border border-slate-200 dark:border-slate-700">
              <div className="flex-shrink-0 mt-1">
                {att.preview ? (
                  <div className="w-8 h-8 rounded overflow-hidden bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700">
                    <img src={att.preview} alt={att.name} className="w-full h-full object-cover" />
                  </div>
                ) : (
                  <div className="w-8 h-8 rounded bg-slate-100 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 flex items-center justify-center">
                    <FileText className="h-4 w-4 text-slate-400 dark:text-slate-500" />
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <Input
                  className={cn(
                    "h-7 mb-1 text-xs bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700",
                    touched && (!att.name || !att.name.trim()) ? 'border-destructive' : ''
                  )}
                  value={att.name}
                  onChange={e => setData(d => ({
                    ...d,
                    attachments: (d.attachments || []).map((a: any, i: number) =>
                      i === idx ? { ...a, name: e.target.value } : a
                    )
                  }))}
                  placeholder="Attachment name"
                  required
                />
                <div className="flex items-center gap-2">
                  <Input
                    className="h-7 text-xs bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 w-24 flex-shrink-0"
                    value={att.type}
                    onChange={e => setData(d => ({
                      ...d,
                      attachments: (d.attachments || []).map((a: any, i: number) =>
                        i === idx ? { ...a, type: e.target.value } : a
                      )
                    }))}
                    placeholder="Type"
                  />
                  {att.size && (
                    <span className="text-xs text-slate-500 dark:text-slate-400 truncate">{formatFileSize(att.size)}</span>
                  )}
                </div>
              </div>
              <Button
                type="button"
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0 rounded-full text-slate-400 dark:text-slate-500 hover:text-destructive hover:bg-slate-200 dark:hover:bg-slate-700"
                onClick={() => removeAttachment(idx)}
                aria-label="Remove attachment"
              >
                <X className="h-3.5 w-3.5" />
              </Button>
            </div>
          ))}

          {touched && (!data.attachments?.length || data.attachments.some((a: any) => !a.name || !a.name.trim())) && (
            <div className="text-xs text-destructive mt-1">At least one attachment with a name is required.</div>
          )}
        </div>

        {/* Manual attachment entry */}
        <div className="flex items-center gap-2 mt-3 border-t border-slate-100 dark:border-slate-800 pt-3">
          <Input
            className="flex-1 h-7 text-xs bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
            value={attachmentName}
            onChange={e => setAttachmentName(e.target.value)}
            placeholder="Attachment name"
          />
          <Input
            className="w-24 h-7 text-xs bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
            value={attachmentType}
            onChange={e => setAttachmentType(e.target.value)}
            placeholder="Type"
          />
          <Button
            type="button"
            size="sm"
            variant="outline"
            className="h-7 px-2 text-xs bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
            onClick={addAttachment}
          >
            Add
          </Button>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="pt-4 pb-3 mt-4 flex justify-between gap-2 sticky bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-transparent dark:from-background dark:via-background dark:to-transparent z-10">
        <Button
          type="button"
          variant="outline"
          className="h-7 px-3 shadow-sm transition-all hover:shadow bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-xs"
          onClick={prevStep}
        >
          <ChevronLeft className="mr-1 h-3 w-3" />
          Back
        </Button>
        <Button
          type="submit"
          className="flex items-center h-7 px-3 shadow-sm transition-all hover:shadow text-xs"
        >
          Next
          <ChevronRight className="ml-1 h-3 w-3" />
        </Button>
      </div>
    </form>
  );
};

export default Step6Attachments;