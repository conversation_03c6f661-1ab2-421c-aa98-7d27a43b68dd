import React, { useState } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';

import { Plus, Trash2, ChevronLeft, ChevronRight, Save, BookOpen, FileText, Check, X, Eye, Edit3, Search, Star, ChevronUp, ChevronDown } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Interface for clause structure
interface Clause {
  title: string;
  content: string;
  category?: string;
  tags?: string[];
}

// Extended interface for library clauses with additional metadata
interface LibraryClause extends Clause {
  id: string;
  lastUpdated: string;
  version: string;
  approved: boolean;
  author: string;
  isFavorite?: boolean;
  lastUsed?: string;
}

// Mock data for available clauses (simplified for wizard)
const availableClauses: LibraryClause[] = [
  {
    id: "cl-001",
    title: "Standard Limitation of Liability",
    content: "In no event shall either party be liable to the other party for any indirect, special, incidental, consequential, or punitive damages, including lost profits, arising out of or relating to this Agreement.",
    category: "liability",
    tags: ["limitation", "standard", "risk-mitigation"],
    lastUpdated: "2023-05-15",
    version: "1.2",
    approved: true,
    author: "Jane Smith",
    isFavorite: true,
    lastUsed: "2023-08-10"
  },
  {
    id: "cl-002",
    title: "Enhanced Limitation of Liability",
    content: "Neither party shall be liable for any indirect, special, incidental, consequential, punitive, or exemplary damages, including, but not limited to, damages for loss of profits, goodwill, use, data, or other intangible losses.",
    category: "liability",
    tags: ["limitation", "enhanced", "high-risk"],
    lastUpdated: "2023-06-22",
    version: "2.0",
    approved: true,
    author: "John Doe"
  },
  {
    id: "cl-003",
    title: "Basic Confidentiality Clause",
    content: "During the term of this Agreement and for a period of three (3) years thereafter, each party shall maintain in confidence all Confidential Information of the other party.",
    category: "confidentiality",
    tags: ["nda", "standard"],
    lastUpdated: "2023-04-10",
    version: "1.0",
    approved: true,
    author: "Sarah Johnson",
    isFavorite: true,
    lastUsed: "2023-09-15"
  },
  {
    id: "cl-004",
    title: "Extended Confidentiality Clause",
    content: "During the term of this Agreement and for a period of five (5) years thereafter, each party shall maintain in confidence all Confidential Information of the other party. 'Confidential Information' means all non-public information disclosed by one party to the other.",
    category: "confidentiality",
    tags: ["nda", "extended", "sensitive"],
    lastUpdated: "2023-07-03",
    version: "1.5",
    approved: true,
    author: "Michael Chen"
  },
  {
    id: "cl-005",
    title: "Standard Termination Clause",
    content: "Either party may terminate this Agreement upon thirty (30) days prior written notice to the other party if the other party materially breaches this Agreement and fails to cure such breach within such thirty (30) day period.",
    category: "termination",
    tags: ["standard", "breach"],
    lastUpdated: "2023-03-18",
    version: "1.1",
    approved: true,
    author: "Jane Smith"
  },
  {
    id: "cl-006",
    title: "Payment Terms (Net-30)",
    content: "Customer shall pay all invoices within thirty (30) days of receipt. Overdue payments shall bear interest at the rate of 1.5% per month or the maximum rate permitted by law, whichever is lower.",
    category: "payment",
    tags: ["financial", "standard"],
    lastUpdated: "2023-05-28",
    version: "1.0",
    approved: true,
    author: "Robert Johnson",
    lastUsed: "2023-09-20"
  },
  {
    id: "cl-007",
    title: "Force Majeure",
    content: "Neither party shall be liable for any failure or delay in performance under this Agreement due to circumstances beyond its reasonable control, including but not limited to acts of God, natural disasters, terrorism, riots, or wars.",
    category: "general",
    tags: ["standard", "risk-mitigation"],
    lastUpdated: "2023-02-14",
    version: "1.3",
    approved: true,
    author: "Emily Williams"
  },
  {
    id: "cl-008",
    title: "GDPR Compliance Clause",
    content: "Each party shall comply with all applicable data protection and privacy laws, including the General Data Protection Regulation (GDPR), when processing personal data in connection with this Agreement.",
    category: "compliance",
    tags: ["gdpr", "data-protection", "regulatory"],
    lastUpdated: "2023-06-30",
    version: "2.1",
    approved: true,
    author: "Michael Chen"
  },
];

// Category and tag data
const categories = [
  "all",
  "liability",
  "confidentiality",
  "termination",
  "payment",
  "compliance",
  "general",
];



// Wizard-specific Clause Selection Component
interface WizardClauseSelectionProps {
  selectedClauses: Clause[];
  onClauseSelect: (clause: LibraryClause) => void;
  onClauseDeselect: (clauseId: string) => void;
}

const WizardClauseSelection: React.FC<WizardClauseSelectionProps> = ({
  selectedClauses,
  onClauseSelect,
  onClauseDeselect
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // Filter clauses based on search and category
  const filteredClauses = availableClauses.filter(clause => {
    const matchesSearch = clause.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         clause.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || clause.category === selectedCategory;
    const matchesFavorites = !showFavoritesOnly || clause.isFavorite;

    return matchesSearch && matchesCategory && matchesFavorites;
  });

  // Check if a clause is already selected
  const isClauseSelected = (clauseId: string) => {
    return selectedClauses.some(selected =>
      selected.title === availableClauses.find(c => c.id === clauseId)?.title
    );
  };

  // Get category badge color
  const getCategoryColor = (_category: string) => {
    // Neutral, accessible chip styling per UI guidelines; avoid strong hues
    const classes = "bg-muted/40 text-foreground border-border/60";
    return classes + (_category ? '' : '');
  };

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg border border-border/50">
        <div className="flex items-center gap-4 text-sm">
          <span className="text-muted-foreground">
            Showing <span className="font-medium text-foreground">{filteredClauses.length}</span> of <span className="font-medium text-foreground">{availableClauses.length}</span> clauses
          </span>
          {onClauseSelect && (
            <span className="text-muted-foreground">
              • <span className="font-medium text-primary">{selectedClauses?.length || 0}</span> selected
            </span>
          )}
        </div>
        <div className="text-xs text-muted-foreground">
          Compact view
        </div>
      </div>

      {/* Search and Filters - Compact */}
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search clauses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-9 border-1.5"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-32 h-9 border-1.5">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            variant={showFavoritesOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
            className="h-9 px-3 border-1.5 flex items-center gap-2"
          >
            <Star className={`h-3 w-3 ${showFavoritesOnly ? 'fill-current' : ''}`} />
            <span className="hidden sm:inline">Favorites</span>
          </Button>

        </div>
      </div>

      {/* Clause List - Enhanced Bar Style */}
      <div className="space-y-2">
        {filteredClauses.map((clause) => {
          const isSelected = isClauseSelected(clause.id);

          return (
            <div
              key={clause.id}
              className={`group cursor-pointer transition-all duration-200 border-1.5 rounded-lg py-2 px-3 ${
                isSelected
                  ? 'border-primary bg-primary/5 shadow-sm ring-1 ring-primary/20'
                  : 'border-border hover:border-primary/50 hover:bg-muted/30 hover:shadow-sm'
              }`}
              onClick={() => {
                if (isSelected) {
                  onClauseDeselect(clause.id);
                } else {
                  onClauseSelect(clause);
                }
              }}
            >
              <div className="flex items-center gap-3">
                {/* Checkbox */}
                <div className="shrink-0">
                  <Checkbox
                    checked={isSelected}
                    onChange={() => {}} // Handled by container click
                    className="pointer-events-none"
                  />
                </div>

                {/* Main Content - Single Row Layout */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between gap-3">
                    {/* Left: Title */}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-semibold text-foreground truncate">
                        {clause.title}
                      </h4>
                    </div>

                    {/* Right: Meta Information */}
                    <div className="flex items-center gap-2 text-xs shrink-0">
                      <Badge
                        variant="outline"
                        className={`px-1.5 py-0.5 text-xs ${getCategoryColor(clause.category || 'general')}`}
                      >
                        {clause.category}
                      </Badge>

                      {(clause.category === 'liability' || clause.category === 'confidentiality') && (
                        <Badge variant="outline" className="px-1.5 py-0.5 text-xs bg-muted/30 text-foreground border-border">
                          Recommended
                        </Badge>
                      )}

                      <span className="font-mono text-xs bg-muted px-1.5 py-0.5 rounded text-muted-foreground">
                        v{clause.version}
                      </span>

                      <span className="text-xs text-muted-foreground hidden sm:inline">
                        {clause.lastUpdated}
                      </span>
                    </div>
                  </div>


                </div>

                {/* Action Buttons - Always visible for better UX */}
                <div className="shrink-0 flex items-center gap-1">
                  {clause.isFavorite && (
                    <Star className="h-3.5 w-3.5 text-amber-500 fill-current" />
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Add preview functionality here if needed
                    }}
                    title={`Preview: ${clause.title}`}
                  >
                    <Eye className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 opacity-60 hover:opacity-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Add to favorites functionality
                    }}
                    title={clause.isFavorite ? "Remove from favorites" : "Add to favorites"}
                  >
                    <Star className={`h-3 w-3 ${clause.isFavorite ? 'fill-current text-amber-500' : 'text-muted-foreground'}`} />
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {filteredClauses.length === 0 && (
        <div className="text-center py-8 border border-dashed border-border rounded-lg bg-muted/20">
          <FileText className="h-8 w-8 mx-auto mb-3 text-muted-foreground opacity-50" />
          <h3 className="text-sm font-medium mb-1">No clauses found</h3>
          <p className="text-xs text-muted-foreground">
            Try adjusting your search terms or filters
          </p>
        </div>
      )}
    </div>
  );
};

// Enhanced Clause Order List Component
interface EnhancedClauseOrderListProps {
  clauses: Clause[];
  onMove: (from: number, to: number) => void;
  onRemove: (idx: number) => void;
  onPreview: (clause: Clause) => void;
}

const EnhancedClauseOrderList: React.FC<EnhancedClauseOrderListProps> = ({
  clauses,
  onMove,
  onRemove,
  onPreview
}) => {
  if (clauses.length === 0) {
    return (
      <div className="text-center py-6 border border-dashed border-border rounded-lg bg-muted/20">
        <FileText className="h-8 w-8 mx-auto mb-3 text-muted-foreground opacity-50" />
        <p className="text-sm font-medium mb-1">No clauses selected yet</p>
        <p className="text-xs text-muted-foreground">Browse the library above to add clauses to your contract</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {clauses.map((clause, idx) => (
        <div
          key={idx}
          className="group border-1.5 border-border rounded-lg p-3 bg-background hover:bg-muted/30 transition-colors"
        >
          <div className="flex items-center gap-3">
            {/* Move Controls */}
            <div className="flex flex-col gap-0.5 shrink-0">
              <Button
                type="button"
                size="sm"
                variant="ghost"
                className="h-5 w-5 p-0 hover:bg-muted opacity-60 hover:opacity-100"
                disabled={idx === 0}
                onClick={() => onMove(idx, idx - 1)}
                title="Move up"
              >
                <ChevronUp className="h-3 w-3" />
              </Button>
              <Button
                type="button"
                size="sm"
                variant="ghost"
                className="h-5 w-5 p-0 hover:bg-muted opacity-60 hover:opacity-100"
                disabled={idx === clauses.length - 1}
                onClick={() => onMove(idx, idx + 1)}
                title="Move down"
              >
                <ChevronDown className="h-3 w-3" />
              </Button>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              {/* Primary row with order, title, category */}
              <div className="flex items-center gap-2 mb-1">
                <span className="text-xs font-mono text-primary bg-primary/10 px-1.5 py-0.5 rounded font-semibold">
                  #{idx + 1}
                </span>
                <h4 className="text-sm font-semibold text-foreground truncate">
                  {clause.title}
                </h4>
                <Badge variant="outline" className="text-xs shrink-0">
                  {clause.category || 'General'}
                </Badge>
                {(clause as LibraryClause).version && (
                  <span className="text-xs text-muted-foreground font-mono ml-auto">
                    v{(clause as LibraryClause).version}
                  </span>
                )}
              </div>

              {/* Content preview */}
              <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed mb-1">
                {clause.content}
              </p>

              {/* Tags and metadata */}
              <div className="flex items-center gap-2 text-xs">
                {clause.tags && clause.tags.length > 0 && (
                  <div className="flex gap-1">
                    {clause.tags.slice(0, 3).map((tag, tagIdx) => (
                      <Badge key={tagIdx} variant="secondary" className="text-xs px-1.5 py-0 bg-muted/50">
                        {tag}
                      </Badge>
                    ))}
                    {clause.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs px-1.5 py-0 bg-muted/50">
                        +{clause.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                {/* Position indicator */}
                <div className="ml-auto flex items-center gap-1 text-muted-foreground">
                  <span>Position {idx + 1} of {clauses.length}</span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-1 shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                type="button"
                size="sm"
                variant="ghost"
                className="h-7 w-7 p-0"
                onClick={() => onPreview(clause)}
                title="Preview clause"
              >
                <Eye className="h-3 w-3" />
              </Button>
              <Button
                type="button"
                size="sm"
                variant="ghost"
                className="h-7 w-7 p-0 hover:bg-destructive/10 hover:text-destructive"
                onClick={() => onRemove(idx)}
                title="Remove clause"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

const Step4LegalClauses: React.FC = () => {
  const { data, setData, nextStep, prevStep } = useContractWizard();
  const [customClauseModal, setCustomClauseModal] = useState(false);
  const [newCustomClause, setNewCustomClause] = useState({ title: '', content: '', category: 'General Provisions', tags: '' });
  const categories = [
    'General Provisions',
    'Definitions',
    'Obligations',
    'Confidentiality',
    'Limitation of Liability',
    'Termination',
    'Dispute Resolution',
    'Intellectual Property',
    // Add more as needed
  ];
  const [touched, setTouched] = useState(false);
  const [libraryClauses, setLibraryClauses] = useState<Clause[]>(
    (data.libraryClauses || []).map((clause: Clause) => ({
      ...clause,
      category: clause.category || 'General Provisions',
      tags: clause.tags || [],
    }))
  );

  // Validation: at least one clause (standard, library, or custom with title+content)
  const isValid =
    (libraryClauses && libraryClauses.length > 0) ||
    (data.customClauses && data.customClauses.length > 0 && data.customClauses.every((c: Clause) => c.title.trim() && c.content.trim()));



  const removeCustomClause = (idx: number) => {
    setData(d => ({ ...d, customClauses: d.customClauses.filter((_: Clause, i: number) => i !== idx) }));
  };

  const handleSelectLibraryClause = (clause: Clause) => {
    setLibraryClauses(prev => {
      if (prev.some(c => c.title === clause.title && c.content === clause.content)) return prev;
      const updated = [
        ...prev,
        {
          ...clause,
          category: clause.category || 'General Provisions',
          tags: clause.tags || [],
        },
      ];
      setData(d => ({ ...d, libraryClauses: updated }));
      return updated;
    });
  };

  const removeLibraryClause = (idx: number) => {
    setLibraryClauses(prev => {
      const updated = prev.filter((_, i) => i !== idx);
      setData(d => ({ ...d, libraryClauses: updated }));
      return updated;
    });
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-foreground">Legal Clauses</h2>
        <p className="text-sm text-muted-foreground">
          Select and customize the legal clauses for your contract. You can choose from our library of standard clauses or create custom ones.
        </p>
      </div>

      {/* Tabbed layout for clause management */}
      <Tabs defaultValue="library" className="w-full">
        <TabsList className="mb-6 bg-muted/50 p-1 rounded-lg border border-border">
          <TabsTrigger
            value="library"
            className="data-[state=active]:bg-background data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-border flex items-center gap-2"
          >
            <BookOpen className="h-4 w-4" />
            Clause Library
          </TabsTrigger>
          <TabsTrigger
            value="custom"
            className="data-[state=active]:bg-background data-[state=active]:shadow-sm data-[state=active]:border data-[state=active]:border-border flex items-center gap-2"
          >
            <Edit3 className="h-4 w-4" />
            Custom Clauses
          </TabsTrigger>
        </TabsList>

        <TabsContent value="library" className="space-y-6">
          <Card className="border-1.5 border-border shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-base font-medium flex items-center gap-2">
                <BookOpen className="h-4 w-4 text-primary" />
                Select Clauses
              </CardTitle>
              <CardDescription className="text-sm">
                Browse and select from our library of standard legal clauses. Click on any clause to add it to your contract.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <WizardClauseSelection
                selectedClauses={libraryClauses}
                onClauseSelect={(clause) => handleSelectLibraryClause({
                  title: clause.title,
                  content: clause.content,
                  category: clause.category,
                  tags: clause.tags
                })}
                onClauseDeselect={(clauseId) => {
                  const clauseToRemove = availableClauses.find(c => c.id === clauseId);
                  if (clauseToRemove) {
                    const index = libraryClauses.findIndex(c => c.title === clauseToRemove.title);
                    if (index !== -1) {
                      removeLibraryClause(index);
                    }
                  }
                }}
              />
            </CardContent>
          </Card>

          {/* Selected Clauses Section */}
          {libraryClauses.length > 0 && (
            <Card className="border-1.5 border-border shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="text-base font-medium flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-600" />
                  Selected Clauses ({libraryClauses.length})
                </CardTitle>
                <CardDescription className="text-sm">
                  These clauses will be included in your contract. Use the arrows to reorder them.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <EnhancedClauseOrderList
                  clauses={libraryClauses}
                  onMove={(from, to) => {
                    if (to < 0 || to >= libraryClauses.length) return;
                    const updated = [...libraryClauses];
                    const [moved] = updated.splice(from, 1);
                    updated.splice(to, 0, moved);
                    setLibraryClauses(updated);
                    setData(d => ({ ...d, libraryClauses: updated }));
                  }}
                  onRemove={removeLibraryClause}
                  onPreview={(clause) => {
                    // Handle clause preview
                    console.log('Preview clause:', clause);
                  }}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>
        {/* Custom Clauses Tab */}
        <TabsContent value="custom" className="space-y-6">
          <Card className="border-1.5 border-border shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-base font-medium flex items-center gap-2">
                <Edit3 className="h-4 w-4 text-primary" />
                Custom Clauses
              </CardTitle>
              <CardDescription>
                Create custom clauses tailored to your specific contract requirements. These will be added alongside your selected library clauses.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <Button
                    type="button"
                    onClick={() => setCustomClauseModal(true)}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Create Custom Clause
                  </Button>
                  {data.customClauses.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {data.customClauses.length} custom clause{data.customClauses.length !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>
              </div>

              {data.customClauses.length === 0 ? (
                <div className="text-center py-12 border border-dashed border-border rounded-lg bg-muted/20">
                  <Edit3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                  <h3 className="text-sm font-medium text-foreground mb-2">No custom clauses yet</h3>
                  <p className="text-xs text-muted-foreground mb-4 max-w-sm mx-auto">
                    Create custom clauses tailored to your specific contract requirements. These will be added alongside your selected library clauses.
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setCustomClauseModal(true)}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Create Your First Custom Clause
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-4">
                  {data.customClauses.map((clause: Clause, idx: number) => {
                  // Check if clause already exists in library
                  const alreadyInLibrary = libraryClauses.some(c => c.title === clause.title && c.content === clause.content);
                  const handleSaveToLibrary = () => {
                    if (alreadyInLibrary || !clause.title.trim() || !clause.content.trim()) return;
                    const updated = [
                      ...libraryClauses,
                      {
                        title: clause.title,
                        content: clause.content,
                        category: clause.category || 'General Provisions',
                        tags: clause.tags || [],
                      },
                    ];
                    setLibraryClauses(updated);
                    setData(d => ({ ...d, libraryClauses: updated }));
                  };
                    return (
                      <Card key={idx} className="border-1.5 border-border shadow-sm hover:shadow-md transition-shadow">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1 min-w-0">
                              <CardTitle className="text-base font-medium mb-2">
                                {clause.title || `Custom Clause #${idx + 1}`}
                              </CardTitle>
                              {clause.category && (
                                <Badge variant="outline" className="text-xs">
                                  {clause.category}
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-1 shrink-0">
                              <Button
                                type="button"
                                size="sm"
                                variant="ghost"
                                className="h-8 w-8 p-0"
                                onClick={handleSaveToLibrary}
                                disabled={alreadyInLibrary || !clause.title.trim() || !clause.content.trim()}
                                title={alreadyInLibrary ? "Already saved to library" : "Save to clause library"}
                              >
                                <Save className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                size="sm"
                                variant="ghost"
                                className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                                onClick={() => removeCustomClause(idx)}
                                title="Remove clause"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">Title</Label>
                            <Input
                              className="bg-background"
                              value={clause.title}
                              onChange={e => setData(d => ({
                                ...d,
                                customClauses: d.customClauses.map((c: Clause, i: number) => i === idx ? { ...c, title: e.target.value } : c)
                              }))}
                              placeholder="Enter clause title"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">Content</Label>
                            <textarea
                              className="w-full border border-border rounded-md px-3 py-3 min-h-[100px] bg-background resize-none focus:ring-2 focus:ring-ring focus:border-transparent text-sm"
                              value={clause.content}
                              onChange={e => setData(d => ({
                                ...d,
                                customClauses: d.customClauses.map((c: Clause, i: number) => i === idx ? { ...c, content: e.target.value } : c)
                              }))}
                              placeholder="Enter the clause text..."
                              rows={3}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Validation Message */}
      {touched && !isValid && (
        <Card className="border-destructive/50 bg-destructive/5">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 text-destructive">
              <X className="h-4 w-4" />
              <span className="text-sm font-medium">At least one clause is required</span>
            </div>
            <p className="text-xs text-destructive/80 mt-1">
              Please select at least one clause from the library or create a custom clause to continue.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-border">
        <Button
          type="button"
          variant="outline"
          onClick={prevStep}
          className="flex items-center gap-2"
        >
          <ChevronLeft className="h-4 w-4" />
          Back
        </Button>
        <Button
          type="button"
          onClick={() => {
            setTouched(true);
            if (isValid) nextStep();
          }}
          className="flex items-center gap-2"
        >
          Continue
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Modal for adding custom clause */}
      {customClauseModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-xl max-h-[90vh] overflow-hidden">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-medium flex items-center gap-2">
                <Edit3 className="h-5 w-5" />
                Create Custom Clause
              </CardTitle>
              <CardDescription>
                Create a clause tailored to your specific contract needs.
              </CardDescription>
            </CardHeader>

            <ScrollArea className="max-h-[60vh]">
              <CardContent className="space-y-6">
                <div className="space-y-3">
                  <Label className="text-sm font-medium">
                    Clause Title <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    className="bg-background"
                    value={newCustomClause.title}
                    onChange={e => setNewCustomClause(c => ({ ...c, title: e.target.value }))}
                    placeholder="e.g. Intellectual Property Rights"
                  />
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium">
                    Clause Content <span className="text-destructive">*</span>
                  </Label>
                  <textarea
                    className="w-full border border-border rounded-md px-3 py-3 min-h-[150px] bg-background resize-none focus:ring-2 focus:ring-ring focus:border-transparent"
                    value={newCustomClause.content}
                    onChange={e => setNewCustomClause(c => ({ ...c, content: e.target.value }))}
                    placeholder="Enter the full text of your clause here..."
                    rows={6}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Category</Label>
                    <Select
                      value={newCustomClause.category}
                      onValueChange={value => setNewCustomClause(c => ({ ...c, category: value }))}
                    >
                      <SelectTrigger className="bg-background">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map(cat => (
                          <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Tags (comma separated)</Label>
                    <Input
                      className="bg-background"
                      value={newCustomClause.tags}
                      onChange={e => setNewCustomClause(c => ({ ...c, tags: e.target.value }))}
                      placeholder="e.g. standard, protection, IP"
                    />
                    <p className="text-xs text-muted-foreground">Optional tags to help organize clauses</p>
                  </div>
                </div>
              </CardContent>
            </ScrollArea>

            <div className="flex justify-end gap-3 pt-4 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCustomClauseModal(false)}
              >
                Cancel
              </Button>
              <Button
                type="button"
                disabled={!newCustomClause.title.trim() || !newCustomClause.content.trim()}
                onClick={() => {
                  if (!newCustomClause.title.trim() || !newCustomClause.content.trim()) return;
                  setData(d => ({
                    ...d,
                    customClauses: [
                      ...(d.customClauses || []),
                      {
                        title: newCustomClause.title.trim(),
                        content: newCustomClause.content.trim(),
                        category: newCustomClause.category,
                        tags: newCustomClause.tags.split(',').map(t => t.trim()).filter(Boolean),
                      },
                    ],
                  }));
                  setNewCustomClause({ title: '', content: '', category: 'General Provisions', tags: '' });
                  setCustomClauseModal(false);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Clause
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default Step4LegalClauses;