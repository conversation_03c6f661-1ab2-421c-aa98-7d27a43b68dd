import React, { useState, useEffect, useMemo } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Filter, 
  BookOpen, 
  Plus,
  Star,
  Copy,
  Edit,
  Trash2,
  Tag,
  Globe,
  Building,
  Shield,
  Zap,
  Heart,
  Eye,
  ThumbsUp
} from 'lucide-react';

interface ClauseItem {
  id: string;
  title: string;
  content: string;
  category: string;
  subcategory?: string;
  jurisdiction: string[];
  industry: string[];
  contractTypes: string[];
  riskLevel: 'low' | 'medium' | 'high';
  popularity: number;
  rating: number;
  tags: string[];
  isCustom: boolean;
  isFavorite: boolean;
  lastUsed?: Date;
  description: string;
  legalNotes?: string;
  alternatives?: string[];
}

/**
 * Enhanced Clause Library with AI-powered search and categorization
 * - Smart search and filtering
 * - Contextual clause recommendations
 * - Custom clause creation and management
 * - Popularity and rating system
 * - Industry and jurisdiction specific clauses
 */
const EnhancedClauseLibrary: React.FC = () => {
  const { data, setData } = useContractWizard();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedJurisdiction, setSelectedJurisdiction] = useState('all');
  const [selectedIndustry, setSelectedIndustry] = useState('all');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);
  const [sortBy, setSortBy] = useState<'relevance' | 'popularity' | 'rating' | 'recent'>('relevance');
  const [isCreatingClause, setIsCreatingClause] = useState(false);
  const [newClause, setNewClause] = useState<Partial<ClauseItem>>({});

  // Mock clause library data
  const [clauseLibrary, setClauseLibrary] = useState<ClauseItem[]>([
    {
      id: 'termination-1',
      title: 'Standard Termination Clause',
      content: 'Either party may terminate this Agreement at any time by providing thirty (30) days written notice to the other party.',
      category: 'Termination',
      subcategory: 'Standard',
      jurisdiction: ['us', 'uk', 'ca'],
      industry: ['general'],
      contractTypes: ['service', 'employment', 'consulting'],
      riskLevel: 'low',
      popularity: 95,
      rating: 4.8,
      tags: ['termination', 'notice', 'standard'],
      isCustom: false,
      isFavorite: false,
      description: 'Basic termination clause allowing either party to end the agreement with notice',
      legalNotes: 'Provides flexibility while ensuring adequate notice period',
      alternatives: ['termination-2', 'termination-3']
    },
    {
      id: 'confidentiality-1',
      title: 'Mutual Confidentiality Agreement',
      content: 'Each party acknowledges that it may have access to certain confidential information of the other party. Each party agrees to maintain in confidence all confidential information received from the other party.',
      category: 'Confidentiality',
      subcategory: 'Mutual',
      jurisdiction: ['us', 'uk', 'eu'],
      industry: ['technology', 'finance', 'healthcare'],
      contractTypes: ['service', 'partnership', 'consulting'],
      riskLevel: 'medium',
      popularity: 88,
      rating: 4.6,
      tags: ['confidentiality', 'mutual', 'information'],
      isCustom: false,
      isFavorite: true,
      description: 'Protects confidential information shared between parties',
      legalNotes: 'Essential for protecting sensitive business information',
      alternatives: ['confidentiality-2']
    },
    {
      id: 'ip-tech-1',
      title: 'Technology IP Ownership',
      content: 'All intellectual property rights in any technology, software, or innovations developed during the performance of this Agreement shall remain the exclusive property of the Company.',
      category: 'Intellectual Property',
      subcategory: 'Ownership',
      jurisdiction: ['us', 'uk'],
      industry: ['technology', 'software'],
      contractTypes: ['employment', 'consulting', 'development'],
      riskLevel: 'high',
      popularity: 76,
      rating: 4.4,
      tags: ['intellectual property', 'technology', 'ownership'],
      isCustom: false,
      isFavorite: false,
      description: 'Establishes clear IP ownership for technology-related work',
      legalNotes: 'Critical for protecting company innovations and developments'
    },
    {
      id: 'payment-net30',
      title: 'Net 30 Payment Terms',
      content: 'Payment is due within thirty (30) days of invoice date. Late payments may incur a service charge of 1.5% per month.',
      category: 'Payment',
      subcategory: 'Terms',
      jurisdiction: ['us', 'ca'],
      industry: ['general'],
      contractTypes: ['service', 'supply', 'consulting'],
      riskLevel: 'low',
      popularity: 92,
      rating: 4.7,
      tags: ['payment', 'net 30', 'invoice'],
      isCustom: false,
      isFavorite: true,
      description: 'Standard 30-day payment terms with late fee provision',
      legalNotes: 'Balances cash flow needs with reasonable payment timeframe'
    },
    {
      id: 'force-majeure-1',
      title: 'Force Majeure Clause',
      content: 'Neither party shall be liable for any failure or delay in performance under this Agreement which is due to an act of God, war, terrorism, epidemic, government action, or other causes beyond the reasonable control of such party.',
      category: 'Risk Management',
      subcategory: 'Force Majeure',
      jurisdiction: ['us', 'uk', 'eu'],
      industry: ['general'],
      contractTypes: ['service', 'supply', 'construction'],
      riskLevel: 'medium',
      popularity: 84,
      rating: 4.5,
      tags: ['force majeure', 'risk', 'liability'],
      isCustom: false,
      isFavorite: false,
      description: 'Protects parties from liability due to extraordinary circumstances',
      legalNotes: 'Particularly important in uncertain times and long-term contracts'
    }
  ]);

  // Smart filtering and search
  const filteredClauses = useMemo(() => {
    let filtered = clauseLibrary;

    // Text search
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(clause =>
        clause.title.toLowerCase().includes(term) ||
        clause.content.toLowerCase().includes(term) ||
        clause.description.toLowerCase().includes(term) ||
        clause.tags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(clause => clause.category === selectedCategory);
    }

    // Jurisdiction filter
    if (selectedJurisdiction !== 'all') {
      filtered = filtered.filter(clause => clause.jurisdiction.includes(selectedJurisdiction));
    }

    // Industry filter
    if (selectedIndustry !== 'all') {
      filtered = filtered.filter(clause => clause.industry.includes(selectedIndustry));
    }

    // Favorites filter
    if (showFavoritesOnly) {
      filtered = filtered.filter(clause => clause.isFavorite);
    }

    // Contextual relevance (based on current contract data)
    if (data.contractType) {
      filtered = filtered.filter(clause => 
        clause.contractTypes.includes(data.contractType) || 
        clause.contractTypes.includes('general')
      );
    }

    // Sort
    switch (sortBy) {
      case 'popularity':
        filtered.sort((a, b) => b.popularity - a.popularity);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'recent':
        filtered.sort((a, b) => {
          const aDate = a.lastUsed || new Date(0);
          const bDate = b.lastUsed || new Date(0);
          return bDate.getTime() - aDate.getTime();
        });
        break;
      default: // relevance
        // Smart relevance scoring based on context
        filtered.sort((a, b) => {
          let scoreA = 0;
          let scoreB = 0;

          // Boost score for matching jurisdiction
          if (data.jurisdiction && a.jurisdiction.includes(data.jurisdiction)) scoreA += 20;
          if (data.jurisdiction && b.jurisdiction.includes(data.jurisdiction)) scoreB += 20;

          // Boost score for matching industry
          if (data.industry && a.industry.includes(data.industry)) scoreA += 15;
          if (data.industry && b.industry.includes(data.industry)) scoreB += 15;

          // Boost score for matching contract type
          if (data.contractType && a.contractTypes.includes(data.contractType)) scoreA += 25;
          if (data.contractType && b.contractTypes.includes(data.contractType)) scoreB += 25;

          // Add popularity and rating to score
          scoreA += a.popularity * 0.3 + a.rating * 5;
          scoreB += b.popularity * 0.3 + b.rating * 5;

          return scoreB - scoreA;
        });
    }

    return filtered;
  }, [clauseLibrary, searchTerm, selectedCategory, selectedJurisdiction, selectedIndustry, showFavoritesOnly, sortBy, data]);

  // Get unique categories, jurisdictions, and industries for filters
  const categories = [...new Set(clauseLibrary.map(c => c.category))];
  const jurisdictions = [...new Set(clauseLibrary.flatMap(c => c.jurisdiction))];
  const industries = [...new Set(clauseLibrary.flatMap(c => c.industry))];

  const addClauseToContract = (clause: ClauseItem) => {
    const newLibraryClause = {
      title: clause.title,
      content: clause.content
    };

    setData(prev => ({
      ...prev,
      libraryClauses: [...prev.libraryClauses, newLibraryClause]
    }));

    // Update usage statistics
    setClauseLibrary(prev => prev.map(c => 
      c.id === clause.id 
        ? { ...c, lastUsed: new Date(), popularity: c.popularity + 1 }
        : c
    ));
  };

  const toggleFavorite = (clauseId: string) => {
    setClauseLibrary(prev => prev.map(c => 
      c.id === clauseId ? { ...c, isFavorite: !c.isFavorite } : c
    ));
  };

  const createCustomClause = () => {
    if (!newClause.title || !newClause.content) return;

    const customClause: ClauseItem = {
      id: `custom-${Date.now()}`,
      title: newClause.title,
      content: newClause.content,
      category: newClause.category || 'Custom',
      jurisdiction: [data.jurisdiction || 'us'],
      industry: [data.industry || 'general'],
      contractTypes: [data.contractType || 'general'],
      riskLevel: 'medium',
      popularity: 0,
      rating: 0,
      tags: newClause.tags || [],
      isCustom: true,
      isFavorite: false,
      description: newClause.description || '',
      ...newClause
    } as ClauseItem;

    setClauseLibrary(prev => [customClause, ...prev]);
    setNewClause({});
    setIsCreatingClause(false);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  return (
    <div className="space-y-4">
      {/* Library Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">Enhanced Clause Library</CardTitle>
              <Badge variant="outline">{filteredClauses.length} clauses</Badge>
            </div>
            <Button onClick={() => setIsCreatingClause(true)} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Create Clause
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search clauses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedJurisdiction} onValueChange={setSelectedJurisdiction}>
              <SelectTrigger>
                <SelectValue placeholder="Jurisdiction" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Jurisdictions</SelectItem>
                {jurisdictions.map(jurisdiction => (
                  <SelectItem key={jurisdiction} value={jurisdiction}>
                    {jurisdiction.toUpperCase()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">Relevance</SelectItem>
                <SelectItem value="popularity">Popularity</SelectItem>
                <SelectItem value="rating">Rating</SelectItem>
                <SelectItem value="recent">Recently Used</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Quick Filters */}
          <div className="flex items-center gap-2 flex-wrap">
            <Button
              variant={showFavoritesOnly ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
            >
              <Heart className="h-3 w-3 mr-1" />
              Favorites
            </Button>
            
            {data.industry && (
              <Button
                variant={selectedIndustry === data.industry ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedIndustry(selectedIndustry === data.industry ? 'all' : data.industry)}
              >
                <Building className="h-3 w-3 mr-1" />
                {data.industry}
              </Button>
            )}
            
            {data.jurisdiction && (
              <Button
                variant={selectedJurisdiction === data.jurisdiction ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedJurisdiction(selectedJurisdiction === data.jurisdiction ? 'all' : data.jurisdiction)}
              >
                <Globe className="h-3 w-3 mr-1" />
                {data.jurisdiction.toUpperCase()}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Clause List */}
      <div className="grid gap-4">
        {filteredClauses.map((clause) => (
          <Card key={clause.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="space-y-3">
                {/* Clause Header */}
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-medium">{clause.title}</h3>
                      {clause.isCustom && <Badge variant="secondary">Custom</Badge>}
                      <Badge className={getRiskColor(clause.riskLevel)}>
                        {clause.riskLevel} risk
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {clause.description}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-1 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleFavorite(clause.id)}
                      className="h-8 w-8 p-0"
                    >
                      <Heart className={`h-4 w-4 ${clause.isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button
                      onClick={() => addClauseToContract(clause)}
                      size="sm"
                      className="h-8"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add
                    </Button>
                  </div>
                </div>

                {/* Clause Content */}
                <div className="bg-muted/50 p-3 rounded text-sm">
                  {clause.content}
                </div>

                {/* Clause Metadata */}
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      {clause.rating.toFixed(1)}
                    </div>
                    <div className="flex items-center gap-1">
                      <ThumbsUp className="h-3 w-3" />
                      {clause.popularity}%
                    </div>
                    <div className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {clause.category}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {clause.jurisdiction.slice(0, 3).map(j => (
                      <Badge key={j} variant="outline" className="text-xs">
                        {j.toUpperCase()}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Tags */}
                {clause.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {clause.tags.map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Create Custom Clause Modal */}
      {isCreatingClause && (
        <Card className="fixed inset-4 z-50 bg-white shadow-lg overflow-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Create Custom Clause</CardTitle>
              <Button
                variant="ghost"
                onClick={() => setIsCreatingClause(false)}
              >
                ×
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Title</label>
              <Input
                value={newClause.title || ''}
                onChange={(e) => setNewClause(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter clause title..."
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Content</label>
              <Textarea
                value={newClause.content || ''}
                onChange={(e) => setNewClause(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Enter clause content..."
                rows={6}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Description</label>
              <Input
                value={newClause.description || ''}
                onChange={(e) => setNewClause(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Brief description of the clause..."
              />
            </div>
            
            <div className="flex gap-4">
              <Button onClick={createCustomClause} disabled={!newClause.title || !newClause.content}>
                <Plus className="h-4 w-4 mr-2" />
                Create Clause
              </Button>
              <Button variant="outline" onClick={() => setIsCreatingClause(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedClauseLibrary;
