// Universal contract template schema for the Contract Wizard
// Add new templates here to support any contract type, industry, or party structure

export type FieldType = 'text' | 'textarea' | 'date' | 'number' | 'select' | 'party' | 'clause' | 'custom' | 'checkbox' | 'file' | 'multiselect' | 'signature';

export interface ContractField {
  key: string;
  label: string;
  type: FieldType;
  required?: boolean;
  options?: string[]; // For select fields
  partyTypeOptions?: string[]; // For party fields
  placeholder?: string;
  helpText?: string;
  conditionalOn?: string; // Display field based on another field's value
  conditionalValue?: string | string[]; // Value(s) that will trigger showing this field
  conditionalType?: 'eq' | 'neq' | 'contains' | 'gt' | 'lt'; // Type of condition (equals, not equals, contains, etc.)
  industryRelevant?: string[]; // Industries where this field is particularly relevant
}

export interface ClauseTemplate {
  id: string;
  label: string;
  content: string;
  optional?: boolean;
  industry?: string[];
  jurisdiction?: string[]; // Jurisdictions where this clause is applicable
  incompatibleWith?: string[]; // Clause IDs that cannot be used with this clause
  requiresClause?: string[]; // Clause IDs that must be included if this one is
}

export interface PartyType {
  type: string; // 'individual', 'company', 'government', 'nonprofit', etc.
  label: string;
  fields: ContractField[];
}

export interface ContractTemplate {
  id: string;
  label: string;
  description: string;
  industry?: string[];
  fields: ContractField[];
  parties: {
    min: number;
    max: number;
    types: string[]; // e.g., ['individual', 'company']
    roles?: { [key: string]: string }; // e.g., {'party1': 'employer', 'party2': 'employee'}
    companyFields?: ContractField[];
    individualFields?: ContractField[];
  };
  clauses: ClauseTemplate[];
  optionalClauses?: ClauseTemplate[];
  customSections?: string[]; // For industry or user-defined sections
  industrySpecificFields?: { [industry: string]: ContractField[] }; // Fields that only appear for specific industries
  requirements?: {
    [key: string]: {
      jurisdictions?: string[];
      description: string;
    };
  };
}

// Define common party types that can be used across templates
export const partyTypes: PartyType[] = [
  {
    type: 'individual',
    label: 'Individual/Person',
    fields: [
      { key: 'name', label: 'Full Name', type: 'text', required: true },
      { key: 'address', label: 'Address', type: 'textarea', required: true },
      { key: 'phoneNumber', label: 'Phone Number', type: 'text', required: false },
      { key: 'email', label: 'Email Address', type: 'text', required: false },
      { key: 'idNumber', label: 'ID/Passport Number', type: 'text', required: false },
      { key: 'dob', label: 'Date of Birth', type: 'date', required: false },
      { key: 'nationality', label: 'Nationality', type: 'text', required: false },
    ]
  },
  {
    type: 'company',
    label: 'Company/Business',
    fields: [
      { key: 'name', label: 'Company Name', type: 'text', required: true },
      { key: 'address', label: 'Registered Address', type: 'textarea', required: true },
      { key: 'registration', label: 'Company Registration Number', type: 'text', required: true },
      { key: 'vat', label: 'VAT/Tax ID', type: 'text', required: false },
      { key: 'representative', label: 'Authorized Representative', type: 'text', required: true },
      { key: 'title', label: 'Representative Title/Position', type: 'text', required: true },
      { key: 'incorporationDate', label: 'Date of Incorporation', type: 'date', required: false },
      { key: 'jurisdiction', label: 'Jurisdiction of Incorporation', type: 'text', required: false },
    ]
  },
  {
    type: 'government',
    label: 'Government Entity',
    fields: [
      { key: 'name', label: 'Entity Name', type: 'text', required: true },
      { key: 'address', label: 'Address', type: 'textarea', required: true },
      { key: 'division', label: 'Division/Unit', type: 'text', required: false },
      { key: 'representative', label: 'Authorized Representative', type: 'text', required: true },
      { key: 'title', label: 'Representative Title/Position', type: 'text', required: true },
      { key: 'authorityReference', label: 'Authority Reference', type: 'text', required: false },
    ]
  },
  {
    type: 'nonprofit',
    label: 'Non-Profit Organization',
    fields: [
      { key: 'name', label: 'Organization Name', type: 'text', required: true },
      { key: 'address', label: 'Address', type: 'textarea', required: true },
      { key: 'registration', label: 'Registration Number', type: 'text', required: true },
      { key: 'taxExempt', label: 'Tax-Exempt Status', type: 'text', required: false },
      { key: 'representative', label: 'Authorized Representative', type: 'text', required: true },
      { key: 'title', label: 'Representative Title/Position', type: 'text', required: true },
    ]
  }
];

// Common industry options for categorization
export const industryOptions = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Manufacturing',
  'Retail',
  'Construction',
  'Entertainment',
  'Legal',
  'Real Estate',
  'Hospitality',
  'Transportation',
  'Agriculture',
  'Energy',
  'Other'
];

export const contractTemplates: ContractTemplate[] = [
  {
    id: 'nda',
    label: 'Non-Disclosure Agreement',
    description: 'Protect confidential information between parties.',
    industry: ['Technology', 'Finance', 'Legal', 'Healthcare', 'Other'],
    fields: [
      { key: 'title', label: 'Contract Title', type: 'text', required: true },
      { key: 'effectiveDate', label: 'Effective Date', type: 'date', required: true },
      { key: 'duration', label: 'Duration', type: 'text', required: false },
      { key: 'jurisdiction', label: 'Jurisdiction', type: 'select', required: true, options: ['US', 'UK', 'EU', 'CA', 'AU', 'Other'] },
      { key: 'description', label: 'Description/Recitals', type: 'textarea', required: false },
      { key: 'confidentialInfoDefinition', label: 'Definition of Confidential Information', type: 'textarea', required: false },
      { key: 'mutual', label: 'Is this a mutual NDA?', type: 'checkbox', required: false },
      {
        key: 'disclosingParty',
        label: 'Disclosing Party',
        type: 'select',
        required: true,
        conditionalOn: 'mutual',
        conditionalValue: 'false',
        options: ['party1', 'party2']
      },
      {
        key: 'receivingParty',
        label: 'Receiving Party',
        type: 'select',
        required: true,
        conditionalOn: 'mutual',
        conditionalValue: 'false',
        options: ['party1', 'party2']
      },
    ],
    parties: {
      min: 2,
      max: 10,
      types: ['individual', 'company', 'government', 'nonprofit'],
      companyFields: [
        { key: 'vat', label: 'VAT Number', type: 'text' },
        { key: 'registration', label: 'Company Registration Number', type: 'text' },
      ],
    },
    clauses: [
      { id: 'confidentiality', label: 'Confidentiality', content: 'Both parties agree to keep information confidential.' },
      { id: 'term', label: 'Term', content: 'This agreement lasts for the duration specified.' },
    ],
    optionalClauses: [
      { id: 'nonCompete', label: 'Non-Compete', content: 'Parties agree not to compete for a specified period.', optional: true },
      { id: 'arbitration', label: 'Arbitration', content: 'Disputes will be resolved by arbitration.', optional: true },
      { id: 'returnOfInfo', label: 'Return of Information', content: 'All confidential information must be returned or destroyed upon termination.', optional: true },
      { id: 'nonSolicitation', label: 'Non-Solicitation', content: 'Parties agree not to solicit employees or clients from each other.', optional: true },
      { id: 'jurisdictionSpecific', label: 'Jurisdiction-Specific Provisions', content: 'Additional provisions required by the specific jurisdiction.', optional: true },
    ],
    customSections: ['Compliance Notes', 'Special Conditions'],
    industrySpecificFields: {
      'Technology': [
        { key: 'sourceCodeAccess', label: 'Source Code Access', type: 'checkbox', required: false },
        { key: 'apiAccess', label: 'API Access', type: 'checkbox', required: false },
      ],
      'Healthcare': [
        { key: 'hipaaCompliance', label: 'HIPAA Compliance Required', type: 'checkbox', required: false },
        { key: 'patientDataAccess', label: 'Patient Data Access', type: 'select', options: ['None', 'Anonymized Only', 'Full Access with Authorization'], required: false },
      ]
    }
  },
  {
    id: 'service',
    label: 'Service Agreement',
    description: 'Agreement for the provision of services.',
    industry: ['Technology', 'Consulting', 'Professional Services', 'Other'],
    fields: [
      { key: 'title', label: 'Contract Title', type: 'text', required: true },
      { key: 'effectiveDate', label: 'Effective Date', type: 'date', required: true },
      { key: 'duration', label: 'Duration', type: 'text', required: false },
      { key: 'jurisdiction', label: 'Jurisdiction', type: 'select', required: true, options: ['US', 'UK', 'EU', 'CA', 'AU', 'Other'] },
      { key: 'description', label: 'Description/Recitals', type: 'textarea', required: false },
      { key: 'contractValue', label: 'Contract Value', type: 'number', required: false },
      { key: 'currency', label: 'Currency', type: 'select', required: false, options: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY', 'INR', 'BRL', 'Other'] },
      { key: 'paymentTerms', label: 'Payment Terms', type: 'text', required: false },
      { key: 'paymentSchedule', label: 'Payment Schedule', type: 'select', required: false, options: ['Upon Completion', 'Monthly', 'Quarterly', 'Milestone-based', 'Upfront', 'Other'] },
      { key: 'deliverables', label: 'Deliverables', type: 'textarea', required: false },
      { key: 'serviceLevel', label: 'Service Level', type: 'textarea', required: false },
      { key: 'changeControl', label: 'Change Control Process', type: 'textarea', required: false },
    ],
    parties: {
      min: 2,
      max: 10,
      types: ['individual', 'company', 'government', 'nonprofit'],
      roles: {'party1': 'service provider', 'party2': 'client'},
      companyFields: [
        { key: 'vat', label: 'VAT Number', type: 'text' },
        { key: 'registration', label: 'Company Registration Number', type: 'text' },
      ],
    },
    clauses: [
      { id: 'scope', label: 'Scope of Work', content: 'Defines the scope of services.' },
      { id: 'payment', label: 'Payment', content: 'Describes payment terms and schedule.' },
    ],
    optionalClauses: [
      { id: 'liability', label: 'Limitation of Liability', content: 'Limits liability for damages.', optional: true },
      { id: 'termination', label: 'Termination', content: 'Describes how the agreement can be terminated.', optional: true },
      { id: 'governingLaw', label: 'Governing Law', content: 'Specifies the governing law.', optional: true },
      { id: 'confidentiality', label: 'Confidentiality', content: 'Parties agree to keep all shared information confidential.', optional: true },
      { id: 'intellectualProperty', label: 'Intellectual Property', content: 'Specifies ownership of intellectual property.', optional: true },
      { id: 'warranty', label: 'Warranty', content: 'Details warranty terms for services provided.', optional: true },
      { id: 'indemnification', label: 'Indemnification', content: 'Parties agree to indemnify each other for specific claims.', optional: true },
      { id: 'insurance', label: 'Insurance', content: 'Specifies required insurance coverage.', optional: true },
      { id: 'nonSolicitation', label: 'Non-Solicitation', content: 'Prevents solicitation of employees or clients.', optional: true },
    ],
    customSections: ['Technical Clauses', 'Compliance Notes', 'Service Level Agreement'],
    industrySpecificFields: {
      'Technology': [
        { key: 'maintenanceSupport', label: 'Maintenance and Support', type: 'textarea', required: false },
        { key: 'dataHosting', label: 'Data Hosting Requirements', type: 'textarea', required: false },
      ],
      'Healthcare': [
        { key: 'hipaaCompliance', label: 'HIPAA Compliance', type: 'checkbox', required: false },
        { key: 'medicalCredentials', label: 'Medical Credentials/Licensing', type: 'textarea', required: false },
      ]
    }
  },
  {
    id: 'employment',
    label: 'Employment Contract',
    description: 'Agreement between employer and employee.',
    industry: ['All'],
    fields: [
      { key: 'title', label: 'Contract Title', type: 'text', required: true },
      { key: 'effectiveDate', label: 'Start Date', type: 'date', required: true },
      { key: 'endDate', label: 'End Date (if applicable)', type: 'date', required: false },
      { key: 'isPermanent', label: 'Is this a permanent position?', type: 'checkbox', required: true },
      { key: 'jurisdiction', label: 'Jurisdiction', type: 'select', required: true, options: ['US', 'UK', 'EU', 'CA', 'AU', 'Other'] },
      { key: 'jobTitle', label: 'Job Title', type: 'text', required: true },
      { key: 'jobDescription', label: 'Job Description', type: 'textarea', required: true },
      { key: 'salary', label: 'Salary/Compensation', type: 'number', required: true },
      { key: 'currency', label: 'Currency', type: 'select', required: true, options: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY', 'INR', 'BRL', 'Other'] },
      { key: 'payFrequency', label: 'Payment Frequency', type: 'select', required: true, options: ['Monthly', 'Bi-weekly', 'Weekly', 'Other'] },
      { key: 'workHours', label: 'Work Hours', type: 'text', required: false },
      { key: 'location', label: 'Work Location', type: 'text', required: false },
      { key: 'isRemote', label: 'Remote Work Allowed', type: 'checkbox', required: false },
      { key: 'benefits', label: 'Benefits', type: 'textarea', required: false },
      { key: 'probationPeriod', label: 'Probation Period (days)', type: 'number', required: false },
      { key: 'noticePeriod', label: 'Notice Period (days)', type: 'number', required: false },
    ],
    parties: {
      min: 2,
      max: 2,
      types: ['individual', 'company', 'government', 'nonprofit'],
      roles: {'party1': 'employer', 'party2': 'employee'},
      companyFields: [
        { key: 'vat', label: 'VAT Number', type: 'text' },
        { key: 'registration', label: 'Company Registration Number', type: 'text' },
      ],
    },
    clauses: [
      { id: 'duties', label: 'Duties and Responsibilities', content: 'The Employee shall perform the duties outlined in the job description.' },
      { id: 'compensation', label: 'Compensation', content: 'Details of salary, benefits, and payment schedule.' },
    ],
    optionalClauses: [
      { id: 'confidentiality', label: 'Confidentiality', content: 'Employee agrees to keep company information confidential.', optional: true },
      { id: 'intellectualProperty', label: 'Intellectual Property', content: 'All work created during employment belongs to the company.', optional: true },
      { id: 'nonCompete', label: 'Non-Compete', content: 'Employee agrees not to work for competitors for a specified period.', optional: true },
      { id: 'nonSolicitation', label: 'Non-Solicitation', content: 'Employee agrees not to solicit clients or employees.', optional: true },
      { id: 'termination', label: 'Termination', content: 'Conditions under which employment can be terminated.', optional: true },
      { id: 'severance', label: 'Severance', content: 'Details of severance package if applicable.', optional: true },
      { id: 'workFromHome', label: 'Work From Home Policy', content: 'Guidelines for remote work.', optional: true },
      { id: 'overtime', label: 'Overtime', content: 'Terms for overtime work and compensation.', optional: true },
    ],
    customSections: ['Special Conditions', 'Company Policies', 'Performance Metrics', 'Training Requirements']
  },
  {
    id: 'lease',
    label: 'Property Lease Agreement',
    description: 'Agreement for leasing real property.',
    industry: ['Real Estate'],
    fields: [
      { key: 'title', label: 'Contract Title', type: 'text', required: true },
      { key: 'effectiveDate', label: 'Start Date', type: 'date', required: true },
      { key: 'endDate', label: 'End Date', type: 'date', required: true },
      { key: 'jurisdiction', label: 'Jurisdiction', type: 'select', required: true, options: ['US', 'UK', 'EU', 'CA', 'AU', 'Other'] },
      { key: 'propertyAddress', label: 'Property Address', type: 'textarea', required: true },
      { key: 'propertyType', label: 'Property Type', type: 'select', required: true, options: ['Residential', 'Commercial', 'Industrial', 'Land', 'Other'] },
      { key: 'rent', label: 'Monthly Rent', type: 'number', required: true },
      { key: 'currency', label: 'Currency', type: 'select', required: true, options: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY', 'INR', 'BRL', 'Other'] },
      { key: 'securityDeposit', label: 'Security Deposit', type: 'number', required: true },
      { key: 'paymentDueDate', label: 'Payment Due Date', type: 'select', required: true, options: ['1st of month', '15th of month', 'Other'] },
      { key: 'utilities', label: 'Utilities Included', type: 'multiselect', options: ['Electricity', 'Water', 'Gas', 'Internet', 'Heating', 'None'] },
      { key: 'pets', label: 'Pets Allowed', type: 'checkbox', required: false },
      { key: 'smoking', label: 'Smoking Allowed', type: 'checkbox', required: false },
    ],
    parties: {
      min: 2,
      max: 5,
      types: ['individual', 'company', 'government', 'nonprofit'],
      roles: {'party1': 'landlord', 'party2': 'tenant'},
      companyFields: [
        { key: 'vat', label: 'VAT Number', type: 'text' },
        { key: 'registration', label: 'Company Registration Number', type: 'text' },
      ],
    },
    clauses: [
      { id: 'premises', label: 'Premises', content: 'Description of the leased property.' },
      { id: 'term', label: 'Term', content: 'Duration of the lease.' },
      { id: 'rent', label: 'Rent', content: 'Payment terms for rent.' },
    ],
    optionalClauses: [
      { id: 'maintenance', label: 'Maintenance', content: 'Responsibilities for property maintenance.', optional: true },
      { id: 'alterations', label: 'Alterations', content: 'Terms for making changes to the property.', optional: true },
      { id: 'sublease', label: 'Sublease', content: 'Terms for subleasing the property.', optional: true },
      { id: 'entryRights', label: 'Entry Rights', content: 'Landlord\'s rights to enter the property.', optional: true },
      { id: 'default', label: 'Default', content: 'Consequences of defaulting on the lease.', optional: true },
      { id: 'insurance', label: 'Insurance', content: 'Insurance requirements for the property.', optional: true },
      { id: 'pets', label: 'Pets', content: 'Rules regarding pets on the property.', optional: true },
      { id: 'smoking', label: 'Smoking', content: 'Rules regarding smoking on the property.', optional: true },
      { id: 'utilities', label: 'Utilities', content: 'Responsibilities for utility payments.', optional: true },
    ],
    customSections: ['Property Condition Report', 'House Rules', 'Renewal Terms']
  },
  {
    id: 'custom',
    label: 'Custom/Other',
    description: 'Start from a blank template or define your own.',
    industry: ['All'],
    fields: [
      { key: 'title', label: 'Contract Title', type: 'text', required: true },
      { key: 'effectiveDate', label: 'Effective Date', type: 'date', required: false },
      { key: 'description', label: 'Description/Recitals', type: 'textarea', required: false },
      { key: 'jurisdiction', label: 'Jurisdiction', type: 'select', required: false, options: ['US', 'UK', 'EU', 'CA', 'AU', 'Other'] },
    ],
    parties: {
      min: 1,
      max: 20,
      types: ['individual', 'company', 'government', 'nonprofit'],
      companyFields: [
        { key: 'vat', label: 'VAT Number', type: 'text' },
        { key: 'registration', label: 'Company Registration Number', type: 'text' },
      ],
    },
    clauses: [],
    optionalClauses: [],
    customSections: [],
  },
];
