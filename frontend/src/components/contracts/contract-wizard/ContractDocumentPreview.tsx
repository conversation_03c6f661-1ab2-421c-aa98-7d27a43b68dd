import React from 'react';
import { useContractWizard } from './ContractWizardContext';
import { WizardDocumentEngine } from '@/engines/document-engine/integrations/WizardDocumentEngine';

/**
 * ContractDocumentPreview - Modern document editing component
 * Uses the new DocumentEngine for enhanced document editing capabilities
 */
const ContractDocumentPreview: React.FC = () => {
  const { data, setData } = useContractWizard();

  return (
    <div className="contract-document-preview h-full">
      <WizardDocumentEngine
        wizardData={data}
        onWizardDataChange={setData}
        className="h-full"
        onError={(error) => {
          console.error('Document Engine Error:', error);
        }}
      />
    </div>
  );
};

export default ContractDocumentPreview;
