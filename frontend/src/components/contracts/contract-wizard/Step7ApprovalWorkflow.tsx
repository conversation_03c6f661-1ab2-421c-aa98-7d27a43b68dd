import React, { useState } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const Step7ApprovalWorkflow: React.FC = () => {
  const { data, setData, nextStep, prevStep } = useContractWizard();
  const [approverName, setApproverName] = useState('');
  const [approverRole, setApproverRole] = useState('');
  const [touched, setTouched] = useState(false);

  const isValid =
    data.approvers &&
    data.approvers.length > 0 &&
    data.approvers.every((a: any) => a.name && a.name.trim() !== '' && a.role && a.role.trim() !== '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);
    if (isValid) nextStep();
  };

  const addApprover = () => {
    if (approverName.trim() && approverRole.trim()) {
      setData(d => ({
        ...d,
        approvers: [...d.approvers, { name: approverName.trim(), role: approverRole.trim() }],
      }));
      setApproverName('');
      setApproverRole('');
    }
  };

  const removeApprover = (idx: number) => {
    setData(d => ({ ...d, approvers: d.approvers.filter((_: any, i: number) => i !== idx) }));
  };

  const setApprovalProcess = (process: 'sequential' | 'parallel') => {
    setData(d => ({ ...d, approvalProcess: process }));
  };

  return (
    <form className="space-y-6" autoComplete="off">
      <div>
        <Label className="mb-2">Add Approvers <span className="text-destructive">*</span></Label>
        <div className="space-y-2">
          {data.approvers.map((appr: any, idx: number) => (
            <div key={idx} className="flex items-center gap-2">
              <Input
                className={touched && (!appr.name || !appr.name.trim()) ? 'border-destructive' : ''}
                value={appr.name}
                onChange={e => setData(d => ({ ...d, approvers: d.approvers.map((a: any, i: number) => i === idx ? { ...a, name: e.target.value } : a) }))}
                placeholder="Name"
                required
              />
              <Input
                className={touched && (!appr.role || !appr.role.trim()) ? 'border-destructive' : ''}
                value={appr.role}
                onChange={e => setData(d => ({ ...d, approvers: d.approvers.map((a: any, i: number) => i === idx ? { ...a, role: e.target.value } : a) }))}
                placeholder="Role"
                required
              />
              <Button type="button" size="icon" variant="ghost" className="text-destructive" onClick={() => removeApprover(idx)} aria-label="Remove approver">
                &times;
              </Button>
            </div>
          ))}
          {touched && (!data.approvers.length || data.approvers.some((a: any) => !a.name || !a.name.trim() || !a.role || !a.role.trim())) && (
            <div className="text-xs text-red-600 mt-1">At least one approver with a name and role is required.</div>
          )}
          <div className="flex items-center gap-2 mt-2">
            <Input
              className="flex-1"
              value={approverName}
              onChange={e => setApproverName(e.target.value)}
              placeholder="Name"
            />
            <Input
              className="w-32"
              value={approverRole}
              onChange={e => setApproverRole(e.target.value)}
              placeholder="Role"
            />
            <Button type="button" size="sm" variant="outline" onClick={addApprover}>Add</Button>
          </div>
        </div>
      </div>
      <div>
        <Label className="mb-2">Approval Process</Label>
        <div className="flex gap-4">
          <label className="flex items-center gap-2">
            <input
              type="radio"
              checked={data.approvalProcess === 'sequential'}
              onChange={() => setApprovalProcess('sequential')}
            />
            <span>Sequential</span>
          </label>
          <label className="flex items-center gap-2">
            <input
              type="radio"
              checked={data.approvalProcess === 'parallel'}
              onChange={() => setApprovalProcess('parallel')}
            />
            <span>Parallel</span>
          </label>
        </div>
      </div>
      <div className="pt-4 flex justify-between gap-2">
        <Button type="button" variant="outline" size="sm" className="h-8 px-3" onClick={prevStep}>
          Back
        </Button>
        <Button
          type="button"
          size="sm"
          className="flex items-center h-8 px-3"
          onClick={() => {
            setTouched(true);
            nextStep(); // always allow navigation, show validation if invalid
          }}
        >
          Next
        </Button>
      </div>
    </form>
  );
};

export default Step7ApprovalWorkflow; 