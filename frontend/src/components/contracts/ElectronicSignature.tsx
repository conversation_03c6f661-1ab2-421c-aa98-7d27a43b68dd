import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircle,
  Clock,
  Edit,
  FileText,
  Loader2,
  Mail,
  Pen,
  RefreshCw,
  Upload,
  UserPlus,
  X,
} from "lucide-react";
import { useApi } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";

interface Signer {
  id: string;
  name: string;
  email: string;
  role: string;
  status: "pending" | "signed" | "declined" | "viewed";
  avatar?: string;
  initials: string;
  order: number;
}

interface ElectronicSignatureProps {
  contractId: string;
  contractTitle?: string;
}

const ElectronicSignature = ({
  contractId,
  contractTitle = "Service Agreement",
}: ElectronicSignatureProps) => {
  const { fetch, api } = useApi();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("signers");
  const [newSignerEmail, setNewSignerEmail] = useState("");
  const [newSignerName, setNewSignerName] = useState("");
  const [newSignerRole, setNewSignerRole] = useState("");
  const [signers, setSigners] = useState<Signer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [contractDetails, setContractDetails] = useState<{title: string}>({title: contractTitle});

  // Fetch signers data when component mounts
  useEffect(() => {
    const fetchSigners = async () => {
      if (!contractId) return;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch contract details to get the title
        const contractResult = await fetch(
          () => api.get(`/contracts/${contractId}`),
          "Loading contract details...",
          "Failed to load contract details"
        );

        if (contractResult) {
          setContractDetails({title: (contractResult as any)?.title || contractTitle});
        }

        // Fetch signers for this contract
        const result = await fetch(
          () => api.get(`/contracts/${contractId}/signers`),
          "Loading signers...",
          "Failed to load signers"
        );

        if (result) {
          // If we get data, use it
          setSigners(result as Signer[]);
        } else {
          // If the endpoint doesn't exist yet or returns no data, use empty array
          setSigners([]);
        }
      } catch (err) {
        console.error("Error fetching signers:", err);
        setError("Failed to load signers. Please try again later.");
        // Use empty array when there's an error
        setSigners([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSigners();
  }, [contractId, fetch, api]);

  const handleAddSigner = async () => {
    if (newSignerEmail && newSignerName) {
      // Create initials from name
      const initials = newSignerName
        .split(" ")
        .map((n) => n[0])
        .join("");

      // Prepare new signer data
      const newSignerData = {
        name: newSignerName,
        email: newSignerEmail,
        role: newSignerRole || "Signer",
        status: "pending",
        initials: initials,
        order: signers.length + 1,
      };

      try {
        // Add signer via API
        const result = await fetch(
          () => api.post(`/contracts/${contractId}/signers`, newSignerData),
          "Adding signer...",
          "Failed to add signer"
        );

        if (result) {
          // If successful, add to local state
          setSigners([...signers, result as Signer]);
          toast({
            title: "Signer added",
            description: `${newSignerName} has been added as a signer.`,
          });
        }

        // Clear form fields
        setNewSignerEmail("");
        setNewSignerName("");
        setNewSignerRole("");
      } catch (err) {
        console.error("Error adding signer:", err);
        toast({
          title: "Error",
          description: "Failed to add signer. Please try again.",
          variant: "destructive",
        });

        // Fallback to client-side only if API fails
        const fallbackSigner: Signer = {
          id: `temp-${Date.now()}`,
          name: newSignerName,
          email: newSignerEmail,
          role: newSignerRole || "Signer",
          status: "pending",
          initials: initials,
          order: signers.length + 1,
        };

        setSigners([...signers, fallbackSigner]);
        setNewSignerEmail("");
        setNewSignerName("");
        setNewSignerRole("");
      }
    }
  };

  const handleRemoveSigner = async (id: string) => {
    try {
      // Remove signer via API
      await fetch(
        () => api.delete(`/contracts/${contractId}/signers/${id}`),
        "Removing signer...",
        "Failed to remove signer"
      );

      // Update local state
      setSigners(signers.filter((signer) => signer.id !== id));
      toast({
        title: "Signer removed",
        description: "The signer has been removed.",
      });
    } catch (err) {
      console.error("Error removing signer:", err);
      toast({
        title: "Error",
        description: "Failed to remove signer. Please try again.",
        variant: "destructive",
      });

      // Fallback to client-side only if API fails
      setSigners(signers.filter((signer) => signer.id !== id));
    }
  };

  const [isSending, setIsSending] = useState(false);

  const handleSendForSignature = async () => {
    if (signers.length === 0) {
      toast({
        title: "No signers",
        description: "Please add at least one signer before sending for signature.",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);

    try {
      // Send contract for signature via API
      const result = await fetch(
        () => api.post(`/contracts/${contractId}/send-for-signature`, { signers }),
        "Sending for signature...",
        "Failed to send for signature"
      );

      if (result) {
        toast({
          title: "Contract sent",
          description: "The contract has been sent for signature.",
        });
      }
    } catch (err) {
      console.error("Error sending contract for signature:", err);
      toast({
        title: "Error",
        description: "Failed to send contract for signature. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  const getStatusBadge = (status: Signer["status"]) => {
    switch (status) {
      case "signed":
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200"
          >
            Signed
          </Badge>
        );
      case "pending":
        return (
          <Badge
            variant="outline"
            className="bg-amber-50 text-amber-700 border-amber-200"
          >
            Pending
          </Badge>
        );
      case "declined":
        return <Badge variant="destructive">Declined</Badge>;
      case "viewed":
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200"
          >
            Viewed
          </Badge>
        );
      default:
        return null;
    }
  };

  const getStatusIcon = (status: Signer["status"]) => {
    switch (status) {
      case "signed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "pending":
        return <Clock className="h-4 w-4 text-amber-500" />;
      case "declined":
        return <X className="h-4 w-4 text-red-500" />;
      case "viewed":
        return <FileText className="h-4 w-4 text-blue-500" />;
      default:
        return null;
    }
  };

  // Calculate signature progress
  const signedCount = signers.filter(
    (signer) => signer.status === "signed",
  ).length;
  const progress = (signedCount / signers.length) * 100;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Electronic Signature</CardTitle>
        <CardDescription>
          Manage signatures for "{contractDetails?.title || contractTitle}"
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="signers">Signers</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="signers" className="space-y-4 pt-4">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Loading signers...</p>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <X className="h-8 w-8 text-destructive mb-4" />
                <p className="text-muted-foreground mb-2">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.reload()}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Signature Progress</h3>
                    <p className="text-sm text-muted-foreground">
                      {signedCount} of {signers.length} signatures collected
                    </p>
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-medium">
                      {signers.length > 0 ? Math.round(progress) : 0}%
                    </span>
                  </div>
                </div>
                <Progress value={signers.length > 0 ? progress : 0} className="h-2" />

                <Separator className="my-4" />

                <div className="space-y-4">
                  <h3 className="font-medium">Signers</h3>

                  <div className="space-y-3">
                    {signers.length === 0 ? (
                      <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg text-center">
                        <UserPlus className="h-8 w-8 text-muted-foreground mb-2" />
                        <p className="text-muted-foreground mb-4">No signers added yet</p>
                        <p className="text-sm text-muted-foreground mb-4">Add signers below to send this contract for signature</p>
                      </div>
                    ) : (
                      signers.map((signer) => (
                        <div
                          key={signer.id}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center h-6 w-6 rounded-full bg-muted text-xs font-medium">
                              {signer.order}
                            </div>
                            <Avatar className="h-8 w-8">
                              {signer.avatar ? (
                                <AvatarImage src={signer.avatar} alt={signer.name} />
                              ) : (
                                <AvatarFallback>{signer.initials}</AvatarFallback>
                              )}
                            </Avatar>
                            <div>
                              <p className="font-medium">{signer.name}</p>
                              <div className="flex items-center text-xs text-muted-foreground">
                                <span>{signer.email}</span>
                                <span className="mx-1">•</span>
                                <span>{signer.role}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            {getStatusBadge(signer.status)}
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleRemoveSigner(signer.id)}
                              disabled={isLoading}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))
                    )}

                    <div className="p-3 border border-dashed rounded-lg">
                      <div className="flex flex-col md:flex-row gap-2">
                        <Input
                          placeholder="Name"
                          value={newSignerName}
                          onChange={(e) => setNewSignerName(e.target.value)}
                          className="flex-1"
                          disabled={isLoading}
                        />
                        <Input
                          placeholder="Email"
                          type="email"
                          value={newSignerEmail}
                          onChange={(e) => setNewSignerEmail(e.target.value)}
                          className="flex-1"
                          disabled={isLoading}
                        />
                        <Input
                          placeholder="Role (optional)"
                          value={newSignerRole}
                          onChange={(e) => setNewSignerRole(e.target.value)}
                          className="flex-1"
                          disabled={isLoading}
                        />
                        <Button
                          onClick={handleAddSigner}
                          disabled={isLoading || !newSignerName || !newSignerEmail}
                        >
                          {isLoading ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <UserPlus className="h-4 w-4 mr-2" />
                          )}
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="settings" className="space-y-4 pt-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Signature Type</h3>
                  <p className="text-sm text-muted-foreground">
                    Choose how signers will sign the document
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" className="gap-2">
                    <Pen className="h-4 w-4" />
                    Draw
                  </Button>
                  <Button variant="default" size="sm" className="gap-2">
                    <Edit className="h-4 w-4" />
                    Type
                  </Button>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Upload className="h-4 w-4" />
                    Upload
                  </Button>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="font-medium">Email Settings</h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email-subject">Email Subject</Label>
                    <Input
                      id="email-subject"
                      defaultValue={`Please sign: ${contractTitle}`}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email-message">Email Message</Label>
                    <Input
                      id="email-message"
                      defaultValue="Please review and sign this document at your earliest convenience."
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="font-medium">Expiration</h3>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    defaultValue="30"
                    className="w-20"
                    min="1"
                  />
                  <span>days after sending</span>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" disabled={isLoading || isSending}>
          <Mail className="mr-2 h-4 w-4" />
          Preview Email
        </Button>
        <Button
          onClick={handleSendForSignature}
          disabled={isLoading || isSending || signers.length === 0}
        >
          {isSending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending...
            </>
          ) : (
            "Send for Signature"
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ElectronicSignature;
