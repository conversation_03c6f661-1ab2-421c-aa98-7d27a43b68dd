import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Clock, Download, Eye, History, RotateCcw, Loader2 } from "lucide-react";
import { useAuth } from "@clerk/clerk-react";
import { toast } from "sonner";

interface ContractVersionHistoryProps {
  contractId: string;
}

interface ContractVersion {
  id: string;
  version_number: number;
  title: string;
  content: any;
  changes_summary?: string;
  change_details?: any;
  created_by: {
    id: string;
    name: string;
  };
  created_at: string;
  is_current: boolean;
  workspace_id: string;
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const ContractVersionHistory = ({
  contractId,
}: ContractVersionHistoryProps) => {
  const { getToken } = useAuth();
  const [versions, setVersions] = useState<ContractVersion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch contract versions
  useEffect(() => {
    const fetchVersions = async () => {
      try {
        setLoading(true);
        const token = await getToken();

        const response = await fetch(`/api/lifecycle/contracts/${contractId}/versions`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch contract versions');
        }

        const data = await response.json();
        setVersions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast.error('Failed to load contract versions');
      } finally {
        setLoading(false);
      }
    };

    if (contractId) {
      fetchVersions();
    }
  }, [contractId, getToken]);

  // Handle view version
  const handleViewVersion = (versionId: string) => {
    console.log("Viewing version:", versionId);
    // TODO: Implement version viewing modal
    toast.info("Version viewing feature coming soon");
  };

  // Handle download version
  const handleDownloadVersion = async (versionId: string) => {
    try {
      const token = await getToken();
      // TODO: Implement version download
      toast.info("Version download feature coming soon");
    } catch (err) {
      toast.error("Failed to download version");
    }
  };

  // Handle restore version
  const handleRestoreVersion = async (versionId: string) => {
    try {
      const token = await getToken();

      const response = await fetch(`/api/lifecycle/contracts/${contractId}/versions/${versionId}/rollback`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to rollback to version');
      }

      const newVersion = await response.json();

      // Refresh the versions list
      const versionsResponse = await fetch(`/api/lifecycle/contracts/${contractId}/versions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (versionsResponse.ok) {
        const updatedVersions = await versionsResponse.json();
        setVersions(updatedVersions);
      }

      toast.success("Successfully rolled back to selected version");
    } catch (err) {
      console.error("Error rolling back version:", err);
      toast.error("Failed to rollback to version");
    }
  };

  // Handle compare versions
  const handleCompareVersions = () => {
    if (versions.length < 2) {
      toast.error("Need at least 2 versions to compare");
      return;
    }

    // For now, compare the current version with the previous one
    const currentVersion = versions.find(v => v.is_current);
    const previousVersion = versions.find(v => !v.is_current);

    if (currentVersion && previousVersion) {
      handleCompareSpecificVersions(currentVersion.id, previousVersion.id);
    } else {
      toast.error("Unable to find versions to compare");
    }
  };

  // Handle compare specific versions
  const handleCompareSpecificVersions = async (version1Id: string, version2Id: string) => {
    try {
      const token = await getToken();

      const response = await fetch(`/api/lifecycle/versions/${version1Id}/compare/${version2Id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to compare versions');
      }

      const comparison = await response.json();

      // For now, show a simple alert with the comparison summary
      // In a real implementation, you'd open a modal or navigate to a comparison view
      const diffCount = comparison.differences?.length || 0;
      toast.success(`Comparison complete: ${diffCount} differences found`);

      // Log the comparison for debugging
      console.log("Version comparison:", comparison);

    } catch (err) {
      console.error("Error comparing versions:", err);
      toast.error("Failed to compare versions");
    }
  };
  // Helper function to get user initials
  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Helper function to parse changes from summary
  const parseChanges = (summary?: string, changeDetails?: any) => {
    if (changeDetails && Array.isArray(changeDetails)) {
      return changeDetails;
    }
    if (summary) {
      return [summary];
    }
    return ["No changes recorded"];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading version history...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <p className="text-red-500">Error loading version history: {error}</p>
        <Button
          variant="outline"
          onClick={() => window.location.reload()}
          className="mt-4"
        >
          Retry
        </Button>
      </div>
    );
  }

  if (versions.length === 0) {
    return (
      <div className="p-8 text-center">
        <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <p className="text-muted-foreground">No version history available</p>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Version History</CardTitle>
              <CardDescription>
                Track all changes made to this contract
              </CardDescription>
            </div>
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={handleCompareVersions}
              disabled={versions.length < 2}
            >
              <RotateCcw className="h-4 w-4" />
              Compare Versions
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="relative pl-6 border-l-2 border-muted space-y-6">
            {versions.map((version, index) => (
              <div key={version.id} className="relative">
                {/* Timeline dot */}
                <div className="absolute -left-[25px] top-0 h-6 w-6 rounded-full bg-background border-2 border-primary flex items-center justify-center">
                  <History className="h-3 w-3 text-primary" />
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">
                          Version {version.version_number}
                        </h3>
                        {version.is_current && (
                          <Badge variant="default">Current</Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{formatDate(version.created_at)}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleViewVersion(version.id)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDownloadVersion(version.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <Separator className="my-3" />

                  <div className="flex items-start gap-3">
                    <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium">
                      {getUserInitials(version.created_by.name)}
                    </div>
                    <div>
                      <div className="font-medium text-sm">
                        {version.created_by.name}
                      </div>
                      <ul className="mt-2 space-y-1">
                        {parseChanges(version.changes_summary, version.change_details).map((change, i) => (
                          <li key={i} className="text-sm">
                            • {change}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {version.is_current ? (
                    <div className="mt-3 flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const previousVersion = versions.find(v => v.version_number === version.version_number - 1);
                          if (previousVersion) {
                            handleRestoreVersion(previousVersion.id);
                          }
                        }}
                        disabled={version.version_number <= 1}
                      >
                        Revert to Previous Version
                      </Button>
                    </div>
                  ) : (
                    <div className="mt-3 flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRestoreVersion(version.id)}
                      >
                        Restore This Version
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContractVersionHistory;
