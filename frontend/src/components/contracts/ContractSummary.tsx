import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Download, FileText, Share2 } from "lucide-react";

interface ContractSummaryProps {
  contractId: string;
  onViewDetails: () => void;
}

const ContractSummary = ({
  contractId,
  onViewDetails,
}: ContractSummaryProps) => {
  // Mock data for a contract
  const contract = {
    id: contractId,
    title: "Service Agreement with Acme Corp",
    type: "Service Agreement",
    status: "active",
    createdBy: {
      name: "<PERSON>",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=jane",
      initials: "JS",
    },
    createdDate: "2023-06-15",
    expiryDate: "2024-06-15",
    counterparty: "Acme Corporation",
    value: "$50,000",
    description:
      "This service agreement outlines the terms and conditions for providing IT consulting services to Acme Corporation for a period of 12 months.",
    riskScore: 82,
    complianceStatus: "Compliant",
    lastModified: "2023-06-20",
    lastModifiedBy: "John Doe",
    versions: 3,
    comments: 8,
    attachments: [
      {
        name: "Acme_Corp_Service_Agreement.pdf",
        size: "2.4 MB",
        date: "2023-06-15",
      },
      { name: "Statement_of_Work.docx", size: "1.1 MB", date: "2023-06-15" },
      {
        name: "Service_Level_Agreement.pdf",
        size: "0.8 MB",
        date: "2023-06-15",
      },
    ],
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline">Draft</Badge>;
      case "review":
        return <Badge variant="secondary">In Review</Badge>;
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "expired":
        return <Badge variant="destructive">Expired</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">{contract.title}</CardTitle>
            <CardDescription className="mt-1">
              {contract.type} • Created on {formatDate(contract.createdDate)}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(contract.status)}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div>
          <h3 className="text-sm font-medium text-muted-foreground mb-1">
            Description
          </h3>
          <p>{contract.description}</p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">
              Counterparty
            </h3>
            <p>{contract.counterparty}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">
              Value
            </h3>
            <p>{contract.value}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">
              Created By
            </h3>
            <div className="flex items-center">
              <div className="h-5 w-5 rounded-full bg-muted flex items-center justify-center text-xs mr-2">
                {contract.createdBy.initials}
              </div>
              <span>{contract.createdBy.name}</span>
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">
              Expiry Date
            </h3>
            <p>
              {contract.expiryDate ? formatDate(contract.expiryDate) : "N/A"}
            </p>
          </div>
        </div>

        <Separator />

        <div>
          <h3 className="text-sm font-medium text-muted-foreground mb-2">
            Attachments
          </h3>
          <div className="space-y-2">
            {contract.attachments.map((attachment, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-2 border rounded-md"
              >
                <div className="flex items-center">
                  <FileText className="h-4 w-4 text-blue-500 mr-2" />
                  <span className="text-sm">{attachment.name}</span>
                </div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <span className="mr-2">{attachment.size}</span>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between border-t pt-4">
        <div className="flex items-center text-sm text-muted-foreground">
          <span>
            Last modified: {contract.lastModified} by {contract.lastModifiedBy}
          </span>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button size="sm" onClick={onViewDetails}>
            View Details
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default ContractSummary;
