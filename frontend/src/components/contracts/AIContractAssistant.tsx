/**
 * Unified AI Contract Assistant
 * Combines chat-based contract generation with advanced AI analysis
 * Supports both standalone usage and integration with contract wizard
 */

import React, { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Bot,
  Send,
  User,
  FileText,
  Sparkles,
  Loader2,
  Copy,
  Check,
  Download,
  RefreshCw,
  Brain,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Shield,
  Zap,
  Plus
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { AIAnalysisService } from "@/services/api-services";

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

interface AISuggestion {
  id: string;
  type: 'clause' | 'term' | 'risk' | 'compliance' | 'optimization';
  title: string;
  description: string;
  content?: string;
  confidence: number;
  priority: 'critical' | 'high' | 'medium' | 'low';
  category: string;
  reasoning: string;
  applicable: boolean;
}

interface AIAnalysis {
  riskScore: number;
  complianceScore: number;
  suggestions: AISuggestion[];
  missingClauses: string[];
  potentialIssues: string[];
  recommendations: string[];
}

interface ContractWizardData {
  id?: string;
  contractType?: string;
  jurisdiction?: string;
  industry?: string;
  paymentTerms?: string;
  standardClauses?: string[];
  effectiveDate?: string;
  parties?: Array<{ name: string; address?: string }>;
  description?: string;
}

interface AIContractAssistantProps {
  onContractGenerated?: (content: string) => void;
  contractData?: ContractWizardData;
  mode?: 'chat' | 'analysis' | 'hybrid';
}

const AIContractAssistant: React.FC<AIContractAssistantProps> = ({
  onContractGenerated,
  contractData,
  mode = 'hybrid'
}) => {
  const { toast } = useToast();

  // Chat-related state
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState<string>(mode === 'analysis' ? 'analysis' : 'chat');
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      role: "assistant",
      content: "Hello! I'm your AI contract assistant. I can help you draft contracts and provide intelligent analysis. Describe what you need or try one of the sample prompts below.",
      timestamp: new Date().toISOString()
    }
  ]);
  const [generatedContract, setGeneratedContract] = useState<string>("");
  const [copiedToClipboard, setCopiedToClipboard] = useState(false);

  // Analysis-related state
  const [analysis, setAnalysis] = useState<AIAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');

  // Sample prompts that users can click on
  const samplePrompts = [
    "Create a consulting agreement for a software developer working with a startup for 6 months",
    "Draft a non-disclosure agreement for sharing confidential information with a potential investor",
    "Write an employment contract for a remote marketing specialist with performance-based bonuses",
    "Create a service agreement for a web design project with milestone payments"
  ];

  // Real AI analysis function using the AI service
  // Types for raw API response
// These are used only for parsing the external API, not for internal state
interface RawAISuggestion {
  type?: string;
  title?: string;
  description?: string;
  content?: string;
  priority?: string;
  reasoning?: string;
  recommendation?: string;
}
interface RawAIRisk {
  type?: string;
  description?: string;
}

const performAIAnalysis = useCallback(async () => {
    if (!contractData?.id) {
      console.warn('No contract ID available for AI analysis');
      return;
    }

    setIsAnalyzing(true);

    try {
      // Call the real AI analysis API
      const response = await AIAnalysisService.runAnalysis(contractData.id, 'contract_analysis');

      if (response.data) {
        // Convert API response to component format
        const aiResult = response.data;
        const convertedAnalysis: AIAnalysis = {
          riskScore: Math.round(aiResult.risk_score * 100), // Convert 0-1 to 0-100
          complianceScore: Math.round(aiResult.compliance_score * 100),
          suggestions: (aiResult.suggestions as RawAISuggestion[]).map((suggestion, index): AISuggestion => ({
  id: `suggestion-${index}`,
  type: ((suggestion.type === 'system' ? 'optimization' : suggestion.type) ?? 'optimization') as AISuggestion['type'],
  title: suggestion.title || 'AI Suggestion',
  description: suggestion.description || '',
  content: suggestion.content,
  confidence: aiResult.confidence ?? 0.8,
  priority: (suggestion.priority as AISuggestion['priority']) || 'medium',
  category: suggestion.type || 'general',
  reasoning: suggestion.reasoning || '',
  applicable: true
})),

          missingClauses: (aiResult.key_risks as RawAIRisk[])
  .filter((risk) => risk.type === 'missing_clause')
  .map((risk) => risk.description ?? ''),
          potentialIssues: (aiResult.key_risks as RawAIRisk[])
  .filter((risk) => risk.type !== 'missing_clause')
  .map((risk) => risk.description ?? ''),
          recommendations: (aiResult.suggestions as RawAISuggestion[]).map((suggestion) => suggestion.recommendation || suggestion.description || '')
        };

        setAnalysis(convertedAnalysis);
      } else {
        throw new Error(response.message || 'AI analysis failed');
      }
    } catch (error) {
      console.error('AI analysis failed:', error);
      // Fallback to mock analysis if API fails
      const fallbackAnalysis: AIAnalysis = {
        riskScore: 50,
        complianceScore: 75,
        suggestions: [{
          id: 'fallback-1',
          type: 'optimization',
          title: 'AI Analysis Unavailable',
          description: 'AI analysis service is temporarily unavailable. Please try again later.',
          confidence: 0.1,
          priority: 'low',
          category: 'system',
          reasoning: 'Service unavailable',
          applicable: false
        }],
        missingClauses: ['AI analysis temporarily unavailable'],
        potentialIssues: ['Unable to perform automated risk assessment'],
        recommendations: ['Manual review recommended']
      };

      setAnalysis(fallbackAnalysis);
    } finally {
      setIsAnalyzing(false);
    }
  }, [contractData]);

  // Helper functions for analysis
  const applySuggestion = (suggestion: AISuggestion) => {
    console.log('Applying suggestion:', suggestion);
    // This would integrate with the contract wizard context to update the data
    toast({
      title: "Suggestion Applied",
      description: `Applied: ${suggestion.title}`,
    });
  };

  const dismissSuggestion = (suggestionId: string) => {
    if (analysis) {
      setAnalysis({
        ...analysis,
        suggestions: analysis.suggestions.filter(s => s.id !== suggestionId)
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Auto-analyze when contract data changes significantly
  useEffect(() => {
    if (contractData?.contractType && contractData?.jurisdiction && mode !== 'chat') {
      const timer = setTimeout(() => {
        performAIAnalysis();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [contractData?.contractType, contractData?.jurisdiction, contractData?.industry, performAIAnalysis, mode]);

  const handleSendPrompt = () => {
    if (!prompt.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      role: "user",
      content: prompt,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsGenerating(true);

    // Clear the input
    setPrompt("");

    // Simulate AI response after a delay
    setTimeout(() => {
      // Generate a response based on the prompt
      const response = generateAIResponse(userMessage.content);

      // Add AI message
      const aiMessage: Message = {
        id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        role: "assistant",
        content: response.message,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsGenerating(false);

      // If a contract was generated, update the state
      if (response.contract) {
        setGeneratedContract(response.contract);
        setActiveTab("preview");

        if (onContractGenerated) {
          onContractGenerated(response.contract);
        }
      }
    }, 2000);
  };

  const handleSamplePromptClick = (promptText: string) => {
    setPrompt(promptText);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendPrompt();
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generatedContract);
    setCopiedToClipboard(true);

    toast({
      title: "Copied to clipboard",
      description: "Contract text has been copied to your clipboard."
    });

    setTimeout(() => setCopiedToClipboard(false), 2000);
  };

  const handleDownload = () => {
    const blob = new Blob([generatedContract], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'generated-contract.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Contract downloaded",
      description: "Your contract has been downloaded as a text file."
    });
  };

  const handleRegenerateContract = () => {
    setIsGenerating(true);

    // Simulate regeneration
    setTimeout(() => {
      const newContract = generateAIResponse(messages[messages.length - 2]?.content || "").contract;
      if (newContract) {
        setGeneratedContract(newContract);

        // Add AI message about regeneration
        const aiMessage: Message = {
          id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          role: "assistant",
          content: "I've regenerated the contract with some variations. You can view it in the preview tab.",
          timestamp: new Date().toISOString()
        };

        setMessages(prev => [...prev, aiMessage]);

        if (onContractGenerated) {
          onContractGenerated(newContract);
        }
      }

      setIsGenerating(false);
    }, 2000);
  };

  // Mock function to generate AI responses
  const generateAIResponse = (userPrompt: string): { message: string, contract?: string } => {
    // Check if the prompt is asking for a contract
    const isContractRequest =
      userPrompt.toLowerCase().includes("contract") ||
      userPrompt.toLowerCase().includes("agreement") ||
      userPrompt.toLowerCase().includes("draft") ||
      userPrompt.toLowerCase().includes("create");

    if (isContractRequest) {
      // Generate a simple contract based on the prompt
      const contractTitle = userPrompt.split(" ").slice(0, 5).join(" ") + "...";

      const contract = `
# ${contractTitle.toUpperCase()}

## PARTIES

This Agreement is made and entered into as of the Effective Date by and between:

**Party A**: [FIRST PARTY NAME], with its principal place of business at [ADDRESS]
**Party B**: [SECOND PARTY NAME], with its principal place of business at [ADDRESS]

## TERMS AND CONDITIONS

1. **SERVICES**
   Party A agrees to provide the following services to Party B: [DESCRIPTION OF SERVICES]

2. **TERM**
   This Agreement shall commence on the Effective Date and continue for a period of [TERM PERIOD], unless terminated earlier in accordance with this Agreement.

3. **COMPENSATION**
   Party B agrees to pay Party A [COMPENSATION AMOUNT] for the services provided under this Agreement.

4. **CONFIDENTIALITY**
   Both parties agree to maintain the confidentiality of any proprietary information disclosed during the course of this Agreement.

5. **TERMINATION**
   Either party may terminate this Agreement with [NOTICE PERIOD] written notice to the other party.

6. **GOVERNING LAW**
   This Agreement shall be governed by and construed in accordance with the laws of [JURISDICTION].

7. **ENTIRE AGREEMENT**
   This Agreement constitutes the entire understanding between the parties and supersedes all prior agreements, representations, and understandings.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the Effective Date.

Party A: ________________________
Date: __________________________

Party B: ________________________
Date: __________________________
      `;

      return {
        message: "I've drafted a contract based on your request. You can view and edit it in the preview tab.",
        contract: contract.trim()
      };
    }

    // Default response for non-contract requests
    return {
      message: "I can help you draft contracts and agreements. Could you provide more details about the type of contract you need?"
    };
  };

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className={`grid mb-4 ${mode === 'hybrid' ? 'grid-cols-3' : mode === 'analysis' ? 'grid-cols-2' : 'grid-cols-2'}`}>
          {mode !== 'analysis' && <TabsTrigger value="chat">Chat</TabsTrigger>}
          {mode !== 'chat' && <TabsTrigger value="analysis">AI Analysis</TabsTrigger>}
          {mode !== 'analysis' && <TabsTrigger value="preview">Contract Preview</TabsTrigger>}
        </TabsList>

        <TabsContent value="chat" className="space-y-4">
          {/* Chat messages */}
          <div className="border rounded-md h-[300px] overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`flex gap-3 max-w-[80%] ${
                    message.role === 'user'
                      ? 'flex-row-reverse'
                      : 'flex-row'
                  }`}
                >
                  <Avatar className="h-8 w-8">
                    {message.role === 'assistant' ? (
                      <>
                        <AvatarFallback className="bg-primary text-primary-foreground">AI</AvatarFallback>
                        <Bot className="h-4 w-4" />
                      </>
                    ) : (
                      <>
                        <AvatarFallback>You</AvatarFallback>
                        <User className="h-4 w-4" />
                      </>
                    )}
                  </Avatar>
                  <div
                    className={`rounded-lg p-3 text-sm ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    {message.content}
                  </div>
                </div>
              </div>
            ))}

            {isGenerating && (
              <div className="flex justify-start">
                <div className="flex gap-3 max-w-[80%]">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-primary text-primary-foreground">AI</AvatarFallback>
                    <Bot className="h-4 w-4" />
                  </Avatar>
                  <div className="rounded-lg p-3 text-sm bg-muted flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Generating response...
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sample prompts */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Sample Prompts:</Label>
            <div className="flex flex-wrap gap-2">
              {samplePrompts.map((samplePrompt, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="cursor-pointer hover:bg-muted"
                  onClick={() => handleSamplePromptClick(samplePrompt)}
                >
                  {samplePrompt.length > 40 ? samplePrompt.substring(0, 40) + '...' : samplePrompt}
                </Badge>
              ))}
            </div>
          </div>

          {/* Input area */}
          <div className="flex gap-2">
            <Textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Describe the contract you need..."
              className="flex-1"
              disabled={isGenerating}
            />
            <Button
              onClick={handleSendPrompt}
              disabled={!prompt.trim() || isGenerating}
              className="self-end"
            >
              {isGenerating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </TabsContent>

        {/* AI Analysis Tab */}
        {mode !== 'chat' && (
          <TabsContent value="analysis" className="space-y-4">
            {/* AI Analysis Header */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-blue-600" />
                    <CardTitle className="text-lg">AI Contract Analysis</CardTitle>
                  </div>
                  <Button
                    onClick={performAIAnalysis}
                    disabled={isAnalyzing || !contractData?.id}
                    size="sm"
                  >
                    {isAnalyzing ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Analyzing...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        Analyze
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>

              {analysis && (
                <CardContent className="space-y-4">
                  {/* Analysis Scores */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 border rounded-lg">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <Shield className="h-5 w-5 text-blue-600" />
                        <span className="font-medium">Risk Score</span>
                      </div>
                      <div className={`text-2xl font-bold ${getScoreColor(100 - analysis.riskScore)}`}>
                        {100 - analysis.riskScore}%
                      </div>
                    </div>
                    <div className="text-center p-3 border rounded-lg">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="font-medium">Compliance</span>
                      </div>
                      <div className={`text-2xl font-bold ${getScoreColor(analysis.complianceScore)}`}>
                        {analysis.complianceScore}%
                      </div>
                    </div>
                  </div>

                  {/* Quick Stats */}
                  <div className="flex gap-4 text-sm text-muted-foreground">
                    <span>{analysis.suggestions.length} suggestions</span>
                    <span>{analysis.missingClauses.length} missing clauses</span>
                    <span>{analysis.potentialIssues.length} potential issues</span>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* AI Suggestions */}
            {analysis && analysis.suggestions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Lightbulb className="h-4 w-4 text-yellow-600" />
                    AI Suggestions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {analysis.suggestions.slice(0, 3).map((suggestion) => (
                    <Alert key={suggestion.id} className="border-l-4">
                      <div className="flex items-start gap-3">
                        <TrendingUp className="h-4 w-4 text-blue-600 mt-1" />
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{suggestion.title}</h4>
                            <div className="flex items-center gap-2">
                              <Badge className={getPriorityColor(suggestion.priority)}>
                                {suggestion.priority}
                              </Badge>
                              <Badge variant="outline">{Math.round(suggestion.confidence * 100)}%</Badge>
                            </div>
                          </div>
                          <AlertDescription>{suggestion.description}</AlertDescription>
                          {suggestion.content && (
                            <div className="bg-muted/50 p-2 rounded text-sm">
                              {suggestion.content}
                            </div>
                          )}
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              onClick={() => applySuggestion(suggestion)}
                            >
                              <Plus className="h-3 w-3 mr-1" />
                              Apply
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => dismissSuggestion(suggestion.id)}
                            >
                              Dismiss
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Alert>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Missing Clauses */}
            {analysis && analysis.missingClauses.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                    Missing Clauses
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {analysis.missingClauses.map((clause, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm">{clause}</span>
                        <Button size="sm" variant="outline">
                          <Plus className="h-3 w-3 mr-1" />
                          Add
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Custom AI Query */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Brain className="h-4 w-4 text-purple-600" />
                  Ask AI Assistant
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Textarea
                  placeholder="Ask a specific question about your contract..."
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  rows={3}
                />
                <Button size="sm" disabled={!customPrompt.trim()}>
                  <Brain className="h-3 w-3 mr-1" />
                  Ask AI
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        <TabsContent value="preview">
          {generatedContract ? (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">Generated Contract</h3>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyToClipboard}
                    disabled={isGenerating}
                  >
                    {copiedToClipboard ? (
                      <>
                        <Check className="h-4 w-4 mr-1" />
                        Copied
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-1" />
                        Copy
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownload}
                    disabled={isGenerating}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRegenerateContract}
                    disabled={isGenerating}
                  >
                    {isGenerating ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-1" />
                    ) : (
                      <RefreshCw className="h-4 w-4 mr-1" />
                    )}
                    Regenerate
                  </Button>
                </div>
              </div>

              <Card className="p-4 h-[400px] overflow-y-auto">
                <pre className="whitespace-pre-wrap font-sans text-sm">
                  {generatedContract}
                </pre>
              </Card>

              <div className="flex justify-end">
                <Button
                  onClick={() => {
                    if (onContractGenerated) {
                      onContractGenerated(generatedContract);
                    }
                  }}
                  disabled={isGenerating}
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  Use This Contract
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-[400px] text-center">
              <Sparkles className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Contract Generated Yet</h3>
              <p className="text-muted-foreground max-w-md mb-4">
                Describe the contract you need in the chat tab, and I&apos;ll generate a draft for you to review.
              </p>
              <Button variant="outline" onClick={() => setActiveTab("chat")}>
                Go to Chat
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIContractAssistant;
