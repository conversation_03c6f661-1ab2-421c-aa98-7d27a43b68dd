import React, { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardFooter
} from "@/components/ui/card";
import EnhancedTable, { type ColumnDef } from "@/components/ui/enhanced-table";
import EnhancedGrid, { type GridItemProps } from "@/components/ui/enhanced-grid";
import { useTableState } from "@/hooks/useTableState";
import { useGridState } from "@/hooks/useGridState";
import {
  Eye,
  FileText,
  MoreHorizontal,
  Download,
  Copy,
  Trash2,
  FileEdit,
  ExternalLink,
  Mail,
  Loader2,
  Brain
} from "lucide-react";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "@/components/ui/use-toast";
import { useApi } from "@/lib/api";
import { ContractService } from "@/services/api-services";
import type { Contract as ApiContract } from "@/services/api-types";
import UnifiedExportManager from "../exports/UnifiedExportManager";
import { cn } from "@/lib/utils";

// Unified interface for contracts with UI-specific properties
interface Contract {
  id: string;
  title: string;
  type: string;
  status: "draft" | "review" | "active" | "expired" | "rejected" | "pending_approval" | "terminated";
  createdBy: {
    name: string;
    avatar?: string;
    initials: string;
    id?: string;
  };
  createdDate: string;
  expiryDate?: string;
  counterparty: string;
  value?: string;
  workspaceId: string;
}

interface UnifiedContractViewProps {
  // Data props
  contracts?: ApiContract[];
  status?: "all" | "draft" | "review" | "active" | "expired";
  searchQuery?: string;
  
  // Display props
  viewMode: "list" | "grid";
  
  // Event handlers
  onContractSelect: (contractId: string) => void;
  onPreviewContract?: (contractId: string) => void;
  onSelectContract?: (contractId: string, isChecked: boolean) => void;
  onSelectAll?: (isChecked: boolean) => void;
  
  // Optional customization
  renderStatusBadge?: (status: string) => React.ReactNode;
  showSelection?: boolean;
  emptyMessage?: string;
}

const UnifiedContractView: React.FC<UnifiedContractViewProps> = ({
  contracts: externalContracts,
  status = "all",
  searchQuery = "",
  viewMode,
  onContractSelect,
  onPreviewContract,
  onSelectContract,
  onSelectAll,
  renderStatusBadge,
  showSelection = false,
  emptyMessage = "No contracts found"
}) => {
  // Workspace context
  const { currentWorkspace, canAccessWorkspace } = useClerkWorkspace();
  const { fetch } = useApi();

  // State management
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [contractToDelete, setContractToDelete] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Initialize state hooks for both view modes
  const tableState = useTableState({
    data: contracts,
    getRowId: (contract) => contract.id,
  });

  const gridState = useGridState({
    data: contracts,
    getItemId: (contract) => contract.id,
  });

  // Data fetching effect
  useEffect(() => {
    const fetchContracts = async () => {
      // If external contracts are provided, use them
      if (externalContracts) {
        const mappedContracts = mapApiContractsToLocal(externalContracts);
        setContracts(mappedContracts);
        setLoading(false);
        return;
      }

      // Otherwise fetch from API
      if (!currentWorkspace) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const workspaceId = currentWorkspace?.id;
        if (!workspaceId) {
          setLoading(false);
          return;
        }

        const params: any = { workspace_id: workspaceId };
        if (status !== "all") params.status = status;
        if (searchQuery) params.search = searchQuery;

        const result = await fetch(
          () => ContractService.getContracts(params),
          "Loading contracts...",
          "Failed to load contracts"
        );

        if (result) {
          const mappedContracts = mapApiContractsToLocal(result);
          setContracts(mappedContracts);
        }
      } catch (err) {
        console.error("Error fetching contracts:", err);
        setError("Failed to load contracts. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchContracts();
  }, [externalContracts, currentWorkspace?.id, status, searchQuery]); // Removed fetch to prevent infinite loops

  // Helper function to map API contracts to local interface
  const mapApiContractsToLocal = (apiContracts: ApiContract[]): Contract[] => {
    return apiContracts.map((contract) => ({
      id: contract.id,
      title: contract.title,
      type: contract.type || 'General',
      status: contract.status as Contract['status'],
      createdBy: {
        name: contract.created_by?.name || 'Unknown',
        initials: contract.created_by?.name?.split(' ').map(n => n[0]).join('') || 'U',
        id: contract.created_by?.id
      },
      createdDate: contract.created_at,
      expiryDate: contract.expiry_date,
      counterparty: contract.counterparty || 'Unknown',
      value: contract.value,
      workspaceId: contract.workspace_id || currentWorkspace?.id || ''
    }));
  };

  // Utility functions
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusBadge = (status: string) => {
    if (renderStatusBadge) {
      return renderStatusBadge(status);
    }

    const statusConfig = {
      draft: { label: "Draft", variant: "secondary" as const },
      review: { label: "Review", variant: "outline" as const },
      active: { label: "Active", variant: "default" as const },
      expired: { label: "Expired", variant: "destructive" as const },
      rejected: { label: "Rejected", variant: "destructive" as const },
      pending_approval: { label: "Pending", variant: "outline" as const },
      terminated: { label: "Terminated", variant: "secondary" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  // Action handlers
  const handleEditContract = (contractId: string) => {
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    toast({
      title: "Editing Contract",
      description: `Now editing: ${contract.title}`,
    });

    onContractSelect(contractId);
  };

  const handleDuplicateContract = (contractId: string) => {
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    toast({
      title: "Contract Duplicated",
      description: `Created a copy of: ${contract.title}`,
    });
  };

  const handleAIAnalysis = (contractId: string) => {
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    // Navigate to AI analysis page
    window.location.href = `/app/contracts/analysis/${contractId}`;
  };

  const handleDeleteContract = async (contractId: string) => {
    setDeleteLoading(true);
    try {
      await fetch(
        () => ContractService.deleteContract(contractId),
        "Deleting contract...",
        "Failed to delete contract"
      );

      setContracts(prev => prev.filter(c => c.id !== contractId));
      toast({
        title: "Contract Deleted",
        description: "The contract has been successfully deleted.",
      });
    } catch (error) {
      console.error("Error deleting contract:", error);
    } finally {
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
      setContractToDelete(null);
    }
  };

  // Selection handlers
  const handleItemSelect = (contractId: string, selected: boolean) => {
    if (viewMode === "grid") {
      gridState.handleItemSelect(contractId, selected);
    } else {
      tableState.handleRowSelect(contractId, selected);
    }
    
    if (onSelectContract) {
      onSelectContract(contractId, selected);
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (viewMode === "grid") {
      gridState.handleSelectAll(selected);
    } else {
      tableState.handleSelectAll(selected);
    }
    
    if (onSelectAll) {
      onSelectAll(selected);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  // Empty state
  if (contracts.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText className="h-12 w-12 text-muted-foreground mb-4 mx-auto" />
        <h3 className="text-lg font-medium">{emptyMessage}</h3>
        <p className="text-muted-foreground mt-2">
          {searchQuery
            ? "Try adjusting your search query"
            : "Create your first contract to get started"}
        </p>
      </div>
    );
  }

  // Render based on view mode
  if (viewMode === "grid") {
    return (
      <>
        <div className="w-full">
          <EnhancedGrid
            data={gridState.filteredData}
            renderItem={renderContractCard}
            selectedItems={showSelection ? gridState.selectedItems : []}
            onItemSelect={showSelection ? handleItemSelect : undefined}
            onSelectAll={showSelection ? handleSelectAll : undefined}
            onItemClick={(contract: Contract) => onContractSelect(contract.id)}
            columns={{ default: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
            gap="gap-3 sm:gap-4"
            keyboardNavigation={true}
            selectionMode={showSelection ? "multiple" : "none"}
            showSelectAll={showSelection}
            emptyMessage={emptyMessage}
            className="w-full"
          />
        </div>
        {renderDeleteDialog()}
      </>
    );
  }

  return (
    <>
      <div className="rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <EnhancedTable
            data={tableState.sortedData}
            columns={getTableColumns()}
            sortConfig={tableState.sortConfig}
            onSort={tableState.handleSort}
            selectedRows={showSelection ? tableState.selectedRows : []}
            onRowSelect={showSelection ? tableState.handleRowSelect : undefined}
            onSelectAll={showSelection ? tableState.handleSelectAll : undefined}
            onRowClick={(contract: Contract) => onContractSelect(contract.id)}
            hoverable={true}
            stickyHeader={false}
            keyboardNavigation={true}
            className="w-full min-w-[600px] responsive-table"
          />
        </div>
      </div>
      {renderDeleteDialog()}
    </>
  );

  // Grid card renderer
  function renderContractCard({ item: contract, isSelected, onSelect }: GridItemProps<Contract>) {
    return (
      <Card
        className={cn(
          "overflow-hidden hover:shadow-md transition-all duration-200 cursor-pointer touch-manipulation",
          "min-h-[120px] sm:min-h-[140px]", // Ensure adequate touch target size
          isSelected && "ring-2 ring-primary",
          "table-row-actions"
        )}
        onClick={() => onContractSelect(contract.id)}
      >
        <CardContent className="p-4 sm:p-5">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-start gap-3 min-w-0 flex-1">
              {showSelection && onSelect && (
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={(checked) => onSelect(!!checked)}
                  onClick={(e) => e.stopPropagation()}
                  className="mt-1 min-h-[20px] min-w-[20px] touch-manipulation"
                />
              )}
              <div className="min-w-0 flex-1">
                <h3 className="font-medium text-base sm:text-lg leading-tight mb-1">
                  {contract.title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {contract.type}
                </p>
              </div>
            </div>
            <div className="flex-shrink-0 ml-2">
              {getStatusBadge(contract.status)}
            </div>
          </div>

          <div className="space-y-1 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Counterparty:</span>
              <span className="truncate ml-2 max-w-[120px]">{contract.counterparty}</span>
            </div>
            <div className="flex justify-between">
              <span>Created:</span>
              <span>{formatDate(contract.createdDate)}</span>
            </div>
            {contract.expiryDate && (
              <div className="flex justify-between">
                <span>Expires:</span>
                <span>{formatDate(contract.expiryDate)}</span>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="p-3 sm:p-4 pt-0 flex items-center justify-between border-t mt-2 flex-wrap gap-2">
          <div className="flex items-center">
            <span className="text-xs text-muted-foreground truncate max-w-[120px] sm:max-w-none">
              By: {contract.createdBy.name}
            </span>
          </div>
          <div className="flex items-center table-row-actions">
            {onPreviewContract && (
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 sm:h-8 sm:w-8 hover:bg-muted"
                onClick={(e) => {
                  e.stopPropagation();
                  onPreviewContract(contract.id);
                }}
                title="Quick Preview"
              >
                <Eye className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              </Button>
            )}
            <UnifiedExportManager
              mode="single"
              contractId={contract.id}
              contractTitle={contract.title}
              variant="ghost"
              size="sm"
              showLabel={false}
              className="h-7 w-7 sm:h-8 sm:w-8"
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 sm:h-8 sm:w-8 hover:bg-muted"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onContractSelect(contract.id)}>
                  <ExternalLink className="mr-2 h-4 w-4" /> Open
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEditContract(contract.id)}>
                  <FileEdit className="mr-2 h-4 w-4" /> Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDuplicateContract(contract.id)}>
                  <Copy className="mr-2 h-4 w-4" /> Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleAIAnalysis(contract.id)}>
                  <Brain className="mr-2 h-4 w-4" /> AI Analysis
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    setContractToDelete(contract.id);
                    setDeleteDialogOpen(true);
                  }}
                  className="text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" /> Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardFooter>
      </Card>
    );
  }

  // Table columns definition
  function getTableColumns(): ColumnDef<Contract>[] {
    return [
      {
        key: "title",
        label: "Contract",
        sortable: true,
        render: (_: any, contract: Contract) => (
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs">
              {contract.createdBy.initials}
            </div>
            <div>
              <div
                className="font-medium cursor-pointer hover:underline transition-colors"
                onClick={() => onContractSelect(contract.id)}
              >
                {contract.title}
              </div>
              <div className="text-xs text-muted-foreground">
                {contract.value && `Value: ${contract.value}`}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: "type",
        label: "Type",
        sortable: true,
      },
      {
        key: "counterparty",
        label: "Counterparty",
        sortable: true,
      },
      {
        key: "createdDate",
        label: "Created",
        sortable: true,
        render: (value: any) => formatDate(value),
      },
      {
        key: "expiryDate",
        label: "Expiry",
        sortable: true,
        render: (value: any) => value ? formatDate(value) : "-",
      },
      {
        key: "status",
        label: "Status",
        sortable: true,
        render: (value: any) => getStatusBadge(value),
      },
      {
        key: "actions",
        label: "Actions",
        sortable: false,
        width: "120px",
        render: (_: any, contract: Contract) => (
          <div className="flex items-center gap-2 table-row-actions">
            {onPreviewContract && (
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  onPreviewContract(contract.id);
                }}
                title="Quick Preview"
                className="h-8 w-8 hover:bg-muted"
              >
                <Eye className="h-4 w-4" />
              </Button>
            )}
            <UnifiedExportManager
              mode="single"
              contractId={contract.id}
              contractTitle={contract.title}
              variant="ghost"
              size="sm"
              showLabel={false}
              className="h-8 w-8"
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 hover:bg-muted"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onContractSelect(contract.id)}>
                  <ExternalLink className="mr-2 h-4 w-4" /> Open
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEditContract(contract.id)}>
                  <FileEdit className="mr-2 h-4 w-4" /> Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDuplicateContract(contract.id)}>
                  <Copy className="mr-2 h-4 w-4" /> Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleAIAnalysis(contract.id)}>
                  <Brain className="mr-2 h-4 w-4" /> AI Analysis
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    setContractToDelete(contract.id);
                    setDeleteDialogOpen(true);
                  }}
                  className="text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" /> Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        ),
      },
    ];
  }

  // Delete dialog renderer
  function renderDeleteDialog() {
    return (
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Contract</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this contract? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => contractToDelete && handleDeleteContract(contractToDelete)}
              disabled={deleteLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }
};

export default UnifiedContractView;
