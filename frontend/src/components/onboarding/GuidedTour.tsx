import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, ArrowRight, ArrowLeft, Target } from 'lucide-react';

interface TourStep {
  id: string;
  title: string;
  content: string;
  target: string; // CSS selector for the element to highlight
  position: 'top' | 'bottom' | 'left' | 'right';
  action?: {
    text: string;
    onClick: () => void;
  };
}

interface GuidedTourProps {
  steps: TourStep[];
  isActive: boolean;
  onComplete: () => void;
  onSkip: () => void;
}

export const GuidedTour: React.FC<GuidedTourProps> = ({
  steps,
  isActive,
  onComplete,
  onSkip
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });

  useEffect(() => {
    if (!isActive || !steps[currentStep]) return;

    const target = document.querySelector(steps[currentStep].target) as HTMLElement;
    if (target) {
      setTargetElement(target);
      
      // Scroll element into view
      target.scrollIntoView({ behavior: 'smooth', block: 'center' });
      
      // Add highlight class
      target.classList.add('tour-highlight');
      
      // Calculate tooltip position
      const rect = target.getBoundingClientRect();
      const position = steps[currentStep].position;
      
      let top = 0;
      let left = 0;
      
      switch (position) {
        case 'top':
          top = rect.top - 10;
          left = rect.left + rect.width / 2;
          break;
        case 'bottom':
          top = rect.bottom + 10;
          left = rect.left + rect.width / 2;
          break;
        case 'left':
          top = rect.top + rect.height / 2;
          left = rect.left - 10;
          break;
        case 'right':
          top = rect.top + rect.height / 2;
          left = rect.right + 10;
          break;
      }
      
      setTooltipPosition({ top, left });
    }

    return () => {
      if (target) {
        target.classList.remove('tour-highlight');
      }
    };
  }, [currentStep, isActive, steps]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleAction = () => {
    const action = steps[currentStep].action;
    if (action) {
      action.onClick();
    }
  };

  if (!isActive || !steps[currentStep]) {
    return null;
  }

  const step = steps[currentStep];

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40" />
      
      {/* Tooltip */}
      <Card 
        className="fixed z-50 w-80 shadow-lg"
        style={{
          top: tooltipPosition.top,
          left: tooltipPosition.left,
          transform: 'translate(-50%, -50%)'
        }}
      >
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <Badge variant="outline">
              {currentStep + 1} of {steps.length}
            </Badge>
            <Button variant="ghost" size="sm" onClick={onSkip}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="space-y-3">
            <h3 className="font-semibold text-sm">{step.title}</h3>
            <p className="text-sm text-gray-600">{step.content}</p>
            
            {step.action && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleAction}
                className="w-full"
              >
                <Target className="h-4 w-4 mr-2" />
                {step.action.text}
              </Button>
            )}
            
            <div className="flex justify-between pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePrevious}
                disabled={currentStep === 0}
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </Button>
              
              <Button size="sm" onClick={handleNext}>
                {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
                {currentStep < steps.length - 1 && <ArrowRight className="h-4 w-4 ml-1" />}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

// Predefined tour configurations
export const contractsTour: TourStep[] = [
  {
    id: 'contracts-list',
    title: 'Your Contracts',
    content: 'This is where all your contracts are displayed. You can filter, search, and manage them here.',
    target: '[data-tour="contracts-list"]',
    position: 'bottom'
  },
  {
    id: 'create-contract',
    title: 'Create New Contract',
    content: 'Click here to create a new contract using our intelligent wizard.',
    target: '[data-tour="create-contract-btn"]',
    position: 'bottom',
    action: {
      text: 'Try Creating a Contract',
      onClick: () => {
        // Navigate to contract creation
        window.location.href = '/contracts/create';
      }
    }
  },
  {
    id: 'contract-filters',
    title: 'Filter & Search',
    content: 'Use these filters to quickly find specific contracts by status, type, or other criteria.',
    target: '[data-tour="contract-filters"]',
    position: 'bottom'
  }
];

export const templatesTour: TourStep[] = [
  {
    id: 'templates-library',
    title: 'Template Library',
    content: 'Browse our collection of pre-built contract templates to speed up your workflow.',
    target: '[data-tour="templates-library"]',
    position: 'bottom'
  },
  {
    id: 'template-categories',
    title: 'Template Categories',
    content: 'Templates are organized by type and industry for easy discovery.',
    target: '[data-tour="template-categories"]',
    position: 'right'
  },
  {
    id: 'use-template',
    title: 'Use Template',
    content: 'Click on any template to start creating a contract based on it.',
    target: '[data-tour="template-card"]:first-child',
    position: 'top'
  }
];

export const dashboardTour: TourStep[] = [
  {
    id: 'dashboard-overview',
    title: 'Dashboard Overview',
    content: 'Get a quick overview of your contract portfolio and recent activity.',
    target: '[data-tour="dashboard-stats"]',
    position: 'bottom'
  },
  {
    id: 'pending-approvals',
    title: 'Pending Approvals',
    content: 'See contracts that need your review or approval.',
    target: '[data-tour="pending-approvals"]',
    position: 'left'
  },
  {
    id: 'recent-activity',
    title: 'Recent Activity',
    content: 'Stay updated with the latest changes and activities in your workspace.',
    target: '[data-tour="recent-activity"]',
    position: 'left'
  }
];

// Hook to manage guided tours
export const useGuidedTour = () => {
  const [activeTour, setActiveTour] = useState<TourStep[] | null>(null);
  const [isActive, setIsActive] = useState(false);

  const startTour = (tour: TourStep[]) => {
    setActiveTour(tour);
    setIsActive(true);
  };

  const completeTour = () => {
    setIsActive(false);
    setActiveTour(null);
  };

  const skipTour = () => {
    setIsActive(false);
    setActiveTour(null);
  };

  return {
    activeTour,
    isActive,
    startTour,
    completeTour,
    skipTour
  };
};
