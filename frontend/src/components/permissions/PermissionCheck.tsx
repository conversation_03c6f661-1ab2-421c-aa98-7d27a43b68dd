import React from 'react';
import { usePermissionCheck } from '@/hooks/usePermissionCheck';
import AccessDeniedDialog from './AccessDeniedDialog';

interface PermissionCheckProps {
  /**
   * The feature title to display in the access denied dialog
   */
  featureTitle?: string;

  /**
   * The workspace ID to check permissions against (defaults to current workspace)
   */
  workspaceId?: string;

  /**
   * Whether to log access attempts
   */
  logAccess?: boolean;

  /**
   * Children to render
   */
  children: React.ReactNode;
}

/**
 * A component that provides permission checking functionality and renders an access denied dialog when needed
 */
const PermissionCheck: React.FC<PermissionCheckProps> = ({
  featureTitle,
  workspaceId,
  logAccess,
  children,
}) => {
  const {
    checkPermission,
    checkAnyPermission,
    isDialogOpen,
    dialogProps,
    closeDialog,
  } = usePermissionCheck({
    featureTitle,
    workspaceId,
    logAccess,
  });

  // Create a context value to pass to children
  const contextValue = {
    checkPermission,
    checkAnyPermission,
  };

  return (
    <>
      {/* Render the children with the permission check context */}
      {React.Children.map(children, child => {
        // If the child is a React element, pass the permission check functions as props
        if (React.isValidElement(child)) {
          return React.cloneElement(child);
        }
        return child;
      })}

      {/* Render the access denied dialog */}
      <AccessDeniedDialog
        open={isDialogOpen}
        onClose={closeDialog}
        featureTitle={dialogProps.featureTitle}
        requiredPermission={dialogProps.requiredPermission}
        customMessage={dialogProps.customMessage}
      />
    </>
  );
};

export default PermissionCheck;
