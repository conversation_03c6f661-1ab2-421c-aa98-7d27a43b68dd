import React, { ComponentType } from 'react';
import PermissionGuard from './PermissionGuard';

interface WithPermissionOptions {
  /**
   * The permission ID required to render the component
   */
  permissionId?: string;
  
  /**
   * Array of permission IDs - any of these permissions will allow access
   */
  anyPermissions?: string[];
  
  /**
   * Array of permission IDs - all of these permissions are required for access
   */
  allPermissions?: string[];
  
  /**
   * The workspace ID to check permissions against (defaults to current workspace)
   */
  workspaceId?: string;
  
  /**
   * Component to render when the user doesn't have the required permissions
   */
  FallbackComponent?: ComponentType<any>;
  
  /**
   * Whether to show a loading state while checking permissions
   */
  showLoading?: boolean;
}

/**
 * Higher-order component that conditionally renders a component based on user permissions
 * @param WrappedComponent The component to wrap with permission checking
 * @param options Permission options
 * @returns A new component that checks permissions before rendering
 */
export function withPermission<P extends object>(
  WrappedComponent: ComponentType<P>,
  options: WithPermissionOptions
): ComponentType<P> {
  const {
    permissionId,
    anyPermissions,
    allPermissions,
    workspaceId,
    FallbackComponent,
    showLoading = false,
  } = options;
  
  // Create a new component that wraps the original component with permission checking
  const WithPermissionComponent = (props: P) => {
    return (
      <PermissionGuard
        permissionId={permissionId}
        anyPermissions={anyPermissions}
        allPermissions={allPermissions}
        workspaceId={workspaceId}
        showLoading={showLoading}
        fallback={FallbackComponent ? <FallbackComponent {...props} /> : null}
      >
        <WrappedComponent {...props} />
      </PermissionGuard>
    );
  };
  
  // Set display name for debugging
  const wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';
  WithPermissionComponent.displayName = `withPermission(${wrappedComponentName})`;
  
  return WithPermissionComponent;
}

export default withPermission;
