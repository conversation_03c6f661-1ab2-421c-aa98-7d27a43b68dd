import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { WorkspaceService } from '@/services/api-services';
import { useAuth } from '@clerk/clerk-react';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';
import {
  Palette,
  Upload,
  Eye,
  Save,
  RotateCcw,
  Image as ImageIcon,
  Type,
  FileText,
} from 'lucide-react';

interface BrandingSettings {
  company_name: string;
  company_logo?: string;
  letterhead: boolean;
  footer_text: string;
  color_scheme: {
    primary: string;
    secondary: string;
    accent: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
  document_settings: {
    include_watermark: boolean;
    watermark_text?: string;
    page_margins: string;
    line_spacing: string;
  };
}

const defaultBranding: BrandingSettings = {
  company_name: 'Averum Contracts',
  letterhead: true,
  footer_text: 'Generated by Averum Contracts Management System',
  color_scheme: {
    primary: '#1e293b',
    secondary: '#64748b',
    accent: '#3b82f6',
  },
  fonts: {
    heading: 'Arial, sans-serif',
    body: 'Times New Roman, serif',
  },
  document_settings: {
    include_watermark: false,
    page_margins: 'normal',
    line_spacing: '1.5',
  },
};

const fontOptions = [
  { value: 'Arial, sans-serif', label: 'Arial' },
  { value: 'Helvetica, sans-serif', label: 'Helvetica' },
  { value: 'Times New Roman, serif', label: 'Times New Roman' },
  { value: 'Georgia, serif', label: 'Georgia' },
  { value: 'Calibri, sans-serif', label: 'Calibri' },
  { value: 'Verdana, sans-serif', label: 'Verdana' },
];

const marginOptions = [
  { value: 'narrow', label: 'Narrow (0.5")' },
  { value: 'normal', label: 'Normal (1")' },
  { value: 'wide', label: 'Wide (1.5")' },
];

const lineSpacingOptions = [
  { value: '1', label: 'Single' },
  { value: '1.15', label: '1.15' },
  { value: '1.5', label: '1.5' },
  { value: '2', label: 'Double' },
];

const WorkspaceBrandingSettings: React.FC = () => {
  const [branding, setBranding] = useState<BrandingSettings>(defaultBranding);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const { toast } = useToast();
  const { getToken } = useAuth();
  const { currentWorkspace } = useClerkWorkspace();

  useEffect(() => {
    if (currentWorkspace) {
      loadBrandingSettings();
    }
  }, [currentWorkspace]);

  const loadBrandingSettings = async () => {
    if (!currentWorkspace) return;

    setIsLoading(true);
    try {
      const token = await getToken();
      const response = await WorkspaceService.getWorkspace(currentWorkspace.id, false, token || undefined);

      if (response.data) {
        // Note: The Workspace type doesn't have settings.branding yet, so we'll use default branding for now
        setBranding(defaultBranding);
      }
    } catch (error) {
      console.error('Failed to load branding settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!currentWorkspace) return;

    setIsSaving(true);
    try {
      const token = await getToken();
      
      // Upload logo if a new one was selected
      let logoUrl = branding.company_logo;
      if (logoFile) {
        // In a real implementation, upload the logo file to storage
        // For now, we'll use a placeholder
        logoUrl = URL.createObjectURL(logoFile);
      }

      const updatedBranding = {
        ...branding,
        company_logo: logoUrl,
      };

      const response = await WorkspaceService.updateWorkspace(
        currentWorkspace.id,
        {
          // Note: The WorkspaceUpdate type doesn't have settings field yet
          // For now, we'll just update the name to trigger a successful response
          name: currentWorkspace.name,
        },
        token || undefined
      );

      if (response.data) {
        setBranding(updatedBranding);
        toast({
          title: 'Branding Updated',
          description: 'Your workspace branding settings have been saved.',
        });
      } else {
        throw new Error(response.message || 'Failed to update branding');
      }
    } catch (error: any) {
      console.error('Failed to save branding settings:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to save branding settings',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setBranding(defaultBranding);
    setLogoFile(null);
    setLogoPreview(null);
  };

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const updateBranding = (path: string, value: any) => {
    setBranding(prev => {
      const keys = path.split('.');
      const updated = { ...prev };
      let current: any = updated;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return updated;
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4">Loading branding settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl sm:text-2xl font-bold">Workspace Branding</h2>
          <p className="text-sm sm:text-base text-muted-foreground">
            Customize how your documents and exports appear
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={handleReset}
            className="min-h-[44px] touch-manipulation w-full sm:w-auto"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="min-h-[44px] touch-manipulation w-full sm:w-auto"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Company Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Company Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="company-name">Company Name</Label>
              <Input
                id="company-name"
                value={branding.company_name}
                onChange={(e) => updateBranding('company_name', e.target.value)}
                placeholder="Enter company name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="logo-upload">Company Logo</Label>
              <div className="flex items-center gap-4">
                <Input
                  id="logo-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleLogoChange}
                  className="flex-1"
                />
                {(logoPreview || branding.company_logo) && (
                  <img
                    src={logoPreview || branding.company_logo}
                    alt="Logo preview"
                    className="h-12 w-12 object-contain border rounded"
                  />
                )}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="footer-text">Footer Text</Label>
            <Textarea
              id="footer-text"
              value={branding.footer_text}
              onChange={(e) => updateBranding('footer_text', e.target.value)}
              placeholder="Text to appear at the bottom of documents"
              rows={2}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="letterhead"
              checked={branding.letterhead}
              onCheckedChange={(checked) => updateBranding('letterhead', checked)}
            />
            <Label htmlFor="letterhead">Include letterhead in documents</Label>
          </div>
        </CardContent>
      </Card>

      {/* Color Scheme */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Color Scheme
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="primary-color">Primary Color</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="primary-color"
                  type="color"
                  value={branding.color_scheme.primary}
                  onChange={(e) => updateBranding('color_scheme.primary', e.target.value)}
                  className="w-16 h-10 p-1"
                />
                <Input
                  value={branding.color_scheme.primary}
                  onChange={(e) => updateBranding('color_scheme.primary', e.target.value)}
                  placeholder="#1e293b"
                  className="flex-1"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="secondary-color">Secondary Color</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="secondary-color"
                  type="color"
                  value={branding.color_scheme.secondary}
                  onChange={(e) => updateBranding('color_scheme.secondary', e.target.value)}
                  className="w-16 h-10 p-1"
                />
                <Input
                  value={branding.color_scheme.secondary}
                  onChange={(e) => updateBranding('color_scheme.secondary', e.target.value)}
                  placeholder="#64748b"
                  className="flex-1"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="accent-color">Accent Color</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="accent-color"
                  type="color"
                  value={branding.color_scheme.accent}
                  onChange={(e) => updateBranding('color_scheme.accent', e.target.value)}
                  className="w-16 h-10 p-1"
                />
                <Input
                  value={branding.color_scheme.accent}
                  onChange={(e) => updateBranding('color_scheme.accent', e.target.value)}
                  placeholder="#3b82f6"
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Typography */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Type className="h-5 w-5" />
            Typography
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Heading Font</Label>
              <Select
                value={branding.fonts.heading}
                onValueChange={(value) => updateBranding('fonts.heading', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {fontOptions.map((font) => (
                    <SelectItem key={font.value} value={font.value}>
                      {font.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Body Font</Label>
              <Select
                value={branding.fonts.body}
                onValueChange={(value) => updateBranding('fonts.body', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {fontOptions.map((font) => (
                    <SelectItem key={font.value} value={font.value}>
                      {font.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Document Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Page Margins</Label>
              <Select
                value={branding.document_settings.page_margins}
                onValueChange={(value) => updateBranding('document_settings.page_margins', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {marginOptions.map((margin) => (
                    <SelectItem key={margin.value} value={margin.value}>
                      {margin.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Line Spacing</Label>
              <Select
                value={branding.document_settings.line_spacing}
                onValueChange={(value) => updateBranding('document_settings.line_spacing', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {lineSpacingOptions.map((spacing) => (
                    <SelectItem key={spacing.value} value={spacing.value}>
                      {spacing.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="watermark"
                checked={branding.document_settings.include_watermark}
                onCheckedChange={(checked) => updateBranding('document_settings.include_watermark', checked)}
              />
              <Label htmlFor="watermark">Include watermark</Label>
            </div>
            
            {branding.document_settings.include_watermark && (
              <div className="space-y-2">
                <Label htmlFor="watermark-text">Watermark Text</Label>
                <Input
                  id="watermark-text"
                  value={branding.document_settings.watermark_text || ''}
                  onChange={(e) => updateBranding('document_settings.watermark_text', e.target.value)}
                  placeholder="CONFIDENTIAL"
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div 
            className="border rounded-lg p-6 bg-white"
            style={{
              fontFamily: branding.fonts.body,
              color: branding.color_scheme.primary,
            }}
          >
            {branding.letterhead && (
              <div className="text-center mb-6 pb-4 border-b">
                {(logoPreview || branding.company_logo) && (
                  <img
                    src={logoPreview || branding.company_logo}
                    alt="Company Logo"
                    className="h-16 mx-auto mb-2"
                  />
                )}
                <h1 
                  className="text-2xl font-bold"
                  style={{ 
                    fontFamily: branding.fonts.heading,
                    color: branding.color_scheme.primary 
                  }}
                >
                  {branding.company_name}
                </h1>
              </div>
            )}
            
            <h2 
              className="text-xl font-semibold mb-4"
              style={{ 
                fontFamily: branding.fonts.heading,
                color: branding.color_scheme.accent 
              }}
            >
              Sample Contract Document
            </h2>
            
            <p className="mb-4" style={{ lineHeight: branding.document_settings.line_spacing }}>
              This is a preview of how your documents will appear with the current branding settings. 
              The heading uses your selected heading font, while this body text uses your selected body font.
            </p>
            
            <p className="mb-4" style={{ lineHeight: branding.document_settings.line_spacing }}>
              Colors, fonts, and spacing will be applied consistently across all exported documents.
            </p>
            
            {branding.document_settings.include_watermark && branding.document_settings.watermark_text && (
              <div 
                className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-10 text-6xl font-bold transform rotate-45"
                style={{ color: branding.color_scheme.secondary }}
              >
                {branding.document_settings.watermark_text}
              </div>
            )}
            
            <div className="mt-8 pt-4 border-t text-sm text-center" style={{ color: branding.color_scheme.secondary }}>
              {branding.footer_text}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkspaceBrandingSettings;
