import React, { useState, useEffect } from 'react';
import { useClerk } from '@clerk/clerk-react';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';
import { useAuditLogger, AuditEventType } from '@/lib/audit-logger';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Loader2, Mail, UserPlus } from 'lucide-react';
import { Role } from '@/lib/clerk-roles';

interface WorkspaceInviteProps {
  workspaceId: string;
  onInviteSuccess?: () => void;
}

const WorkspaceInvite: React.FC<WorkspaceInviteProps> = ({
  workspaceId,
  onInviteSuccess,
}) => {
  const clerk = useClerk();
  const { getWorkspaceRoles, getWorkspaceById } = useClerkWorkspace();
  const { logEvent } = useAuditLogger();

  const [isOpen, setIsOpen] = useState(false);
  const [email, setEmail] = useState('');
  const [roleId, setRoleId] = useState('');
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Load roles when the dialog opens
  useEffect(() => {
    if (isOpen) {
      const loadRoles = async () => {
        try {
          const workspaceRoles = await getWorkspaceRoles(workspaceId);
          setRoles(workspaceRoles);

          // Set default role to the least privileged role (usually Read Only)
          const readOnlyRole = workspaceRoles.find(r => r.id === 'role-readonly');
          if (readOnlyRole) {
            setRoleId(readOnlyRole.id);
          } else if (workspaceRoles.length > 0) {
            setRoleId(workspaceRoles[0].id);
          }
        } catch (error) {
          console.error('Error loading roles:', error);
          setError('Failed to load roles');
        }
      };

      loadRoles();
    }
  }, [isOpen, workspaceId, getWorkspaceRoles]);

  const handleInvite = async () => {
    if (!email.trim()) {
      setError('Email is required');
      return;
    }

    if (!roleId) {
      setError('Role is required');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Get the workspace
      const workspace = getWorkspaceById(workspaceId);
      if (!workspace) {
        throw new Error('Workspace not found');
      }

      // Create the invitation
      // Note: This is a mock implementation since we don't have the actual Clerk API
      // In a real implementation, you would use the Clerk API to create an invitation
      const invitation = {
        id: `inv_${Date.now()}`,
        emailAddress: email,
        role: 'org:member',
        status: 'pending'
      };

      // Log the invitation
      logEvent(AuditEventType.USER_INVITED, {
        workspaceId,
        workspaceName: workspace.name,
        targetId: email,
        targetType: 'user',
        targetName: email,
        details: {
          roleId,
          invitationId: invitation.id,
        },
      });

      setSuccess(true);
      setEmail('');

      // Call the success callback
      if (onInviteSuccess) {
        onInviteSuccess();
      }

      // Close the dialog after a delay
      setTimeout(() => {
        setIsOpen(false);
        setSuccess(false);
      }, 2000);
    } catch (err: any) {
      console.error('Error inviting user:', err);
      setError(err.message || 'Failed to invite user');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="default" size="sm" className="gap-2">
          <UserPlus className="h-4 w-4" />
          Invite User
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Invite User to Workspace</DialogTitle>
          <DialogDescription>
            Send an invitation to join this workspace. The user will receive an email with a link to accept the invitation.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="flex-1"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Select value={roleId} onValueChange={setRoleId}>
              <SelectTrigger id="role">
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                {roles.map((role) => (
                  <SelectItem key={role.id} value={role.id}>
                    {role.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {roles.find(r => r.id === roleId)?.description || 'Select a role to see its description'}
            </p>
          </div>

          {error && (
            <div className="text-sm text-destructive">{error}</div>
          )}

          {success && (
            <div className="text-sm text-green-600 flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-600" />
              Invitation sent successfully!
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleInvite} disabled={isLoading || !email || !roleId}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              'Send Invitation'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WorkspaceInvite;
