import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Save, Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { Workspace, validateWorkspaceName, validateWorkspaceDescription } from "@/types/workspace";

interface WorkspaceFormProps {
  workspace?: Workspace;
  onClose: () => void;
  onWorkspaceCreated?: (workspace: Workspace) => void;
  onWorkspaceUpdated?: (id: string, workspace: Partial<Workspace>) => void;
}

const WorkspaceForm = ({
  workspace,
  onClose,
  onWorkspaceCreated,
  onWorkspaceUpdated,
}: WorkspaceFormProps) => {
  const { toast } = useToast();
  const { createWorkspace, updateWorkspace } = useClerkWorkspace();
  const isEditing = !!workspace;
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ name?: string; description?: string }>({});
  const [formData, setFormData] = useState({
    name: workspace?.name || "",
    description: workspace?.description || "",
    visibility: "private",
    isActive: workspace?.isActive || false,
    template: "blank",
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear errors when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors({ ...errors, [name]: undefined });
    }
  };



  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value });
  };

  const validateForm = (): boolean => {
    const newErrors: { name?: string; description?: string } = {};

    const nameError = validateWorkspaceName(formData.name);
    if (nameError) {
      newErrors.name = nameError;
    }

    const descriptionError = validateWorkspaceDescription(formData.description);
    if (descriptionError) {
      newErrors.description = descriptionError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      if (isEditing && workspace) {
        // Update existing workspace using ClerkWorkspaceProvider
        const result = await updateWorkspace(workspace.id, {
          name: formData.name,
          description: formData.description
        });

        if (result && onWorkspaceUpdated) {
          toast({
            title: "Workspace updated",
            description: "The workspace has been updated successfully.",
          });
          onWorkspaceUpdated(workspace.id, {
            name: formData.name,
            description: formData.description,
            isActive: formData.isActive,
          });
        }
      } else {
        // Create new workspace using ClerkWorkspaceProvider
        const newWorkspace = await createWorkspace({
          name: formData.name,
          description: formData.description,
          settings: {
            visibility: formData.visibility as 'private' | 'public',
          },
        });

        if (newWorkspace) {
          toast({
            title: "Workspace created",
            description: "The new workspace has been created successfully.",
          });

          // Call the callback with the workspace data
          if (onWorkspaceCreated) {
            onWorkspaceCreated(newWorkspace);
          }

          // Close the form after successful creation
          if (onClose) {
            onClose();
          }
        }
      }
    } catch (error) {
      console.error("Error submitting workspace form:", error);
      toast({
        title: "Error",
        description: isEditing
          ? "Failed to update workspace. Please try again."
          : "Failed to create workspace. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Workspace Name</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="Enter workspace name"
            required
            className={errors.name ? "border-destructive" : ""}
          />
          {errors.name && (
            <p className="text-sm text-destructive">{errors.name}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Enter workspace description"
            rows={3}
            className={errors.description ? "border-destructive" : ""}
          />
          {errors.description && (
            <p className="text-sm text-destructive">{errors.description}</p>
          )}
        </div>

        {!isEditing && (
          <div className="space-y-2">
            <Label htmlFor="visibility">Visibility</Label>
            <Select
              value={formData.visibility}
              onValueChange={(value) =>
                handleSelectChange("visibility", value)
              }
            >
              <SelectTrigger id="visibility">
                <SelectValue placeholder="Select visibility" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="private">Private</SelectItem>
                <SelectItem value="public">Public</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Private workspaces are only visible to invited members
            </p>
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-4">
        <Button variant="outline" type="button" onClick={onClose}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {isEditing ? "Updating..." : "Creating..."}
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              {isEditing ? "Update Workspace" : "Create Workspace"}
            </>
          )}
        </Button>
      </div>
    </form>
  );
};

export default WorkspaceForm;
