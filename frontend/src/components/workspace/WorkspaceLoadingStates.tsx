/**
 * Reusable Workspace Loading State Components
 * Eliminates duplication of loading UI patterns across workspace components
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Shield, FileText, Plus, RefreshCw } from 'lucide-react';

// Loading spinner component
export const WorkspaceLoadingSpinner: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  description?: string;
}> = ({ size = 'md', message = 'Loading workspaces...', description }) => {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-12 w-12',
    lg: 'h-16 w-16',
  };

  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <Loader2 className={`${sizeClasses[size]} text-primary animate-spin mb-4`} />
      <h3 className="text-lg font-medium">{message}</h3>
      {description && (
        <p className="text-muted-foreground mt-2">{description}</p>
      )}
    </div>
  );
};

// Error state component
export const WorkspaceErrorState: React.FC<{
  error: string;
  onRetry?: () => void;
  retryLabel?: string;
}> = ({ error, onRetry, retryLabel = 'Try Again' }) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <Shield className="h-12 w-12 text-destructive mb-4" />
      <h3 className="text-lg font-medium">Error loading workspaces</h3>
      <p className="text-muted-foreground mt-2 max-w-md">{error}</p>
      {onRetry && (
        <Button onClick={onRetry} variant="outline" className="mt-4">
          <RefreshCw className="mr-2 h-4 w-4" />
          {retryLabel}
        </Button>
      )}
    </div>
  );
};

// Empty state component
export const WorkspaceEmptyState: React.FC<{
  title?: string;
  description?: string;
  onCreateWorkspace?: () => void;
  createLabel?: string;
  showCreateButton?: boolean;
}> = ({ 
  title = 'No workspaces found',
  description = 'Create your first workspace to get started',
  onCreateWorkspace,
  createLabel = 'Create Workspace',
  showCreateButton = true,
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <FileText className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-medium">{title}</h3>
      <p className="text-muted-foreground mt-2 max-w-md">{description}</p>
      {showCreateButton && onCreateWorkspace && (
        <Button onClick={onCreateWorkspace} className="mt-4">
          <Plus className="mr-2 h-4 w-4" />
          {createLabel}
        </Button>
      )}
    </div>
  );
};

// Skeleton loading component for workspace cards
export const WorkspaceCardSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="border rounded-lg p-6 animate-pulse">
          <div className="flex items-center mb-4">
            <div className="h-8 w-8 bg-muted rounded-full mr-3"></div>
            <div className="h-4 bg-muted rounded w-32"></div>
          </div>
          <div className="space-y-2 mb-4">
            <div className="h-3 bg-muted rounded w-full"></div>
            <div className="h-3 bg-muted rounded w-2/3"></div>
          </div>
          <div className="flex justify-between items-center mb-4">
            <div className="h-3 bg-muted rounded w-20"></div>
            <div className="h-3 bg-muted rounded w-16"></div>
          </div>
          <div className="h-8 bg-muted rounded w-full"></div>
        </div>
      ))}
    </div>
  );
};

// Inline loading state for workspace switcher
export const WorkspaceSwitcherLoading: React.FC<{
  message?: string;
}> = ({ message = 'Loading workspaces...' }) => {
  return (
    <div className="px-2 py-4 text-center">
      <div className="flex items-center justify-center gap-2">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">{message}</span>
      </div>
    </div>
  );
};

// Inline error state for workspace switcher
export const WorkspaceSwitcherError: React.FC<{
  error: string;
  onRetry?: () => void;
}> = ({ error, onRetry }) => {
  return (
    <div className="px-2 py-4 text-center space-y-2">
      <span className="text-sm text-destructive block">{error}</span>
      {onRetry && (
        <Button size="sm" variant="outline" onClick={onRetry} className="w-full">
          <RefreshCw className="mr-2 h-3 w-3" />
          Retry
        </Button>
      )}
    </div>
  );
};

// Inline empty state for workspace switcher
export const WorkspaceSwitcherEmpty: React.FC<{
  onCreateWorkspace?: () => void;
}> = ({ onCreateWorkspace }) => {
  return (
    <div className="px-2 py-4 text-center space-y-2">
      <span className="text-sm text-muted-foreground block">No workspaces available</span>
      {onCreateWorkspace && (
        <Button
          size="sm"
          variant="outline"
          onClick={onCreateWorkspace}
          className="w-full"
        >
          <Plus className="mr-2 h-3 w-3" />
          Create Workspace
        </Button>
      )}
    </div>
  );
};

// Workspace switching loading overlay
export const WorkspaceSwitchingOverlay: React.FC<{
  isVisible: boolean;
  workspaceName?: string;
}> = ({ isVisible, workspaceName }) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-card border rounded-lg p-6 shadow-lg max-w-sm w-full mx-4">
        <div className="flex items-center justify-center mb-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
        <h3 className="text-lg font-medium text-center mb-2">Switching Workspace</h3>
        {workspaceName && (
          <p className="text-sm text-muted-foreground text-center">
            Loading {workspaceName}...
          </p>
        )}
      </div>
    </div>
  );
};

// Workspace operation feedback component
export const WorkspaceOperationFeedback: React.FC<{
  operation: 'creating' | 'updating' | 'deleting' | 'loading';
  workspaceName?: string;
  isVisible: boolean;
}> = ({ operation, workspaceName, isVisible }) => {
  if (!isVisible) return null;

  const messages = {
    creating: `Creating ${workspaceName || 'workspace'}...`,
    updating: `Updating ${workspaceName || 'workspace'}...`,
    deleting: `Deleting ${workspaceName || 'workspace'}...`,
    loading: `Loading ${workspaceName || 'workspace'}...`,
  };

  return (
    <div className="flex items-center gap-2 text-sm text-muted-foreground">
      <Loader2 className="h-4 w-4 animate-spin" />
      <span>{messages[operation]}</span>
    </div>
  );
};

// Compact loading state for small spaces
export const WorkspaceCompactLoading: React.FC<{
  size?: 'xs' | 'sm';
}> = ({ size = 'sm' }) => {
  const sizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
  };

  return (
    <Loader2 className={`${sizeClasses[size]} animate-spin text-muted-foreground`} />
  );
};
