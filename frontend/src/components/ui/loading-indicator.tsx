import React from "react";
import { cn } from "@/lib/utils";

interface LoadingIndicatorProps {
  isLoading: boolean;
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "spinner" | "dots" | "pulse" | "bar";
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  isLoading,
  className,
  size = "md",
  variant = "spinner"
}) => {
  if (!isLoading) return null;

  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6", 
    lg: "h-8 w-8"
  };

  const renderSpinner = () => (
    <div
      className={cn(
        "animate-spin rounded-full border-2 border-muted border-t-primary",
        sizeClasses[size],
        className
      )}
    />
  );

  const renderDots = () => (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            "rounded-full bg-primary animate-pulse",
            size === "sm" ? "h-1 w-1" : size === "md" ? "h-1.5 w-1.5" : "h-2 w-2"
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: "1s"
          }}
        />
      ))}
    </div>
  );

  const renderPulse = () => (
    <div
      className={cn(
        "rounded-full bg-primary/20 animate-pulse",
        sizeClasses[size],
        className
      )}
    />
  );

  const renderBar = () => (
    <div className={cn("w-full bg-muted rounded-full overflow-hidden", className)}>
      <div 
        className="h-1 bg-primary rounded-full animate-pulse"
        style={{
          animation: "loading-bar 2s ease-in-out infinite"
        }}
      />
    </div>
  );

  switch (variant) {
    case "dots":
      return renderDots();
    case "pulse":
      return renderPulse();
    case "bar":
      return renderBar();
    default:
      return renderSpinner();
  }
};

// Inline loading indicator for buttons
interface ButtonLoadingProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
}

const ButtonLoading: React.FC<ButtonLoadingProps> = ({
  isLoading,
  children,
  loadingText,
  className
}) => {
  if (isLoading) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <LoadingIndicator isLoading={true} size="sm" />
        {loadingText && <span>{loadingText}</span>}
      </div>
    );
  }

  return <>{children}</>;
};

// Page loading overlay
interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  className?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  message = "Loading...",
  className
}) => {
  if (!isLoading) return null;

  return (
    <div className={cn(
      "fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center",
      className
    )}>
      <div className="flex flex-col items-center gap-4 p-6 bg-card rounded-lg shadow-lg border">
        <LoadingIndicator isLoading={true} size="lg" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  );
};

// Subtle loading bar for top of page
interface LoadingBarProps {
  isLoading: boolean;
  className?: string;
}

const LoadingBar: React.FC<LoadingBarProps> = ({
  isLoading,
  className
}) => {
  if (!isLoading) return null;

  return (
    <div className={cn("fixed top-0 left-0 right-0 z-50", className)}>
      <div className="h-0.5 bg-primary/20 overflow-hidden">
        <div 
          className="h-full bg-primary transition-all duration-300 ease-out"
          style={{
            width: "30%",
            animation: "loading-progress 2s ease-in-out infinite"
          }}
        />
      </div>
    </div>
  );
};

export { LoadingIndicator, ButtonLoading, LoadingOverlay, LoadingBar };
