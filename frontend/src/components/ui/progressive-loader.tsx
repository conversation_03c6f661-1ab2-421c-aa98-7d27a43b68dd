import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { 
  ContractTableSkeleton, 
  ContractGridSkeleton, 
  SearchFilterSkeleton, 
  TabsSkeleton, 
  PageHeaderSkeleton 
} from "./skeleton";

interface ProgressiveLoaderProps {
  isLoading: boolean;
  viewMode: "list" | "grid";
  className?: string;
  children: React.ReactNode;
}

const ProgressiveLoader: React.FC<ProgressiveLoaderProps> = ({
  isLoading,
  viewMode,
  className,
  children
}) => {
  const [loadingStage, setLoadingStage] = useState(0);

  useEffect(() => {
    if (!isLoading) {
      setLoadingStage(0);
      return;
    }

    // Progressive loading stages
    const stages = [
      { delay: 0, stage: 1 },     // Show header skeleton
      { delay: 200, stage: 2 },   // Show tabs skeleton
      { delay: 400, stage: 3 },   // Show search skeleton
      { delay: 600, stage: 4 },   // Show content skeleton
    ];

    const timeouts = stages.map(({ delay, stage }) =>
      setTimeout(() => setLoadingStage(stage), delay)
    );

    return () => {
      timeouts.forEach(clearTimeout);
    };
  }, [isLoading]);

  if (!isLoading) {
    return (
      <div className={cn("animate-fade-in", className)}>
        {children}
      </div>
    );
  }

  return (
    <div className={cn("page-container", className)}>
      {/* Stage 1: Header */}
      {loadingStage >= 1 && (
        <div className="animate-fade-in">
          <PageHeaderSkeleton />
        </div>
      )}
      
      {/* Stage 2: Tabs */}
      {loadingStage >= 2 && (
        <div className="animate-fade-in">
          <TabsSkeleton />
        </div>
      )}
      
      {/* Stage 3: Search and filters */}
      {loadingStage >= 3 && (
        <div className="animate-fade-in">
          <SearchFilterSkeleton />
        </div>
      )}
      
      {/* Stage 4: Content */}
      {loadingStage >= 4 && (
        <div className="animate-fade-in">
          {viewMode === "list" ? (
            <ContractTableSkeleton rows={8} staggered />
          ) : (
            <ContractGridSkeleton items={9} staggered />
          )}
        </div>
      )}
    </div>
  );
};

// Enhanced loading state with shimmer effect
interface SmartSkeletonProps {
  isLoading: boolean;
  hasError: boolean;
  isEmpty: boolean;
  viewMode: "list" | "grid";
  className?: string;
  children: React.ReactNode;
  emptyState?: React.ReactNode;
  errorState?: React.ReactNode;
}

const SmartSkeleton: React.FC<SmartSkeletonProps> = ({
  isLoading,
  hasError,
  isEmpty,
  viewMode,
  className,
  children,
  emptyState,
  errorState
}) => {
  if (isLoading) {
    return (
      <ProgressiveLoader 
        isLoading={isLoading} 
        viewMode={viewMode} 
        className={className}
      >
        {children}
      </ProgressiveLoader>
    );
  }

  if (hasError && errorState) {
    return (
      <div className={cn("animate-fade-in", className)}>
        {errorState}
      </div>
    );
  }

  if (isEmpty && emptyState) {
    return (
      <div className={cn("animate-fade-in", className)}>
        {emptyState}
      </div>
    );
  }

  return (
    <div className={cn("animate-fade-in", className)}>
      {children}
    </div>
  );
};

// Skeleton wrapper for individual items that can show loading state
interface ItemSkeletonProps {
  isLoading: boolean;
  className?: string;
  children: React.ReactNode;
  skeletonHeight?: string;
}

const ItemSkeleton: React.FC<ItemSkeletonProps> = ({
  isLoading,
  className,
  children,
  skeletonHeight = "h-16"
}) => {
  if (isLoading) {
    return (
      <div className={cn("animate-shimmer rounded-md bg-muted/50", skeletonHeight, className)} />
    );
  }

  return (
    <div className={cn("animate-fade-in", className)}>
      {children}
    </div>
  );
};

export { ProgressiveLoader, SmartSkeleton, ItemSkeleton };
