import React, { useState, useCallback, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";

export interface GridItemProps<T = any> {
  item: T;
  index: number;
  isSelected: boolean;
  isFocused: boolean;
  onSelect?: (selected: boolean) => void;
  onClick?: () => void;
  onDoubleClick?: () => void;
  className?: string;
}

export interface EnhancedGridProps<T = any> {
  data: T[];
  renderItem: (props: GridItemProps<T>) => React.ReactNode;
  selectedItems?: string[];
  onItemSelect?: (id: string, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
  getItemId?: (item: T) => string;
  onItemClick?: (item: T, index: number) => void;
  onItemDoubleClick?: (item: T, index: number) => void;
  className?: string;
  gridClassName?: string;
  columns?: {
    default: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: string;
  loading?: boolean;
  emptyMessage?: string;
  keyboardNavigation?: boolean;
  selectionMode?: "none" | "single" | "multiple";
  showSelectAll?: boolean;
}

const EnhancedGrid = <T extends Record<string, any>>({
  data,
  renderItem,
  selectedItems = [],
  onItemSelect,
  onSelectAll,
  getItemId = (item: T) => item.id,
  onItemClick,
  onItemDoubleClick,
  className,
  gridClassName,
  columns = { default: 1, sm: 2, md: 3, lg: 4 },
  gap = "gap-4",
  loading = false,
  emptyMessage = "No items found",
  keyboardNavigation = true,
  selectionMode = "multiple",
  showSelectAll = true,
}: EnhancedGridProps<T>) => {
  const [focusedItemIndex, setFocusedItemIndex] = useState<number>(-1);
  const gridRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!keyboardNavigation) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (!gridRef.current?.contains(document.activeElement)) return;

      const cols = getColumnsForCurrentBreakpoint();
      const totalItems = data.length;

      switch (e.key) {
        case "ArrowRight":
          e.preventDefault();
          setFocusedItemIndex(prev => Math.min(prev + 1, totalItems - 1));
          break;
        case "ArrowLeft":
          e.preventDefault();
          setFocusedItemIndex(prev => Math.max(prev - 1, 0));
          break;
        case "ArrowDown":
          e.preventDefault();
          setFocusedItemIndex(prev => Math.min(prev + cols, totalItems - 1));
          break;
        case "ArrowUp":
          e.preventDefault();
          setFocusedItemIndex(prev => Math.max(prev - cols, 0));
          break;
        case "Enter":
        case " ":
          e.preventDefault();
          if (focusedItemIndex >= 0 && onItemClick) {
            onItemClick(data[focusedItemIndex], focusedItemIndex);
          }
          break;
        case "Escape":
          setFocusedItemIndex(-1);
          gridRef.current?.blur();
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [keyboardNavigation, focusedItemIndex, data, onItemClick]);

  // Focus management
  useEffect(() => {
    if (focusedItemIndex >= 0 && itemRefs.current[focusedItemIndex]) {
      itemRefs.current[focusedItemIndex]?.focus();
    }
  }, [focusedItemIndex]);

  // Get current columns based on screen size (simplified)
  const getColumnsForCurrentBreakpoint = () => {
    // This is a simplified version - in a real implementation,
    // you'd use a resize observer or media queries
    return columns.lg || columns.default;
  };

  // Generate grid column classes
  const getGridClasses = () => {
    const classes = [`grid-cols-${columns.default}`];
    if (columns.sm) classes.push(`sm:grid-cols-${columns.sm}`);
    if (columns.md) classes.push(`md:grid-cols-${columns.md}`);
    if (columns.lg) classes.push(`lg:grid-cols-${columns.lg}`);
    if (columns.xl) classes.push(`xl:grid-cols-${columns.xl}`);
    return classes.join(" ");
  };

  // Handle item selection
  const handleItemSelect = (item: T, selected: boolean) => {
    if (onItemSelect && selectionMode !== "none") {
      onItemSelect(getItemId(item), selected);
    }
  };

  // Handle select all
  const handleSelectAll = (selected: boolean) => {
    if (onSelectAll && selectionMode === "multiple") {
      onSelectAll(selected);
    }
  };

  // Check if all items are selected
  const allSelected = data.length > 0 && selectedItems.length === data.length;
  const someSelected = selectedItems.length > 0 && selectedItems.length < data.length;

  if (loading) {
    return (
      <div className={cn("flex items-center justify-center py-12", className)}>
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={cn("flex items-center justify-center py-12", className)}>
        <div className="text-center">
          <p className="text-muted-foreground">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      {/* Select all header */}
      {selectionMode === "multiple" && showSelectAll && (
        <div className="flex items-center gap-2 mb-4 p-2 border-b">
          <input
            type="checkbox"
            checked={allSelected}
            ref={(el) => {
              if (el) el.indeterminate = someSelected;
            }}
            onChange={(e) => handleSelectAll(e.target.checked)}
            className="rounded border-border"
          />
          <span className="text-sm text-muted-foreground">
            {selectedItems.length > 0 
              ? `${selectedItems.length} of ${data.length} selected`
              : "Select all"
            }
          </span>
        </div>
      )}

      {/* Grid */}
      <div
        ref={gridRef}
        className={cn(
          "grid",
          getGridClasses(),
          gap,
          gridClassName
        )}
        tabIndex={keyboardNavigation ? 0 : -1}
      >
        {data.map((item, index) => {
          const itemId = getItemId(item);
          const isSelected = selectedItems.includes(itemId);
          const isFocused = focusedItemIndex === index;

          return (
            <div
              key={itemId}
              ref={(el) => (itemRefs.current[index] = el)}
              className={cn(
                "relative transition-all duration-200",
                isFocused && "ring-2 ring-primary ring-inset",
                keyboardNavigation && "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-inset"
              )}
              onClick={() => {
                onItemClick?.(item, index);
                setFocusedItemIndex(index);
              }}
              onDoubleClick={() => onItemDoubleClick?.(item, index)}
              tabIndex={keyboardNavigation ? 0 : -1}
              onFocus={() => setFocusedItemIndex(index)}
            >
              {renderItem({
                item,
                index,
                isSelected,
                isFocused,
                onSelect: selectionMode !== "none" 
                  ? (selected) => handleItemSelect(item, selected)
                  : undefined,
                onClick: () => onItemClick?.(item, index),
                onDoubleClick: () => onItemDoubleClick?.(item, index),
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default EnhancedGrid;
