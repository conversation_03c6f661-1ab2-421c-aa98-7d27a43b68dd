import React, { ReactNode } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";

export interface FormFieldProps {
  id: string;
  name: string;
  label?: string;
  type?: "text" | "email" | "password" | "number" | "date" | "textarea" | "select" | "checkbox";
  placeholder?: string;
  value: any;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  error?: string | string[];
  touched?: boolean;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  options?: { value: string; label: string }[];
  children?: ReactNode;
  description?: string;
  min?: number | string;
  max?: number | string;
  step?: number | string;
  rows?: number;
  autoComplete?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  id,
  name,
  label,
  type = "text",
  placeholder,
  value,
  onChange,
  onBlur,
  error,
  touched,
  required,
  disabled,
  className,
  options,
  children,
  description,
  min,
  max,
  step,
  rows = 3,
  autoComplete,
}) => {
  // Format error message for display
  const errorMessage = Array.isArray(error) ? error[0] : error;
  const showError = touched && errorMessage;

  // Generate a unique ID for accessibility
  const fieldId = id || `field-${name}`;
  const errorId = `${fieldId}-error`;
  const descriptionId = `${fieldId}-description`;

  // Determine aria attributes for accessibility
  const ariaInvalid = showError ? true : undefined;
  const ariaDescribedby = [
    description ? descriptionId : null,
    showError ? errorId : null,
  ]
    .filter(Boolean)
    .join(" ") || undefined;
  const ariaRequired = required ? true : undefined;

  // Render different input types
  const renderInput = () => {
    switch (type) {
      case "textarea":
        return (
          <Textarea
            id={fieldId}
            name={name}
            placeholder={placeholder}
            value={value || ""}
            onChange={onChange}
            onBlur={onBlur}
            disabled={disabled}
            className={cn(
              "min-h-[44px] touch-manipulation", // Enhanced mobile touch target
              showError && "border-destructive focus-visible:ring-destructive",
              className
            )}
            rows={rows}
            aria-invalid={ariaInvalid}
            aria-describedby={ariaDescribedby}
            aria-required={ariaRequired}
          />
        );

      case "select":
        return (
          <Select
            name={name}
            value={value || ""}
            onValueChange={(value) => {
              // Create a synthetic event to match the onChange interface
              const syntheticEvent = {
                target: {
                  name,
                  value,
                },
              } as React.ChangeEvent<HTMLSelectElement>;
              onChange(syntheticEvent);
            }}
            disabled={disabled}
          >
            <SelectTrigger
              id={fieldId}
              className={cn(
                "min-h-[44px] touch-manipulation", // Enhanced mobile touch target
                showError && "border-destructive focus-visible:ring-destructive",
                className
              )}
              aria-invalid={ariaInvalid}
              aria-describedby={ariaDescribedby}
              aria-required={ariaRequired}
            >
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
              {children}
            </SelectContent>
          </Select>
        );

      case "checkbox":
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fieldId}
              name={name}
              checked={!!value}
              onCheckedChange={(checked) => {
                // Create a synthetic event to match the onChange interface
                const syntheticEvent = {
                  target: {
                    name,
                    type: "checkbox",
                    checked: checked === true,
                  },
                } as React.ChangeEvent<HTMLInputElement>;
                onChange(syntheticEvent);
              }}
              disabled={disabled}
              className={cn(showError && "border-destructive focus-visible:ring-destructive", className)}
              aria-invalid={ariaInvalid}
              aria-describedby={ariaDescribedby}
              aria-required={ariaRequired}
            />
            {label && (
              <Label
                htmlFor={fieldId}
                className={cn("text-sm font-normal", disabled && "opacity-50")}
              >
                {label}
                {required && <span className="text-destructive ml-1">*</span>}
              </Label>
            )}
          </div>
        );

      default:
        // Determine mobile-optimized input attributes
        const getMobileInputMode = () => {
          switch (type) {
            case "email":
              return "email";
            case "number":
              return "numeric";
            default:
              return "text";
          }
        };

        const getPattern = () => {
          return type === "number" ? "[0-9]*" : undefined;
        };

        return (
          <Input
            id={fieldId}
            name={name}
            type={type}
            placeholder={placeholder}
            value={value || ""}
            onChange={onChange}
            onBlur={onBlur}
            disabled={disabled}
            className={cn(
              "min-h-[44px] touch-manipulation", // Enhanced mobile touch target
              showError && "border-destructive focus-visible:ring-destructive",
              className
            )}
            min={min}
            max={max}
            step={step}
            autoComplete={autoComplete}
            inputMode={getMobileInputMode()}
            pattern={getPattern()}
            aria-invalid={ariaInvalid}
            aria-describedby={ariaDescribedby}
            aria-required={ariaRequired}
          />
        );
    }
  };

  return (
    <div className={cn("space-y-2", type === "checkbox" ? "flex items-start space-y-0 space-x-2" : "")}>
      {label && type !== "checkbox" && (
        <Label
          htmlFor={fieldId}
          className={cn(disabled && "opacity-50")}
        >
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </Label>
      )}

      {description && (
        <p id={descriptionId} className="text-xs text-muted-foreground">
          {description}
        </p>
      )}

      {renderInput()}

      {showError && (
        <p id={errorId} className="text-xs text-destructive mt-1">
          {errorMessage}
        </p>
      )}
    </div>
  );
};
