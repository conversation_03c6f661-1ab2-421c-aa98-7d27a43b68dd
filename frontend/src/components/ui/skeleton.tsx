import React from "react"
import { cn } from "@/lib/utils"

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn("animate-shimmer rounded-md bg-muted/50", className)}
      {...props}
    />
  )
}

// Enhanced skeleton patterns for common UI elements
const SkeletonCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("space-y-3 p-5 border border-border/50 rounded-xl", className)}
    {...props}
  >
    <div className="space-y-2">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-3 w-1/2" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-8 w-16" />
      <Skeleton className="h-3 w-full" />
    </div>
  </div>
))
SkeletonCard.displayName = "SkeletonCard"

const SkeletonTable = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { rows?: number }
>(({ className, rows = 5, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("space-y-3", className)}
    {...props}
  >
    {/* Table header */}
    <div className="flex space-x-4 pb-2 border-b border-border/50">
      <Skeleton className="h-4 w-1/4" />
      <Skeleton className="h-4 w-1/3" />
      <Skeleton className="h-4 w-1/4" />
      <Skeleton className="h-4 w-1/6" />
    </div>
    {/* Table rows */}
    {Array.from({ length: rows }).map((_, i) => (
      <div key={i} className="flex space-x-4 py-2">
        <Skeleton className="h-4 w-1/4" />
        <Skeleton className="h-4 w-1/3" />
        <Skeleton className="h-4 w-1/4" />
        <Skeleton className="h-4 w-1/6" />
      </div>
    ))}
  </div>
))
SkeletonTable.displayName = "SkeletonTable"

const SkeletonList = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { items?: number }
>(({ className, items = 3, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("space-y-3", className)}
    {...props}
  >
    {Array.from({ length: items }).map((_, i) => (
      <div key={i} className="flex items-center space-x-3 p-3 border border-border/50 rounded-lg">
        <Skeleton className="h-10 w-10 rounded-full" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-3 w-1/2" />
        </div>
        <Skeleton className="h-6 w-16" />
      </div>
    ))}
  </div>
))
SkeletonList.displayName = "SkeletonList"

const SkeletonForm = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("space-y-4", className)}
    {...props}
  >
    <div className="space-y-2">
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-9 w-full" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-4 w-24" />
      <Skeleton className="h-9 w-full" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-4 w-28" />
      <Skeleton className="h-20 w-full" />
    </div>
    <div className="flex space-x-2 pt-2">
      <Skeleton className="h-9 w-20" />
      <Skeleton className="h-9 w-16" />
    </div>
  </div>
))
SkeletonForm.displayName = "SkeletonForm"

// Contract-specific skeleton components
const ContractTableSkeleton = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { rows?: number; staggered?: boolean }
>(({ className, rows = 5, staggered = true, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("border rounded-md", className)}
    {...props}
  >
    {/* Table header skeleton */}
    <div className="border-b">
      <div className="flex items-center h-12 px-4 gap-4">
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-28" />
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-20" />
      </div>
    </div>

    {/* Table rows skeleton with staggered animation */}
    <div className={staggered ? "stagger-children" : ""}>
      {Array.from({ length: rows }).map((_, index) => (
        <div
          key={index}
          className="flex items-center h-16 px-4 gap-4 border-b last:border-b-0"
          style={staggered ? { animationDelay: `${index * 0.1}s` } : {}}
        >
          <Skeleton className="h-4 w-4" />
          <div className="space-y-1">
            <Skeleton className="h-4 w-40" />
            <Skeleton className="h-3 w-24" />
          </div>
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-28" />
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-24" />
          <div className="flex items-center gap-1">
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8 rounded" />
            <Skeleton className="h-8 w-8 rounded" />
            <Skeleton className="h-8 w-8 rounded" />
          </div>
        </div>
      ))}
    </div>
  </div>
))
ContractTableSkeleton.displayName = "ContractTableSkeleton"

const ContractGridSkeleton = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { items?: number; staggered?: boolean }
>(({ className, items = 6, staggered = true, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", staggered ? "stagger-children" : "", className)}
    {...props}
  >
    {Array.from({ length: items }).map((_, index) => (
      <div
        key={index}
        className="border rounded-lg p-4 space-y-3"
        style={staggered ? { animationDelay: `${index * 0.1}s` } : {}}
      >
        <div className="flex items-start justify-between">
          <div className="space-y-2 flex-1">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <Skeleton className="h-4 w-4" />
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4 rounded-full" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4 rounded-full" />
            <Skeleton className="h-4 w-20" />
          </div>
        </div>

        <div className="flex items-center justify-between pt-2">
          <Skeleton className="h-6 w-16 rounded-full" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8 rounded" />
            <Skeleton className="h-8 w-8 rounded" />
          </div>
        </div>
      </div>
    ))}
  </div>
))
ContractGridSkeleton.displayName = "ContractGridSkeleton"

const SearchFilterSkeleton = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col md:flex-row spacing-tight mb-5 items-center", className)}
    {...props}
  >
    <div className="relative flex-1">
      <Skeleton className="h-9 w-full rounded-md" />
    </div>
    <div className="flex items-center gap-2">
      <div className="flex border rounded-md">
        <Skeleton className="h-8 w-8 rounded-r-none" />
        <Skeleton className="h-8 w-8 rounded-l-none" />
      </div>
      <Skeleton className="h-8 w-16 rounded" />
      <Skeleton className="h-8 w-16 rounded" />
    </div>
  </div>
))
SearchFilterSkeleton.displayName = "SearchFilterSkeleton"

const TabsSkeleton = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("w-full max-w-3xl mb-5", className)}
    {...props}
  >
    <div className="grid grid-cols-5 gap-1 p-1 bg-muted rounded-lg">
      {Array.from({ length: 5 }).map((_, index) => (
        <Skeleton key={index} className="h-9 rounded-md" />
      ))}
    </div>
  </div>
))
TabsSkeleton.displayName = "TabsSkeleton"

const PageHeaderSkeleton = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center justify-between mb-6", className)}
    {...props}
  >
    <Skeleton className="h-7 w-48" />
    <Skeleton className="h-9 w-32 rounded" />
  </div>
))
PageHeaderSkeleton.displayName = "PageHeaderSkeleton"

const ContractDetailsSkeleton = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("space-y-6", className)}
    {...props}
  >
    {/* Header */}
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <Skeleton className="h-8 w-8 rounded" />
        <div className="space-y-1">
          <Skeleton className="h-6 w-64" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Skeleton className="h-9 w-20 rounded" />
        <Skeleton className="h-9 w-24 rounded" />
        <Skeleton className="h-9 w-20 rounded" />
        <Skeleton className="h-9 w-20 rounded" />
      </div>
    </div>

    {/* Tabs */}
    <div className="space-y-4">
      <div className="flex space-x-1 border-b">
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton key={index} className="h-10 w-20 rounded-t" />
        ))}
      </div>

      {/* Content sections */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-4">
          <Skeleton className="h-96 w-full rounded-lg" />
        </div>
        <div className="space-y-4">
          <div className="border rounded-lg p-4 space-y-3">
            <Skeleton className="h-5 w-24" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </div>
          <div className="border rounded-lg p-4 space-y-3">
            <Skeleton className="h-5 w-20" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
))
ContractDetailsSkeleton.displayName = "ContractDetailsSkeleton"

export {
  Skeleton,
  SkeletonCard,
  SkeletonTable,
  SkeletonList,
  SkeletonForm,
  ContractTableSkeleton,
  ContractGridSkeleton,
  SearchFilterSkeleton,
  TabsSkeleton,
  PageHeaderSkeleton,
  ContractDetailsSkeleton
}
