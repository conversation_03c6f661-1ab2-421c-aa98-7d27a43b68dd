import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface ResponsiveLayoutProps {
  children: ReactNode;
  className?: string;
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  className,
}) => {
  return (
    <div className={cn("container mx-auto px-4 sm:px-6 lg:px-8", className)}>
      {children}
    </div>
  );
};

interface ResponsiveGridProps {
  children: ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  className?: string;
}

const gapClasses = {
  none: "",
  xs: "gap-1",
  sm: "gap-2",
  md: "gap-4",
  lg: "gap-6",
  xl: "gap-8",
};

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 4,
  },
  gap = "md",
  className,
}) => {
  const gridClasses = cn(
    "grid",
    gap !== "none" && gapClasses[gap],
    columns.xs === 1 && "grid-cols-1",
    columns.xs === 2 && "grid-cols-2",
    columns.xs === 3 && "grid-cols-3",
    columns.xs === 4 && "grid-cols-4",
    columns.sm === 1 && "sm:grid-cols-1",
    columns.sm === 2 && "sm:grid-cols-2",
    columns.sm === 3 && "sm:grid-cols-3",
    columns.sm === 4 && "sm:grid-cols-4",
    columns.md === 1 && "md:grid-cols-1",
    columns.md === 2 && "md:grid-cols-2",
    columns.md === 3 && "md:grid-cols-3",
    columns.md === 4 && "md:grid-cols-4",
    columns.lg === 1 && "lg:grid-cols-1",
    columns.lg === 2 && "lg:grid-cols-2",
    columns.lg === 3 && "lg:grid-cols-3",
    columns.lg === 4 && "lg:grid-cols-4",
    columns.xl === 1 && "xl:grid-cols-1",
    columns.xl === 2 && "xl:grid-cols-2",
    columns.xl === 3 && "xl:grid-cols-3",
    columns.xl === 4 && "xl:grid-cols-4",
    className
  );

  return <div className={gridClasses}>{children}</div>;
};

interface ResponsiveStackProps {
  children: ReactNode;
  direction?: "row" | "column";
  spacing?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  wrap?: boolean;
  align?: "start" | "center" | "end" | "stretch" | "baseline";
  justify?: "start" | "center" | "end" | "between" | "around" | "evenly";
  className?: string;
  responsive?: boolean;
}

export const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  direction = "column",
  spacing = "md",
  wrap = false,
  align,
  justify,
  className,
  responsive = false,
}) => {
  const alignmentClasses = {
    start: "items-start",
    center: "items-center",
    end: "items-end",
    stretch: "items-stretch",
    baseline: "items-baseline",
  };

  const justifyClasses = {
    start: "justify-start",
    center: "justify-center",
    end: "justify-end",
    between: "justify-between",
    around: "justify-around",
    evenly: "justify-evenly",
  };

  const classes = cn(
    "flex",
    direction === "row" ? "flex-row" : "flex-col",
    responsive && direction === "row" ? "flex-col md:flex-row" : "",
    responsive && direction === "column" ? "flex-col" : "",
    wrap && "flex-wrap",
    spacing !== "none" && gapClasses[spacing],
    align && alignmentClasses[align],
    justify && justifyClasses[justify],
    className
  );

  return <div className={classes}>{children}</div>;
};

interface ResponsiveSectionProps {
  children: ReactNode;
  title?: string;
  description?: string;
  className?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  contentClassName?: string;
  id?: string;
}

export const ResponsiveSection: React.FC<ResponsiveSectionProps> = ({
  children,
  title,
  description,
  className,
  titleClassName,
  descriptionClassName,
  contentClassName,
  id,
}) => {
  return (
    <section id={id} className={cn("py-6 md:py-8", className)}>
      {(title || description) && (
        <div className="mb-6">
          {title && (
            <h2 className={cn("text-lg font-medium mb-2", titleClassName)}>
              {title}
            </h2>
          )}
          {description && (
            <p className={cn("text-sm text-muted-foreground", descriptionClassName)}>
              {description}
            </p>
          )}
        </div>
      )}
      <div className={contentClassName}>{children}</div>
    </section>
  );
};
