import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Plus,
  BookOpen,
  Filter,
  Grid3X3,
  List,
  Sparkles,
  Clock,
  Zap,
  MoreHorizontal
} from 'lucide-react';

const ClauseLibraryShowcase = () => {
  const [activeTab, setActiveTab] = useState('Browse');

  // Mock clause library data matching the screenshot
  const clauseLibrary = [
    {
      id: 'liability-1',
      title: 'Standard Limitation of Liability',
      content: 'In no event shall either party be liable to the other party for any indirect, special, incidental, consequential, or punitive damages, including...',
      category: 'Liability',
      tags: ['liability', 'limitation', 'standard', 'risk-mitigation'],
      version: 'v2.0',
      updated: '2023-05-15',
      riskLevel: 'medium'
    },
    {
      id: 'liability-2', 
      title: 'Enhanced Limitation of Liability',
      content: 'Neither party shall be liable for any indirect, special, incidental, consequential, punitive, or exemplary damages, including, but not limited t...',
      category: 'Liability',
      tags: ['liability', 'enhanced', 'high-risk'],
      version: 'v2.0',
      updated: '2023-04-22',
      riskLevel: 'high'
    },
    {
      id: 'confidentiality-1',
      title: 'Basic Confidentiality Clause', 
      content: 'During the term of this Agreement and for a period of three (3) years thereafter, each party shall maintain in confidence all Confidential...',
      category: 'Confidentiality',
      tags: ['confidentiality', 'nda', 'standard'],
      version: 'v1.0',
      updated: '2023-04-15',
      riskLevel: 'low'
    },
    {
      id: 'confidentiality-2',
      title: 'Extended Confidentiality Clause',
      content: 'During the term of this Agreement and for a period of five (5) years thereafter, each party shall maintain in confidence all Confidential...',
      category: 'Confidentiality', 
      tags: ['confidentiality', 'rule', 'extended', 'sensitive'],
      version: 'v1.5',
      updated: '2023-07-03',
      riskLevel: 'medium'
    },
    {
      id: 'termination-1',
      title: 'Standard Termination Clause',
      content: 'Either party may terminate this Agreement upon thirty (30) days prior written notice to the other party if the other party materially breaches this...',
      category: 'Termination',
      tags: ['termination', 'standard', 'breach'],
      version: 'v1.1',
      updated: '2023-03-18',
      riskLevel: 'low'
    },
    {
      id: 'payment-1',
      title: 'Payment Terms (Net-30)',
      content: 'Customer shall pay all invoices within thirty (30) days of receipt. Overdue payments shall bear interest at the rate of 1.5% per month or the...',
      category: 'Payment',
      tags: ['payment', 'financial', 'standard'],
      version: 'v1.0',
      updated: '2023-05-12',
      riskLevel: 'low'
    },
    {
      id: 'force-majeure-1',
      title: 'Force Majeure',
      content: 'Neither party shall be liable for any failure or delay in performance under this Agreement due to circumstances beyond its reasonable control...',
      category: 'General',
      tags: ['general', 'standard', 'risk-mitigation'],
      version: 'v1.3',
      updated: '2023-02-14',
      riskLevel: 'medium'
    },
    {
      id: 'gdpr-1',
      title: 'GDPR Compliance Clause',
      content: 'Each party shall comply with all applicable data protection and privacy laws, including the General Data Protection Regulation (GDPR)...',
      category: 'Compliance',
      tags: ['compliance', 'gdpr', 'data-protection', 'regulatory'],
      version: 'v2.1',
      updated: '2023-06-30',
      riskLevel: 'high'
    }
  ];

  const tabs = ['Browse', 'Favorites', 'Recently Used', 'Organize', 'Smart Suggestions'];


  return (
    <section className="py-16 sm:py-24 relative">
      {/* Solid white backdrop to isolate from gradient background */}
      <div className="absolute inset-0 bg-white z-0"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-4">
            <BookOpen className="w-4 h-4" />
            Clause Library
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight mb-6">
            Comprehensive 
            <span className="block text-primary">Legal Clause Library</span>
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto">
            Browse our extensive collection of pre-approved legal clauses. Each clause is 
            risk-assessed, rated by usage, and optimized for different jurisdictions and industries.
          </p>
        </div>

        {/* Mockup Window */}
        <div className="bg-white rounded-xl shadow-2xl overflow-hidden border border-gray-200">
          {/* Apple-style Window Header */}
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex items-center gap-2">
            <div className="flex gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div className="flex-1 text-center">
              <span className="text-sm font-medium text-gray-600">Clause Library</span>
            </div>
          </div>

          {/* Main Content */}
          <div className="h-[600px] bg-white">
            {/* Main Content Area */}
            <div className="flex flex-col h-full bg-white">
              {/* Header */}
              <div className="p-6 border-b border-gray-200 bg-white">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">Clause Library</h2>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Filter className="w-4 h-4 mr-2" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Grid3X3 className="w-4 h-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <List className="w-4 h-4" />
                    </Button>
                    <Button size="sm">
                      <Plus className="w-4 h-4 mr-2" />
                      New Clause
                    </Button>
                  </div>
                </div>

                {/* Tabs */}
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-4">
                  {tabs.map((tab) => (
                    <button
                      key={tab}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        activeTab === tab
                          ? 'bg-white text-gray-900 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900'
                      }`}
                      onClick={() => setActiveTab(tab)}
                    >
                      {tab === 'Smart Suggestions' && <Sparkles className="h-4 w-4 inline mr-1" />}
                      {tab === 'Recently Used' && <Clock className="h-4 w-4 inline mr-1" />}
                      {tab === 'Organize' && <Zap className="h-4 w-4 inline mr-1" />}
                      {tab}
                    </button>
                  ))}
                </div>
              </div>

              {/* Clause Cards Grid */}
              <div className="flex-1 p-6 overflow-y-auto bg-white">

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {clauseLibrary.map((clause) => (
                    <div key={clause.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow bg-white shadow-sm">
                      <div className="space-y-3">
                        {/* Clause Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-sm mb-1">{clause.title}</h4>
                            <div className="flex items-center gap-2 mb-2">
                              {clause.tags.slice(0, 3).map(tag => {
                                const tagColors = {
                                  'liability': 'bg-red-100 text-red-700',
                                  'confidentiality': 'bg-blue-100 text-blue-700', 
                                  'termination': 'bg-orange-100 text-orange-700',
                                  'payment': 'bg-green-100 text-green-700',
                                  'compliance': 'bg-yellow-100 text-yellow-700',
                                  'general': 'bg-gray-100 text-gray-700'
                                };
                                const colorClass = tagColors[tag as keyof typeof tagColors] || 'bg-gray-100 text-gray-700';
                                return (
                                  <Badge key={tag} className={`text-xs px-2 py-0.5 ${colorClass}`}>
                                    {tag}
                                  </Badge>
                                );
                              })}
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </div>

                        {/* Clause Content Preview */}
                        <div className="text-xs text-muted-foreground line-clamp-3">
                          {clause.content}
                        </div>

                        {/* Clause Footer */}
                        <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
                          <span>{clause.version}</span>
                          <span>Updated: {clause.updated}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Highlights */}
        <div className="grid md:grid-cols-3 gap-6 mt-12">
          <div className="text-center p-6">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <BookOpen className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">Organized Library</h3>
            <p className="text-sm text-muted-foreground">
              Browse, search, and organize clauses with smart categorization and tagging
            </p>
          </div>
          <div className="text-center p-6">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">Smart Suggestions</h3>
            <p className="text-sm text-muted-foreground">
              AI-powered recommendations based on your contract type and requirements
            </p>
          </div>
          <div className="text-center p-6">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="w-6 h-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">Usage Tracking</h3>
            <p className="text-sm text-muted-foreground">
              Track recently used clauses and favorites for quick access
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClauseLibraryShowcase;
