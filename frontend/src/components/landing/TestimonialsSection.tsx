import { <PERSON>uote, <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { useState } from 'react';

const TestimonialsSection = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const testimonials = [
    {
      quote: "Averum Contracts has revolutionized our contract management process. The AI analysis catches issues we might have missed, and the collaboration features have streamlined our workflow significantly.",
      author: "<PERSON>",
      role: "Legal Director",
      company: "TechCorp",
      metric: "75% faster contract creation",
      avatar: "SJ"
    },
    {
      quote: "The contract wizard is incredibly intuitive. What used to take hours now takes minutes. The template library covers all our needs perfectly.",
      author: "<PERSON>",
      role: "Senior Counsel",
      company: "FinanceFirst",
      metric: "90% reduction in errors",
      avatar: "MR"
    },
    {
      quote: "Security and compliance were our top concerns. Averum Contracts exceeded our expectations with bank-grade security and comprehensive audit trails.",
      author: "<PERSON>",
      role: "Chief Legal Officer",
      company: "HealthTech",
      metric: "100% compliance assurance",
      avatar: "EC"
    }
  ];

  return (
    <section id="testimonials" className="py-16 sm:py-24 lg:py-32 relative">
      {/* Removed competing background to use unified background */}

      <div className="relative px-4 sm:px-6 lg:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16 lg:mb-20">
            <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-2 bg-primary/10 rounded-full text-xs sm:text-sm font-medium text-primary mb-4 sm:mb-6">
              <Sparkles className="w-3 sm:w-4 h-3 sm:h-4" />
              Customer Stories
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 sm:mb-8 lg:mb-10 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-[1.15] sm:leading-[1.15] md:leading-[1.15] lg:leading-[1.15] xl:leading-[1.15] pt-2">
              See what our customers say
            </h2>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed px-4 sm:px-0">
              Real results from legal teams who trust Averum Contracts for their contract management needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {testimonials.map((testimonial, index) => {
              const isHovered = hoveredIndex === index;
              return (
                <div
                  key={index}
                  className={`group p-6 sm:p-8 rounded-xl sm:rounded-2xl border border-border bg-card transition-all duration-300 hover:shadow-xl hover:scale-105 ${
                    isHovered ? 'shadow-xl scale-105' : 'shadow-sm'
                  }`}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  {/* Quote icon */}
                  <div className="flex justify-center mb-4 sm:mb-6">
                    <div className="w-10 sm:w-12 h-10 sm:h-12 bg-muted rounded-xl sm:rounded-2xl flex items-center justify-center border border-border">
                      <Quote className="w-5 sm:w-6 h-5 sm:h-6 text-muted-foreground" />
                    </div>
                  </div>

                  {/* Stars */}
                  <div className="flex justify-center gap-1 mb-4 sm:mb-6">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 sm:w-5 h-4 sm:h-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>

                  {/* Metric */}
                  <div className="text-center mb-4 sm:mb-6">
                    <div className="text-2xl sm:text-3xl font-bold mb-2 leading-[1.2]">{testimonial.metric}</div>
                  </div>

                  {/* Quote */}
                  <p className="text-sm sm:text-base text-muted-foreground leading-relaxed mb-6 sm:mb-8 text-center italic">
                    "{testimonial.quote}"
                  </p>

                  {/* Author */}
                  <div className="flex items-center gap-3 sm:gap-4">
                    <div className="w-10 sm:w-12 h-10 sm:h-12 bg-background/80 backdrop-blur-sm rounded-full flex items-center justify-center border flex-shrink-0">
                      <span className="text-sm sm:text-base font-bold">{testimonial.avatar}</span>
                    </div>
                    <div className="min-w-0">
                      <p className="text-sm sm:text-base font-bold truncate leading-[1.3]">{testimonial.author}</p>
                      <p className="text-xs sm:text-sm text-muted-foreground truncate leading-relaxed">{testimonial.role}, {testimonial.company}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
