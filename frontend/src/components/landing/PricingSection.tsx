import { Button } from '@/components/ui/button';
import { CheckCircle, Sparkles, Crown, Building, Zap, Users, Bot, Shield, Clock, Star } from 'lucide-react';
import { useState } from 'react';

interface PricingSectionProps {
  onGetStarted: () => void;
}

const PricingSection = ({ onGetStarted }: PricingSectionProps) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');

  const plans = [
    {
      name: "Starter",
      price: billingCycle === 'monthly' ? "$29" : "$24",
      originalPrice: billingCycle === 'monthly' ? null : "$29",
      period: billingCycle === 'monthly' ? "per month" : "per month, billed annually",
      description: "Perfect for individual users and freelancers",
      targetAudience: "Solo practitioners, freelancers, small businesses",
      features: [
        "1 workspace only",
        "Single user (no team members)",
        "25 AI analysis requests/month",
        "10 contracts per month",
        "Basic contract templates (15+)",
        "Standard e-signature",
        "Email support",
        "Document storage (5GB)",
        "Basic reporting dashboard",
        "Mobile app access"
      ],
      limitations: [
        "No team collaboration",
        "Limited AI requests",
        "Basic templates only"
      ],
      icon: Sparkles,
      highlight: "Great for getting started",
      ctaText: "Start Free Trial",
      savings: billingCycle === 'annual' ? "Save 17%" : null
    },
    {
      name: "Professional",
      price: billingCycle === 'monthly' ? "$79" : "$65",
      originalPrice: billingCycle === 'monthly' ? null : "$79",
      period: billingCycle === 'monthly' ? "per user/month" : "per user/month, billed annually",
      description: "For growing teams and legal departments",
      targetAudience: "Small to medium teams, growing businesses",
      features: [
        "Unlimited workspaces",
        "Up to 15 team members",
        "200 AI analysis requests/month",
        "Unlimited contracts",
        "Premium templates (100+)",
        "Advanced AI risk scoring",
        "Priority email & chat support",
        "Advanced approval workflows",
        "Document storage (100GB)",
        "Advanced analytics & insights",
        "Team collaboration tools",
        "Custom clause library",
        "Bulk operations",
        "API access (basic)"
      ],
      limitations: [],
      isPopular: true,
      icon: Crown,
      highlight: "Most popular choice",
      ctaText: "Start Free Trial",
      savings: billingCycle === 'annual' ? "Save 18%" : null
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "Contact for pricing",
      description: "For large organizations with complex needs",
      targetAudience: "Large enterprises, law firms, corporations",
      features: [
        "Unlimited everything",
        "Unlimited team members",
        "Unlimited AI requests",
        "Custom AI model training",
        "White-label solutions",
        "24/7 dedicated support",
        "Advanced security & compliance",
        "Unlimited document storage",
        "Custom integrations & API",
        "On-premise deployment",
        "Advanced analytics & reporting",
        "Custom contract templates",
        "SLA guarantees",
        "Dedicated account manager",
        "Custom training & onboarding",
        "Advanced audit trails"
      ],
      limitations: [],
      icon: Building,
      highlight: "Maximum flexibility & control",
      ctaText: "Contact Sales",
      isEnterprise: true
    }
  ];

  return (
    <section id="pricing" className="py-16 sm:py-24 lg:py-32 relative">
      {/* Removed competing background to use unified background */}

      <div className="relative px-4 sm:px-6 lg:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16 lg:mb-20">
            <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-2 bg-primary/10 rounded-full text-xs sm:text-sm font-medium text-primary mb-4 sm:mb-6">
              <Sparkles className="w-3 sm:w-4 h-3 sm:h-4" />
              Pricing Plans
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 sm:mb-8 lg:mb-10 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent leading-[1.15] sm:leading-[1.15] md:leading-[1.15] lg:leading-[1.15] xl:leading-[1.15] pt-2">
              Simple, transparent pricing
            </h2>
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed px-4 sm:px-0">
              Choose the plan that fits your needs. All plans include our core features with no hidden fees and 7-day free trial.
            </p>
          </div>

          {/* Billing Toggle */}
          <div className="flex justify-center mb-8 sm:mb-12">
            <div className="bg-muted p-1 rounded-xl border border-border">
              <div className="flex">
                <button
                  onClick={() => setBillingCycle('monthly')}
                  className={`px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-medium rounded-lg transition-all duration-200 ${
                    billingCycle === 'monthly'
                      ? 'bg-background text-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setBillingCycle('annual')}
                  className={`px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-medium rounded-lg transition-all duration-200 relative ${
                    billingCycle === 'annual'
                      ? 'bg-background text-foreground shadow-sm'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  Annual
                  <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full font-bold">
                    Save up to 18%
                  </span>
                </button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 max-w-6xl mx-auto">
            {plans.map((plan, index) => {
              const IconComponent = plan.icon;
              const isHovered = hoveredIndex === index;
              return (
                <div
                  key={index}
                  className={`group relative rounded-xl sm:rounded-2xl p-4 sm:p-6 border-2 transition-all duration-300 hover:shadow-xl hover:scale-[1.01] ${
                    plan.isPopular
                      ? 'border-primary shadow-lg lg:scale-[1.01] bg-card'
                      : 'border-border shadow-md bg-card'
                  } ${isHovered ? 'shadow-xl lg:scale-[1.01]' : ''}`}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  {plan.isPopular && (
                    <div className="absolute -top-2 sm:-top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-primary text-primary-foreground text-xs px-3 sm:px-4 py-1 sm:py-1.5 rounded-full font-bold shadow-md">
                        Most Popular
                      </span>
                    </div>
                  )}

                  {plan.savings && (
                    <div className="absolute -top-1 -right-1">
                      <span className="bg-green-500 text-white text-xs px-2 py-0.5 rounded-full font-bold shadow-md">
                        {plan.savings}
                      </span>
                    </div>
                  )}

                  {/* Icon */}
                  <div className="flex justify-center mb-3 sm:mb-4">
                    <div className={`w-10 sm:w-12 h-10 sm:h-12 rounded-lg sm:rounded-xl flex items-center justify-center transition-all duration-300 ${
                      isHovered ? 'scale-105 shadow-md' : ''
                    } bg-muted border border-border`}>
                      <IconComponent className={`w-5 sm:w-6 h-5 sm:h-6 ${plan.isPopular ? 'text-primary' : 'text-foreground'}`} />
                    </div>
                  </div>

                  <div className="text-center mb-4 sm:mb-5">
                    <h3 className="text-lg sm:text-xl font-bold mb-1 leading-[1.3]">{plan.name}</h3>
                    <p className="text-xs sm:text-sm text-muted-foreground mb-1 px-1 leading-relaxed">{plan.description}</p>
                    {plan.highlight && (
                      <p className="text-xs text-primary font-medium mb-2 sm:mb-3 leading-relaxed">{plan.highlight}</p>
                    )}
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-[1.2]">{plan.price}</div>
                      {plan.originalPrice && (
                        <div className="text-sm sm:text-base text-muted-foreground line-through">{plan.originalPrice}</div>
                      )}
                    </div>
                    <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">{plan.period}</p>
                    {plan.targetAudience && (
                      <p className="text-xs text-muted-foreground/80 mt-1 italic leading-relaxed">{plan.targetAudience}</p>
                    )}
                  </div>

                  <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
                    {/* Features */}
                    <div>
                      <h4 className="text-xs sm:text-sm font-semibold text-foreground mb-2 flex items-center gap-1">
                        <Star className="w-3 h-3 text-primary" />
                        What's included:
                      </h4>
                      <ul className="space-y-1 sm:space-y-2">
                        {plan.features.slice(0, 4).map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start gap-2">
                            <div className="w-4 sm:w-5 h-4 sm:h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                              <CheckCircle className="w-2.5 sm:w-3 h-2.5 sm:h-3 text-green-600 dark:text-green-400" />
                            </div>
                            <span className="text-xs text-muted-foreground leading-relaxed">{feature}</span>
                          </li>
                        ))}
                        {plan.features.length > 4 && (
                          <li className="flex items-center gap-2 pt-1">
                            <span className="text-xs text-muted-foreground/60 italic">
                              +{plan.features.length - 4} more features (see comparison below)
                            </span>
                          </li>
                        )}
                      </ul>
                    </div>


                  </div>

                  <Button
                    variant={plan.isPopular ? "default" : "outline"}
                    size="lg"
                    className="w-full text-sm sm:text-base lg:text-lg py-4 sm:py-6 h-auto transition-all duration-300 hover:scale-105 font-medium"
                    onClick={plan.isEnterprise ? undefined : onGetStarted}
                  >
                    {plan.ctaText}
                  </Button>
                </div>
              );
            })}
          </div>

          {/* Feature Comparison Table */}
          <div className="mt-16 sm:mt-20 lg:mt-24">
            <div className="text-center mb-8 sm:mb-12">
              <h3 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                Compare Plans
              </h3>
              <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto">
                See exactly what's included in each plan to make the best choice for your needs.
              </p>
            </div>

            <div className="bg-card border border-border rounded-2xl overflow-hidden shadow-lg">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border bg-muted/50">
                      <th className="text-left p-4 sm:p-6 font-semibold text-foreground">Features</th>
                      <th className="text-center p-4 sm:p-6 font-semibold text-foreground min-w-[120px]">Starter</th>
                      <th className="text-center p-4 sm:p-6 font-semibold text-foreground min-w-[120px] bg-primary/10">
                        Professional
                        <div className="text-xs text-primary font-normal mt-1">Most Popular</div>
                      </th>
                      <th className="text-center p-4 sm:p-6 font-semibold text-foreground min-w-[120px]">Enterprise</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { feature: "Workspaces", starter: "1", professional: "Unlimited", enterprise: "Unlimited" },
                      { feature: "Team Members", starter: "1 (solo)", professional: "Up to 15", enterprise: "Unlimited" },
                      { feature: "AI Analysis Requests", starter: "25/month", professional: "200/month", enterprise: "Unlimited" },
                      { feature: "Contracts per Month", starter: "10", professional: "Unlimited", enterprise: "Unlimited" },
                      { feature: "Document Storage", starter: "5GB", professional: "100GB", enterprise: "Unlimited" },
                      { feature: "Contract Templates", starter: "15+ Basic", professional: "100+ Premium", enterprise: "Custom + All" },
                      { feature: "E-Signature", starter: "✓", professional: "✓", enterprise: "✓" },
                      { feature: "Mobile App", starter: "✓", professional: "✓", enterprise: "✓" },
                      { feature: "Email Support", starter: "✓", professional: "✓", enterprise: "✓" },
                      { feature: "Priority Support", starter: "✗", professional: "✓", enterprise: "24/7 Dedicated" },
                      { feature: "Advanced AI Risk Scoring", starter: "✗", professional: "✓", enterprise: "✓" },
                      { feature: "Team Collaboration", starter: "✗", professional: "✓", enterprise: "✓" },
                      { feature: "Advanced Workflows", starter: "✗", professional: "✓", enterprise: "✓" },
                      { feature: "API Access", starter: "✗", professional: "Basic", enterprise: "Full" },
                      { feature: "Custom Integrations", starter: "✗", professional: "✗", enterprise: "✓" },
                      { feature: "White-label Options", starter: "✗", professional: "✗", enterprise: "✓" },
                      { feature: "On-premise Deployment", starter: "✗", professional: "✗", enterprise: "✓" },
                      { feature: "SLA Guarantees", starter: "✗", professional: "✗", enterprise: "✓" }
                    ].map((row, index) => (
                      <tr key={index} className={`border-b border-border ${index % 2 === 0 ? 'bg-background' : 'bg-muted/20'}`}>
                        <td className="p-4 sm:p-6 font-medium text-foreground">{row.feature}</td>
                        <td className="p-4 sm:p-6 text-center text-sm text-muted-foreground">
                          {row.starter === "✓" ? (
                            <CheckCircle className="w-5 h-5 text-green-500 mx-auto" />
                          ) : row.starter === "✗" ? (
                            <span className="text-muted-foreground/50">—</span>
                          ) : (
                            row.starter
                          )}
                        </td>
                        <td className="p-4 sm:p-6 text-center text-sm text-muted-foreground bg-primary/5">
                          {row.professional === "✓" ? (
                            <CheckCircle className="w-5 h-5 text-green-500 mx-auto" />
                          ) : row.professional === "✗" ? (
                            <span className="text-muted-foreground/50">—</span>
                          ) : (
                            row.professional
                          )}
                        </td>
                        <td className="p-4 sm:p-6 text-center text-sm text-muted-foreground">
                          {row.enterprise === "✓" ? (
                            <CheckCircle className="w-5 h-5 text-green-500 mx-auto" />
                          ) : row.enterprise === "✗" ? (
                            <span className="text-muted-foreground/50">—</span>
                          ) : (
                            row.enterprise
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Value Propositions */}
          <div className="mt-16 sm:mt-20 lg:mt-24">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
              <div className="text-center p-6 sm:p-8 bg-card border border-border rounded-2xl">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h4 className="text-lg font-semibold mb-2">7-Day Free Trial</h4>
                <p className="text-sm text-muted-foreground">
                  Try all features risk-free. No credit card required. Cancel anytime.
                </p>
              </div>

              <div className="text-center p-6 sm:p-8 bg-card border border-border rounded-2xl">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="text-lg font-semibold mb-2">No Setup Fees</h4>
                <p className="text-sm text-muted-foreground">
                  Get started immediately. No hidden costs, setup fees, or long-term contracts.
                </p>
              </div>

              <div className="text-center p-6 sm:p-8 bg-card border border-border rounded-2xl">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h4 className="text-lg font-semibold mb-2">Easy Upgrades</h4>
                <p className="text-sm text-muted-foreground">
                  Scale seamlessly as you grow. Upgrade or downgrade your plan anytime.
                </p>
              </div>
            </div>
          </div>

          {/* ROI Section */}
          <div className="mt-16 sm:mt-20 lg:mt-24 text-center">
            <div className="bg-gradient-to-br from-primary/10 via-primary/5 to-transparent p-8 sm:p-12 rounded-3xl border border-primary/20">
              <h3 className="text-2xl sm:text-3xl font-bold mb-4 text-foreground">
                Save Time, Reduce Costs
              </h3>
              <p className="text-base sm:text-lg text-muted-foreground mb-8 max-w-3xl mx-auto">
                Our customers typically save 15+ hours per week on contract management and reduce legal review costs by 60%.
                The Professional plan pays for itself in just one week.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-4xl mx-auto">
                <div className="text-center">
                  <div className="text-3xl sm:text-4xl font-bold text-primary mb-2">15+</div>
                  <div className="text-sm text-muted-foreground">Hours saved per week</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl sm:text-4xl font-bold text-primary mb-2">60%</div>
                  <div className="text-sm text-muted-foreground">Reduction in legal costs</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl sm:text-4xl font-bold text-primary mb-2">1 Week</div>
                  <div className="text-sm text-muted-foreground">Typical payback period</div>
                </div>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mt-16 sm:mt-20 lg:mt-24">
            <div className="text-center mb-8 sm:mb-12">
              <h3 className="text-2xl sm:text-3xl font-bold mb-4">Frequently Asked Questions</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 max-w-5xl mx-auto">
              <div className="p-6 bg-card border border-border rounded-xl">
                <h4 className="font-semibold mb-2">Can I change plans anytime?</h4>
                <p className="text-sm text-muted-foreground">
                  Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences.
                </p>
              </div>
              <div className="p-6 bg-card border border-border rounded-xl">
                <h4 className="font-semibold mb-2">What happens to my data if I cancel?</h4>
                <p className="text-sm text-muted-foreground">
                  You can export all your data anytime. We keep your data for 30 days after cancellation to allow for easy reactivation.
                </p>
              </div>
              <div className="p-6 bg-card border border-border rounded-xl">
                <h4 className="font-semibold mb-2">Do you offer discounts for nonprofits?</h4>
                <p className="text-sm text-muted-foreground">
                  Yes, we offer special pricing for qualified nonprofits and educational institutions. Contact our sales team for details.
                </p>
              </div>
              <div className="p-6 bg-card border border-border rounded-xl">
                <h4 className="font-semibold mb-2">Is my data secure?</h4>
                <p className="text-sm text-muted-foreground">
                  Absolutely. We use enterprise-grade security with SOC 2 compliance, end-to-end encryption, and regular security audits.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
