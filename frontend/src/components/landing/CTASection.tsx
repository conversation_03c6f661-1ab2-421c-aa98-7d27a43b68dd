import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, CheckCircle } from 'lucide-react';

interface CTASectionProps {
  onGetStarted: () => void;
}

const CTASection = ({ onGetStarted }: CTASectionProps) => {
  return (
    <section className="py-16 sm:py-24 lg:py-32 bg-gradient-to-br from-primary via-primary to-primary/90 text-primary-foreground relative">
      {/* Enhanced background decoration - adjusted for mobile */}
      <div className="absolute top-1/4 right-1/4 w-64 sm:w-80 lg:w-96 h-64 sm:h-80 lg:h-96 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 left-1/4 w-48 sm:w-64 lg:w-80 h-48 sm:h-64 lg:h-80 bg-white/5 rounded-full blur-3xl"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[300px] sm:w-[400px] lg:w-[600px] h-[300px] sm:h-[400px] lg:h-[600px] bg-white/5 rounded-full blur-3xl"></div>

      <div className="relative px-4 sm:px-6 lg:px-12">
        <div className="max-w-6xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-2 bg-white/20 rounded-full text-xs sm:text-sm font-medium mb-6 sm:mb-8 backdrop-blur-sm">
            <Sparkles className="w-3 sm:w-4 h-3 sm:h-4" />
            Start Your Journey
          </div>

          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-bold mb-8 sm:mb-10 leading-[1.1] sm:leading-[1.1] md:leading-[1.1] lg:leading-[1.1] xl:leading-[1.1] 2xl:leading-[1.1] pt-2">
            Ready to transform your
            <span className="block">legal workflow?</span>
          </h2>

          <p className="text-base sm:text-lg md:text-xl lg:text-2xl mb-8 sm:mb-10 lg:mb-12 opacity-95 max-w-4xl mx-auto leading-relaxed px-4 sm:px-0">
            Join thousands of legal professionals who trust Averum Contracts for their contract management needs.
            Start your free trial today and experience the future of legal technology.
          </p>

          {/* Trust indicators */}
          <div className="flex flex-col sm:flex-row flex-wrap justify-center items-center gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-10 lg:mb-12 opacity-90">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 sm:w-5 h-4 sm:h-5" />
              <span className="text-sm sm:text-base lg:text-lg">7-day free trial</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 sm:w-5 h-4 sm:h-5" />
              <span className="text-sm sm:text-base lg:text-lg">No credit card required</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 sm:w-5 h-4 sm:h-5" />
              <span className="text-sm sm:text-base lg:text-lg">Cancel anytime</span>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center">
            <Button
              size="lg"
              onClick={onGetStarted}
              className="text-base sm:text-lg lg:text-xl px-8 sm:px-10 lg:px-12 py-4 sm:py-6 lg:py-8 h-auto font-semibold bg-background text-foreground hover:bg-background/90 shadow-2xl hover:scale-105 transition-all duration-300 w-full sm:w-auto"
            >
              Start free trial
              <ArrowRight className="w-4 sm:w-5 lg:w-6 h-4 sm:h-5 lg:h-6 ml-2 sm:ml-3" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
