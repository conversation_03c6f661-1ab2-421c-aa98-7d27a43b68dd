import { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';

interface UsePermissionOptions {
  /**
   * The workspace ID to check permissions against (defaults to current workspace)
   */
  workspaceId?: string;
  
  /**
   * Whether to skip the permission check
   */
  skip?: boolean;
}

/**
 * Hook to check if the current user has a specific permission
 * @param permissionId The permission ID to check
 * @param options Options for the permission check
 * @returns An object with the result of the permission check
 */
export function usePermission(
  permissionId: string,
  options: UsePermissionOptions = {}
) {
  const { workspaceId, skip = false } = options;
  const { user } = useUser();
  const { currentWorkspace, hasPermission } = useClerkWorkspace();
  
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    const checkPermission = async () => {
      if (skip || !user) {
        setHasAccess(false);
        setIsLoading(false);
        return;
      }
      
      // Use the current workspace ID if none is provided
      const wsId = workspaceId || currentWorkspace?.id;
      
      if (!wsId) {
        setHasAccess(false);
        setIsLoading(false);
        return;
      }
      
      try {
        setIsLoading(true);
        const result = await hasPermission(wsId, user.id, permissionId);
        setHasAccess(result);
        setError(null);
      } catch (err) {
        console.error('Error checking permission:', err);
        setHasAccess(false);
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setIsLoading(false);
      }
    };
    
    checkPermission();
  }, [user, currentWorkspace, workspaceId, permissionId, hasPermission, skip]);
  
  return { hasPermission: hasAccess, isLoading, error };
}

/**
 * Hook to check if the current user has any of the specified permissions
 * @param permissionIds Array of permission IDs to check
 * @param options Options for the permission check
 * @returns An object with the result of the permission check
 */
export function useAnyPermission(
  permissionIds: string[],
  options: UsePermissionOptions = {}
) {
  const { workspaceId, skip = false } = options;
  const { user } = useUser();
  const { currentWorkspace, hasAnyPermission } = useClerkWorkspace();
  
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    const checkPermissions = async () => {
      if (skip || !user || permissionIds.length === 0) {
        setHasAccess(false);
        setIsLoading(false);
        return;
      }
      
      // Use the current workspace ID if none is provided
      const wsId = workspaceId || currentWorkspace?.id;
      
      if (!wsId) {
        setHasAccess(false);
        setIsLoading(false);
        return;
      }
      
      try {
        setIsLoading(true);
        const result = await hasAnyPermission(wsId, user.id, permissionIds);
        setHasAccess(result);
        setError(null);
      } catch (err) {
        console.error('Error checking permissions:', err);
        setHasAccess(false);
        setError(err instanceof Error ? err : new Error('Unknown error'));
      } finally {
        setIsLoading(false);
      }
    };
    
    checkPermissions();
  }, [user, currentWorkspace, workspaceId, permissionIds, hasAnyPermission, skip]);
  
  return { hasPermission: hasAccess, isLoading, error };
}
