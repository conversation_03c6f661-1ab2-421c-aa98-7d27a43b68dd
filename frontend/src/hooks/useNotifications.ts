import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@clerk/clerk-react';
import { NotificationService } from '@/services/api-services';
import { useToast } from '@/components/ui/use-toast';
import type {
  Notification,
  NotificationFilters,
  NotificationSummary,
  NotificationStatus,
  NotificationType
} from '@/services/api-types';

interface UseNotificationsOptions {
  workspaceId: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableRealTime?: boolean;
}

interface UseNotificationsReturn {
  notifications: Notification[];
  summary: NotificationSummary | null;
  loading: boolean;
  error: string | null;
  refreshNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  deleteAllNotifications: () => Promise<void>;
  filterNotifications: (filters: Partial<NotificationFilters>) => void;
  clearError: () => void;
}

export function useNotifications(options: UseNotificationsOptions): UseNotificationsReturn {
  const { workspaceId, autoRefresh = true, refreshInterval = 30000, enableRealTime = true } = options;
  const { getToken } = useAuth();
  const { toast } = useToast();
  
  // State
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [summary, setSummary] = useState<NotificationSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<Partial<NotificationFilters>>({});
  
  // Refs for cleanup
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  
  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    // Don't fetch if no valid workspace ID
    if (!workspaceId || workspaceId.trim() === '') {
      console.log('⏳ useNotifications: No valid workspace ID, skipping fetch');
      setNotifications([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const notificationFilters: NotificationFilters = {
        workspace_id: workspaceId,
        skip: 0,
        limit: 50,
        ...filters
      };

      const response = await NotificationService.getNotifications(notificationFilters);
      setNotifications(response.data);

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch notifications';
      setError(errorMessage);
      console.error('Error fetching notifications:', err);
    } finally {
      setLoading(false);
    }
  }, [workspaceId, filters]);
  
  // Fetch summary
  const fetchSummary = useCallback(async () => {
    // Don't fetch if no valid workspace ID
    if (!workspaceId || workspaceId.trim() === '') {
      console.log('⏳ useNotifications: No valid workspace ID, skipping summary fetch');
      setSummary(null);
      return;
    }

    try {
      const response = await NotificationService.getNotificationSummary(workspaceId);
      setSummary(response.data);
    } catch (err: any) {
      console.error('Error fetching notification summary:', err);
    }
  }, [workspaceId]);
  
  // Refresh notifications and summary
  const refreshNotifications = useCallback(async () => {
    await Promise.all([fetchNotifications(), fetchSummary()]);
  }, [fetchNotifications, fetchSummary]);
  
  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await NotificationService.markAsRead(notificationId);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, status: 'read' as NotificationStatus, read_at: new Date().toISOString() }
            : notification
        )
      );
      
      // Update summary
      setSummary(prev => prev ? {
        ...prev,
        unread_count: Math.max(0, prev.unread_count - 1)
      } : null);
      
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to mark notification as read';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [toast]);
  
  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    // Don't proceed if no valid workspace ID
    if (!workspaceId || workspaceId.trim() === '') {
      console.log('⏳ useNotifications: No valid workspace ID, cannot mark all as read');
      toast({
        title: "Error",
        description: "No workspace selected",
        variant: "destructive"
      });
      return;
    }

    try {
      await NotificationService.markAllAsRead(workspaceId);

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({
          ...notification,
          status: 'read' as NotificationStatus,
          read_at: notification.read_at || new Date().toISOString()
        }))
      );

      // Update summary
      setSummary(prev => prev ? { ...prev, unread_count: 0 } : null);

      toast({
        title: "Success",
        description: "All notifications marked as read"
      });

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to mark all notifications as read';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [workspaceId, toast]);
  
  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      await NotificationService.deleteNotification(notificationId);
      
      // Update local state
      const deletedNotification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(notification => notification.id !== notificationId));
      
      // Update summary
      setSummary(prev => {
        if (!prev) return null;
        const newSummary = { ...prev, total_count: Math.max(0, prev.total_count - 1) };
        if (deletedNotification?.status === 'unread') {
          newSummary.unread_count = Math.max(0, prev.unread_count - 1);
        }
        return newSummary;
      });
      
      toast({
        title: "Success",
        description: "Notification deleted"
      });
      
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete notification';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [notifications, toast]);
  
  // Delete all notifications
  const deleteAllNotifications = useCallback(async () => {
    // Don't proceed if no valid workspace ID
    if (!workspaceId || workspaceId.trim() === '') {
      console.log('⏳ useNotifications: No valid workspace ID, cannot delete all notifications');
      toast({
        title: "Error",
        description: "No workspace selected",
        variant: "destructive"
      });
      return;
    }

    try {
      await NotificationService.deleteAllNotifications(workspaceId);

      // Update local state
      setNotifications([]);
      setSummary(prev => prev ? { ...prev, total_count: 0, unread_count: 0, type_counts: {} } : null);

      toast({
        title: "Success",
        description: "All notifications deleted"
      });

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete all notifications';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [workspaceId, toast]);
  
  // Filter notifications
  const filterNotifications = useCallback((newFilters: Partial<NotificationFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);
  
  // Setup auto-refresh
  useEffect(() => {
    // Only setup auto-refresh if we have a valid workspace ID
    if (autoRefresh && refreshInterval > 0 && workspaceId && workspaceId.trim() !== '') {
      console.log('🔄 useNotifications: Setting up auto-refresh for workspace:', workspaceId);
      refreshIntervalRef.current = setInterval(refreshNotifications, refreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          console.log('🧹 useNotifications: Clearing auto-refresh interval');
          clearInterval(refreshIntervalRef.current);
        }
      };
    } else {
      // Clear any existing interval if workspace ID becomes invalid
      if (refreshIntervalRef.current) {
        console.log('🧹 useNotifications: Clearing auto-refresh interval due to invalid workspace ID');
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    }
  }, [autoRefresh, refreshInterval, workspaceId, refreshNotifications]);
  
  // Initial fetch
  useEffect(() => {
    if (workspaceId && workspaceId.trim() !== '') {
      console.log('🔄 useNotifications: Initial fetch for workspace:', workspaceId);
      refreshNotifications();
    } else {
      console.log('⏳ useNotifications: No valid workspace ID for initial fetch');
    }
  }, [workspaceId, refreshNotifications]);
  
  // Cleanup
  useEffect(() => {
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);
  
  return {
    notifications,
    summary,
    loading,
    error,
    refreshNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications,
    filterNotifications,
    clearError
  };
}
