import React, { useState, useCallback, useRef } from 'react';
import type { ModalAction, ModalType, ModalSize, ModalVariant } from '@/components/ui/unified-modal';

export interface ModalConfig {
  title?: string;
  description?: string;
  type?: ModalType;
  size?: ModalSize;
  variant?: ModalVariant;
  closable?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  mobileAsDrawer?: boolean;
  actions?: ModalAction[];
  primaryAction?: ModalAction;
  secondaryAction?: ModalAction;
  onClose?: () => void;
  onOpen?: () => void;
}

export interface ModalState {
  isOpen: boolean;
  config: ModalConfig;
  content: React.ReactNode;
  loading: boolean;
  loadingText: string;
}

export interface ModalControls {
  // Basic controls
  open: (content: React.ReactNode, config?: ModalConfig) => void;
  close: () => void;
  toggle: () => void;
  
  // Content management
  setContent: (content: React.ReactNode) => void;
  setConfig: (config: Partial<ModalConfig>) => void;
  
  // Loading state
  setLoading: (loading: boolean, loadingText?: string) => void;
  
  // Convenience methods
  confirm: (options: ConfirmOptions) => Promise<boolean>;
  alert: (options: AlertOptions) => Promise<void>;
  prompt: (options: PromptOptions) => Promise<string | null>;
  
  // State
  isOpen: boolean;
  config: ModalConfig;
  content: React.ReactNode;
  loading: boolean;
  loadingText: string;
}

export interface ConfirmOptions {
  title?: string;
  description?: string;
  confirmLabel?: string;
  cancelLabel?: string;
  variant?: ModalVariant;
  destructive?: boolean;
}

export interface AlertOptions {
  title?: string;
  description?: string;
  confirmLabel?: string;
  variant?: ModalVariant;
}

export interface PromptOptions {
  title?: string;
  description?: string;
  placeholder?: string;
  defaultValue?: string;
  confirmLabel?: string;
  cancelLabel?: string;
  required?: boolean;
  validator?: (value: string) => string | null; // Return error message or null if valid
}

const defaultConfig: ModalConfig = {
  type: 'dialog',
  size: 'md',
  variant: 'default',
  closable: true,
  closeOnOverlayClick: true,
  closeOnEscape: true,
  mobileAsDrawer: false,
};

export const useModal = (initialConfig?: ModalConfig): ModalControls => {
  const [state, setState] = useState<ModalState>({
    isOpen: false,
    config: { ...defaultConfig, ...initialConfig },
    content: null,
    loading: false,
    loadingText: 'Loading...',
  });

  const resolveRef = useRef<((value: any) => void) | null>(null);

  const open = useCallback((content: React.ReactNode, config?: ModalConfig) => {
    setState(prev => ({
      ...prev,
      isOpen: true,
      content,
      config: { ...prev.config, ...config },
    }));
  }, []);

  const close = useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: false,
    }));
    
    // Resolve any pending promises with null/false
    if (resolveRef.current) {
      resolveRef.current(null);
      resolveRef.current = null;
    }
  }, []);

  const toggle = useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: !prev.isOpen,
    }));
  }, []);

  const setContent = useCallback((content: React.ReactNode) => {
    setState(prev => ({
      ...prev,
      content,
    }));
  }, []);

  const setConfig = useCallback((config: Partial<ModalConfig>) => {
    setState(prev => ({
      ...prev,
      config: { ...prev.config, ...config },
    }));
  }, []);

  const setLoading = useCallback((loading: boolean, loadingText = 'Loading...') => {
    setState(prev => ({
      ...prev,
      loading,
      loadingText,
    }));
  }, []);

  const confirm = useCallback((options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      resolveRef.current = resolve;

      const config: ModalConfig = {
        type: 'alert',
        title: options.title || 'Confirm Action',
        description: options.description,
        variant: options.destructive ? 'destructive' : (options.variant || 'default'),
        primaryAction: {
          label: options.confirmLabel || 'Confirm',
          variant: options.destructive ? 'destructive' : 'default',
          onClick: () => {
            resolve(true);
            resolveRef.current = null;
            close();
          },
        },
        secondaryAction: {
          label: options.cancelLabel || 'Cancel',
          variant: 'outline',
          onClick: () => {
            resolve(false);
            resolveRef.current = null;
            close();
          },
        },
        onClose: () => {
          resolve(false);
          resolveRef.current = null;
        },
      };

      open(null, config);
    });
  }, [open, close]);

  const alert = useCallback((options: AlertOptions): Promise<void> => {
    return new Promise((resolve) => {
      resolveRef.current = resolve;

      const config: ModalConfig = {
        type: 'alert',
        title: options.title || 'Alert',
        description: options.description,
        variant: options.variant || 'default',
        primaryAction: {
          label: options.confirmLabel || 'OK',
          onClick: () => {
            resolve();
            resolveRef.current = null;
            close();
          },
        },
        onClose: () => {
          resolve();
          resolveRef.current = null;
        },
      };

      open(null, config);
    });
  }, [open, close]);

  const prompt = useCallback((options: PromptOptions): Promise<string | null> => {
    return new Promise((resolve) => {
      // For now, use browser's native prompt as a fallback
      // In a real implementation, you would create a custom input modal
      const result = window.prompt(
        options.description || options.title || 'Please enter a value:',
        options.defaultValue || ''
      );
      
      if (result === null) {
        resolve(null);
        return;
      }
      
      // Basic validation
      if (options.required && !result.trim()) {
        window.alert('This field is required');
        resolve(null);
        return;
      }
      
      if (options.validator) {
        const validationError = options.validator(result);
        if (validationError) {
          window.alert(validationError);
          resolve(null);
          return;
        }
      }
      
      resolve(result);
    });
  }, []);

  return {
    // Basic controls
    open,
    close,
    toggle,
    
    // Content management
    setContent,
    setConfig,
    
    // Loading state
    setLoading,
    
    // Convenience methods
    confirm,
    alert,
    prompt,
    
    // State
    isOpen: state.isOpen,
    config: state.config,
    content: state.content,
    loading: state.loading,
    loadingText: state.loadingText,
  };
};

// Convenience hook for simple modals
export const useSimpleModal = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  return {
    isOpen,
    open: () => setIsOpen(true),
    close: () => setIsOpen(false),
    toggle: () => setIsOpen(prev => !prev),
    onOpenChange: setIsOpen,
  };
};

export default useModal;
