import { useState, useCallback } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';
import { useAuditLogger, AuditEventType } from '@/lib/audit-logger';

interface UsePermissionCheckOptions {
  /**
   * The feature title to display in the access denied dialog
   */
  featureTitle?: string;
  
  /**
   * The workspace ID to check permissions against (defaults to current workspace)
   */
  workspaceId?: string;
  
  /**
   * Whether to log access attempts
   */
  logAccess?: boolean;
}

/**
 * Hook that provides a function to check permissions and returns state for an access denied dialog
 */
export function usePermissionCheck(options: UsePermissionCheckOptions = {}) {
  const { featureTitle, workspaceId, logAccess = true } = options;
  const { user } = useUser();
  const { currentWorkspace, hasPermission, hasAnyPermission } = useClerkWorkspace();
  const { logEvent } = useAuditLogger();
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogProps, setDialogProps] = useState<{
    featureTitle?: string;
    requiredPermission?: string;
    customMessage?: string;
  }>({});
  
  /**
   * Check if the user has the required permission and show a dialog if not
   * @param permissionId The permission ID to check
   * @param options Additional options
   * @returns A promise that resolves to true if the user has permission, false otherwise
   */
  const checkPermission = useCallback(async (
    permissionId: string,
    options: {
      featureTitle?: string;
      customMessage?: string;
      onSuccess?: () => void;
    } = {}
  ) => {
    if (!user) {
      setDialogProps({
        featureTitle: options.featureTitle || featureTitle,
        customMessage: 'You need to be logged in to access this feature.',
      });
      setIsDialogOpen(true);
      return false;
    }
    
    // Use the current workspace ID if none is provided
    const wsId = workspaceId || currentWorkspace?.id;
    
    if (!wsId) {
      setDialogProps({
        featureTitle: options.featureTitle || featureTitle,
        customMessage: 'You need to select a workspace to access this feature.',
      });
      setIsDialogOpen(true);
      return false;
    }
    
    try {
      const result = await hasPermission(wsId, user.id, permissionId);
      
      if (logAccess) {
        // Log the access attempt
        logEvent(
          result ? AuditEventType.ACCESS_GRANTED : AuditEventType.ACCESS_DENIED,
          {
            targetId: permissionId,
            targetType: 'permission',
            targetName: permissionId,
            details: {
              featureTitle: options.featureTitle || featureTitle,
              workspaceId: wsId,
            },
          }
        );
      }
      
      if (result) {
        // User has permission, call the success callback if provided
        if (options.onSuccess) {
          options.onSuccess();
        }
        return true;
      } else {
        // User doesn't have permission, show the dialog
        setDialogProps({
          featureTitle: options.featureTitle || featureTitle,
          requiredPermission: permissionId,
          customMessage: options.customMessage,
        });
        setIsDialogOpen(true);
        return false;
      }
    } catch (error) {
      console.error('Error checking permission:', error);
      
      // Show an error dialog
      setDialogProps({
        featureTitle: options.featureTitle || featureTitle,
        customMessage: 'An error occurred while checking permissions.',
      });
      setIsDialogOpen(true);
      return false;
    }
  }, [
    user,
    currentWorkspace,
    workspaceId,
    featureTitle,
    hasPermission,
    logAccess,
    logEvent,
  ]);
  
  /**
   * Check if the user has any of the required permissions and show a dialog if not
   * @param permissionIds The permission IDs to check
   * @param options Additional options
   * @returns A promise that resolves to true if the user has any permission, false otherwise
   */
  const checkAnyPermission = useCallback(async (
    permissionIds: string[],
    options: {
      featureTitle?: string;
      customMessage?: string;
      onSuccess?: () => void;
    } = {}
  ) => {
    if (!user) {
      setDialogProps({
        featureTitle: options.featureTitle || featureTitle,
        customMessage: 'You need to be logged in to access this feature.',
      });
      setIsDialogOpen(true);
      return false;
    }
    
    // Use the current workspace ID if none is provided
    const wsId = workspaceId || currentWorkspace?.id;
    
    if (!wsId) {
      setDialogProps({
        featureTitle: options.featureTitle || featureTitle,
        customMessage: 'You need to select a workspace to access this feature.',
      });
      setIsDialogOpen(true);
      return false;
    }
    
    try {
      const result = await hasAnyPermission(wsId, user.id, permissionIds);
      
      if (logAccess) {
        // Log the access attempt
        logEvent(
          result ? AuditEventType.ACCESS_GRANTED : AuditEventType.ACCESS_DENIED,
          {
            targetId: permissionIds.join(','),
            targetType: 'permission',
            targetName: permissionIds.join(', '),
            details: {
              featureTitle: options.featureTitle || featureTitle,
              workspaceId: wsId,
            },
          }
        );
      }
      
      if (result) {
        // User has permission, call the success callback if provided
        if (options.onSuccess) {
          options.onSuccess();
        }
        return true;
      } else {
        // User doesn't have permission, show the dialog
        setDialogProps({
          featureTitle: options.featureTitle || featureTitle,
          requiredPermission: permissionIds.join(' or '),
          customMessage: options.customMessage,
        });
        setIsDialogOpen(true);
        return false;
      }
    } catch (error) {
      console.error('Error checking permissions:', error);
      
      // Show an error dialog
      setDialogProps({
        featureTitle: options.featureTitle || featureTitle,
        customMessage: 'An error occurred while checking permissions.',
      });
      setIsDialogOpen(true);
      return false;
    }
  }, [
    user,
    currentWorkspace,
    workspaceId,
    featureTitle,
    hasAnyPermission,
    logAccess,
    logEvent,
  ]);
  
  return {
    checkPermission,
    checkAnyPermission,
    isDialogOpen,
    dialogProps,
    closeDialog: () => setIsDialogOpen(false),
  };
}
