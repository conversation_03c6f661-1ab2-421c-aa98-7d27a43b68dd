import { useState, useMemo, useCallback } from "react";
import type { SortConfig, SortDirection } from "@/components/ui/enhanced-table";

export interface UseTableStateOptions<T> {
  data: T[];
  getRowId?: (row: T) => string;
  defaultSort?: SortConfig;
  defaultSelected?: string[];
}

export interface UseTableStateReturn<T> {
  // Sorting
  sortConfig: SortConfig;
  setSortConfig: (config: SortConfig) => void;
  sortedData: T[];
  
  // Selection
  selectedRows: string[];
  setSelectedRows: (rows: string[]) => void;
  isRowSelected: (rowId: string) => boolean;
  toggleRowSelection: (rowId: string) => void;
  selectRow: (rowId: string) => void;
  deselectRow: (rowId: string) => void;
  selectAll: () => void;
  deselectAll: () => void;
  toggleSelectAll: () => void;
  
  // Computed states
  allSelected: boolean;
  someSelected: boolean;
  selectedCount: number;
  
  // Handlers for table component
  handleSort: (config: SortConfig) => void;
  handleRowSelect: (rowId: string, selected: boolean) => void;
  handleSelectAll: (selected: boolean) => void;
}

export function useTableState<T extends Record<string, any>>({
  data,
  getRowId = (row: T) => row.id,
  defaultSort = { key: "", direction: null },
  defaultSelected = [],
}: UseTableStateOptions<T>): UseTableStateReturn<T> {
  const [sortConfig, setSortConfig] = useState<SortConfig>(defaultSort);
  const [selectedRows, setSelectedRows] = useState<string[]>(defaultSelected);

  // Sort data based on current sort config
  const sortedData = useMemo(() => {
    if (!sortConfig.key || !sortConfig.direction) {
      return data;
    }

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortConfig.direction === "asc" ? 1 : -1;
      if (bValue == null) return sortConfig.direction === "asc" ? -1 : 1;

      // Handle different data types
      if (typeof aValue === "string" && typeof bValue === "string") {
        const comparison = aValue.localeCompare(bValue);
        return sortConfig.direction === "asc" ? comparison : -comparison;
      }

      if (typeof aValue === "number" && typeof bValue === "number") {
        const comparison = aValue - bValue;
        return sortConfig.direction === "asc" ? comparison : -comparison;
      }

      if (aValue instanceof Date && bValue instanceof Date) {
        const comparison = aValue.getTime() - bValue.getTime();
        return sortConfig.direction === "asc" ? comparison : -comparison;
      }

      // Fallback to string comparison
      const comparison = String(aValue).localeCompare(String(bValue));
      return sortConfig.direction === "asc" ? comparison : -comparison;
    });
  }, [data, sortConfig]);

  // Selection utilities
  const isRowSelected = useCallback((rowId: string) => {
    return selectedRows.includes(rowId);
  }, [selectedRows]);

  const toggleRowSelection = useCallback((rowId: string) => {
    setSelectedRows(prev => 
      prev.includes(rowId) 
        ? prev.filter(id => id !== rowId)
        : [...prev, rowId]
    );
  }, []);

  const selectRow = useCallback((rowId: string) => {
    setSelectedRows(prev => 
      prev.includes(rowId) ? prev : [...prev, rowId]
    );
  }, []);

  const deselectRow = useCallback((rowId: string) => {
    setSelectedRows(prev => prev.filter(id => id !== rowId));
  }, []);

  const selectAll = useCallback(() => {
    setSelectedRows(data.map(getRowId));
  }, [data, getRowId]);

  const deselectAll = useCallback(() => {
    setSelectedRows([]);
  }, []);

  const toggleSelectAll = useCallback(() => {
    if (selectedRows.length === data.length) {
      deselectAll();
    } else {
      selectAll();
    }
  }, [selectedRows.length, data.length, selectAll, deselectAll]);

  // Computed states
  const allSelected = data.length > 0 && selectedRows.length === data.length;
  const someSelected = selectedRows.length > 0 && selectedRows.length < data.length;
  const selectedCount = selectedRows.length;

  // Handlers for table component
  const handleSort = useCallback((config: SortConfig) => {
    setSortConfig(config);
  }, []);

  const handleRowSelect = useCallback((rowId: string, selected: boolean) => {
    if (selected) {
      selectRow(rowId);
    } else {
      deselectRow(rowId);
    }
  }, [selectRow, deselectRow]);

  const handleSelectAll = useCallback((selected: boolean) => {
    if (selected) {
      selectAll();
    } else {
      deselectAll();
    }
  }, [selectAll, deselectAll]);

  return {
    // Sorting
    sortConfig,
    setSortConfig,
    sortedData,
    
    // Selection
    selectedRows,
    setSelectedRows,
    isRowSelected,
    toggleRowSelection,
    selectRow,
    deselectRow,
    selectAll,
    deselectAll,
    toggleSelectAll,
    
    // Computed states
    allSelected,
    someSelected,
    selectedCount,
    
    // Handlers
    handleSort,
    handleRowSelect,
    handleSelectAll,
  };
}
