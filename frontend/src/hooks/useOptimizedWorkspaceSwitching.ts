/**
 * Optimized Workspace Switching Hook
 * Provides seamless workspace switching with optimistic updates and caching
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { useAuth } from '@clerk/clerk-react';
import { workspaceCache } from '../lib/workspace-cache';
import { api } from '../lib/api';

// Workspace switching state
interface WorkspaceSwitchState {
  isLoading: boolean;
  previousWorkspaceId: string | null;
  error: string | null;
  optimisticWorkspaceId: string | null;
}

// Animation states
interface AnimationState {
  isTransitioning: boolean;
  direction: 'in' | 'out' | null;
}

// Hook options
interface UseOptimizedWorkspaceSwitchingOptions {
  onWorkspaceChanged?: (workspaceId: string) => void;
  onError?: (error: string, workspaceId: string) => void;
  enablePreloading?: boolean;
  enableOptimisticUpdates?: boolean;
  debounceMs?: number;
}

export const useOptimizedWorkspaceSwitching = (
  options: UseOptimizedWorkspaceSwitchingOptions = {}
) => {
  const {
    onWorkspaceChanged,
    onError,
    enablePreloading = true,
    enableOptimisticUpdates = true,
    debounceMs = 300,
  } = options;

  const { getToken } = useAuth();
  
  // State management
  const [switchState, setSwitchState] = useState<WorkspaceSwitchState>({
    isLoading: false,
    previousWorkspaceId: null,
    error: null,
    optimisticWorkspaceId: null,
  });

  const [animationState, setAnimationState] = useState<AnimationState>({
    isTransitioning: false,
    direction: null,
  });

  // Refs for managing async operations
  const switchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const currentSwitchRef = useRef<string | null>(null);
  const rollbackDataRef = useRef<any>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (switchTimeoutRef.current) {
        clearTimeout(switchTimeoutRef.current);
      }
      workspaceCache.clearAllCache();
    };
  }, []);

  // Optimistic workspace switch with rollback capability
  const switchWorkspaceOptimistic = useCallback(
    async (workspaceId: string, workspaceName: string): Promise<boolean> => {
      // Prevent concurrent switches
      if (currentSwitchRef.current === workspaceId) {
        return true;
      }

      // Cancel any pending switch
      if (switchTimeoutRef.current) {
        clearTimeout(switchTimeoutRef.current);
      }

      const previousWorkspaceId = api.getWorkspaceId();
      currentSwitchRef.current = workspaceId;

      try {
        // Store rollback data
        rollbackDataRef.current = {
          workspaceId: previousWorkspaceId,
          timestamp: Date.now(),
        };

        // Start transition animation
        setAnimationState({
          isTransitioning: true,
          direction: 'out',
        });

        // Optimistic update - change workspace immediately
        if (enableOptimisticUpdates) {
          setSwitchState(prev => ({
            ...prev,
            isLoading: true,
            previousWorkspaceId,
            error: null,
            optimisticWorkspaceId: workspaceId,
          }));

          // Update API service immediately for optimistic UI
          api.setWorkspaceId(workspaceId);
          
          // Notify about optimistic change
          onWorkspaceChanged?.(workspaceId);
        }

        // Transition to 'in' animation after a brief delay
        setTimeout(() => {
          setAnimationState({
            isTransitioning: true,
            direction: 'in',
          });
        }, 150);

        // Start background data fetching
        const token = await getToken();
        
        // Preload data for the new workspace
        if (enablePreloading) {
          workspaceCache.preloadWorkspaceData(workspaceId, token);
        }

        // Simulate API delay for demonstration (remove in production)
        await new Promise(resolve => setTimeout(resolve, 200));

        // Complete the switch
        setSwitchState(prev => ({
          ...prev,
          isLoading: false,
          optimisticWorkspaceId: null,
        }));

        // Complete transition animation
        setTimeout(() => {
          setAnimationState({
            isTransitioning: false,
            direction: null,
          });
        }, 300);

        console.log(`✅ Successfully switched to workspace: ${workspaceName}`);
        return true;

      } catch (error: any) {
        console.error(`❌ Failed to switch to workspace: ${workspaceName}`, error);

        // Rollback optimistic changes
        if (rollbackDataRef.current) {
          api.setWorkspaceId(rollbackDataRef.current.workspaceId);
          onWorkspaceChanged?.(rollbackDataRef.current.workspaceId);
        }

        // Update error state
        setSwitchState(prev => ({
          ...prev,
          isLoading: false,
          error: error.message || 'Failed to switch workspace',
          optimisticWorkspaceId: null,
        }));

        // Reset animation
        setAnimationState({
          isTransitioning: false,
          direction: null,
        });

        onError?.(error.message || 'Failed to switch workspace', workspaceId);
        return false;

      } finally {
        currentSwitchRef.current = null;
      }
    },
    [getToken, onWorkspaceChanged, onError, enablePreloading, enableOptimisticUpdates]
  );

  // Debounced workspace switch to prevent rapid switching
  const switchWorkspaceDebounced = useCallback(
    (workspaceId: string, workspaceName: string) => {
      if (switchTimeoutRef.current) {
        clearTimeout(switchTimeoutRef.current);
      }

      switchTimeoutRef.current = setTimeout(() => {
        switchWorkspaceOptimistic(workspaceId, workspaceName);
      }, debounceMs);
    },
    [switchWorkspaceOptimistic, debounceMs]
  );

  // Immediate workspace switch (bypasses debouncing)
  const switchWorkspaceImmediate = useCallback(
    (workspaceId: string, workspaceName: string) => {
      if (switchTimeoutRef.current) {
        clearTimeout(switchTimeoutRef.current);
      }
      return switchWorkspaceOptimistic(workspaceId, workspaceName);
    },
    [switchWorkspaceOptimistic]
  );

  // Preload workspace data
  const preloadWorkspace = useCallback(
    async (workspaceId: string) => {
      if (!enablePreloading) return;
      
      try {
        const token = await getToken();
        workspaceCache.preloadWorkspaceData(workspaceId, token);
      } catch (error) {
        console.warn(`Failed to preload workspace ${workspaceId}:`, error);
      }
    },
    [getToken, enablePreloading]
  );

  // Get cached data for workspace
  const getWorkspaceData = useCallback(
    async (workspaceId: string) => {
      try {
        const token = await getToken();
        const [contracts, templates] = await Promise.all([
          workspaceCache.getContracts(workspaceId, token),
          workspaceCache.getTemplates(workspaceId),
        ]);

        return { contracts, templates };
      } catch (error) {
        console.error(`Failed to get workspace data for ${workspaceId}:`, error);
        return { contracts: [], templates: [] };
      }
    },
    [getToken]
  );

  // Get loading states for workspace data
  const getLoadingStates = useCallback(
    (workspaceId: string) => workspaceCache.getLoadingStates(workspaceId),
    []
  );

  // Get error states for workspace data
  const getErrorStates = useCallback(
    (workspaceId: string) => workspaceCache.getErrorStates(workspaceId),
    []
  );

  // Clear workspace cache
  const clearWorkspaceCache = useCallback(
    (workspaceId?: string) => {
      if (workspaceId) {
        workspaceCache.clearWorkspaceCache(workspaceId);
      } else {
        workspaceCache.clearAllCache();
      }
    },
    []
  );

  // Clear error state
  const clearError = useCallback(() => {
    setSwitchState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    // State
    switchState,
    animationState,
    
    // Actions
    switchWorkspace: switchWorkspaceDebounced,
    switchWorkspaceImmediate,
    preloadWorkspace,
    clearError,
    
    // Data access
    getWorkspaceData,
    getLoadingStates,
    getErrorStates,
    
    // Cache management
    clearWorkspaceCache,
    
    // Computed properties
    isLoading: switchState.isLoading,
    isTransitioning: animationState.isTransitioning,
    error: switchState.error,
    currentWorkspaceId: switchState.optimisticWorkspaceId || api.getWorkspaceId(),
  };
};
