// We'll define a simple interface for PDFDocumentProxy to avoid import issues
interface PDFDocumentProxy {
  numPages: number;
  getMetadata(): Promise<any>;
  getPage(pageNumber: number): Promise<any>;
}

// Types for document parsing
export interface ParsedDocument {
  text: string;
  metadata: DocumentMetadata;
  sections: DocumentSection[];
  entities: NamedEntity[];
}

export interface DocumentMetadata {
  title?: string;
  author?: string;
  creationDate?: Date;
  modificationDate?: Date;
  pageCount?: number;
  fileType: string;
  fileSize?: number;
}

export interface DocumentSection {
  id: string;
  title: string;
  content: string;
  level: number;
  startIndex: number;
  endIndex: number;
}

export interface NamedEntity {
  id: string;
  text: string;
  type: 'person' | 'organization' | 'date' | 'money' | 'percentage' | 'location' | 'other';
  startIndex: number;
  endIndex: number;
}

// Main document parser class
export class DocumentParser {
  /**
   * Parse a document file and extract text and structure
   */
  async parseDocument(file: File): Promise<ParsedDocument> {
    const fileType = this.getFileType(file);

    switch (fileType) {
      case 'pdf':
        return this.parsePdf(file);
      case 'docx':
        return this.parseDocx(file);
      case 'txt':
        return this.parseTxt(file);
      case 'html':
        return this.parseHtml(file);
      default:
        throw new Error(`Unsupported file type: ${fileType}`);
    }
  }

  /**
   * Extract text from HTML content
   */
  extractTextFromHtml(html: string): string {
    // Create a temporary DOM element
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Get the text content
    return tempDiv.textContent || '';
  }

  /**
   * Parse PDF document
   */
  private async parsePdf(file: File): Promise<ParsedDocument> {
    try {
      // Dynamically import PDF.js
      const pdfjs = await import('pdfjs-dist/webpack');

      // Set the worker source
      pdfjs.GlobalWorkerOptions.workerSrc = new URL(
        'pdfjs-dist/build/pdf.worker.min.js',
        import.meta.url
      ).toString();

      // Load the PDF file
      const arrayBuffer = await file.arrayBuffer();
      const loadingTask = pdfjs.getDocument(arrayBuffer);
      const pdf = await loadingTask.promise;

      // Extract text from each page
      const textContent: string[] = [];
      const numPages = pdf.numPages;

      for (let i = 1; i <= numPages; i++) {
        const page = await pdf.getPage(i);
        const content = await page.getTextContent();
        const pageText = content.items
          .map((item: any) => item.str)
          .join(' ');

        textContent.push(pageText);
      }

      const fullText = textContent.join('\n\n');

      // Extract metadata
      const metadata = await this.extractPdfMetadata(pdf, file);

      // Extract sections and entities
      const sections = this.extractSections(fullText);
      const entities = this.extractEntities(fullText);

      return {
        text: fullText,
        metadata,
        sections,
        entities
      };
    } catch (error) {
      console.error('Error parsing PDF:', error);
      return this.createEmptyResult(file, 'Failed to parse PDF');
    }
  }

  /**
   * Parse DOCX document
   */
  private async parseDocx(file: File): Promise<ParsedDocument> {
    try {
      // For now, let's handle DOCX files with a simpler approach
      // In a production environment, you would use mammoth.js properly

      // Read the file as text (this won't preserve formatting but will work for basic text extraction)
      const text = await file.text();
      const result = { value: text };

      const fullText = result.value;

      // Extract metadata
      const metadata = {
        fileType: 'docx',
        fileSize: file.size
      };

      // Extract sections and entities
      const sections = this.extractSections(fullText);
      const entities = this.extractEntities(fullText);

      return {
        text: fullText,
        metadata,
        sections,
        entities
      };
    } catch (error) {
      console.error('Error parsing DOCX:', error);
      return this.createEmptyResult(file, 'Failed to parse DOCX');
    }
  }

  /**
   * Parse TXT document
   */
  private async parseTxt(file: File): Promise<ParsedDocument> {
    try {
      // Read the text file
      const text = await file.text();

      // Extract metadata
      const metadata = {
        fileType: 'txt',
        fileSize: file.size
      };

      // Extract sections and entities
      const sections = this.extractSections(text);
      const entities = this.extractEntities(text);

      return {
        text,
        metadata,
        sections,
        entities
      };
    } catch (error) {
      console.error('Error parsing TXT:', error);
      return this.createEmptyResult(file, 'Failed to parse TXT');
    }
  }

  /**
   * Parse HTML document
   */
  private async parseHtml(file: File): Promise<ParsedDocument> {
    try {
      // Read the HTML file
      const html = await file.text();

      // Extract text from HTML
      const text = this.extractTextFromHtml(html);

      // Extract metadata
      const metadata = {
        fileType: 'html',
        fileSize: file.size
      };

      // Extract sections and entities
      const sections = this.extractSections(text);
      const entities = this.extractEntities(text);

      return {
        text,
        metadata,
        sections,
        entities
      };
    } catch (error) {
      console.error('Error parsing HTML:', error);
      return this.createEmptyResult(file, 'Failed to parse HTML');
    }
  }

  /**
   * Extract metadata from PDF document
   */
  private async extractPdfMetadata(pdf: PDFDocumentProxy, file: File): Promise<DocumentMetadata> {
    try {
      const metadata = await pdf.getMetadata();

      return {
        title: metadata.info?.Title,
        author: metadata.info?.Author,
        creationDate: metadata.info?.CreationDate ? new Date(metadata.info.CreationDate) : undefined,
        modificationDate: metadata.info?.ModDate ? new Date(metadata.info.ModDate) : undefined,
        pageCount: pdf.numPages,
        fileType: 'pdf',
        fileSize: file.size
      };
    } catch (error) {
      console.error('Error extracting PDF metadata:', error);
      return {
        fileType: 'pdf',
        fileSize: file.size
      };
    }
  }

  /**
   * Extract sections from text using heuristics
   */
  private extractSections(text: string): DocumentSection[] {
    const sections: DocumentSection[] = [];

    // Simple section extraction based on common patterns
    // This is a basic implementation that can be enhanced with more sophisticated algorithms

    // Look for section headers (e.g., "1. Introduction", "Article 1", etc.)
    const sectionRegexes = [
      /(?:\n|\r|\r\n)(\d+\.\s+[A-Z][A-Za-z\s]+)(?:\n|\r|\r\n)/g,  // Numbered sections: "1. Introduction"
      /(?:\n|\r|\r\n)(Article\s+\d+[\.:]\s*[A-Z][A-Za-z\s]+)(?:\n|\r|\r\n)/g,  // Articles: "Article 1: Definitions"
      /(?:\n|\r|\r\n)([A-Z][A-Z\s]+)(?:\n|\r|\r\n)/g,  // ALL CAPS headers: "INTRODUCTION"
    ];

    let sectionId = 1;

    for (const regex of sectionRegexes) {
      let match;
      while ((match = regex.exec(text)) !== null) {
        const title = match[1].trim();
        const startIndex = match.index;

        // Find the end of this section (start of next section or end of text)
        let endIndex = text.length;
        for (const endRegex of sectionRegexes) {
          const endMatch = new RegExp(endRegex.source, 'g');
          endMatch.lastIndex = startIndex + match[0].length;
          const nextMatch = endMatch.exec(text);
          if (nextMatch && nextMatch.index < endIndex) {
            endIndex = nextMatch.index;
          }
        }

        sections.push({
          id: `section-${sectionId++}`,
          title,
          content: text.substring(startIndex, endIndex).trim(),
          level: 1,  // Default level
          startIndex,
          endIndex
        });
      }
    }

    // Sort sections by their position in the document
    return sections.sort((a, b) => a.startIndex - b.startIndex);
  }

  /**
   * Extract named entities from text
   */
  private extractEntities(text: string): NamedEntity[] {
    const entities: NamedEntity[] = [];

    // Simple entity extraction based on common patterns
    // This is a basic implementation that can be enhanced with NLP libraries

    // Extract dates
    const dateRegex = /\b(\d{1,2}\/\d{1,2}\/\d{2,4}|\d{1,2}-\d{1,2}-\d{2,4}|January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}\b/g;
    this.extractEntitiesWithRegex(text, dateRegex, 'date', entities);

    // Extract monetary amounts
    const moneyRegex = /\b(\$\d{1,3}(,\d{3})*(\.\d{2})?|\d{1,3}(,\d{3})*(\.\d{2})?\s+(dollars|USD|EUR|GBP))\b/g;
    this.extractEntitiesWithRegex(text, moneyRegex, 'money', entities);

    // Extract percentages
    const percentageRegex = /\b(\d+(\.\d+)?%)\b/g;
    this.extractEntitiesWithRegex(text, percentageRegex, 'percentage', entities);

    // Extract organizations (simple heuristic)
    const orgRegex = /\b([A-Z][a-z]+(\s[A-Z][a-z]+)+\s+(Inc\.|LLC|Ltd\.|Corp\.|Corporation|Company))\b/g;
    this.extractEntitiesWithRegex(text, orgRegex, 'organization', entities);

    return entities;
  }

  /**
   * Helper method to extract entities using regex
   */
  private extractEntitiesWithRegex(
    text: string,
    regex: RegExp,
    type: NamedEntity['type'],
    entities: NamedEntity[]
  ): void {
    let match;
    let entityId = entities.length + 1;

    while ((match = regex.exec(text)) !== null) {
      entities.push({
        id: `entity-${entityId++}`,
        text: match[0],
        type,
        startIndex: match.index,
        endIndex: match.index + match[0].length
      });
    }
  }

  /**
   * Get file type from File object
   */
  private getFileType(file: File): string {
    const fileName = file.name.toLowerCase();

    if (fileName.endsWith('.pdf')) return 'pdf';
    if (fileName.endsWith('.docx') || fileName.endsWith('.doc')) return 'docx';
    if (fileName.endsWith('.txt')) return 'txt';
    if (fileName.endsWith('.html') || fileName.endsWith('.htm')) return 'html';

    // Fallback to MIME type
    const mimeType = file.type.toLowerCase();

    if (mimeType === 'application/pdf') return 'pdf';
    if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        mimeType === 'application/msword') return 'docx';
    if (mimeType === 'text/plain') return 'txt';
    if (mimeType === 'text/html') return 'html';

    return 'unknown';
  }

  /**
   * Create an empty result for error cases
   */
  private createEmptyResult(file: File, text: string): ParsedDocument {
    return {
      text,
      metadata: {
        fileType: this.getFileType(file),
        fileSize: file.size
      },
      sections: [],
      entities: []
    };
  }
}

// Create a singleton instance
export const documentParser = new DocumentParser();
