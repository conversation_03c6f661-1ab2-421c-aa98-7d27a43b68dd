import React from 'react';
import { api } from './api-services';

// Notification types
export interface Notification {
  id: string;
  type: 'contract_expiry' | 'approval_required' | 'contract_updated' | 'renewal_due' | 'system' | 'ai_analysis';
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  read: boolean;
  created_at: string;
  workspace_id: string;
  user_id: string;
  metadata?: {
    contract_id?: string;
    approval_id?: string;
    action_url?: string;
    [key: string]: any;
  };
}

export interface NotificationPreferences {
  email_enabled: boolean;
  push_enabled: boolean;
  contract_expiry: boolean;
  approval_required: boolean;
  contract_updates: boolean;
  renewal_reminders: boolean;
  ai_insights: boolean;
  system_updates: boolean;
}

export interface NotificationStats {
  total_unread: number;
  by_type: Record<string, number>;
  by_priority: Record<string, number>;
}

// WebSocket connection for real-time notifications
class NotificationWebSocket {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: ((notification: Notification) => void)[] = [];

  connect(token: string, workspaceId: string) {
    try {
      // In a real implementation, this would connect to your WebSocket server
      const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8000'}/ws/notifications?token=${token}&workspace=${workspaceId}`;
      
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        console.log('Notification WebSocket connected');
        this.reconnectAttempts = 0;
      };
      
      this.ws.onmessage = (event) => {
        try {
          const notification: Notification = JSON.parse(event.data);
          this.notifyListeners(notification);
        } catch (error) {
          console.error('Failed to parse notification:', error);
        }
      };
      
      this.ws.onclose = () => {
        console.log('Notification WebSocket disconnected');
        this.attemptReconnect(token, workspaceId);
      };
      
      this.ws.onerror = (error) => {
        console.error('Notification WebSocket error:', error);
      };
    } catch (error) {
      console.error('Failed to connect to notification WebSocket:', error);
    }
  }

  private attemptReconnect(token: string, workspaceId: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect(token, workspaceId);
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  addListener(callback: (notification: Notification) => void) {
    this.listeners.push(callback);
  }

  removeListener(callback: (notification: Notification) => void) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  private notifyListeners(notification: Notification) {
    this.listeners.forEach(listener => listener(notification));
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners = [];
  }
}

// Singleton WebSocket instance
const notificationWS = new NotificationWebSocket();

// Notification Service
export const NotificationService = {
  // WebSocket methods
  connectWebSocket: (token: string, workspaceId: string) => {
    notificationWS.connect(token, workspaceId);
  },

  disconnectWebSocket: () => {
    notificationWS.disconnect();
  },

  addNotificationListener: (callback: (notification: Notification) => void) => {
    notificationWS.addListener(callback);
  },

  removeNotificationListener: (callback: (notification: Notification) => void) => {
    notificationWS.removeListener(callback);
  },

  // API methods
  async getNotifications(workspaceId: string, limit: number = 50, offset: number = 0, token?: string) {
    return api.get<{ notifications: Notification[]; total: number }>(
      `/notifications?workspace_id=${workspaceId}&limit=${limit}&offset=${offset}`,
      {},
      token
    );
  },

  async getUnreadCount(workspaceId: string, token?: string) {
    return api.get<{ count: number }>(`/notifications/unread-count?workspace_id=${workspaceId}`, {}, token);
  },

  async markAsRead(notificationId: string, token?: string) {
    return api.put<Notification>(`/notifications/${notificationId}/read`, {}, {}, token);
  },

  async markAllAsRead(workspaceId: string, token?: string) {
    return api.put<{ updated_count: number }>(`/notifications/mark-all-read`, { workspace_id: workspaceId }, {}, token);
  },

  async deleteNotification(notificationId: string, token?: string) {
    return api.delete(`/notifications/${notificationId}`, {}, token);
  },

  async getNotificationStats(workspaceId: string, token?: string) {
    return api.get<NotificationStats>(`/notifications/stats?workspace_id=${workspaceId}`, {}, token);
  },

  async getPreferences(token?: string) {
    return api.get<NotificationPreferences>('/notifications/preferences', {}, token);
  },

  async updatePreferences(preferences: Partial<NotificationPreferences>, token?: string) {
    return api.put<NotificationPreferences>('/notifications/preferences', preferences, {}, token);
  },

  async createNotification(notification: Omit<Notification, 'id' | 'created_at'>, token?: string) {
    return api.post<Notification>('/notifications', notification, {}, token);
  },

  // Utility methods
  getPriorityColor: (priority: string) => {
    switch (priority) {
      case 'urgent': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'yellow';
      case 'low': return 'blue';
      default: return 'gray';
    }
  },

  getTypeIcon: (type: string) => {
    switch (type) {
      case 'contract_expiry': return '⏰';
      case 'approval_required': return '✋';
      case 'contract_updated': return '📝';
      case 'renewal_due': return '🔄';
      case 'ai_analysis': return '🤖';
      case 'system': return '⚙️';
      default: return '📢';
    }
  },

  formatTimeAgo: (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return date.toLocaleDateString();
  },

  // Browser notification support
  requestPermission: async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  },

  showBrowserNotification: (notification: Notification) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.priority === 'urgent',
      });

      browserNotification.onclick = () => {
        window.focus();
        if (notification.metadata?.action_url) {
          window.location.href = notification.metadata.action_url;
        }
        browserNotification.close();
      };

      // Auto-close after 5 seconds for non-urgent notifications
      if (notification.priority !== 'urgent') {
        setTimeout(() => browserNotification.close(), 5000);
      }
    }
  },

  // Sound notifications
  playNotificationSound: (priority: string) => {
    try {
      const audio = new Audio();
      switch (priority) {
        case 'urgent':
          audio.src = '/sounds/urgent.mp3';
          break;
        case 'high':
          audio.src = '/sounds/high.mp3';
          break;
        default:
          audio.src = '/sounds/default.mp3';
          break;
      }
      audio.volume = 0.3;
      audio.play().catch(() => {
        // Ignore audio play errors (user interaction required)
      });
    } catch (error) {
      // Ignore audio errors
    }
  }
};

// React hook for notifications
export const useNotifications = (workspaceId: string) => {
  const [notifications, setNotifications] = React.useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = React.useState(0);
  const [loading, setLoading] = React.useState(true);

  // This would be implemented with proper React hooks
  // For now, it's a placeholder structure
  return {
    notifications,
    unreadCount,
    loading,
    markAsRead: (id: string) => NotificationService.markAsRead(id),
    markAllAsRead: () => NotificationService.markAllAsRead(workspaceId),
    deleteNotification: (id: string) => NotificationService.deleteNotification(id),
    refresh: () => {
      // Refresh notifications
    }
  };
};
