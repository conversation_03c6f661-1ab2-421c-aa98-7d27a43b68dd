import { documentParser, ParsedDocument, DocumentSection } from './documentParser';
import {
  DocumentParsingError,
  UnsupportedFileTypeError,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
} from '../lib/errors';

/**
 * Unified Contract Extraction Service
 *
 * This service consolidates document parsing and contract-specific extraction
 * to eliminate duplication and provide a single interface for contract processing.
 *
 * Key improvements:
 * - Single method for complete contract extraction
 * - Eliminates need to call both documentParser and contractExtractor separately
 * - Maintains backward compatibility with existing interfaces
 */

// Types for contract extraction
export interface ExtractedContract {
  fullText: string;
  metadata: ContractMetadata;
  parties: ContractParty[];
  clauses: ContractClause[];
  dates: ContractDate[];
  amounts: ContractAmount[];
  obligations: ContractObligation[];
  // Include parsed document data for completeness
  parsedDocument?: ParsedDocument;
}

export interface ContractMetadata {
  title?: string;
  type?: string;
  effectiveDate?: Date;
  expirationDate?: Date;
  status?: string;
  pageCount?: number;
  fileType: string;
  fileSize?: number;
}

export interface ContractParty {
  id: string;
  name: string;
  type: 'individual' | 'organization' | 'unknown';
  role?: string;
  address?: string;
  identifiers?: Record<string, string>; // e.g., { "tax_id": "*********" }
}

export interface ContractClause {
  id: string;
  title: string;
  content: string;
  category?: string;
  location: string;
  riskLevel?: 'low' | 'medium' | 'high' | 'unknown';
  riskReason?: string;
}

export interface ContractDate {
  id: string;
  date: Date | string;
  type: 'effective' | 'expiration' | 'signing' | 'payment' | 'delivery' | 'other';
  description?: string;
  context: string;
}

export interface ContractAmount {
  id: string;
  amount: number | string;
  currency: string;
  type: 'total' | 'payment' | 'fee' | 'penalty' | 'other';
  description?: string;
  context: string;
}

export interface ContractObligation {
  id: string;
  party: string;
  action: string;
  deadline?: Date | string;
  condition?: string;
  context: string;
}

// Main contract extractor class
export class ContractExtractor {
  /**
   * UNIFIED METHOD: Extract complete contract information from a file
   * This method combines document parsing and contract extraction in one call
   * to eliminate the need for separate documentParser.parseDocument() calls
   */
  async extractCompleteContract(file: File): Promise<{
    extractedContract: ExtractedContract;
    parsedDocument: ParsedDocument;
  }> {
    try {
      // Parse the document
      const parsedDocument = await documentParser.parseDocument(file);

      // Extract contract-specific information
      const extractedContract = this.extractFromParsedDocument(parsedDocument);

      // Include parsed document in the contract for completeness
      extractedContract.parsedDocument = parsedDocument;

      return {
        extractedContract,
        parsedDocument
      };
    } catch (error) {
      console.error('Error extracting complete contract information:', error);

      // Use enhanced error handling for logging
      ErrorHandler.handle(error, 'contract extraction');

      // For unsupported file types, throw specific error
      if (error instanceof Error && error.message.includes('Unsupported file type')) {
        throw new UnsupportedFileTypeError(file.type, ['pdf', 'docx', 'txt', 'html']);
      }

      // For parsing errors, throw specific error
      if (error instanceof Error && error.message.includes('parse')) {
        throw new DocumentParsingError(file.name, file.type);
      }

      // For other errors, create fallback result
      const emptyContract = this.createEmptyResult(file);
      const emptyDocument: ParsedDocument = {
        text: '',
        metadata: { fileType: file.type || 'unknown', fileSize: file.size },
        sections: [],
        entities: []
      };
      return {
        extractedContract: emptyContract,
        parsedDocument: emptyDocument
      };
    }
  }

  /**
   * LEGACY METHOD: Extract contract information from a file
   * @deprecated Use extractCompleteContract() instead for better performance
   */
  async extractFromFile(file: File): Promise<ExtractedContract> {
    try {
      // First parse the document
      const parsedDocument = await documentParser.parseDocument(file);

      // Then extract contract-specific information
      return this.extractFromParsedDocument(parsedDocument);
    } catch (error) {
      console.error('Error extracting contract information:', error);
      return this.createEmptyResult(file);
    }
  }

  /**
   * Extract contract information from HTML content
   */
  extractFromHtml(html: string): ExtractedContract {
    try {
      // Extract text from HTML
      const text = documentParser.extractTextFromHtml(html);
      
      // Create a minimal parsed document
      const parsedDocument: ParsedDocument = {
        text,
        metadata: {
          fileType: 'html'
        },
        sections: this.extractSectionsFromText(text),
        entities: []
      };
      
      // Extract contract-specific information
      return this.extractFromParsedDocument(parsedDocument);
    } catch (error) {
      console.error('Error extracting contract information from HTML:', error);
      return this.createEmptyResult();
    }
  }

  /**
   * Extract contract information from text
   */
  extractFromText(text: string): ExtractedContract {
    try {
      // Create a minimal parsed document
      const parsedDocument: ParsedDocument = {
        text,
        metadata: {
          fileType: 'txt'
        },
        sections: this.extractSectionsFromText(text),
        entities: []
      };
      
      // Extract contract-specific information
      return this.extractFromParsedDocument(parsedDocument);
    } catch (error) {
      console.error('Error extracting contract information from text:', error);
      return this.createEmptyResult();
    }
  }

  /**
   * Extract contract information from a parsed document
   */
  private extractFromParsedDocument(parsedDocument: ParsedDocument): ExtractedContract {
    const { text, metadata, sections } = parsedDocument;
    
    // Extract contract metadata
    const contractMetadata = this.extractContractMetadata(text, metadata);
    
    // Extract parties
    const parties = this.extractParties(text, sections);
    
    // Extract clauses
    const clauses = this.extractClauses(sections);
    
    // Extract dates
    const dates = this.extractDates(text, sections);
    
    // Extract amounts
    const amounts = this.extractAmounts(text, sections);
    
    // Extract obligations
    const obligations = this.extractObligations(text, sections, parties);
    
    return {
      fullText: text,
      metadata: contractMetadata,
      parties,
      clauses,
      dates,
      amounts,
      obligations
    };
  }

  /**
   * Extract contract metadata
   */
  private extractContractMetadata(text: string, documentMetadata: any): ContractMetadata {
    // Basic metadata from document
    const metadata: ContractMetadata = {
      title: documentMetadata.title,
      pageCount: documentMetadata.pageCount,
      fileType: documentMetadata.fileType,
      fileSize: documentMetadata.fileSize
    };
    
    // Try to extract contract title if not available
    if (!metadata.title) {
      const titleMatch = text.match(/(?:^|\n)([^\n]{5,100}(?:agreement|contract|lease|license))(?:$|\n)/i);
      if (titleMatch) {
        metadata.title = titleMatch[1].trim();
      }
    }
    
    // Try to extract contract type
    const typePatterns = [
      /(?:this|the)\s+([a-z\s]{3,30}(?:agreement|contract))(?:\s+is|,|\s+\()/i,
      /(?:^|\n)([a-z\s]{3,30}(?:agreement|contract))(?:$|\n)/i
    ];
    
    for (const pattern of typePatterns) {
      const typeMatch = text.match(pattern);
      if (typeMatch) {
        metadata.type = typeMatch[1].trim();
        break;
      }
    }
    
    // Try to extract effective date
    const effectiveDatePatterns = [
      /effective\s+(?:date|as\s+of)(?:\s+is)?(?:\s+the)?[\s:]*([a-z0-9,\s]+\d{4})/i,
      /(?:this|the)\s+agreement\s+is\s+(?:dated|made|entered\s+into)(?:\s+as\s+of)?[\s:]*([a-z0-9,\s]+\d{4})/i,
      /(?:dated|effective)[\s:]*([a-z0-9,\s]+\d{4})/i
    ];
    
    for (const pattern of effectiveDatePatterns) {
      const dateMatch = text.match(pattern);
      if (dateMatch) {
        try {
          metadata.effectiveDate = new Date(dateMatch[1].trim());
          break;
        } catch (e) {
          // Invalid date format, continue to next pattern
        }
      }
    }
    
    return metadata;
  }

  /**
   * Extract parties from contract text
   */
  private extractParties(text: string, sections: DocumentSection[]): ContractParty[] {
    const parties: ContractParty[] = [];
    
    // Look for party definitions in the first 20% of the document
    const introText = text.substring(0, Math.floor(text.length * 0.2));
    
    // Common patterns for party introductions
    const partyPatterns = [
      /(?:between|by\s+and\s+between)\s+([^,]+),\s+(?:a|an)\s+([^,]+)(?:,\s+(?:with\s+(?:its\s+)?(?:principal\s+)?(?:place\s+of\s+business|offices?|address)\s+(?:at|located\s+at|in)\s+([^,\.]+)))?/gi,
      /([^,]+),\s+(?:a|an)\s+([^,]+)(?:\s+organized\s+and\s+existing\s+under\s+the\s+laws\s+of\s+([^,\.]+))?(?:,\s+(?:with\s+(?:its\s+)?(?:principal\s+)?(?:place\s+of\s+business|offices?|address)\s+(?:at|located\s+at|in)\s+([^,\.]+)))?(?:\s+\((?:"|'|)([^")]+)(?:"|'|)\))?/gi,
      /([^,]+)\s+\((?:"|'|)([^")]+)(?:"|'|)\)/gi
    ];
    
    let partyId = 1;
    
    for (const pattern of partyPatterns) {
      let match: RegExpExecArray | null;
      while ((match = pattern.exec(introText)) !== null) {
        // Different patterns capture different groups
        let name: string, type: 'individual' | 'organization' | 'unknown', address: string, role: string;
        
        if (match.length >= 3) {
          name = match[1].trim();
          type = match[2].includes('individual') ? 'individual' : 'organization';
          address = match[3]?.trim();
          role = match[5]?.trim();
        } else {
          name = match[1].trim();
          role = match[2]?.trim();
          type = 'unknown';
        }
        
        // Check if this party is already added (by name)
        if (!parties.some(p => p.name.toLowerCase() === name.toLowerCase())) {
          parties.push({
            id: `party-${partyId++}`,
            name,
            type,
            role,
            address
          });
        }
      }
    }
    
    // If no parties found with the patterns, try to extract from "PARTIES" section if it exists
    if (parties.length === 0) {
      const partiesSection = sections.find(s => 
        s.title.toLowerCase().includes('parties') || 
        s.title.toLowerCase().includes('between')
      );
      
      if (partiesSection) {
        // Simple extraction of capitalized names
        const nameMatches = partiesSection.content.match(/\b[A-Z][A-Z\s]+(?:,\s+(?:INC|LLC|LTD|CORP|CORPORATION|COMPANY))?\b/g);
        if (nameMatches) {
          nameMatches.forEach(name => {
            if (!parties.some(p => p.name === name)) {
              parties.push({
                id: `party-${partyId++}`,
                name,
                type: name.match(/INC|LLC|LTD|CORP|CORPORATION|COMPANY/) ? 'organization' : 'unknown'
              });
            }
          });
        }
      }
    }
    
    return parties;
  }

  /**
   * Extract clauses from document sections
   */
  private extractClauses(sections: DocumentSection[]): ContractClause[] {
    const clauses: ContractClause[] = [];
    
    // Map sections to clauses
    sections.forEach((section, index) => {
      // Skip very short sections or those that don't look like clauses
      if (section.content.length < 50 || 
          section.title.toLowerCase().includes('signature') ||
          section.title.toLowerCase().includes('witness')) {
        return;
      }
      
      // Determine clause category based on title keywords
      let category = 'general';
      const title = section.title.toLowerCase();
      
      if (title.includes('confidential')) category = 'confidentiality';
      else if (title.includes('term') || title.includes('duration')) category = 'term';
      else if (title.includes('terminat')) category = 'termination';
      else if (title.includes('payment') || title.includes('fee') || title.includes('price')) category = 'payment';
      else if (title.includes('intellectual') || title.includes('property') || title.includes('copyright')) category = 'intellectual property';
      else if (title.includes('warranty') || title.includes('guaranty')) category = 'warranty';
      else if (title.includes('indemnity') || title.includes('indemnif')) category = 'indemnification';
      else if (title.includes('govern') && title.includes('law')) category = 'governing law';
      else if (title.includes('dispute')) category = 'dispute resolution';
      else if (title.includes('force majeure')) category = 'force majeure';
      else if (title.includes('assign')) category = 'assignment';
      else if (title.includes('notice')) category = 'notices';
      else if (title.includes('entire') && title.includes('agreement')) category = 'entire agreement';
      
      // Determine risk level based on content keywords
      let riskLevel: 'low' | 'medium' | 'high' | 'unknown' = 'unknown';
      let riskReason = '';
      
      const content = section.content.toLowerCase();
      
      if (content.includes('unlimited liability') || 
          content.includes('shall defend') || 
          content.includes('shall indemnify') ||
          content.includes('no limitation') ||
          content.includes('non-compete') ||
          content.includes('perpetual')) {
        riskLevel = 'high';
        riskReason = 'Contains unlimited liability or strong obligations';
      } else if (content.includes('limited liability') || 
                content.includes('limitation of liability') ||
                content.includes('cap on liability') ||
                content.includes('may terminate') ||
                content.includes('confidential information')) {
        riskLevel = 'medium';
        riskReason = 'Contains liability limitations or important business terms';
      } else if (content.includes('reasonable efforts') ||
                content.includes('business days') ||
                content.includes('notice period')) {
        riskLevel = 'low';
        riskReason = 'Contains standard business terms';
      }
      
      clauses.push({
        id: `clause-${index + 1}`,
        title: section.title,
        content: section.content,
        category,
        location: `Section ${index + 1}`,
        riskLevel,
        riskReason
      });
    });
    
    return clauses;
  }

  /**
   * Extract dates from contract text
   */
  private extractDates(text: string, sections: DocumentSection[]): ContractDate[] {
    const dates: ContractDate[] = [];
    let dateId = 1;
    
    // Common date formats
    const datePatterns = [
      {
        regex: /(?:effective|commencement)\s+date(?:\s+is)?(?:\s+of)?[\s:]*([a-z0-9,\s]+\d{4})/gi,
        type: 'effective'
      },
      {
        regex: /(?:expiration|termination)\s+date(?:\s+is)?(?:\s+of)?[\s:]*([a-z0-9,\s]+\d{4})/gi,
        type: 'expiration'
      },
      {
        regex: /(?:executed|signed)(?:\s+on)?(?:\s+this)?[\s:]*([a-z0-9,\s]+\d{4})/gi,
        type: 'signing'
      },
      {
        regex: /payment(?:\s+due)?(?:\s+date)?(?:\s+is)?(?:\s+on)?[\s:]*([a-z0-9,\s]+\d{4})/gi,
        type: 'payment'
      },
      {
        regex: /(?:delivery|performance)(?:\s+date)?(?:\s+is)?(?:\s+on)?[\s:]*([a-z0-9,\s]+\d{4})/gi,
        type: 'delivery'
      }
    ];
    
    // Extract dates with context
    for (const pattern of datePatterns) {
      let match;
      while ((match = pattern.regex.exec(text)) !== null) {
        const dateStr = match[1].trim();
        let dateObj: Date | null = null;
        
        try {
          dateObj = new Date(dateStr);
        } catch (e) {
          // Invalid date format
        }
        
        // Get context (text around the date)
        const startIdx = Math.max(0, match.index - 50);
        const endIdx = Math.min(text.length, match.index + match[0].length + 50);
        const context = text.substring(startIdx, endIdx);
        
        dates.push({
          id: `date-${dateId++}`,
          date: dateObj || dateStr,
          type: pattern.type as any,
          context
        });
      }
    }
    
    return dates;
  }

  /**
   * Extract monetary amounts from contract text
   */
  private extractAmounts(text: string, sections: DocumentSection[]): ContractAmount[] {
    const amounts: ContractAmount[] = [];
    let amountId = 1;
    
    // Common amount patterns
    const amountPatterns = [
      {
        regex: /(?:total|contract|agreement)\s+(?:price|amount|sum|value)(?:\s+is)?(?:\s+of)?[\s:]*(\$[\d,]+(?:\.\d{2})?|\d[\d,]*(?:\.\d{2})?\s+(?:dollars|USD|EUR|GBP))/gi,
        type: 'total'
      },
      {
        regex: /(?:payment|fee)(?:\s+amount)?(?:\s+is)?(?:\s+of)?[\s:]*(\$[\d,]+(?:\.\d{2})?|\d[\d,]*(?:\.\d{2})?\s+(?:dollars|USD|EUR|GBP))/gi,
        type: 'payment'
      },
      {
        regex: /(?:monthly|annual|quarterly)\s+(?:payment|fee)(?:\s+is)?(?:\s+of)?[\s:]*(\$[\d,]+(?:\.\d{2})?|\d[\d,]*(?:\.\d{2})?\s+(?:dollars|USD|EUR|GBP))/gi,
        type: 'payment'
      },
      {
        regex: /(?:penalty|late\s+fee)(?:\s+is)?(?:\s+of)?[\s:]*(\$[\d,]+(?:\.\d{2})?|\d[\d,]*(?:\.\d{2})?\s+(?:dollars|USD|EUR|GBP))/gi,
        type: 'penalty'
      }
    ];
    
    // Extract amounts with context
    for (const pattern of amountPatterns) {
      let match;
      while ((match = pattern.regex.exec(text)) !== null) {
        const amountStr = match[1].trim();
        let amount = amountStr;
        let currency = 'USD';
        
        // Try to parse amount and currency
        if (amountStr.startsWith('$')) {
          amount = amountStr.substring(1).replace(/,/g, '');
          currency = 'USD';
        } else if (amountStr.includes('dollars')) {
          amount = amountStr.replace(/\s+dollars/i, '').replace(/,/g, '');
          currency = 'USD';
        } else if (amountStr.includes('USD')) {
          amount = amountStr.replace(/\s+USD/i, '').replace(/,/g, '');
          currency = 'USD';
        } else if (amountStr.includes('EUR')) {
          amount = amountStr.replace(/\s+EUR/i, '').replace(/,/g, '');
          currency = 'EUR';
        } else if (amountStr.includes('GBP')) {
          amount = amountStr.replace(/\s+GBP/i, '').replace(/,/g, '');
          currency = 'GBP';
        }
        
        // Get context (text around the amount)
        const startIdx = Math.max(0, match.index - 50);
        const endIdx = Math.min(text.length, match.index + match[0].length + 50);
        const context = text.substring(startIdx, endIdx);
        
        amounts.push({
          id: `amount-${amountId++}`,
          amount,
          currency,
          type: pattern.type as any,
          context
        });
      }
    }
    
    return amounts;
  }

  /**
   * Extract obligations from contract text
   */
  private extractObligations(text: string, sections: DocumentSection[], parties: ContractParty[]): ContractObligation[] {
    const obligations: ContractObligation[] = [];
    let obligationId = 1;
    
    // Get party names for obligation extraction
    const partyNames = parties.map(p => p.name);
    
    // If no parties found, use generic terms
    if (partyNames.length === 0) {
      partyNames.push('Party A', 'Party B', 'Seller', 'Buyer', 'Licensor', 'Licensee', 'Vendor', 'Client');
    }
    
    // Create regex pattern for party names
    const partyPattern = partyNames.map(name => name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|');
    
    // Common obligation patterns
    const obligationPatterns = [
      new RegExp(`(${partyPattern})\\s+shall\\s+([^,.]+)`, 'gi'),
      new RegExp(`(${partyPattern})\\s+agrees\\s+to\\s+([^,.]+)`, 'gi'),
      new RegExp(`(${partyPattern})\\s+must\\s+([^,.]+)`, 'gi'),
      new RegExp(`(${partyPattern})\\s+will\\s+([^,.]+)`, 'gi'),
      new RegExp(`(${partyPattern})\\s+is\\s+obligated\\s+to\\s+([^,.]+)`, 'gi')
    ];
    
    // Extract obligations from each section
    for (const section of sections) {
      for (const pattern of obligationPatterns) {
        let match;
        while ((match = pattern.exec(section.content)) !== null) {
          const party = match[1].trim();
          const action = match[2].trim();
          
          // Skip if action is too short or generic
          if (action.length < 5 || 
              action.toLowerCase() === 'be' || 
              action.toLowerCase() === 'have') {
            continue;
          }
          
          // Get context (text around the obligation)
          const startIdx = Math.max(0, match.index - 30);
          const endIdx = Math.min(section.content.length, match.index + match[0].length + 70);
          const context = section.content.substring(startIdx, endIdx);
          
          // Try to extract deadline if present
          let deadline = undefined;
          const deadlineMatch = context.match(/(?:within|by|before|no\s+later\s+than)\s+([^,.]+)/i);
          if (deadlineMatch) {
            deadline = deadlineMatch[1].trim();
          }
          
          // Try to extract condition if present
          let condition = undefined;
          const conditionMatch = context.match(/(?:if|provided\s+that|on\s+condition\s+that|subject\s+to)\s+([^,.]+)/i);
          if (conditionMatch) {
            condition = conditionMatch[1].trim();
          }
          
          obligations.push({
            id: `obligation-${obligationId++}`,
            party,
            action,
            deadline,
            condition,
            context
          });
        }
      }
    }
    
    return obligations;
  }

  /**
   * Extract sections from text
   */
  private extractSectionsFromText(text: string): DocumentSection[] {
    const sections: DocumentSection[] = [];
    
    // Simple section extraction based on common patterns
    // This is a basic implementation that can be enhanced with more sophisticated algorithms
    
    // Look for section headers (e.g., "1. Introduction", "Article 1", etc.)
    const sectionRegexes = [
      /(?:\n|\r|\r\n)(\d+\.\s+[A-Z][A-Za-z\s]+)(?:\n|\r|\r\n)/g,  // Numbered sections: "1. Introduction"
      /(?:\n|\r|\r\n)(Article\s+\d+[\.:]\s*[A-Z][A-Za-z\s]+)(?:\n|\r|\r\n)/g,  // Articles: "Article 1: Definitions"
      /(?:\n|\r|\r\n)([A-Z][A-Z\s]+)(?:\n|\r|\r\n)/g,  // ALL CAPS headers: "INTRODUCTION"
    ];
    
    let sectionId = 1;
    
    for (const regex of sectionRegexes) {
      let match;
      while ((match = regex.exec(text)) !== null) {
        const title = match[1].trim();
        const startIndex = match.index;
        
        // Find the end of this section (start of next section or end of text)
        let endIndex = text.length;
        for (const endRegex of sectionRegexes) {
          const endMatch = new RegExp(endRegex.source, 'g');
          endMatch.lastIndex = startIndex + match[0].length;
          const nextMatch = endMatch.exec(text);
          if (nextMatch && nextMatch.index < endIndex) {
            endIndex = nextMatch.index;
          }
        }
        
        sections.push({
          id: `section-${sectionId++}`,
          title,
          content: text.substring(startIndex, endIndex).trim(),
          level: 1,  // Default level
          startIndex,
          endIndex
        });
      }
    }
    
    // Sort sections by their position in the document
    return sections.sort((a, b) => a.startIndex - b.startIndex);
  }

  /**
   * Create an empty result for error cases
   */
  private createEmptyResult(file?: File): ExtractedContract {
    return {
      fullText: '',
      metadata: {
        fileType: file ? this.getFileType(file) : 'unknown',
        fileSize: file?.size
      },
      parties: [],
      clauses: [],
      dates: [],
      amounts: [],
      obligations: []
    };
  }

  /**
   * Get file type from File object
   */
  private getFileType(file: File): string {
    const fileName = file.name.toLowerCase();
    
    if (fileName.endsWith('.pdf')) return 'pdf';
    if (fileName.endsWith('.docx') || fileName.endsWith('.doc')) return 'docx';
    if (fileName.endsWith('.txt')) return 'txt';
    if (fileName.endsWith('.html') || fileName.endsWith('.htm')) return 'html';
    
    // Fallback to MIME type
    const mimeType = file.type.toLowerCase();
    
    if (mimeType === 'application/pdf') return 'pdf';
    if (mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || 
        mimeType === 'application/msword') return 'docx';
    if (mimeType === 'text/plain') return 'txt';
    if (mimeType === 'text/html') return 'html';
    
    return 'unknown';
  }
}

// Create a singleton instance
export const contractExtractor = new ContractExtractor();
