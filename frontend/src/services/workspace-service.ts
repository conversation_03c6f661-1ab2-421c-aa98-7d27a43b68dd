/**
 * Centralized Workspace Service
 * Consolidates all workspace-related API operations and data management
 */

import {
  Workspace,
  WorkspaceApiResponse,
  WorkspaceCreatePayload,
  WorkspaceUpdatePayload,
  mapApiResponseToWorkspace,
  isWorkspaceApiResponse
} from '../types/workspace';
import { WorkspaceService as APIWorkspaceService } from './api-services';

// Request deduplication and caching
const activeRequests = new Map<string, Promise<any>>();
const abortControllers = new Map<string, AbortController>();

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const workspaceCache = new Map<string, { data: Workspace[]; timestamp: number }>();

/**
 * Centralized workspace service with request deduplication, caching, and error handling
 */
export class CentralizedWorkspaceService {
  private static instance: CentralizedWorkspaceService;
  
  static getInstance(): CentralizedWorkspaceService {
    if (!CentralizedWorkspaceService.instance) {
      CentralizedWorkspaceService.instance = new CentralizedWorkspaceService();
    }
    return CentralizedWorkspaceService.instance;
  }

  /**
   * Fetch all workspaces for the current user
   */
  async fetchWorkspaces(getToken: () => Promise<string | null>, force = false): Promise<Workspace[]> {
    const requestId = 'fetch-workspaces';

    // Check cache first (unless forced)
    if (!force) {
      const cached = this.getCachedWorkspaces();
      if (cached) {
        console.log('🔄 WorkspaceService: Using cached workspaces');
        return cached;
      }
    }

    // Check if request is already active - if so, wait for it instead of cancelling
    if (activeRequests.has(requestId)) {
      console.log('🔄 WorkspaceService: Request already active, waiting for result');
      try {
        return await activeRequests.get(requestId)!;
      } catch (error: any) {
        // If the active request failed, we'll start a new one below
        console.log('🔄 WorkspaceService: Active request failed, starting new request');
        activeRequests.delete(requestId);
        abortControllers.delete(requestId);
      }
    }

    // Create new abort controller (don't cancel previous requests)
    const abortController = new AbortController();
    abortControllers.set(requestId, abortController);

    console.log('🔄 WorkspaceService: Starting workspace fetch...');

    const requestPromise = this.performFetch(abortController.signal, getToken);
    activeRequests.set(requestId, requestPromise);

    try {
      const workspaces = await requestPromise;

      // Cache the results
      this.setCachedWorkspaces(workspaces);

      console.log('✅ WorkspaceService: Successfully fetched workspaces:', workspaces.length);
      return workspaces;
    } catch (error: any) {
      if (error.name === 'AbortError' || error.message === 'Request was cancelled') {
        console.log('🚫 WorkspaceService: Request was cancelled');
        // For cancelled requests, try to return cached data if available
        const cached = this.getCachedWorkspaces();
        if (cached) {
          console.log('🔄 WorkspaceService: Returning cached data after cancellation');
          return cached;
        }
        throw error;
      }

      console.error('❌ WorkspaceService: Failed to fetch workspaces:', error);
      throw new Error(error.message || 'Failed to fetch workspaces');
    } finally {
      // Cleanup
      activeRequests.delete(requestId);
      abortControllers.delete(requestId);
    }
  }

  /**
   * Create a new workspace
   */
  async createWorkspace(
    workspaceData: WorkspaceCreatePayload, 
    getToken: () => Promise<string | null>
  ): Promise<Workspace> {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      console.log('🔄 WorkspaceService: Creating workspace:', workspaceData.name);

      const response = await APIWorkspaceService.createWorkspace(workspaceData, token);
      
      if (!response.data) {
        throw new Error('No workspace data received from backend');
      }

      const newWorkspace = mapApiResponseToWorkspace(response.data as WorkspaceApiResponse);
      
      // Invalidate cache to force refresh
      this.invalidateCache();
      
      console.log('✅ WorkspaceService: Successfully created workspace:', newWorkspace.name);
      return newWorkspace;
    } catch (error: any) {
      console.error('❌ WorkspaceService: Failed to create workspace:', error);
      throw new Error(error.message || 'Failed to create workspace');
    }
  }

  /**
   * Update an existing workspace
   */
  async updateWorkspace(
    workspaceId: string,
    workspaceData: WorkspaceUpdatePayload,
    getToken: () => Promise<string | null>
  ): Promise<Workspace> {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      console.log('🔄 WorkspaceService: Updating workspace:', workspaceId);

      const response = await APIWorkspaceService.updateWorkspace(workspaceId, workspaceData, token);
      
      if (!response.data) {
        throw new Error('No workspace data received from backend');
      }

      const updatedWorkspace = mapApiResponseToWorkspace(response.data as WorkspaceApiResponse);
      
      // Update cache
      this.updateCachedWorkspace(updatedWorkspace);
      
      console.log('✅ WorkspaceService: Successfully updated workspace:', updatedWorkspace.name);
      return updatedWorkspace;
    } catch (error: any) {
      console.error('❌ WorkspaceService: Failed to update workspace:', error);
      throw new Error(error.message || 'Failed to update workspace');
    }
  }

  /**
   * Delete a workspace
   */
  async deleteWorkspace(
    workspaceId: string,
    getToken: () => Promise<string | null>
  ): Promise<boolean> {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      console.log('🔄 WorkspaceService: Deleting workspace:', workspaceId);

      const response = await APIWorkspaceService.deleteWorkspace(workspaceId, token);
      
      if (response.data) {
        // Remove from cache
        this.removeCachedWorkspace(workspaceId);
        console.log('✅ WorkspaceService: Successfully deleted workspace:', workspaceId);
        return true;
      }
      
      return false;
    } catch (error: any) {
      console.error('❌ WorkspaceService: Failed to delete workspace:', error);
      throw new Error(error.message || 'Failed to delete workspace');
    }
  }

  /**
   * Get a specific workspace by ID
   */
  async getWorkspace(
    workspaceId: string,
    getToken: () => Promise<string | null>
  ): Promise<Workspace | null> {
    try {
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await APIWorkspaceService.getWorkspace(workspaceId, false, token);
      
      if (!response.data) {
        return null;
      }

      return mapApiResponseToWorkspace(response.data as WorkspaceApiResponse);
    } catch (error: any) {
      console.error('❌ WorkspaceService: Failed to get workspace:', error);
      return null;
    }
  }

  /**
   * Perform the actual API fetch
   */
  private async performFetch(signal: AbortSignal, getToken: () => Promise<string | null>): Promise<Workspace[]> {
    // Check if already cancelled before starting
    if (signal.aborted) {
      throw new Error('Request was cancelled');
    }

    const token = await getToken();

    if (!token) {
      throw new Error('No authentication token available');
    }

    // Check again after async operation
    if (signal.aborted) {
      throw new Error('Request was cancelled');
    }

    // Make API call
    const response = await APIWorkspaceService.getWorkspaces(token);

    // Final check after API call
    if (signal.aborted) {
      throw new Error('Request was cancelled');
    }

    if (!response.data) {
      throw new Error('No workspace data received from backend');
    }

    // Validate and map workspace data
    const workspaces: Workspace[] = response.data
      .filter(isWorkspaceApiResponse)
      .map(mapApiResponseToWorkspace);

    return workspaces;
  }

  /**
   * Cache management methods
   */
  private getCachedWorkspaces(): Workspace[] | null {
    const cached = workspaceCache.get('workspaces');
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  private setCachedWorkspaces(workspaces: Workspace[]): void {
    workspaceCache.set('workspaces', {
      data: workspaces,
      timestamp: Date.now(),
    });
  }

  private updateCachedWorkspace(updatedWorkspace: Workspace): void {
    const cached = workspaceCache.get('workspaces');
    if (cached) {
      const updatedData = cached.data.map(ws => 
        ws.id === updatedWorkspace.id ? updatedWorkspace : ws
      );
      workspaceCache.set('workspaces', {
        data: updatedData,
        timestamp: cached.timestamp,
      });
    }
  }

  private removeCachedWorkspace(workspaceId: string): void {
    const cached = workspaceCache.get('workspaces');
    if (cached) {
      const updatedData = cached.data.filter(ws => ws.id !== workspaceId);
      workspaceCache.set('workspaces', {
        data: updatedData,
        timestamp: cached.timestamp,
      });
    }
  }

  private invalidateCache(): void {
    workspaceCache.clear();
  }

  /**
   * Request management methods
   */
  cancelRequest(requestId: string): void {
    const controller = abortControllers.get(requestId);
    if (controller) {
      controller.abort();
      abortControllers.delete(requestId);
      activeRequests.delete(requestId);
      console.log(`🚫 WorkspaceService: Cancelled request: ${requestId}`);
    }
  }

  cancelAllRequests(): void {
    console.log('🚫 WorkspaceService: Cancelling all active requests');
    abortControllers.forEach((controller, requestId) => {
      controller.abort();
      console.log(`🚫 WorkspaceService: Cancelled request: ${requestId}`);
    });
    abortControllers.clear();
    activeRequests.clear();
  }



  /**
   * Refresh workspaces (force fetch)
   */
  async refreshWorkspaces(getToken: () => Promise<string | null>): Promise<Workspace[]> {
    return this.fetchWorkspaces(getToken, true);
  }

  /**
   * Check if workspaces are loading
   */
  isLoading(): boolean {
    return activeRequests.size > 0;
  }
}

// Export singleton instance
export const centralizedWorkspaceService = CentralizedWorkspaceService.getInstance();
