{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "noEmitOnError": false, "moduleResolution": "node", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "downlevelIteration": true, "forceConsistentCasingInFileNames": true, "strict": false, "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "exclude": ["src/tempobook"], "references": [{"path": "./tsconfig.node.json"}]}