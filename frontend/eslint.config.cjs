// ESLint 9+ config for TypeScript React project (CommonJS for compatibility with "type": "module")
const tsParser = require('@typescript-eslint/parser');
const tsPlugin = require('@typescript-eslint/eslint-plugin');
const reactPlugin = require('eslint-plugin-react');

module.exports = [
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 2021,
        sourceType: 'module',
        ecmaFeatures: { jsx: true },
      },
    },
    plugins: {
      '@typescript-eslint': tsPlugin,
      react: reactPlugin,
    },
    rules: Object.assign({},
      tsPlugin.configs.recommended.rules,
      reactPlugin.configs.recommended.rules,
      {
        // Place to specify additional or override rules
        'react/react-in-jsx-scope': 'off',
      }
    ),
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
  {
    ignores: ['node_modules', 'dist', 'build'],
  },
];
