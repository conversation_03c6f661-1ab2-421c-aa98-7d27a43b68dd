[phases.setup]
nixPkgs = ["nodejs", "python39", "python39Packages.pip", "python39Packages.setuptools", "python39Packages.wheel"]

[phases.install]
cmds = [
    "cd frontend && npm install",
    "cd backend && pip install --upgrade pip setuptools wheel",
    "cd backend && pip install -r requirements.txt --no-cache-dir"
]

[phases.build]
cmds = [
    "cd frontend && npm run build"
]

[start]
cmd = "cd backend && python run.py"
