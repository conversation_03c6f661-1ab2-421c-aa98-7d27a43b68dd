# Railway Deployment Guide

This guide explains how to deploy the Averum Contracts application to Railway.

## 🚀 Deployment Options

### Option 1: Single Service (Recommended)
Deploy both frontend and backend as a single service with the backend serving static files.

### Option 2: Separate Services
Deploy frontend and backend as separate Railway services.

## 📋 Prerequisites

1. **Railway Account**: Sign up at [railway.app](https://railway.app)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **Environment Variables**: Gather all required environment variables

## 🔧 Required Environment Variables

Set these in your Railway project settings:

### Required Variables
```bash
# Supabase Configuration
SUPABASE_URL=https://kdcjdbufciuvvznqnotx.supabase.co
SUPABASE_KEY=your_supabase_service_role_key
SUPABASE_ANON_KEY=your_supabase_anon_key

# Clerk Authentication
CLERK_SECRET_KEY=your_clerk_secret_key
CLERK_PUBLISHABLE_KEY=pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ

# Environment
ENVIRONMENT=production
NODE_ENV=production
```

### Optional Variables
```bash
# AI Services (optional)
GEMINI_API_KEY=your_gemini_api_key
HUGGINGFACE_API_KEY=your_huggingface_api_key

# Redis (optional, for caching)
REDIS_URL=your_redis_url
```

## 🚀 Deployment Steps

### Single Service Deployment

1. **Connect Repository**
   - Go to Railway dashboard
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Choose your repository

2. **Configure Environment Variables**
   - Go to your project settings
   - Add all required environment variables listed above

3. **Deploy**
   - Railway will automatically detect the `nixpacks.toml` and `railway.toml`
   - The build process will:
     - Install Node.js and Python dependencies
     - Build the frontend
     - Start the backend server (which serves both API and frontend)

4. **Access Your Application**
   - Railway will provide a URL like `https://your-app.railway.app`
   - The backend serves the frontend at the root URL
   - API endpoints are available at `/api/*`

### Separate Services Deployment

1. **Deploy Backend Service**
   - Create new Railway service
   - Use `railway-backend.toml` configuration
   - Set backend environment variables

2. **Deploy Frontend Service**
   - Create another Railway service
   - Use `railway-frontend.toml` configuration
   - Set `VITE_API_BASE_URL` to your backend service URL

## 🔍 Troubleshooting

### Common Issues

1. **Healthcheck Failures**
   - Ensure `/api/health` endpoint is accessible
   - Check that all required environment variables are set
   - Verify database connectivity

2. **Build Failures**
   - Check that all dependencies are listed in requirements.txt and package.json
   - Ensure Python and Node.js versions are compatible

3. **CORS Issues**
   - Update `BACKEND_CORS_ORIGINS` to include your Railway domain
   - For single service: CORS is handled automatically
   - For separate services: Add frontend URL to CORS origins

4. **Static File Issues**
   - Ensure `dist` folder is created during build
   - Check that static file paths are correct

### Debugging Commands

```bash
# Check logs in Railway dashboard
# Or use Railway CLI:
railway logs

# Check environment variables
railway variables

# Redeploy
railway up
```

## 📊 Monitoring

- Use Railway's built-in monitoring
- Check `/api/health` endpoint for health status
- Monitor logs for errors and performance issues

## 🔒 Security Considerations

1. **Environment Variables**: Never commit sensitive data to git
2. **CORS**: Configure appropriate CORS origins for production
3. **HTTPS**: Railway provides HTTPS by default
4. **Database**: Use Supabase RLS policies for data security

## 🎯 Performance Tips

1. **Caching**: Enable Redis for better performance
2. **CDN**: Consider using Railway's CDN for static assets
3. **Database**: Optimize Supabase queries and indexes
4. **Monitoring**: Set up alerts for performance issues
