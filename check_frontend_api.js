/**
 * <PERSON><PERSON><PERSON> to check if the frontend is properly connected to the backend API
 * Run this in the browser console while on the frontend application
 */

async function checkFrontendApiConnection() {
  console.log('Checking frontend API connection...');
  
  // Test endpoints to check
  const endpoints = [
    '/api/workspaces',
    '/api/users/me',
    '/api/contracts',
    '/api/templates'
  ];
  
  const results = {};
  
  // Check each endpoint
  for (const endpoint of endpoints) {
    try {
      console.log(`Testing endpoint: ${endpoint}`);
      
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const status = response.status;
      let data = null;
      
      try {
        // Try to parse the response as JSON
        data = await response.json();
      } catch (e) {
        console.warn(`Could not parse response from ${endpoint} as JSON`);
      }
      
      results[endpoint] = {
        status,
        isMock: false,
        data: data,
        error: status >= 400 ? 'Error response' : null
      };
      
      // Check if this might be mock data
      if (status === 200 && data) {
        // Look for patterns that might indicate mock data
        const dataStr = JSON.stringify(data);
        if (
          dataStr.includes('"id":"mock-') || 
          dataStr.includes('"id":"c1"') ||
          dataStr.includes('"id":"c2"')
        ) {
          results[endpoint].isMock = true;
          results[endpoint].warning = 'Response appears to contain mock data';
        }
      }
      
    } catch (error) {
      results[endpoint] = {
        status: 'Error',
        error: error.message,
        isMock: false
      };
    }
  }
  
  // Print summary
  console.log('=== API CONNECTION TEST RESULTS ===');
  let mockDataFound = false;
  
  for (const [endpoint, result] of Object.entries(results)) {
    console.log(`${endpoint}: ${result.status === 200 ? '✅' : '❌'} Status: ${result.status}`);
    
    if (result.error) {
      console.log(`  Error: ${result.error}`);
    }
    
    if (result.isMock) {
      console.log(`  ⚠️ WARNING: This endpoint appears to be returning mock data`);
      mockDataFound = true;
    }
  }
  
  if (mockDataFound) {
    console.log('\n⚠️ MOCK DATA DETECTED: Some endpoints appear to be using mock data instead of real API data');
  } else {
    console.log('\n✅ No mock data detected. Frontend appears to be using the real API');
  }
  
  return results;
}

// Run the check
checkFrontendApiConnection();
