#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to apply the database schema to Supabase.
This script connects to Supabase and executes SQL statements to create the missing tables.
"""

import os
import sys
import requests
import json
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client, Client

# Add the backend directory to the path so we can import from app
backend_path = Path(__file__).parent / "backend"
sys.path.append(str(backend_path))

def apply_schema():
    """
    Apply the database schema to Supabase.
    """
    # Load environment variables from backend/.env
    env_path = backend_path / ".env"
    load_dotenv(dotenv_path=env_path)

    # Get Supabase credentials from environment variables
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")

    if not supabase_url or not supabase_key:
        print("Error: SUPABASE_URL and SUPABASE_KEY environment variables must be set.")
        print(f"Current values: URL={supabase_url}, KEY={'*****' if supabase_key else None}")
        return False

    try:
        print(f"Connecting to Supabase at: {supabase_url}")
        
        # Create Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # Check which tables already exist
        existing_tables = check_existing_tables(supabase)
        print(f"Existing tables: {', '.join(existing_tables)}")
        
        # Read the schema SQL file
        schema_path = backend_path / "app" / "db" / "schema.sql"
        with open(schema_path, "r") as f:
            schema_sql = f.read()
        
        # Split the SQL into individual statements
        statements = schema_sql.split(";")
        
        # Apply the schema using Supabase's REST API
        apply_schema_via_rest_api(supabase_url, supabase_key, statements, existing_tables)
        
        # Verify the tables were created
        new_existing_tables = check_existing_tables(supabase)
        print(f"Tables after schema application: {', '.join(new_existing_tables)}")
        
        # Check for missing tables
        all_tables = [
            "users", "workspaces", "workspace_members", "contracts", "templates", "folders",
            "contract_signers", "clauses", "documents", "document_signers", "permissions",
            "roles", "notifications", "activity_logs", "export_history", "ai_analysis_results"
        ]
        
        missing_tables = [table for table in all_tables if table not in new_existing_tables]
        if missing_tables:
            print(f"Warning: The following tables are still missing: {', '.join(missing_tables)}")
        else:
            print("All tables have been successfully created!")
        
        return True
    
    except Exception as e:
        print(f"Error applying schema: {e}")
        return False

def check_existing_tables(supabase: Client):
    """
    Check which tables already exist in the database.
    """
    tables = [
        "users", "workspaces", "workspace_members", "contracts", "templates", "folders",
        "contract_signers", "clauses", "documents", "document_signers"
    ]
    
    existing_tables = []
    
    for table in tables:
        try:
            response = supabase.table(table).select("*").limit(1).execute()
            existing_tables.append(table)
        except Exception:
            pass
    
    return existing_tables

def apply_schema_via_rest_api(supabase_url, supabase_key, statements, existing_tables):
    """
    Apply the schema using Supabase's REST API.
    """
    # Supabase SQL API endpoint
    sql_url = f"{supabase_url}/rest/v1/rpc/execute_sql"
    
    # Headers for the request
    headers = {
        "apikey": supabase_key,
        "Authorization": f"Bearer {supabase_key}",
        "Content-Type": "application/json",
        "Prefer": "return=minimal"
    }
    
    # Process each statement
    for statement in statements:
        statement = statement.strip()
        if not statement:
            continue
        
        # Skip statements for tables that already exist
        skip = False
        for table in existing_tables:
            if f"CREATE TABLE IF NOT EXISTS {table}" in statement:
                print(f"Skipping creation of existing table: {table}")
                skip = True
                break
        
        if skip:
            continue
        
        # Execute the SQL statement
        print(f"Executing: {statement[:100]}...")
        
        try:
            # Prepare the request payload
            payload = {
                "sql": statement
            }
            
            # Send the request
            response = requests.post(sql_url, headers=headers, json=payload)
            
            # Check the response
            if response.status_code == 200:
                print("  Success!")
            else:
                print(f"  Error: {response.status_code} - {response.text}")
        
        except Exception as e:
            print(f"  Error executing statement: {e}")

if __name__ == "__main__":
    print("Applying database schema to Supabase...")
    success = apply_schema()
    
    if success:
        print("Schema application completed.")
        sys.exit(0)
    else:
        print("Schema application failed.")
        sys.exit(1)
