-- Missing tables for LegalAI V4 database schema
-- Apply these directly in Supabase SQL Editor

-- 1. Permissions table (system-wide permission definitions)
CREATE TABLE IF NOT EXISTS permissions (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Roles table (workspace-specific role definitions)
CREATE TABLE IF NOT EXISTS roles (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    permissions TEXT[] DEFAULT '{}',
    is_system BOOLEAN DEFAULT FALSE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(name, workspace_id)
);

-- 3. Notifications table (user notification system)
CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL, -- 'approval', 'contract', 'system', 'mention'
    status TEXT NOT NULL DEFAULT 'unread', -- 'unread', 'read'
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    sender_id TEXT REFERENCES users(id),
    entity_id TEXT,
    entity_type TEXT,
    action_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- 4. Activity Logs table (audit trail and activity tracking)
CREATE TABLE IF NOT EXISTS activity_logs (
    id TEXT PRIMARY KEY,
    event_type TEXT NOT NULL,
    user_id TEXT NOT NULL REFERENCES users(id),
    user_name TEXT NOT NULL,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    workspace_name TEXT NOT NULL,
    target_id TEXT,
    target_type TEXT,
    target_name TEXT,
    details JSONB,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Export History table (document export tracking)
CREATE TABLE IF NOT EXISTS export_history (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    contract_title TEXT NOT NULL,
    format TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    download_url TEXT NOT NULL,
    template_used TEXT,
    branding_settings JSONB,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    exported_by JSONB NOT NULL,
    exported_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    download_count INTEGER DEFAULT 0,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 6. AI Analysis Results table (AI contract analysis storage)
CREATE TABLE IF NOT EXISTS ai_analysis_results (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    risk_score FLOAT NOT NULL,
    compliance_score FLOAT NOT NULL,
    language_clarity FLOAT NOT NULL,
    key_risks JSONB DEFAULT '[]',
    suggestions JSONB DEFAULT '[]',
    extracted_clauses JSONB DEFAULT '[]',
    compliance_issues JSONB DEFAULT '[]',
    obligations JSONB DEFAULT '[]',
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Update clauses table to add workspace_id
ALTER TABLE clauses ADD COLUMN IF NOT EXISTS workspace_id TEXT REFERENCES workspaces(id) ON DELETE CASCADE;

-- Update documents table to add file metadata fields
ALTER TABLE documents ADD COLUMN IF NOT EXISTS file_path TEXT;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS file_info JSONB;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS folder TEXT;

-- Performance Indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_workspace ON notifications(user_id, workspace_id);
CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);
CREATE INDEX IF NOT EXISTS idx_activity_logs_workspace_created ON activity_logs(workspace_id, created_at);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_export_history_workspace_contract ON export_history(workspace_id, contract_id);
CREATE INDEX IF NOT EXISTS idx_export_history_status ON export_history(status);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_contract ON ai_analysis_results(contract_id);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_workspace ON ai_analysis_results(workspace_id);
CREATE INDEX IF NOT EXISTS idx_roles_workspace ON roles(workspace_id);
CREATE INDEX IF NOT EXISTS idx_clauses_workspace ON clauses(workspace_id);
