#!/bin/bash

# LegalAI V4 Demo Data Seeding Script
# This script sets up the demo environment with realistic sample data

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check environment variables
check_env_vars() {
    print_status "Checking environment variables..."
    
    if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_KEY" ]; then
        print_error "Required environment variables not set:"
        echo "  - SUPABASE_URL"
        echo "  - SUPABASE_KEY"
        echo ""
        echo "Please set these variables or create a .env file in the backend directory."
        echo "Example:"
        echo "  export SUPABASE_URL='your-supabase-url'"
        echo "  export SUPABASE_KEY='your-supabase-key'"
        exit 1
    fi
    
    print_success "Environment variables are set"
}

# Function to check Python and dependencies
check_python_setup() {
    print_status "Checking Python setup..."
    
    if ! command_exists python3; then
        print_error "Python 3 is not installed"
        exit 1
    fi
    
    if ! command_exists pip3; then
        print_error "pip3 is not installed"
        exit 1
    fi
    
    print_success "Python setup is ready"
}

# Function to install Python dependencies
install_dependencies() {
    print_status "Installing Python dependencies..."
    
    cd backend
    
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
        print_success "Dependencies installed"
    else
        print_warning "requirements.txt not found, installing basic dependencies"
        pip3 install supabase python-dotenv
    fi
    
    cd ..
}

# Function to load environment variables
load_env() {
    if [ -f "backend/.env" ]; then
        print_status "Loading environment variables from backend/.env"
        export $(cat backend/.env | grep -v '^#' | xargs)
    elif [ -f ".env" ]; then
        print_status "Loading environment variables from .env"
        export $(cat .env | grep -v '^#' | xargs)
    else
        print_warning "No .env file found, using system environment variables"
    fi
}

# Function to run the seeding script
run_seeding() {
    print_status "Starting demo data seeding..."
    
    cd backend
    
    # Run the seeding script
    if [ "$1" = "--clear" ]; then
        print_warning "Clearing existing demo data..."
        python3 seed_demo_data.py --clear
    else
        python3 seed_demo_data.py
    fi
    
    cd ..
}

# Function to verify seeding
verify_seeding() {
    print_status "Verifying seeding results..."
    
    # Here you could add verification logic
    # For now, we'll just check if the script completed successfully
    if [ $? -eq 0 ]; then
        print_success "Demo data seeding completed successfully!"
        echo ""
        echo "🎉 Your LegalAI V4 demo environment is ready!"
        echo ""
        echo "Demo users created:"
        echo "  📧 <EMAIL> - Legal Operations Manager"
        echo "  📧 <EMAIL> - Senior Legal Counsel"
        echo "  📧 <EMAIL> - Business Development Manager"
        echo "  📧 <EMAIL> - Independent Consultant"
        echo "  📧 <EMAIL> - HR Director"
        echo ""
        echo "Demo workspaces:"
        echo "  🏢 Demo Tech Workspace"
        echo "  🏢 Demo Legal Workspace"
        echo "  🏢 Demo HR Workspace"
        echo "  🏢 Demo Consulting Workspace"
        echo ""
        echo "You can now start the application and explore with realistic data!"
    else
        print_error "Seeding failed. Please check the error messages above."
        exit 1
    fi
}

# Main function
main() {
    echo "🚀 LegalAI V4 Demo Data Seeding Script"
    echo "======================================"
    echo ""
    
    # Parse command line arguments
    CLEAR_DATA=false
    if [ "$1" = "--clear" ] || [ "$1" = "-c" ]; then
        CLEAR_DATA=true
        print_warning "Will clear existing demo data before seeding"
    fi
    
    # Check if help is requested
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --clear, -c    Clear existing demo data before seeding"
        echo "  --help, -h     Show this help message"
        echo ""
        echo "Environment variables required:"
        echo "  SUPABASE_URL   Your Supabase project URL"
        echo "  SUPABASE_KEY   Your Supabase service key"
        echo ""
        echo "Example:"
        echo "  $0              # Seed with existing data"
        echo "  $0 --clear      # Clear existing demo data first"
        exit 0
    fi
    
    # Run checks and setup
    load_env
    check_env_vars
    check_python_setup
    install_dependencies
    
    # Run seeding
    if [ "$CLEAR_DATA" = true ]; then
        run_seeding --clear
    else
        run_seeding
    fi
    
    # Verify results
    verify_seeding
}

# Run main function with all arguments
main "$@"
