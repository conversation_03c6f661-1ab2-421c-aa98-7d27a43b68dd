-- Row Level Security Policies for Missing Tables
-- Apply these after creating the tables

-- Permissions table policies
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;

CREATE POLICY permissions_select_policy ON permissions
    FOR SELECT USING (true);  -- All authenticated users can view permissions

CREATE POLICY permissions_insert_policy ON permissions
    FOR INSERT WITH CHECK (false);  -- Only system can insert permissions

CREATE POLICY permissions_update_policy ON permissions
    FOR UPDATE USING (false);  -- Only system can update permissions

CREATE POLICY permissions_delete_policy ON permissions
    FOR DELETE USING (false);  -- Only system can delete permissions

-- Roles table policies
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY roles_select_policy ON roles
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view roles in workspaces they are members of

CREATE POLICY roles_insert_policy ON roles
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can create roles

CREATE POLICY roles_update_policy ON roles
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) AND is_system = false
    );  -- Only workspace admins can update non-system roles

CREATE POLICY roles_delete_policy ON roles
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) AND is_system = false
    );  -- Only workspace admins can delete non-system roles

-- Notifications table policies
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY notifications_select_policy ON notifications
    FOR SELECT USING (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view their own notifications in workspaces they are members of

CREATE POLICY notifications_insert_policy ON notifications
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create notifications in workspaces they are members of

CREATE POLICY notifications_update_policy ON notifications
    FOR UPDATE USING (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update their own notifications

CREATE POLICY notifications_delete_policy ON notifications
    FOR DELETE USING (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only delete their own notifications

-- Activity Logs table policies
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY activity_logs_select_policy ON activity_logs
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view activity logs in workspaces they are members of

CREATE POLICY activity_logs_insert_policy ON activity_logs
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create activity logs in workspaces they are members of

CREATE POLICY activity_logs_update_policy ON activity_logs
    FOR UPDATE USING (false);  -- Activity logs cannot be updated

CREATE POLICY activity_logs_delete_policy ON activity_logs
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete activity logs

-- Export History table policies
ALTER TABLE export_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY export_history_select_policy ON export_history
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view export history in workspaces they are members of

CREATE POLICY export_history_insert_policy ON export_history
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create export history in workspaces they are members of

CREATE POLICY export_history_update_policy ON export_history
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update export history in workspaces they are members of

CREATE POLICY export_history_delete_policy ON export_history
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete export history

-- AI Analysis Results table policies
ALTER TABLE ai_analysis_results ENABLE ROW LEVEL SECURITY;

CREATE POLICY ai_analysis_results_select_policy ON ai_analysis_results
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view AI analysis results in workspaces they are members of

CREATE POLICY ai_analysis_results_insert_policy ON ai_analysis_results
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create AI analysis results in workspaces they are members of

CREATE POLICY ai_analysis_results_update_policy ON ai_analysis_results
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update AI analysis results in workspaces they are members of

CREATE POLICY ai_analysis_results_delete_policy ON ai_analysis_results
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only delete AI analysis results in workspaces they are members of
