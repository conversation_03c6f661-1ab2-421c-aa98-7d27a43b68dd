import { Extension } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableHeader from '@tiptap/extension-table-header';
import TableCell from '@tiptap/extension-table-cell';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import { Color } from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import Focus from '@tiptap/extension-focus';
import Typography from '@tiptap/extension-typography';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
// import Superscript from '@tiptap/extension-superscript';
// import Subscript from '@tiptap/extension-subscript';
import { DocumentEngineConfig } from '../core/DocumentTypes';

// Custom Legal Document Extensions

// Auto-numbering extension for legal documents
const AutoNumbering = Extension.create({
  name: 'autoNumbering',
  
  addOptions() {
    return {
      types: ['heading'],
      levels: {
        1: { prefix: '', suffix: '.' },
        2: { prefix: '', suffix: '.' },
        3: { prefix: '', suffix: '.' },
      }
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          'data-section-number': {
            default: null,
            parseHTML: element => element.getAttribute('data-section-number'),
            renderHTML: attributes => {
              if (!attributes['data-section-number']) {
                return {};
              }
              return {
                'data-section-number': attributes['data-section-number'],
              };
            },
          },
        },
      },
    ];
  },

  // TODO: Implement custom commands for auto-numbering

  updateSectionNumbers(tr: any) {
    // Implementation for auto-numbering logic
    // This would be expanded to handle complex legal document numbering
    const counters = { 1: 0, 2: 0, 3: 0 };
    
    tr.doc.descendants((node: any, pos: number) => {
      if (node.type.name === 'heading') {
        const level = node.attrs.level;
        if (level <= 3) {
          counters[level as keyof typeof counters]++;
          // Reset lower level counters
          for (let i = level + 1; i <= 3; i++) {
            counters[i as keyof typeof counters] = 0;
          }
          
          const sectionNumber = this.generateSectionNumber(counters, level);
          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            'data-section-number': sectionNumber
          });
        }
      }
    });
  },

  generateSectionNumber(counters: Record<number, number>, level: number): string {
    const parts = [];
    for (let i = 1; i <= level; i++) {
      if (counters[i] > 0) {
        parts.push(counters[i].toString());
      }
    }
    return parts.join('.');
  }
});

// Legal Clause extension for structured legal content
const LegalClause = Extension.create({
  name: 'legalClause',
  
  addOptions() {
    return {
      types: ['paragraph', 'heading'],
      clauseTypes: ['whereas', 'definition', 'covenant', 'condition', 'exhibit']
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          'data-clause-type': {
            default: null,
            parseHTML: element => element.getAttribute('data-clause-type'),
            renderHTML: attributes => {
              if (!attributes['data-clause-type']) {
                return {};
              }
              return {
                'data-clause-type': attributes['data-clause-type'],
                class: `legal-clause legal-clause--${attributes['data-clause-type']}`
              };
            },
          },
          'data-clause-id': {
            default: null,
            parseHTML: element => element.getAttribute('data-clause-id'),
            renderHTML: attributes => {
              if (!attributes['data-clause-id']) {
                return {};
              }
              return {
                'data-clause-id': attributes['data-clause-id'],
              };
            },
          },
        },
      },
    ];
  },

  // TODO: Implement custom commands for clause typing
});

// Cross-reference extension for legal documents
const CrossReference = Extension.create({
  name: 'crossReference',

  addOptions() {
    return {
      HTMLAttributes: {
        class: 'cross-reference',
      },
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: ['textStyle'],
        attributes: {
          'data-cross-ref': {
            default: null,
            parseHTML: element => element.getAttribute('data-cross-ref'),
            renderHTML: attributes => {
              if (!attributes['data-cross-ref']) {
                return {};
              }
              return {
                'data-cross-ref': attributes['data-cross-ref'],
                class: 'cross-reference',
                title: `Reference to section ${attributes['data-cross-ref']}`
              };
            },
          },
        },
      },
    ];
  },

  // TODO: Implement custom commands for cross-references
});

// Document structure tracking extension
const DocumentStructure = Extension.create({
  name: 'documentStructure',
  
  addStorage() {
    return {
      outline: [],
      sections: [],
      lastUpdate: Date.now()
    };
  },

  onUpdate() {
    // Update document structure when content changes
    // TODO: Implement document structure tracking
  },

  addMethods() {
    return {
      updateDocumentStructure: () => {
    const outline: any[] = [];
    const sections: any[] = [];
    
    this.editor.state.doc.descendants((node, pos) => {
      if (node.type.name === 'heading') {
        const section = {
          id: `section-${pos}`,
          type: 'heading',
          level: node.attrs.level,
          title: node.textContent,
          position: pos,
          numbering: node.attrs['data-section-number'] || ''
        };
        
        sections.push(section);
        outline.push({
          id: section.id,
          title: section.title,
          level: section.level,
          numbering: section.numbering,
          children: [],
          sectionId: section.id
        });
      }
    });

        this.storage.outline = outline;
        this.storage.sections = sections;
        this.storage.lastUpdate = Date.now();
      }
    };
  }
});

// Configuration function to create extensions based on mode
export function createTipTapExtensions(config: {
  mode?: string;
  collaboration?: any;
  permissions?: any;
}): any[] {
  try {
    const baseExtensions = [
      StarterKit.configure({
        history: {
          depth: 100,
        },
        heading: {
          levels: [1, 2, 3, 4, 5, 6],
        },
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right', 'justify'],
      }),
      Table.configure({
        resizable: true,
        handleWidth: 5,
        cellMinWidth: 50,
      }),
      TableRow,
      TableHeader,
      TableCell,
      TextStyle,
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      Focus.configure({
        className: 'has-focus',
        mode: 'all',
      }),
      Typography,
      Placeholder.configure({
        placeholder: 'Start typing your document...',
        emptyEditorClass: 'is-editor-empty',
      }),
      CharacterCount,
      // Superscript,
      // Subscript,
    ];

    // Add legal document specific extensions (simplified to avoid schema conflicts)
    const legalExtensions = [
      // Temporarily disable custom extensions to fix schema issues
      // AutoNumbering,
      // LegalClause,
      // CrossReference,
      // DocumentStructure,
    ];

    // Add collaboration extensions if enabled (placeholder for future)
    const collaborationExtensions: any[] = [];
    if (config.collaboration?.enabled) {
      // Future: Add Y.js collaboration extensions
      // collaborationExtensions.push(Collaboration, CollaborationCursor);
    }

    return [
      ...baseExtensions,
      ...legalExtensions,
      ...collaborationExtensions,
    ];
  } catch (error) {
    console.error('Error creating TipTap extensions:', error);
    // Return minimal safe extensions as fallback
    return [
      StarterKit,
      Underline,
      TextStyle,
      Placeholder.configure({
        placeholder: 'Start typing your document...',
        emptyEditorClass: 'is-editor-empty',
      }),
    ];
  }
}

// Export individual extensions for custom usage
export {
  AutoNumbering,
  LegalClause,
  CrossReference,
  DocumentStructure,
};
