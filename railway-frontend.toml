[build]
builder = "nixpacks"

[deploy]
startCommand = "npm run preview"
healthcheckPath = "/"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

[env]
NODE_ENV = "production"
VITE_API_BASE_URL = "${{RAILWAY_STATIC_URL}}/api"
VITE_CLERK_PUBLISHABLE_KEY = "pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ"
VITE_SUPABASE_URL = "https://kdcjdbufciuvvznqnotx.supabase.co"
VITE_SUPABASE_ANON_KEY = "${{SUPABASE_ANON_KEY}}"
VITE_ENVIRONMENT = "production"
VITE_USE_NEW_DOCUMENT_ENGINE = "true"
