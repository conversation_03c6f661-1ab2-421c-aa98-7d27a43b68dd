"""
Safe test for document generation dependencies (avoiding WeasyPrint import issues)
"""

import sys
import os

print("🔧 Testing Document Generation Dependencies (Safe Mode)")
print("=" * 60)

# Test ReportLab (primary PDF generator)
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet
    from reportlab.lib import colors
    print("✅ ReportLab: Available")
    reportlab_available = True
except ImportError as e:
    print(f"❌ ReportLab: Not available - {e}")
    reportlab_available = False

# Test python-docx
try:
    from docx import Document as DocxDocument
    print("✅ python-docx: Available")
    docx_available = True
except ImportError as e:
    print(f"❌ python-docx: Not available - {e}")
    docx_available = False

# Test Jinja2
try:
    from jinja2 import Template, Environment, FileSystemLoader
    print("✅ Jinja2: Available")
    jinja2_available = True
except ImportError as e:
    print(f"❌ Jinja2: Not available - {e}")
    jinja2_available = False

# Note about WeasyPrint
print("⚠️ WeasyPrint: Skipped (requires system dependencies on macOS)")

print("\n" + "=" * 60)
print("📋 Dependency Summary:")
print(f"   ReportLab (PDF): {'✅' if reportlab_available else '❌'}")
print(f"   python-docx (DOCX): {'✅' if docx_available else '❌'}")
print(f"   Jinja2 (Templates): {'✅' if jinja2_available else '❌'}")
print(f"   WeasyPrint (Advanced PDF): ⚠️ (Optional, system deps required)")

# Test comprehensive PDF generation with ReportLab
if reportlab_available:
    print("\n🔄 Testing Professional PDF Generation with ReportLab...")
    try:
        import tempfile
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT
        
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
            # Create document
            doc = SimpleDocTemplate(
                temp_file.name,
                pagesize=letter,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Get styles
            styles = getSampleStyleSheet()
            
            # Create custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#1f2937')
            )
            
            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                textColor=colors.HexColor('#374151')
            )
            
            # Build document content
            story = []
            
            # Title
            story.append(Paragraph("SOFTWARE DEVELOPMENT AGREEMENT", title_style))
            story.append(Spacer(1, 20))
            
            # Contract metadata table
            contract_data = [
                ['Contract Type:', 'Service Agreement'],
                ['Effective Date:', 'January 1, 2024'],
                ['Contract Value:', '$150,000.00'],
                ['Status:', 'Draft']
            ]
            
            contract_table = Table(contract_data, colWidths=[2*inch, 3*inch])
            contract_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f3f4f6')),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#e5e7eb'))
            ]))
            
            story.append(contract_table)
            story.append(Spacer(1, 20))
            
            # Parties section
            story.append(Paragraph("PARTIES", heading_style))
            story.append(Paragraph("<b>Party A:</b> TechCorp Inc.<br/>123 Tech Street, Silicon Valley, CA 94000<br/>Representative: John Smith, CEO", styles['Normal']))
            story.append(Spacer(1, 10))
            story.append(Paragraph("<b>Party B:</b> ClientCorp LLC<br/>456 Business Ave, New York, NY 10001<br/>Representative: Jane Doe, CTO", styles['Normal']))
            story.append(Spacer(1, 20))
            
            # Terms section
            story.append(Paragraph("TERMS AND CONDITIONS", heading_style))
            
            clauses = [
                ("1. Scope of Work", "The Service Provider agrees to develop a custom web application according to the specifications outlined in Exhibit A."),
                ("2. Payment Terms", "Client agrees to pay the total contract amount of $150,000 in four equal installments of $37,500 each."),
                ("3. Intellectual Property", "All intellectual property created during this engagement shall remain the property of the Client."),
                ("4. Confidentiality", "Both parties agree to maintain the confidentiality of all proprietary information."),
                ("5. Termination", "Either party may terminate this agreement with 30 days written notice.")
            ]
            
            for title, content in clauses:
                story.append(Paragraph(f"<b>{title}</b>", styles['Normal']))
                story.append(Paragraph(content, styles['Normal']))
                story.append(Spacer(1, 12))
            
            # Signature section
            story.append(Spacer(1, 30))
            story.append(Paragraph("SIGNATURES", heading_style))
            story.append(Paragraph("IN WITNESS WHEREOF, the parties hereto have executed this Agreement as of the Effective Date first written above.", styles['Normal']))
            story.append(Spacer(1, 30))
            
            # Signature blocks
            sig_data = [
                ['Party A: TechCorp Inc.', 'Party B: ClientCorp LLC'],
                ['', ''],
                ['Signature: ________________________', 'Signature: ________________________'],
                ['Date: ________________________', 'Date: ________________________'],
                ['Name: John Smith', 'Name: Jane Doe'],
                ['Title: CEO', 'Title: CTO']
            ]
            
            sig_table = Table(sig_data, colWidths=[3*inch, 3*inch])
            sig_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
            ]))
            
            story.append(sig_table)
            
            # Footer
            story.append(Spacer(1, 30))
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=8,
                alignment=TA_CENTER,
                textColor=colors.HexColor('#6b7280')
            )
            story.append(Paragraph("Generated by Averum Contracts - Professional Document Management", footer_style))
            
            # Build PDF
            doc.build(story)
            
            # Check file size
            file_size = os.path.getsize(temp_file.name)
            print(f"✅ Professional PDF Generation: Success ({file_size:,} bytes)")
            print(f"   📄 Features: Custom styling, tables, signatures, branding")
            
            # Clean up
            os.unlink(temp_file.name)
            
    except Exception as e:
        print(f"❌ PDF Generation Test: Failed - {e}")

# Test DOCX generation with advanced features
if docx_available:
    print("\n🔄 Testing Professional DOCX Generation...")
    try:
        import tempfile
        from docx import Document
        from docx.shared import Inches
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        
        with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as temp_file:
            doc = Document()
            
            # Title
            title = doc.add_heading('SOFTWARE DEVELOPMENT AGREEMENT', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Contract metadata
            doc.add_heading('Contract Information', level=2)
            table = doc.add_table(rows=4, cols=2)
            table.style = 'Table Grid'
            
            cells = [
                ('Contract Type:', 'Service Agreement'),
                ('Effective Date:', 'January 1, 2024'),
                ('Contract Value:', '$150,000.00'),
                ('Status:', 'Draft')
            ]
            
            for i, (label, value) in enumerate(cells):
                table.cell(i, 0).text = label
                table.cell(i, 1).text = value
            
            # Parties
            doc.add_heading('Parties', level=2)
            doc.add_paragraph('Party A: TechCorp Inc.\n123 Tech Street, Silicon Valley, CA 94000\nRepresentative: John Smith, CEO')
            doc.add_paragraph('Party B: ClientCorp LLC\n456 Business Ave, New York, NY 10001\nRepresentative: Jane Doe, CTO')
            
            # Terms
            doc.add_heading('Terms and Conditions', level=2)
            clauses = [
                ("Scope of Work", "The Service Provider agrees to develop a custom web application."),
                ("Payment Terms", "Client agrees to pay $150,000 in four equal installments."),
                ("Intellectual Property", "All IP created shall remain the property of the Client."),
                ("Confidentiality", "Both parties agree to maintain confidentiality."),
                ("Termination", "Either party may terminate with 30 days notice.")
            ]
            
            for i, (title, content) in enumerate(clauses, 1):
                doc.add_paragraph(f"{i}. {title}", style='Heading 3')
                doc.add_paragraph(content)
            
            # Signatures
            doc.add_heading('Signatures', level=2)
            doc.add_paragraph('IN WITNESS WHEREOF, the parties hereto have executed this Agreement.')
            
            # Save document
            doc.save(temp_file.name)
            
            # Check file size
            file_size = os.path.getsize(temp_file.name)
            print(f"✅ Professional DOCX Generation: Success ({file_size:,} bytes)")
            print(f"   📄 Features: Headings, tables, formatting, structure")
            
            # Clean up
            os.unlink(temp_file.name)
            
    except Exception as e:
        print(f"❌ DOCX Generation Test: Failed - {e}")

# Test advanced template rendering
if jinja2_available:
    print("\n🔄 Testing Advanced Template Rendering...")
    try:
        from jinja2 import Template
        
        template_content = """
<!DOCTYPE html>
<html>
<head>
    <title>{{ contract.title }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .section { margin: 20px 0; }
        .clause { margin: 15px 0; padding: 10px; border-left: 3px solid #007acc; }
        .signature { margin-top: 40px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ contract.title }}</h1>
        <p>Contract Value: ${{ "{:,.2f}".format(contract.value) }}</p>
    </div>
    
    <div class="section">
        <h2>Parties</h2>
        {% for party in contract.parties %}
        <div class="party">
            <strong>{{ party.name }}</strong><br>
            {{ party.address }}<br>
            Representative: {{ party.representative }}
        </div>
        {% endfor %}
    </div>
    
    <div class="section">
        <h2>Terms and Conditions</h2>
        {% for clause in contract.clauses %}
        <div class="clause">
            <h4>{{ loop.index }}. {{ clause.title }}</h4>
            <p>{{ clause.content }}</p>
        </div>
        {% endfor %}
    </div>
    
    <div class="signature">
        <h2>Signatures</h2>
        <p>Generated on: {{ generation.date }}</p>
    </div>
</body>
</html>
        """
        
        template = Template(template_content)
        
        # Sample data
        contract_data = {
            "title": "Software Development Agreement",
            "value": 150000.00,
            "parties": [
                {
                    "name": "TechCorp Inc.",
                    "address": "123 Tech Street, Silicon Valley, CA 94000",
                    "representative": "John Smith, CEO"
                },
                {
                    "name": "ClientCorp LLC", 
                    "address": "456 Business Ave, New York, NY 10001",
                    "representative": "Jane Doe, CTO"
                }
            ],
            "clauses": [
                {"title": "Scope of Work", "content": "Development of custom web application."},
                {"title": "Payment Terms", "content": "Payment in four equal installments."},
                {"title": "Intellectual Property", "content": "All IP remains with Client."}
            ]
        }
        
        generation_data = {
            "date": "January 1, 2024"
        }
        
        rendered = template.render(contract=contract_data, generation=generation_data)
        
        # Check if key elements are rendered
        checks = [
            "Software Development Agreement" in rendered,
            "TechCorp Inc." in rendered,
            "$150,000.00" in rendered,
            "Scope of Work" in rendered,
            len(rendered) > 1000  # Reasonable size check
        ]
        
        if all(checks):
            print(f"✅ Advanced Template Rendering: Success ({len(rendered):,} characters)")
            print(f"   📄 Features: Loops, filters, formatting, conditionals")
        else:
            print("❌ Template Rendering Test: Failed - Content validation failed")
            
    except Exception as e:
        print(f"❌ Template Rendering Test: Failed - {e}")

print("\n" + "=" * 60)

# Overall assessment
working_deps = sum([reportlab_available, docx_available, jinja2_available])
total_deps = 3

if working_deps == total_deps:
    print("🎉 All core dependencies are working! Document generation is ready for production.")
    print("📝 Note: Using ReportLab for PDF generation (WeasyPrint optional)")
    print("✨ Features available:")
    print("   • Professional PDF generation with custom styling")
    print("   • DOCX document creation with tables and formatting") 
    print("   • Advanced HTML template rendering")
    print("   • Batch document processing")
    print("   • Custom branding and styling")
elif working_deps >= 2:
    print("⚠️ Most dependencies are working. Some features may be limited.")
else:
    print("❌ Critical dependencies are missing. Document generation may not work properly.")

print(f"\n📊 Status: {working_deps}/{total_deps} core dependencies working")
print("🚀 Ready to proceed with document generation service integration!")
