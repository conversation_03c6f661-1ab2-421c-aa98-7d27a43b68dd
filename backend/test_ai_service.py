"""
Test script for AI service functionality
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.services.ai_service import ai_service, ContractAnalysisService
from app.core.config import settings

async def test_ai_service():
    """Test the AI service with sample contract text."""
    
    print("🤖 Testing Averum Contracts AI Service")
    print("=" * 50)
    
    # Sample contract text for testing
    sample_contract = """
    EMPLOYMENT AGREEMENT
    
    This Employment Agreement ("Agreement") is entered into on January 1, 2024, 
    between TechCorp Inc., a Delaware corporation ("Company"), and <PERSON> ("Employee").
    
    1. POSITION AND DUTIES
    Employee shall serve as Senior Software Engineer and shall perform duties 
    as assigned by the Company's management.
    
    2. COMPENSATION
    Company shall pay Employee a base salary of $120,000 per year, payable in 
    accordance with Company's standard payroll practices.
    
    3. CONFIDENTIALITY
    Employee agrees to maintain the confidentiality of all proprietary information 
    and trade secrets of the Company.
    
    4. TERMINATION
    Either party may terminate this Agreement with 30 days written notice.
    
    5. GOVERNING LAW
    This Agreement shall be governed by the laws of the State of Delaware.
    """
    
    print(f"📄 Sample Contract Length: {len(sample_contract)} characters")
    print(f"🔧 Environment: {settings.ENVIRONMENT}")
    print(f"🔑 HuggingFace API Key: {'✅ Configured' if settings.HUGGINGFACE_API_KEY else '❌ Missing'}")
    print(f"🔑 Gemini API Key: {'✅ Configured' if settings.GEMINI_API_KEY else '❌ Missing'}")
    print()
    
    try:
        print("🚀 Starting AI Analysis...")
        
        # Test the AI service
        result = await ai_service.analyze_contract(sample_contract, "contract_analysis")
        
        print("✅ AI Analysis Completed Successfully!")
        print("=" * 50)
        print(f"📊 Analysis Results:")
        print(f"   Contract Type: {result.contract_type}")
        print(f"   Risk Score: {result.risk_score:.2f}")
        print(f"   Compliance Score: {result.compliance_score:.2f}")
        print(f"   Language Clarity: {result.language_clarity:.2f}")
        print(f"   Confidence: {result.confidence:.2f}")
        print(f"   Provider: {result.provider}")
        print(f"   Processing Time: {result.processing_time:.2f}s")
        print()
        
        print(f"🚨 Key Risks ({len(result.key_risks)}):")
        for i, risk in enumerate(result.key_risks[:3], 1):  # Show first 3 risks
            print(f"   {i}. {risk.get('description', 'No description')}")
        
        print(f"💡 Suggestions ({len(result.suggestions)}):")
        for i, suggestion in enumerate(result.suggestions[:3], 1):  # Show first 3 suggestions
            print(f"   {i}. {suggestion.get('title', 'No title')}")
        
        print(f"📋 Extracted Clauses ({len(result.extracted_clauses)}):")
        for i, clause in enumerate(result.extracted_clauses[:3], 1):  # Show first 3 clauses
            print(f"   {i}. {clause.get('title', 'No title')}")
        
        print()
        print("🎉 AI Service Test Completed Successfully!")
        
        # Test caching
        print("\n🔄 Testing Cache Functionality...")
        start_time = asyncio.get_event_loop().time()
        cached_result = await ai_service.analyze_contract(sample_contract, "contract_analysis")
        end_time = asyncio.get_event_loop().time()
        
        if end_time - start_time < 0.1:  # Should be very fast if cached
            print("✅ Cache is working - second analysis was very fast!")
        else:
            print("⚠️ Cache might not be working - second analysis took normal time")
        
        return True
        
    except Exception as e:
        print(f"❌ AI Analysis Failed: {str(e)}")
        print(f"Error Type: {type(e).__name__}")
        
        # Test fallback functionality
        print("\n🔄 Testing Fallback Analysis...")
        try:
            service = ContractAnalysisService()
            fallback_result = service._create_basic_fallback_analysis(sample_contract)
            print("✅ Fallback analysis working!")
            print(f"   Fallback Risk Score: {fallback_result.risk_score}")
            print(f"   Fallback Provider: {fallback_result.provider}")
            return True
        except Exception as fallback_error:
            print(f"❌ Fallback also failed: {str(fallback_error)}")
            return False

async def test_cache_service():
    """Test the cache service functionality."""
    print("\n🗄️ Testing Cache Service")
    print("=" * 30)
    
    try:
        from app.services.cache_service import ai_cache
        
        # Test basic cache operations
        test_key = "test_key"
        test_value = {"test": "data", "timestamp": "2024-01-01"}
        
        # Set value
        await ai_cache.set(test_key, test_value, ttl=60)
        print("✅ Cache set operation successful")
        
        # Get value
        retrieved_value = await ai_cache.get(test_key)
        if retrieved_value == test_value:
            print("✅ Cache get operation successful")
        else:
            print("❌ Cache get operation failed - values don't match")
        
        # Test cache stats
        stats = ai_cache.get_stats()
        print(f"📊 Cache Stats: {stats}")
        
        # Clean up
        await ai_cache.delete(test_key)
        print("✅ Cache delete operation successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Cache test failed: {str(e)}")
        return False

async def main():
    """Main test function."""
    print("🧪 Averum Contracts AI Service Test Suite")
    print("=" * 60)
    
    # Test cache service first
    cache_success = await test_cache_service()
    
    # Test AI service
    ai_success = await test_ai_service()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   Cache Service: {'✅ PASS' if cache_success else '❌ FAIL'}")
    print(f"   AI Service: {'✅ PASS' if ai_success else '❌ FAIL'}")
    
    if cache_success and ai_success:
        print("\n🎉 All tests passed! AI service is ready for production.")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please check the configuration and try again.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
