# Averum Contracts Backend

This is the backend API for the Averum Contracts Management System, built with FastAPI and Supabase.

## Features

- RESTful API with FastAPI
- PostgreSQL database with Supabase
- Authentication with Clerk
- Comprehensive error handling
- API documentation with Swagger UI
- Type validation with Pydantic

## Getting Started

### Prerequisites

- Python 3.8+
- Supabase account
- Clerk account

### Installation

1. Clone the repository
2. Navigate to the backend directory:
   ```
   cd backend
   ```
3. Create a virtual environment:
   ```
   python -m venv venv
   ```
4. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`
5. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
6. Copy the example environment file:
   ```
   cp .env.example .env
   ```
7. Update the `.env` file with your Supabase and Clerk credentials

### Running the Server

```
python run.py
```

The API will be available at http://localhost:8000 and the API documentation at http://localhost:8000/api/docs

## API Endpoints

### Contracts

- `GET /api/contracts` - List all contracts
- `POST /api/contracts` - Create a new contract
- `GET /api/contracts/{contract_id}` - Get a specific contract
- `PUT /api/contracts/{contract_id}` - Update a contract
- `DELETE /api/contracts/{contract_id}` - Delete a contract

### Workspaces

- `GET /api/workspaces` - List all workspaces
- `POST /api/workspaces` - Create a new workspace
- `GET /api/workspaces/{workspace_id}` - Get a specific workspace
- `PUT /api/workspaces/{workspace_id}` - Update a workspace
- `DELETE /api/workspaces/{workspace_id}` - Delete a workspace

### Users

- `GET /api/users/me` - Get current user information
- `PUT /api/users/me` - Update current user information
- `GET /api/users/workspace/{workspace_id}` - Get all users in a workspace
- `POST /api/users/workspace/{workspace_id}/invite` - Invite a user to a workspace

### Templates

- `GET /api/templates` - List all templates
- `POST /api/templates` - Create a new template
- `GET /api/templates/{template_id}` - Get a specific template
- `PUT /api/templates/{template_id}` - Update a template
- `DELETE /api/templates/{template_id}` - Delete a template

## Database Schema

The backend uses the following database tables in Supabase:

1. `users` - User information
2. `workspaces` - Workspace information
3. `workspace_members` - Workspace membership information
4. `contracts` - Contract information
5. `templates` - Template information

## Authentication

The backend uses Clerk for authentication. JWT tokens are verified using Clerk's JWKS endpoint.

## Development

### Running Tests

```
pytest
```

### Code Formatting

```
black .
```

### Type Checking

```
mypy .
```
