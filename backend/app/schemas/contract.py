from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class ContractStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    EXPIRED = "expired"
    TERMINATED = "terminated"
    PENDING_APPROVAL = "pending_approval"
    REJECTED = "rejected"

class Party(BaseModel):
    type: str  # 'individual' or 'company'
    name: str
    address: Optional[str] = None
    representative: Optional[str] = None
    title: Optional[str] = None
    role: Optional[str] = None

class Clause(BaseModel):
    title: str
    content: str

class Attachment(BaseModel):
    name: str
    file_path: str
    file_type: str
    size: Optional[int] = None
    uploaded_at: datetime

class Approver(BaseModel):
    user_id: str
    name: str
    email: str
    status: str = "pending"  # pending, approved, rejected
    comments: Optional[str] = None
    approved_at: Optional[datetime] = None

class ContractBase(BaseModel):
    title: str
    type: str
    jurisdiction: Optional[str] = None
    effective_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None
    description: Optional[str] = None
    counterparty: Optional[str] = None
    value: Optional[str] = None
    currency: Optional[str] = "USD"
    status: ContractStatus = ContractStatus.DRAFT
    workspace_id: str

class ContractCreate(ContractBase):
    parties: Optional[List[Party]] = None
    clauses: Optional[List[Clause]] = None
    attachments: Optional[List[Attachment]] = None
    approvers: Optional[List[Approver]] = None
    approval_process: Optional[str] = "sequential"  # sequential or parallel
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None

class ContractUpdate(BaseModel):
    title: Optional[str] = None
    type: Optional[str] = None
    jurisdiction: Optional[str] = None
    effective_date: Optional[datetime] = None
    expiry_date: Optional[datetime] = None
    description: Optional[str] = None
    counterparty: Optional[str] = None
    value: Optional[str] = None
    currency: Optional[str] = None
    status: Optional[ContractStatus] = None
    parties: Optional[List[Party]] = None
    clauses: Optional[List[Clause]] = None
    attachments: Optional[List[Attachment]] = None
    approvers: Optional[List[Approver]] = None
    approval_process: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    workspace_id: Optional[str] = None

class ContractInDB(ContractBase):
    id: str
    created_by: Dict[str, str]  # Contains name, id, and possibly avatar/initials
    created_at: datetime
    updated_at: Optional[datetime] = None
    parties: Optional[List[Party]] = None
    clauses: Optional[List[Clause]] = None
    attachments: Optional[List[Attachment]] = None
    approvers: Optional[List[Approver]] = None
    approval_process: str = "sequential"
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    starred: Optional[bool] = False
    folder_id: Optional[str] = None

class Contract(ContractInDB):
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
