from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class ComplexityLevel(str, Enum):
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"

class TemplateBase(BaseModel):
    title: str
    description: str
    type: str
    complexity: ComplexityLevel
    industry: Optional[str] = None
    tags: Optional[List[str]] = None
    icon: Optional[str] = None

class TemplateCreate(TemplateBase):
    content: Dict[str, Any]  # Template content structure
    workspace_id: str

class TemplateUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None
    complexity: Optional[ComplexityLevel] = None
    industry: Optional[str] = None
    tags: Optional[List[str]] = None
    icon: Optional[str] = None
    content: Optional[Dict[str, Any]] = None
    workspace_id: Optional[str] = None

class TemplateInDB(TemplateBase):
    id: str
    created_by: Dict[str, str]  # Contains name and id
    created_at: datetime
    updated_at: Optional[datetime] = None
    usage_count: int = 0
    rating: Optional[float] = None
    is_user_created: bool = False
    folder_id: Optional[str] = None
    content: Dict[str, Any]  # Template content structure
    workspace_id: str

class Template(TemplateInDB):
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
