from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class FolderBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="The name of the folder")
    description: Optional[str] = Field(None, max_length=1000, description="Optional description of the folder")
    parent_id: Optional[str] = Field(None, description="ID of the parent folder for nested folders")
    workspace_id: str = Field(..., description="ID of the workspace this folder belongs to")

class FolderCreate(FolderBase):
    """Schema for creating a new folder"""
    pass

class FolderUpdate(BaseModel):
    """Schema for updating an existing folder"""
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="The name of the folder")
    description: Optional[str] = Field(None, max_length=1000, description="Optional description of the folder")
    parent_id: Optional[str] = Field(None, description="ID of the parent folder for nested folders")

class Folder(FolderBase):
    """Schema for folder response"""
    id: str = Field(..., description="Unique identifier for the folder")
    created_by: str = Field(..., description="ID of the user who created the folder")
    created_at: datetime = Field(..., description="Timestamp when the folder was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the folder was last updated")

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
