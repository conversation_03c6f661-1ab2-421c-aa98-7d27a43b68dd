"""
Pydantic schemas for AI analysis functionality.
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class AnalysisType(str, Enum):
    """Types of AI analysis that can be performed."""
    CONTRACT_ANALYSIS = "contract_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    COMPLIANCE_CHECK = "compliance_check"
    CLAUSE_EXTRACTION = "clause_extraction"


class RiskSeverity(str, Enum):
    """Risk severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SuggestionPriority(str, Enum):
    """Priority levels for AI suggestions."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AIRisk(BaseModel):
    """AI-identified risk in a contract."""
    type: str = Field(..., description="Type of risk (e.g., 'liability', 'financial', 'legal')")
    severity: RiskSeverity = Field(..., description="Severity level of the risk")
    description: str = Field(..., description="Description of the risk")
    recommendation: str = Field(..., description="Recommended action to mitigate the risk")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="AI confidence in this risk assessment")


class AISuggestion(BaseModel):
    """AI suggestion for contract improvement."""
    type: str = Field(..., description="Type of suggestion (e.g., 'clause_addition', 'modification')")
    priority: SuggestionPriority = Field(..., description="Priority level of the suggestion")
    title: str = Field(..., description="Title of the suggestion")
    description: str = Field(..., description="Detailed description of the suggestion")
    content: Optional[str] = Field(None, description="Suggested content or clause text")
    reasoning: Optional[str] = Field(None, description="AI reasoning behind this suggestion")


class AIClause(BaseModel):
    """AI-extracted clause from a contract."""
    type: str = Field(..., description="Type of clause (e.g., 'confidentiality', 'termination')")
    title: str = Field(..., description="Title or heading of the clause")
    content: str = Field(..., description="Full text content of the clause")
    importance: str = Field(..., description="Importance level (low, medium, high, critical)")
    position: Optional[int] = Field(None, description="Position in the document")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="AI confidence in clause extraction")


class AIComplianceIssue(BaseModel):
    """AI-identified compliance issue."""
    regulation: str = Field(..., description="Relevant regulation or standard")
    issue_type: str = Field(..., description="Type of compliance issue")
    description: str = Field(..., description="Description of the compliance issue")
    severity: RiskSeverity = Field(..., description="Severity of the compliance issue")
    recommendation: str = Field(..., description="Recommended action to address the issue")


class AIObligation(BaseModel):
    """AI-identified obligation in a contract."""
    party: str = Field(..., description="Party responsible for the obligation")
    obligation_type: str = Field(..., description="Type of obligation")
    description: str = Field(..., description="Description of the obligation")
    deadline: Optional[str] = Field(None, description="Deadline for fulfilling the obligation")
    importance: str = Field(..., description="Importance level of the obligation")


class AIAnalysisRequest(BaseModel):
    """Request for AI analysis of a contract."""
    contract_id: str = Field(..., description="ID of the contract to analyze")
    analysis_type: AnalysisType = Field(AnalysisType.CONTRACT_ANALYSIS, description="Type of analysis to perform")
    options: Optional[Dict[str, Any]] = Field(None, description="Additional analysis options")


class AIAnalysisResponse(BaseModel):
    """Response from AI analysis of a contract."""
    success: bool = Field(..., description="Whether the analysis was successful")
    analysis_id: str = Field(..., description="Unique ID of the analysis result")
    contract_id: str = Field(..., description="ID of the analyzed contract")
    analysis_type: str = Field(..., description="Type of analysis performed")
    contract_type: str = Field(..., description="AI-identified type of contract")
    risk_score: float = Field(..., ge=0.0, le=1.0, description="Overall risk score (0.0 = low risk, 1.0 = high risk)")
    compliance_score: float = Field(..., ge=0.0, le=1.0, description="Compliance score (0.0 = non-compliant, 1.0 = fully compliant)")
    language_clarity: float = Field(..., ge=0.0, le=1.0, description="Language clarity score (0.0 = unclear, 1.0 = very clear)")
    key_risks: List[AIRisk] = Field(default_factory=list, description="List of identified risks")
    suggestions: List[AISuggestion] = Field(default_factory=list, description="List of improvement suggestions")
    extracted_clauses: List[AIClause] = Field(default_factory=list, description="List of extracted clauses")
    compliance_issues: List[AIComplianceIssue] = Field(default_factory=list, description="List of compliance issues")
    obligations: List[AIObligation] = Field(default_factory=list, description="List of identified obligations")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Overall confidence in the analysis")
    provider: str = Field(..., description="AI provider used for analysis")
    processing_time: float = Field(..., description="Time taken to process the analysis (seconds)")
    created_at: datetime = Field(..., description="Timestamp when analysis was created")


class AIAnalysisResult(BaseModel):
    """Stored AI analysis result from database."""
    id: str = Field(..., description="Unique ID of the analysis result")
    contract_id: str = Field(..., description="ID of the analyzed contract")
    risk_score: float = Field(..., ge=0.0, le=1.0, description="Overall risk score")
    compliance_score: float = Field(..., ge=0.0, le=1.0, description="Compliance score")
    language_clarity: float = Field(..., ge=0.0, le=1.0, description="Language clarity score")
    key_risks: List[Dict[str, Any]] = Field(default_factory=list, description="List of identified risks")
    suggestions: List[Dict[str, Any]] = Field(default_factory=list, description="List of improvement suggestions")
    extracted_clauses: List[Dict[str, Any]] = Field(default_factory=list, description="List of extracted clauses")
    compliance_issues: List[Dict[str, Any]] = Field(default_factory=list, description="List of compliance issues")
    obligations: List[Dict[str, Any]] = Field(default_factory=list, description="List of identified obligations")
    workspace_id: str = Field(..., description="ID of the workspace")
    created_at: datetime = Field(..., description="Timestamp when analysis was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when analysis was last updated")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class AIAnalysisSummary(BaseModel):
    """Summary of AI analysis for dashboard display."""
    total_analyses: int = Field(..., description="Total number of analyses performed")
    average_risk_score: float = Field(..., description="Average risk score across all analyses")
    average_compliance_score: float = Field(..., description="Average compliance score")
    high_risk_contracts: int = Field(..., description="Number of high-risk contracts")
    compliance_issues_count: int = Field(..., description="Total number of compliance issues identified")
    most_common_risks: List[Dict[str, Any]] = Field(default_factory=list, description="Most common risk types")
    recent_analyses: List[AIAnalysisResult] = Field(default_factory=list, description="Recent analysis results")


class AIProviderStatus(BaseModel):
    """Status of AI providers."""
    huggingface_available: bool = Field(..., description="Whether Hugging Face API is available")
    gemini_available: bool = Field(..., description="Whether Gemini API is available")
    primary_provider: str = Field(..., description="Currently active primary provider")
    fallback_provider: str = Field(..., description="Currently active fallback provider")
    last_check: datetime = Field(..., description="Last time provider status was checked")


class AIServiceHealth(BaseModel):
    """Health status of the AI service."""
    status: str = Field(..., description="Overall service status (healthy, degraded, unavailable)")
    providers: AIProviderStatus = Field(..., description="Status of individual providers")
    cache_hit_rate: Optional[float] = Field(None, description="Cache hit rate percentage")
    average_response_time: Optional[float] = Field(None, description="Average response time in seconds")
    error_rate: Optional[float] = Field(None, description="Error rate percentage")
    last_successful_analysis: Optional[datetime] = Field(None, description="Timestamp of last successful analysis")
