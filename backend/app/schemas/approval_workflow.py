"""
Pydantic schemas for approval workflow system.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from enum import Enum


class WorkflowType(str, Enum):
    """Types of approval workflows."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    HYBRID = "hybrid"


class ApprovalStatus(str, Enum):
    """Status of individual approvals."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    ESCALATED = "escalated"
    EXPIRED = "expired"
    SKIPPED = "skipped"


class WorkflowStatus(str, Enum):
    """Status of entire workflow."""
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    REJECTED = "rejected"
    CANCELLED = "cancelled"
    ESCALATED = "escalated"


class Priority(str, Enum):
    """Priority levels for approvals."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class ApproverBase(BaseModel):
    """Base model for approvers."""
    user_id: str = Field(..., description="ID of the user who needs to approve")
    order: int = Field(..., description="Order in the approval sequence")
    required: bool = Field(True, description="Whether this approval is required")
    role: str = Field("approver", description="Role of the approver (approver, reviewer, etc.)")
    conditions: Dict[str, Any] = Field(default_factory=dict, description="Conditions for this approval")


class ApproverCreate(ApproverBase):
    """Model for creating an approver."""
    pass


class ApproverInDB(ApproverBase):
    """Model for approver as stored in database."""
    id: str = Field(..., description="Unique identifier for the approval")
    workflow_id: str = Field(..., description="ID of the workflow this approval belongs to")
    status: ApprovalStatus = Field(ApprovalStatus.PENDING, description="Current status of the approval")
    created_at: datetime = Field(..., description="When the approval was created")
    approved_at: Optional[datetime] = Field(None, description="When the approval was completed")
    rejected_at: Optional[datetime] = Field(None, description="When the approval was rejected")
    comments: Optional[str] = Field(None, description="Comments from the approver")
    approved_by: Optional[str] = Field(None, description="User ID who approved")
    rejected_by: Optional[str] = Field(None, description="User ID who rejected")


class Approver(ApproverInDB):
    """Model for approver with user details."""
    user: Optional[Dict[str, Any]] = Field(None, description="User details")
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class EscalationRule(BaseModel):
    """Model for escalation rules."""
    timeout_hours: int = Field(..., description="Hours before escalation")
    escalate_to: str = Field(..., description="User ID to escalate to")
    notification_intervals: List[int] = Field(default_factory=list, description="Hours at which to send reminder notifications")
    auto_approve: bool = Field(False, description="Whether to auto-approve after escalation timeout")


class WorkflowCondition(BaseModel):
    """Model for workflow conditions."""
    field: str = Field(..., description="Field to check (e.g., 'contract_value', 'contract_type')")
    operator: str = Field(..., description="Comparison operator (eq, gt, lt, gte, lte, in, contains)")
    value: Union[str, int, float, List[str]] = Field(..., description="Value to compare against")
    action: str = Field(..., description="Action to take if condition is met (require_approval, skip, escalate)")


class WorkflowTemplateBase(BaseModel):
    """Base model for workflow templates."""
    name: str = Field(..., description="Name of the workflow template")
    description: Optional[str] = Field(None, description="Description of the template")
    workflow_type: WorkflowType = Field(..., description="Type of workflow")
    approvers: List[ApproverCreate] = Field(..., description="List of approvers in the template")
    escalation_rules: Dict[str, EscalationRule] = Field(default_factory=dict, description="Escalation rules")
    conditions: List[WorkflowCondition] = Field(default_factory=list, description="Conditions for applying this template")
    default_due_hours: int = Field(48, description="Default hours for completion")
    workspace_id: str = Field(..., description="Workspace this template belongs to")


class WorkflowTemplateCreate(WorkflowTemplateBase):
    """Model for creating a workflow template."""
    pass


class WorkflowTemplateInDB(WorkflowTemplateBase):
    """Model for workflow template as stored in database."""
    id: str = Field(..., description="Unique identifier for the template")
    created_by: str = Field(..., description="User who created the template")
    created_at: datetime = Field(..., description="When the template was created")
    updated_at: Optional[datetime] = Field(None, description="When the template was last updated")
    is_active: bool = Field(True, description="Whether the template is active")


class WorkflowTemplate(WorkflowTemplateInDB):
    """Model for workflow template with all details."""
    created_by_user: Optional[Dict[str, Any]] = Field(None, description="User who created the template")
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class ApprovalWorkflowBase(BaseModel):
    """Base model for approval workflows."""
    contract_id: str = Field(..., description="ID of the contract being approved")
    workspace_id: str = Field(..., description="ID of the workspace")
    workflow_type: WorkflowType = Field(..., description="Type of workflow")
    due_date: Optional[datetime] = Field(None, description="When the workflow should be completed")
    escalation_rules: Dict[str, Any] = Field(default_factory=dict, description="Escalation rules for this workflow")
    conditions: Dict[str, Any] = Field(default_factory=dict, description="Conditions for this workflow")
    template_id: Optional[str] = Field(None, description="Template used to create this workflow")
    priority: Priority = Field(Priority.MEDIUM, description="Priority of this workflow")


class ApprovalWorkflowCreate(ApprovalWorkflowBase):
    """Model for creating an approval workflow."""
    approvers: List[ApproverCreate] = Field(..., description="List of approvers for this workflow")


class ApprovalWorkflowInDB(ApprovalWorkflowBase):
    """Model for approval workflow as stored in database."""
    id: str = Field(..., description="Unique identifier for the workflow")
    status: WorkflowStatus = Field(WorkflowStatus.DRAFT, description="Current status of the workflow")
    created_by: str = Field(..., description="User who created the workflow")
    created_at: datetime = Field(..., description="When the workflow was created")
    started_at: Optional[datetime] = Field(None, description="When the workflow was started")
    completed_at: Optional[datetime] = Field(None, description="When the workflow was completed")
    rejection_reason: Optional[str] = Field(None, description="Reason for rejection if workflow was rejected")


class ApprovalWorkflow(ApprovalWorkflowInDB):
    """Model for approval workflow with all details."""
    approvers: List[Approver] = Field(default_factory=list, description="List of approvers")
    contract: Optional[Dict[str, Any]] = Field(None, description="Contract details")
    created_by_user: Optional[Dict[str, Any]] = Field(None, description="User who created the workflow")
    template: Optional[WorkflowTemplate] = Field(None, description="Template used for this workflow")
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class ApprovalAction(BaseModel):
    """Model for approval actions."""
    action: str = Field(..., description="Action to take (approve, reject, request_changes)")
    comments: Optional[str] = Field(None, description="Comments for the action")
    conditions: Optional[Dict[str, Any]] = Field(None, description="Additional conditions or metadata")


class ApprovalUpdate(BaseModel):
    """Model for updating an approval."""
    status: Optional[ApprovalStatus] = Field(None, description="New status")
    comments: Optional[str] = Field(None, description="Comments for the update")


class WorkflowProgress(BaseModel):
    """Model for workflow progress information."""
    workflow_id: str = Field(..., description="ID of the workflow")
    total_approvals: int = Field(..., description="Total number of required approvals")
    completed_approvals: int = Field(..., description="Number of completed approvals")
    pending_approvals: int = Field(..., description="Number of pending approvals")
    progress_percentage: float = Field(..., description="Progress as a percentage")
    current_approver: Optional[Dict[str, Any]] = Field(None, description="Current approver (for sequential workflows)")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")


class WorkflowSummary(BaseModel):
    """Model for workflow summary statistics."""
    total_workflows: int = Field(..., description="Total number of workflows")
    active_workflows: int = Field(..., description="Number of active workflows")
    completed_workflows: int = Field(..., description="Number of completed workflows")
    rejected_workflows: int = Field(..., description="Number of rejected workflows")
    overdue_workflows: int = Field(..., description="Number of overdue workflows")
    average_completion_time_hours: float = Field(..., description="Average completion time in hours")


class AIRoutingRecommendation(BaseModel):
    """Model for AI-generated routing recommendations."""
    recommended_approvers: List[str] = Field(..., description="Recommended approver roles")
    workflow_type: WorkflowType = Field(..., description="Recommended workflow type")
    priority: Priority = Field(..., description="Recommended priority")
    estimated_time_hours: int = Field(..., description="Estimated completion time")
    confidence: float = Field(..., description="AI confidence in the recommendation")
    reasoning: str = Field(..., description="Explanation of the recommendation")
    risk_factors: List[str] = Field(default_factory=list, description="Identified risk factors")


class BulkApprovalAction(BaseModel):
    """Model for bulk approval actions."""
    approval_ids: List[str] = Field(..., description="List of approval IDs")
    action: str = Field(..., description="Action to take (approve, reject)")
    comments: Optional[str] = Field(None, description="Comments for all approvals")


class WorkflowFilters(BaseModel):
    """Model for filtering workflows."""
    workspace_id: str = Field(..., description="Workspace ID")
    status: Optional[WorkflowStatus] = Field(None, description="Filter by status")
    workflow_type: Optional[WorkflowType] = Field(None, description="Filter by workflow type")
    priority: Optional[Priority] = Field(None, description="Filter by priority")
    created_by: Optional[str] = Field(None, description="Filter by creator")
    contract_id: Optional[str] = Field(None, description="Filter by contract")
    due_before: Optional[datetime] = Field(None, description="Filter by due date")
    created_after: Optional[datetime] = Field(None, description="Filter by creation date")
    skip: int = Field(0, ge=0, description="Number of records to skip")
    limit: int = Field(50, ge=1, le=100, description="Maximum number of records to return")


class ApprovalFilters(BaseModel):
    """Model for filtering approvals."""
    workspace_id: str = Field(..., description="Workspace ID")
    user_id: Optional[str] = Field(None, description="Filter by approver")
    status: Optional[ApprovalStatus] = Field(None, description="Filter by status")
    workflow_id: Optional[str] = Field(None, description="Filter by workflow")
    contract_id: Optional[str] = Field(None, description="Filter by contract")
    due_before: Optional[datetime] = Field(None, description="Filter by due date")
    skip: int = Field(0, ge=0, description="Number of records to skip")
    limit: int = Field(50, ge=1, le=100, description="Maximum number of records to return")
