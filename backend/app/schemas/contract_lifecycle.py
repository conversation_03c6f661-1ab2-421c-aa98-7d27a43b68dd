from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

# Contract Version Models
class ContractVersionBase(BaseModel):
    contract_id: str
    version_number: int
    title: str
    content: Dict[str, Any]  # Full contract data snapshot
    changes_summary: Optional[str] = None
    change_details: Optional[Dict[str, Any]] = None

class ContractVersionCreate(ContractVersionBase):
    workspace_id: str

class ContractVersion(ContractVersionBase):
    id: str
    created_by: Dict[str, str]
    created_at: datetime
    is_current: bool = False
    workspace_id: str

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

# Contract Renewal Models
class RenewalStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    COMPLETED = "completed"

class ContractRenewalBase(BaseModel):
    contract_id: str
    original_expiry_date: datetime
    new_expiry_date: datetime
    renewal_period: Optional[str] = None  # e.g., "1 year", "6 months"
    auto_renewal: bool = False
    renewal_notice_period: int = 30  # Days before expiry to send notice
    renewal_terms: Optional[Dict[str, Any]] = None

class ContractRenewalCreate(ContractRenewalBase):
    workspace_id: str

class ContractRenewalUpdate(BaseModel):
    new_expiry_date: Optional[datetime] = None
    renewal_period: Optional[str] = None
    auto_renewal: Optional[bool] = None
    renewal_notice_period: Optional[int] = None
    renewal_status: Optional[RenewalStatus] = None
    renewal_terms: Optional[Dict[str, Any]] = None

class ContractRenewal(ContractRenewalBase):
    id: str
    renewal_status: RenewalStatus = RenewalStatus.PENDING
    requested_by: Dict[str, str]
    requested_at: datetime
    approved_by: Optional[Dict[str, str]] = None
    approved_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    workspace_id: str

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

# Contract Lifecycle Event Models
class LifecycleEventType(str, Enum):
    CREATED = "created"
    UPDATED = "updated"
    STATUS_CHANGED = "status_changed"
    APPROVED = "approved"
    ACTIVATED = "activated"
    RENEWED = "renewed"
    EXPIRED = "expired"
    TERMINATED = "terminated"
    MIGRATED = "migrated"
    DELETED = "deleted"

class ContractLifecycleEventBase(BaseModel):
    contract_id: str
    event_type: LifecycleEventType
    event_description: str
    event_data: Optional[Dict[str, Any]] = None

class ContractLifecycleEventCreate(ContractLifecycleEventBase):
    workspace_id: str
    triggered_by: Optional[str] = None  # user_id or 'system'

class ContractLifecycleEvent(ContractLifecycleEventBase):
    id: str
    triggered_by: Optional[str] = None
    triggered_at: datetime
    workspace_id: str

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

# Contract Reminder Models
class ReminderType(str, Enum):
    EXPIRY_WARNING = "expiry_warning"
    RENEWAL_DUE = "renewal_due"
    REVIEW_REQUIRED = "review_required"
    APPROVAL_PENDING = "approval_pending"
    CUSTOM = "custom"

class ContractReminderBase(BaseModel):
    contract_id: str
    reminder_type: ReminderType
    reminder_date: datetime
    days_before_event: Optional[int] = None
    message: str
    recipients: List[str]  # Array of user IDs

class ContractReminderCreate(ContractReminderBase):
    workspace_id: str

class ContractReminderUpdate(BaseModel):
    reminder_date: Optional[datetime] = None
    message: Optional[str] = None
    recipients: Optional[List[str]] = None
    sent: Optional[bool] = None

class ContractReminder(ContractReminderBase):
    id: str
    sent: bool = False
    sent_at: Optional[datetime] = None
    workspace_id: str

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

# Version Comparison Models
class VersionDiff(BaseModel):
    field: str
    old_value: Any
    new_value: Any
    change_type: str  # "added", "removed", "modified"

class VersionComparison(BaseModel):
    from_version: ContractVersion
    to_version: ContractVersion
    differences: List[VersionDiff]
    summary: str

# Lifecycle Analytics Models
class ContractLifecycleStats(BaseModel):
    total_contracts: int
    active_contracts: int
    expired_contracts: int
    pending_renewals: int
    upcoming_expirations: int  # Next 30 days
    average_contract_duration: Optional[float] = None  # In days
    renewal_rate: Optional[float] = None  # Percentage

class ExpirationAlert(BaseModel):
    contract_id: str
    contract_title: str
    expiry_date: datetime
    days_until_expiry: int
    status: str
    workspace_id: str

# Bulk Operations Models
class BulkRenewalRequest(BaseModel):
    contract_ids: List[str]
    new_expiry_date: datetime
    renewal_period: Optional[str] = None
    renewal_terms: Optional[Dict[str, Any]] = None

class BulkRenewalResponse(BaseModel):
    successful_renewals: List[str]
    failed_renewals: List[Dict[str, str]]  # contract_id -> error_message
    total_processed: int

# Contract Status Transition Models
class StatusTransition(BaseModel):
    from_status: str
    to_status: str
    reason: Optional[str] = None
    effective_date: Optional[datetime] = None

class StatusTransitionRequest(BaseModel):
    contract_id: str
    new_status: str
    reason: Optional[str] = None
    effective_date: Optional[datetime] = None

# Renewal Workflow Models
class RenewalWorkflowStep(BaseModel):
    step_name: str
    step_description: str
    required: bool = True
    completed: bool = False
    completed_at: Optional[datetime] = None
    completed_by: Optional[str] = None

class RenewalWorkflow(BaseModel):
    renewal_id: str
    workflow_steps: List[RenewalWorkflowStep]
    current_step: Optional[str] = None
    workflow_status: str = "in_progress"  # in_progress, completed, failed
    started_at: datetime
    completed_at: Optional[datetime] = None
