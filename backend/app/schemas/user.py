from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class NotificationPreferences(BaseModel):
    email_approvals: bool = True
    email_updates: bool = True
    email_reminders: bool = True
    email_comments: bool = True
    system_approvals: bool = True
    browser_notifications: bool = True
    email_digest_frequency: str = "daily"  # daily, weekly, never

class UserBase(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    title: Optional[str] = None
    company: Optional[str] = None
    timezone: Optional[str] = "UTC"
    bio: Optional[str] = None

class UserCreate(UserBase):
    pass

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    title: Optional[str] = None
    company: Optional[str] = None
    timezone: Optional[str] = None
    bio: Optional[str] = None
    notification_preferences: Optional[NotificationPreferences] = None

class UserInDB(UserBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    workspaces: List[str] = []  # List of workspace IDs
    workspace_roles: Dict[str, str] = {}  # Map of workspace IDs to role IDs
    notification_preferences: NotificationPreferences
    avatar: Optional[str] = None
    initials: str  # Generated from first_name and last_name

class User(UserInDB):
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class UserWithPermissions(User):
    permissions: List[str] = []
