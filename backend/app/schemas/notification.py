from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class NotificationType(str, Enum):
    """Notification type enumeration."""
    APPROVAL = "approval"
    CONTRACT = "contract"
    SYSTEM = "system"
    MENTION = "mention"

class NotificationStatus(str, Enum):
    """Notification status enumeration."""
    UNREAD = "unread"
    READ = "read"

class NotificationBase(BaseModel):
    """Base notification model."""
    title: str = Field(..., description="The title of the notification")
    message: str = Field(..., description="The message content of the notification")
    type: NotificationType = Field(..., description="The type of notification")
    workspace_id: str = Field(..., description="The workspace ID this notification belongs to")
    sender_id: Optional[str] = Field(None, description="The ID of the user who triggered this notification")
    entity_id: Optional[str] = Field(None, description="The ID of the related entity (contract, document, etc.)")
    entity_type: Optional[str] = Field(None, description="The type of the related entity")
    action_url: Optional[str] = Field(None, description="URL to navigate to when notification is clicked")

class NotificationCreate(NotificationBase):
    """Model for creating a notification."""
    user_id: str = Field(..., description="The ID of the user who will receive this notification")

class NotificationUpdate(BaseModel):
    """Model for updating a notification."""
    status: Optional[NotificationStatus] = Field(None, description="The status of the notification")
    read_at: Optional[datetime] = Field(None, description="When the notification was read")

class NotificationInDB(NotificationBase):
    """Model for a notification as stored in the database."""
    id: str = Field(..., description="The unique identifier for the notification")
    user_id: str = Field(..., description="The ID of the user who will receive this notification")
    status: NotificationStatus = Field(default=NotificationStatus.UNREAD, description="The status of the notification")
    created_at: datetime = Field(..., description="When the notification was created")
    read_at: Optional[datetime] = Field(None, description="When the notification was read")

class Notification(NotificationInDB):
    """Model for a notification with all fields."""
    sender: Optional[Dict[str, Any]] = Field(None, description="Information about the sender")
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class NotificationBulkUpdate(BaseModel):
    """Model for bulk notification updates."""
    notification_ids: List[str] = Field(..., description="List of notification IDs to update")
    status: NotificationStatus = Field(..., description="The status to set for all notifications")

class NotificationBulkDelete(BaseModel):
    """Model for bulk notification deletion."""
    notification_ids: List[str] = Field(..., description="List of notification IDs to delete")

class NotificationFilters(BaseModel):
    """Model for notification filtering parameters."""
    workspace_id: str = Field(..., description="The workspace ID to filter by")
    status: Optional[NotificationStatus] = Field(None, description="Filter by notification status")
    type: Optional[NotificationType] = Field(None, description="Filter by notification type")
    sender_id: Optional[str] = Field(None, description="Filter by sender ID")
    entity_type: Optional[str] = Field(None, description="Filter by entity type")
    start_date: Optional[datetime] = Field(None, description="Filter notifications created after this date")
    end_date: Optional[datetime] = Field(None, description="Filter notifications created before this date")
    skip: int = Field(default=0, description="Number of notifications to skip for pagination")
    limit: int = Field(default=50, description="Maximum number of notifications to return")

class NotificationSummary(BaseModel):
    """Model for notification summary statistics."""
    total_count: int = Field(..., description="Total number of notifications")
    unread_count: int = Field(..., description="Number of unread notifications")
    type_counts: Dict[str, int] = Field(..., description="Count of notifications by type")

class NotificationTemplate(BaseModel):
    """Model for notification templates."""
    type: NotificationType = Field(..., description="The type of notification this template is for")
    title_template: str = Field(..., description="Template string for the notification title")
    message_template: str = Field(..., description="Template string for the notification message")
    action_url_template: Optional[str] = Field(None, description="Template string for the action URL")
    variables: List[str] = Field(default=[], description="List of variables used in the templates")

class NotificationPreferencesUpdate(BaseModel):
    """Model for updating notification preferences."""
    email_approvals: Optional[bool] = Field(None, description="Enable email notifications for approvals")
    email_updates: Optional[bool] = Field(None, description="Enable email notifications for updates")
    email_reminders: Optional[bool] = Field(None, description="Enable email notifications for reminders")
    email_comments: Optional[bool] = Field(None, description="Enable email notifications for comments")
    system_approvals: Optional[bool] = Field(None, description="Enable system notifications for approvals")
    browser_notifications: Optional[bool] = Field(None, description="Enable browser notifications")
    email_digest_frequency: Optional[str] = Field(None, description="Frequency for email digest (daily, weekly, never)")

class NotificationWebSocketMessage(BaseModel):
    """Model for WebSocket notification messages."""
    type: str = Field(..., description="Message type (notification, ping, etc.)")
    data: Optional[Dict[str, Any]] = Field(None, description="Message data")
    notification: Optional[Notification] = Field(None, description="Notification data for notification messages")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")

class NotificationBroadcast(BaseModel):
    """Model for broadcasting notifications to multiple users."""
    user_ids: List[str] = Field(..., description="List of user IDs to send the notification to")
    notification_data: NotificationBase = Field(..., description="The notification data to send")
    send_real_time: bool = Field(default=True, description="Whether to send via WebSocket")
    send_email: bool = Field(default=False, description="Whether to send via email")
