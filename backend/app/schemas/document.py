from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

class DocumentSigner(BaseModel):
    """Model for a document signer."""
    id: str = Field(..., description="The unique identifier for the signer")
    document_id: str = Field(..., description="The ID of the document this signer is associated with")
    name: str = Field(..., description="The name of the signer")
    email: str = Field(..., description="The email address of the signer")
    role: str = Field(..., description="The role of the signer (e.g., 'Contract Owner', 'Legal Counsel')")
    status: str = Field(..., description="The status of the signature (pending, completed, declined, current)")
    order: int = Field(..., description="The order in which the signer should sign")
    avatar: Optional[str] = Field(None, description="URL to the signer's avatar image")
    completed_at: Optional[datetime] = Field(None, description="When the signer signed the document")
    declined_at: Optional[datetime] = Field(None, description="When the signer declined to sign the document")
    decline_reason: Optional[str] = Field(None, description="The reason the signer declined to sign")
    signature_data: Optional[Dict[str, Any]] = Field(None, description="The signature data")

class SignatureType(BaseModel):
    """Model for signature data."""
    type: str = Field(..., description="The type of signature (typed, drawn, uploaded)")
    value: str = Field(..., description="The signature value")

class SignatureData(BaseModel):
    """Model for signature data submission."""
    signer_id: str = Field(..., description="The ID of the signer")
    signature: SignatureType = Field(..., description="The signature data")

class FileInfo(BaseModel):
    """Model for file information."""
    filename: str = Field(..., description="Original filename of the document")
    content_type: str = Field(..., description="MIME type of the file")
    size: int = Field(..., description="Size of the file in bytes")
    path: str = Field(..., description="Path to the file in the storage bucket")
    url: str = Field(..., description="URL to access the file")
    uploaded_at: datetime = Field(..., description="When the file was uploaded")

class DocumentBase(BaseModel):
    """Base model for a document."""
    title: str = Field(..., description="The title of the document")
    filename: str = Field(..., description="The filename of the document")
    workspace_id: str = Field(..., description="The ID of the workspace this document belongs to")
    content: Optional[str] = Field(None, description="The content of the document")
    file_url: Optional[str] = Field(None, description="URL to the document file")
    file_path: Optional[str] = Field(None, description="Path to the file in the storage bucket")
    file_info: Optional[FileInfo] = Field(None, description="Information about the stored file")
    folder: Optional[str] = Field(None, description="Folder path within the workspace")
    expires_at: Optional[datetime] = Field(None, description="When the document expires")

class DocumentCreate(DocumentBase):
    """Model for creating a new document."""
    pass

class DocumentUpdate(BaseModel):
    """Model for updating an existing document."""
    title: Optional[str] = None
    filename: Optional[str] = None
    workspace_id: Optional[str] = None
    content: Optional[str] = None
    file_url: Optional[str] = None
    file_path: Optional[str] = None
    file_info: Optional[FileInfo] = None
    folder: Optional[str] = None
    status: Optional[str] = None
    expires_at: Optional[datetime] = None

class Document(DocumentBase):
    """Model for a document with all fields."""
    id: str = Field(..., description="The unique identifier for the document")
    created_at: datetime = Field(..., description="When the document was created")
    created_by: str = Field(..., description="The ID of the user who created this document")
    updated_at: Optional[datetime] = Field(None, description="When the document was last updated")
    status: str = Field(..., description="The status of the document (draft, in_progress, completed, declined)")
    signers: Optional[List[DocumentSigner]] = Field(None, description="The signers of the document")

    class Config:
        from_attributes = True
