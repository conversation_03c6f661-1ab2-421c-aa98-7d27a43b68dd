from pydantic import BaseModel, Field, EmailStr
from typing import Optional
from datetime import datetime

class SignerBase(BaseModel):
    """Base model for a contract signer."""
    name: str = <PERSON>(..., description="The name of the signer")
    email: EmailStr = Field(..., description="The email address of the signer")
    role: str = Field(..., description="The role of the signer (e.g., 'Contract Owner', 'Legal Counsel')")
    status: str = Field("pending", description="The status of the signature (pending, signed, declined, viewed)")
    initials: str = Field(..., description="The initials of the signer")
    order: int = Field(..., description="The order in which the signer should sign")
    avatar: Optional[str] = Field(None, description="URL to the signer's avatar image")

class SignerCreate(SignerBase):
    """Model for creating a new signer."""
    pass

class SignerUpdate(BaseModel):
    """Model for updating an existing signer."""
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    role: Optional[str] = None
    status: Optional[str] = None
    initials: Optional[str] = None
    order: Optional[int] = None
    avatar: Optional[str] = None

class Signer(SignerBase):
    """Model for a signer with all fields."""
    id: str = Field(..., description="The unique identifier for the signer")
    contract_id: str = Field(..., description="The ID of the contract this signer is associated with")
    created_at: datetime = Field(..., description="When the signer was created")
    created_by: str = Field(..., description="The ID of the user who created this signer")
    signed_at: Optional[datetime] = Field(None, description="When the signer signed the contract")
    
    class Config:
        from_attributes = True
