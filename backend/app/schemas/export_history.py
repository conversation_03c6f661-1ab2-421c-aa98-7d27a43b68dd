from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class ExportHistoryBase(BaseModel):
    contract_id: str
    contract_title: str
    format: str
    file_size: int
    download_url: str
    template_used: Optional[str] = None
    branding_settings: Optional[Dict[str, Any]] = None
    workspace_id: str

class ExportHistoryCreate(ExportHistoryBase):
    pass

class ExportHistoryUpdate(BaseModel):
    download_count: Optional[int] = None
    status: Optional[str] = None
    expires_at: Optional[datetime] = None

class ExportHistoryInDB(ExportHistoryBase):
    id: str
    exported_by: Dict[str, str]  # Contains name and id
    exported_at: datetime
    expires_at: Optional[datetime] = None
    download_count: int = 0
    status: str = "active"  # active, expired, archived
    created_at: datetime
    updated_at: Optional[datetime] = None

class ExportHistory(ExportHistoryInDB):
    pass

class ExportHistoryResponse(BaseModel):
    id: str
    contract_id: str
    contract_title: str
    format: str
    file_size: int
    download_url: str
    exported_by: Dict[str, str]
    exported_at: datetime
    expires_at: Optional[datetime] = None
    download_count: int
    status: str
    template_used: Optional[str] = None
    workspace_id: str
