"""
Cache Service for Averum Contracts
Provides caching functionality for AI analysis results and other data
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from app.core.config import settings

logger = logging.getLogger(__name__)


class CacheService:
    """Simple in-memory cache service with TTL support."""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._default_ttl = settings.AI_CACHE_TTL
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if key not in self._cache:
            return None
        
        cache_entry = self._cache[key]
        
        # Check if expired
        if datetime.now() > cache_entry["expires_at"]:
            del self._cache[key]
            return None
        
        logger.debug(f"Cache hit for key: {key}")
        return cache_entry["value"]
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with TTL."""
        if ttl is None:
            ttl = self._default_ttl
        
        expires_at = datetime.now() + timedelta(seconds=ttl)
        
        self._cache[key] = {
            "value": value,
            "expires_at": expires_at,
            "created_at": datetime.now()
        }
        
        logger.debug(f"Cache set for key: {key}, TTL: {ttl}s")
    
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        if key in self._cache:
            del self._cache[key]
            logger.debug(f"Cache deleted for key: {key}")
            return True
        return False
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        self._cache.clear()
        logger.debug("Cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_entries = len(self._cache)
        expired_entries = 0
        
        now = datetime.now()
        for entry in self._cache.values():
            if now > entry["expires_at"]:
                expired_entries += 1
        
        return {
            "total_entries": total_entries,
            "expired_entries": expired_entries,
            "active_entries": total_entries - expired_entries
        }
    
    async def cleanup_expired(self) -> int:
        """Remove expired entries from cache."""
        now = datetime.now()
        expired_keys = []
        
        for key, entry in self._cache.items():
            if now > entry["expires_at"]:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
        
        logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
        return len(expired_keys)


# Global cache service instance
cache_service = CacheService()


# Redis Cache Service (for production use)
class RedisCacheService:
    """Redis-based cache service for production environments."""
    
    def __init__(self):
        self.redis_client = None
        self._default_ttl = settings.AI_CACHE_TTL
        self._initialize_redis()
    
    def _initialize_redis(self):
        """Initialize Redis connection if available."""
        try:
            import redis
            
            if settings.REDIS_URL:
                self.redis_client = redis.from_url(settings.REDIS_URL)
            else:
                self.redis_client = redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    db=settings.REDIS_DB,
                    decode_responses=True
                )
            
            # Test connection
            self.redis_client.ping()
            logger.info("Redis cache service initialized successfully")
            
        except ImportError:
            logger.warning("Redis not available, falling back to in-memory cache")
            self.redis_client = None
        except Exception as e:
            logger.warning(f"Failed to connect to Redis: {e}, falling back to in-memory cache")
            self.redis_client = None
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache."""
        if not self.redis_client:
            return await cache_service.get(key)
        
        try:
            value = self.redis_client.get(key)
            if value:
                logger.debug(f"Redis cache hit for key: {key}")
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"Redis get error for key {key}: {e}")
            return await cache_service.get(key)
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in Redis cache with TTL."""
        if not self.redis_client:
            return await cache_service.set(key, value, ttl)
        
        if ttl is None:
            ttl = self._default_ttl
        
        try:
            serialized_value = json.dumps(value, default=str)
            self.redis_client.setex(key, ttl, serialized_value)
            logger.debug(f"Redis cache set for key: {key}, TTL: {ttl}s")
        except Exception as e:
            logger.error(f"Redis set error for key {key}: {e}")
            await cache_service.set(key, value, ttl)
    
    async def delete(self, key: str) -> bool:
        """Delete value from Redis cache."""
        if not self.redis_client:
            return await cache_service.delete(key)
        
        try:
            result = self.redis_client.delete(key)
            logger.debug(f"Redis cache deleted for key: {key}")
            return bool(result)
        except Exception as e:
            logger.error(f"Redis delete error for key {key}: {e}")
            return await cache_service.delete(key)
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        if not self.redis_client:
            return await cache_service.clear()
        
        try:
            self.redis_client.flushdb()
            logger.debug("Redis cache cleared")
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
            await cache_service.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self.redis_client:
            return cache_service.get_stats()
        
        try:
            info = self.redis_client.info()
            return {
                "total_keys": info.get("db0", {}).get("keys", 0),
                "memory_usage": info.get("used_memory_human", "0B"),
                "connected_clients": info.get("connected_clients", 0),
                "redis_version": info.get("redis_version", "unknown")
            }
        except Exception as e:
            logger.error(f"Redis stats error: {e}")
            return cache_service.get_stats()


# Use Redis cache in production, in-memory cache for development
if settings.ENVIRONMENT == "production" and settings.REDIS_URL:
    ai_cache = RedisCacheService()
else:
    ai_cache = cache_service

logger.info(f"Using cache service: {type(ai_cache).__name__}")
