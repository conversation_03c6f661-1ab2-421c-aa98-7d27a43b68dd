"""
AI Service Layer for Averum Contracts
Implements hybrid Hugging Face + Google Gemini AI integration for contract analysis
"""

import asyncio
import hashlib
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
import httpx
from pydantic import BaseModel
from app.core.config import settings
from app.services.cache_service import ai_cache
from app.services.clause_analysis_service import clause_analysis_service
from app.services.performance_service import performance_service

logger = logging.getLogger(__name__)


class AIAnalysisResult(BaseModel):
    """Standardized AI analysis result structure."""
    contract_type: str
    risk_score: float
    compliance_score: float
    language_clarity: float
    key_risks: List[Dict[str, Any]]
    suggestions: List[Dict[str, Any]]
    extracted_clauses: List[Dict[str, Any]]
    compliance_issues: List[Dict[str, Any]]
    obligations: List[Dict[str, Any]]
    confidence: float
    provider: str
    processing_time: float
    created_at: datetime


class AIServiceError(Exception):
    """Base exception for AI service errors."""
    pass


class RateLimitError(AIServiceError):
    """Raised when rate limits are exceeded."""
    pass


class ModelUnavailableError(AIServiceError):
    """Raised when AI model is unavailable."""
    pass


class HuggingFaceProvider:
    """Hugging Face Inference API provider for legal document analysis."""
    
    def __init__(self):
        self.api_key = settings.HUGGINGFACE_API_KEY
        self.base_url = "https://api-inference.huggingface.co/models"
        self.models = {
            "contract_analysis": "nlpaueb/legal-bert-base-uncased",
            "risk_assessment": "pile-of-law/legalbert-large-1.7M-2",
            "clause_extraction": "nlpaueb/legal-bert-base-uncased"
        }
        self.timeout = settings.AI_TIMEOUT
    
    @performance_service.track_request("ai_analyze_contract")
    async def analyze_contract(self, text: str, analysis_type: str = "contract_analysis") -> AIAnalysisResult:
        """Analyze contract using Hugging Face models."""
        if not self.api_key:
            raise AIServiceError("Hugging Face API key not configured")
        
        start_time = datetime.now()
        
        try:
            # For now, implement a structured analysis using the legal models
            # This would be enhanced with actual model-specific implementations
            result = await self._perform_legal_analysis(text, analysis_type)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return AIAnalysisResult(
                contract_type=result.get("contract_type", "Unknown"),
                risk_score=result.get("risk_score", 0.5),
                compliance_score=result.get("compliance_score", 0.8),
                language_clarity=result.get("language_clarity", 0.7),
                key_risks=result.get("key_risks", []),
                suggestions=result.get("suggestions", []),
                extracted_clauses=result.get("extracted_clauses", []),
                compliance_issues=result.get("compliance_issues", []),
                obligations=result.get("obligations", []),
                confidence=result.get("confidence", 0.8),
                provider="huggingface",
                processing_time=processing_time,
                created_at=datetime.now()
            )
            
        except httpx.TimeoutException:
            raise AIServiceError("Hugging Face API timeout")
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 429:
                raise RateLimitError("Hugging Face rate limit exceeded")
            elif e.response.status_code >= 500:
                raise ModelUnavailableError("Hugging Face service unavailable")
            else:
                raise AIServiceError(f"Hugging Face API error: {e.response.status_code}")
    
    @performance_service.track_request("ai_huggingface_api")
    async def _perform_legal_analysis(self, text: str, analysis_type: str) -> Dict[str, Any]:
        """Perform legal analysis using Hugging Face models."""
        model_name = self.models.get(analysis_type, self.models["contract_analysis"])
        url = f"{self.base_url}/{model_name}"
        
        headers = {"Authorization": f"Bearer {self.api_key}"}
        payload = {
            "inputs": text[:2000],  # Limit input size for now
            "parameters": {
                "return_all_scores": True,
                "use_cache": True
            }
        }
        
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()
            
            # Parse the response and convert to our standard format
            raw_result = response.json()
            return await self._parse_huggingface_result(raw_result, text, analysis_type)
    
    async def _parse_huggingface_result(self, raw_result: Any, text: str, analysis_type: str) -> Dict[str, Any]:
        """Parse Hugging Face response into standardized format."""
        # This is a simplified implementation
        # In production, this would use the actual model outputs
        
        # Basic contract type detection based on keywords
        contract_type = self._detect_contract_type(text)
        
        # Basic risk assessment
        risk_score = self._calculate_basic_risk_score(text)
        
        # Extract basic information
        return {
            "contract_type": contract_type,
            "risk_score": risk_score,
            "compliance_score": 0.8,  # Placeholder
            "language_clarity": 0.7,  # Placeholder
            "key_risks": self._identify_basic_risks(text),
            "suggestions": self._generate_basic_suggestions(text),
            "extracted_clauses": await self._extract_basic_clauses(text),
            "compliance_issues": [],
            "obligations": [],
            "confidence": 0.8
        }
    
    def _detect_contract_type(self, text: str) -> str:
        """Basic contract type detection."""
        text_lower = text.lower()
        
        if any(term in text_lower for term in ["employment", "employee", "employer", "salary", "position"]):
            return "Employment Agreement"
        elif any(term in text_lower for term in ["non-disclosure", "nda", "confidential", "proprietary"]):
            return "Non-Disclosure Agreement"
        elif any(term in text_lower for term in ["service", "services", "provider", "client"]):
            return "Service Agreement"
        elif any(term in text_lower for term in ["lease", "rent", "tenant", "landlord"]):
            return "Lease Agreement"
        elif any(term in text_lower for term in ["purchase", "sale", "buy", "sell", "goods"]):
            return "Purchase Agreement"
        else:
            return "General Contract"
    
    def _calculate_basic_risk_score(self, text: str) -> float:
        """Basic risk score calculation."""
        risk_indicators = [
            "penalty", "damages", "liability", "breach", "default",
            "termination", "indemnify", "unlimited", "personal guarantee"
        ]
        
        text_lower = text.lower()
        risk_count = sum(1 for indicator in risk_indicators if indicator in text_lower)
        
        # Normalize to 0-1 scale
        return min(risk_count / 10.0, 1.0)
    
    def _identify_basic_risks(self, text: str) -> List[Dict[str, Any]]:
        """Identify basic risks in the contract."""
        risks = []
        text_lower = text.lower()
        
        if "unlimited liability" in text_lower:
            risks.append({
                "type": "liability",
                "severity": "high",
                "description": "Unlimited liability clause detected",
                "recommendation": "Consider limiting liability exposure"
            })
        
        if "personal guarantee" in text_lower:
            risks.append({
                "type": "financial",
                "severity": "high", 
                "description": "Personal guarantee requirement",
                "recommendation": "Review personal guarantee terms carefully"
            })
        
        return risks
    
    def _generate_basic_suggestions(self, text: str) -> List[Dict[str, Any]]:
        """Generate basic suggestions for contract improvement."""
        suggestions = []
        
        if "force majeure" not in text.lower():
            suggestions.append({
                "type": "clause_addition",
                "priority": "medium",
                "title": "Add Force Majeure Clause",
                "description": "Consider adding a force majeure clause to protect against unforeseeable circumstances"
            })
        
        return suggestions
    
    async def _extract_basic_clauses(self, text: str) -> List[Dict[str, Any]]:
        """Extract clauses using advanced clause analysis service."""
        try:
            # Use the comprehensive clause analysis service
            analysis_result = await clause_analysis_service.analyze_contract_comprehensively(text)

            # Convert extracted clauses to the expected format
            clauses = []
            for clause in analysis_result["extracted_clauses"]:
                clauses.append({
                    "type": clause["type"],
                    "title": clause["title"],
                    "content": clause["content"][:200] + "..." if len(clause["content"]) > 200 else clause["content"],
                    "importance": clause["importance"],
                    "risk_level": clause["risk_level"],
                    "confidence": clause["confidence"],
                    "category": clause["category"],
                    "position": clause["position"]
                })

            # Add missing clauses information
            missing_clauses = []
            for missing in analysis_result["missing_clauses"]:
                missing_clauses.append({
                    "type": missing["type"],
                    "title": missing["title"],
                    "importance": missing["importance"],
                    "risk_level": missing["risk_level"],
                    "description": missing["description"],
                    "recommendation": missing["recommendation"]
                })

            # Add metadata about the analysis
            clauses.append({
                "type": "analysis_metadata",
                "title": "Clause Analysis Summary",
                "content": f"Found {len(clauses)} clauses, {len(missing_clauses)} missing standard clauses",
                "importance": "info",
                "risk_level": "low",
                "confidence": 1.0,
                "category": "metadata",
                "position": 999,
                "missing_clauses": missing_clauses,
                "overall_risk_score": analysis_result["overall_risk_score"],
                "completeness_score": analysis_result["completeness_score"],
                "contract_type": analysis_result["contract_type"]
            })

            return clauses

        except Exception as e:
            logger.error(f"Error in advanced clause extraction: {e}")
            # Fallback to basic extraction
            return self._extract_basic_clauses_fallback(text)

    def _extract_basic_clauses_fallback(self, text: str) -> List[Dict[str, Any]]:
        """Fallback basic clause extraction."""
        clauses = []

        if "confidential" in text.lower():
            clauses.append({
                "type": "confidentiality",
                "title": "Confidentiality Clause",
                "content": "Confidentiality provisions detected",
                "importance": "high",
                "risk_level": "medium",
                "confidence": 0.6,
                "category": "data_protection",
                "position": 1
            })

        if "terminate" in text.lower() or "termination" in text.lower():
            clauses.append({
                "type": "termination",
                "title": "Termination Clause",
                "content": "Termination provisions detected",
                "importance": "critical",
                "risk_level": "high",
                "confidence": 0.6,
                "category": "contract_lifecycle",
                "position": 2
            })

        if "liable" in text.lower() or "liability" in text.lower():
            clauses.append({
                "type": "liability",
                "title": "Liability Clause",
                "content": "Liability provisions detected",
                "importance": "critical",
                "risk_level": "high",
                "confidence": 0.6,
                "category": "risk_management",
                "position": 3
            })

        return clauses


class GeminiProvider:
    """Google Gemini API provider for advanced contract analysis."""
    
    def __init__(self):
        self.api_key = settings.GEMINI_API_KEY
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model = settings.GEMINI_MODEL
        self.timeout = settings.AI_TIMEOUT
    
    async def analyze_contract(self, text: str, analysis_type: str = "contract_analysis") -> AIAnalysisResult:
        """Analyze contract using Google Gemini."""
        if not self.api_key:
            raise AIServiceError("Gemini API key not configured")
        
        start_time = datetime.now()
        
        try:
            result = await self._perform_gemini_analysis(text, analysis_type)
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return AIAnalysisResult(
                contract_type=result.get("contract_type", "Unknown"),
                risk_score=result.get("risk_score", 0.5),
                compliance_score=result.get("compliance_score", 0.8),
                language_clarity=result.get("language_clarity", 0.7),
                key_risks=result.get("key_risks", []),
                suggestions=result.get("suggestions", []),
                extracted_clauses=result.get("extracted_clauses", []),
                compliance_issues=result.get("compliance_issues", []),
                obligations=result.get("obligations", []),
                confidence=result.get("confidence", 0.9),
                provider="gemini",
                processing_time=processing_time,
                created_at=datetime.now()
            )
            
        except httpx.TimeoutException:
            raise AIServiceError("Gemini API timeout")
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 429:
                raise RateLimitError("Gemini rate limit exceeded")
            elif e.response.status_code >= 500:
                raise ModelUnavailableError("Gemini service unavailable")
            else:
                raise AIServiceError(f"Gemini API error: {e.response.status_code}")
    
    async def _perform_gemini_analysis(self, text: str, analysis_type: str) -> Dict[str, Any]:
        """Perform analysis using Gemini API."""
        prompt = self._build_analysis_prompt(text, analysis_type)
        url = f"{self.base_url}/models/{self.model}:generateContent"

        headers = {
            "Content-Type": "application/json",
            "x-goog-api-key": self.api_key
        }

        payload = {
            "contents": [{
                "parts": [{"text": prompt}]
            }],
            "generationConfig": {
                "temperature": 0.1,
                "maxOutputTokens": 2048,
                "topP": 0.8,
                "topK": 40
            }
        }

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()

            raw_result = response.json()
            return self._parse_gemini_result(raw_result, analysis_type)

    def _build_analysis_prompt(self, text: str, analysis_type: str) -> str:
        """Build analysis prompt for Gemini."""
        base_prompt = f"""
        You are a legal AI assistant specializing in contract analysis.
        Analyze the following contract text and provide a structured response.

        Contract Text:
        {text[:4000]}  # Limit for context window

        Please provide a JSON response with:
        1. contract_type: The type of contract (e.g., Employment, NDA, Service Agreement)
        2. risk_score: Risk level from 0.0 to 1.0 (1.0 = highest risk)
        3. compliance_score: Compliance level from 0.0 to 1.0 (1.0 = fully compliant)
        4. language_clarity: Language clarity from 0.0 to 1.0 (1.0 = very clear)
        5. key_risks: Array of risk objects with type, severity, description, recommendation
        6. suggestions: Array of suggestion objects with type, priority, title, description
        7. extracted_clauses: Array of clause objects with type, title, content, importance
        8. compliance_issues: Array of compliance issue objects
        9. obligations: Array of obligation objects
        10. confidence: Your confidence in this analysis from 0.0 to 1.0

        Format your response as valid JSON only.
        """

        return base_prompt

    def _parse_gemini_result(self, raw_result: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """Parse Gemini response into standardized format."""
        try:
            content = raw_result["candidates"][0]["content"]["parts"][0]["text"]

            # Extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                analysis_data = json.loads(json_match.group())
                return analysis_data
            else:
                # Fallback to basic analysis if JSON parsing fails
                return self._create_fallback_analysis(content)

        except (KeyError, json.JSONDecodeError, IndexError) as e:
            logger.error(f"Failed to parse Gemini response: {e}")
            return self._create_fallback_analysis("Error parsing response")

    def _create_fallback_analysis(self, content: str) -> Dict[str, Any]:
        """Create fallback analysis when parsing fails."""
        return {
            "contract_type": "Unknown",
            "risk_score": 0.5,
            "compliance_score": 0.8,
            "language_clarity": 0.7,
            "key_risks": [],
            "suggestions": [],
            "extracted_clauses": [],
            "compliance_issues": [],
            "obligations": [],
            "confidence": 0.3
        }


class ContractAnalysisService:
    """Main AI service that orchestrates multiple providers."""

    def __init__(self):
        self.primary_provider = HuggingFaceProvider()
        self.fallback_provider = GeminiProvider()
        self.cache = ai_cache
        self.max_retries = settings.AI_MAX_RETRIES

    async def analyze_contract(self, text: str, analysis_type: str = "contract_analysis") -> AIAnalysisResult:
        """Analyze contract with fallback support."""
        # Check cache first
        cache_key = self._generate_cache_key(text, analysis_type)
        cached_result = await self.cache.get(cache_key)
        if cached_result:
            logger.info("Returning cached AI analysis result")
            return cached_result

        # Try primary provider with retries
        for attempt in range(self.max_retries):
            try:
                result = await self.primary_provider.analyze_contract(text, analysis_type)

                # Cache successful result
                await self.cache.set(cache_key, result)

                return result

            except RateLimitError:
                wait_time = 2 ** attempt
                logger.warning(f"Rate limit hit, waiting {wait_time}s before retry {attempt + 1}")
                await asyncio.sleep(wait_time)
                continue

            except ModelUnavailableError:
                logger.warning("Primary provider unavailable, switching to fallback")
                break

            except AIServiceError as e:
                if attempt == self.max_retries - 1:
                    logger.error(f"Primary provider failed after {self.max_retries} attempts: {e}")
                    break
                await asyncio.sleep(1)

        # Try fallback provider
        try:
            logger.info("Using fallback provider (Gemini)")
            result = await self.fallback_provider.analyze_contract(text, analysis_type)

            # Cache fallback result with shorter TTL
            await self.cache.set(cache_key, result, ttl=1800)  # 30 minutes

            return result

        except Exception as e:
            logger.error(f"Fallback provider also failed: {e}")
            # Return basic analysis as last resort
            return self._create_basic_fallback_analysis(text)

    def _generate_cache_key(self, text: str, analysis_type: str) -> str:
        """Generate cache key for analysis request."""
        content_hash = hashlib.sha256(text.encode()).hexdigest()[:16]
        return f"ai_analysis:{analysis_type}:{content_hash}"

    def _create_basic_fallback_analysis(self, text: str) -> AIAnalysisResult:
        """Create basic fallback analysis when all providers fail."""
        return AIAnalysisResult(
            contract_type="Unknown",
            risk_score=0.5,
            compliance_score=0.5,
            language_clarity=0.5,
            key_risks=[{
                "type": "analysis_unavailable",
                "severity": "medium",
                "description": "AI analysis temporarily unavailable",
                "recommendation": "Manual review recommended"
            }],
            suggestions=[],
            extracted_clauses=[],
            compliance_issues=[],
            obligations=[],
            confidence=0.1,
            provider="fallback",
            processing_time=0.0,
            created_at=datetime.now()
        )


# Global AI service instance
ai_service = ContractAnalysisService()
