"""
Workspace service for handling workspace operations.
"""
from typing import Dict, List, Optional, Any
from app.db.database import get_supabase_client
from datetime import datetime
import uuid

class WorkspaceService:
    """Service for handling workspace operations."""

    @staticmethod
    async def get_workspace(workspace_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a workspace by ID.

        Args:
            workspace_id: The ID of the workspace

        Returns:
            The workspace data or None if not found
        """
        supabase = get_supabase_client()

        try:
            response = supabase.table("workspaces").select("*").eq("id", workspace_id).execute()

            if response.error:
                print(f"Error getting workspace: {response.error.message}")
                return None

            if not response.data:
                return None

            return response.data[0]
        except Exception as e:
            print(f"Error getting workspace: {e}")
            return None

    @staticmethod
    async def get_workspaces(user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all workspaces or workspaces for a specific user.

        Args:
            user_id: Optional user ID to filter workspaces

        Returns:
            List of workspaces
        """
        supabase = get_supabase_client()

        try:
            if user_id:
                # Get workspaces where the user is a member
                response = supabase.table("workspace_members").select("workspace_id").eq("user_id", user_id).execute()

                if response.error:
                    print(f"Error getting workspace members: {response.error.message}")
                    return []

                if not response.data:
                    return []

                # Get the workspace IDs
                workspace_ids = [member["workspace_id"] for member in response.data]

                # Get the workspaces
                response = supabase.table("workspaces").select("*").in_("id", workspace_ids).execute()
            else:
                # Get all workspaces
                response = supabase.table("workspaces").select("*").execute()

            if response.error:
                print(f"Error getting workspaces: {response.error.message}")
                return []

            return response.data or []
        except Exception as e:
            print(f"Error getting workspaces: {e}")
            return []

    @staticmethod
    async def create_workspace(workspace_data: Dict[str, Any], user_id: str) -> Optional[Dict[str, Any]]:
        """
        Create a new workspace.

        Args:
            workspace_data: The workspace data
            user_id: The ID of the user creating the workspace

        Returns:
            The created workspace data or None if creation failed
        """
        supabase = get_supabase_client()

        try:
            # Prepare workspace data
            workspace_id = f"ws-{str(uuid.uuid4())}"
            new_workspace = {
                "id": workspace_id,
                "name": workspace_data.get("name", "New Workspace"),
                "description": workspace_data.get("description", ""),
                "created_by": user_id,
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "settings": workspace_data.get("settings", {}),
                "is_active": True
            }

            # Insert workspace
            response = supabase.table("workspaces").insert(new_workspace).execute()

            if response.error:
                print(f"Error creating workspace: {response.error.message}")
                return None

            # Add the creator as a member with admin role
            member_data = {
                "id": f"wsm-{str(uuid.uuid4())}",
                "workspace_id": workspace_id,
                "user_id": user_id,
                "role_id": "role-admin",  # Default admin role
                "joined_at": datetime.utcnow().isoformat(),
                "status": "active"
            }

            member_response = supabase.table("workspace_members").insert(member_data).execute()

            if member_response.error:
                print(f"Error adding member to workspace: {member_response.error.message}")
                # Continue anyway, the workspace was created

            return response.data[0] if response.data else None
        except Exception as e:
            print(f"Error creating workspace: {e}")
            return None

    @staticmethod
    async def update_workspace(workspace_id: str, workspace_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Update a workspace.

        Args:
            workspace_id: The ID of the workspace to update
            workspace_data: The updated workspace data

        Returns:
            The updated workspace data or None if update failed
        """
        supabase = get_supabase_client()

        try:
            # Prepare update data
            update_data = {
                "updated_at": datetime.utcnow().isoformat()
            }

            # Add fields to update
            if "name" in workspace_data:
                update_data["name"] = workspace_data["name"]

            if "description" in workspace_data:
                update_data["description"] = workspace_data["description"]

            if "settings" in workspace_data:
                update_data["settings"] = workspace_data["settings"]

            if "is_active" in workspace_data:
                update_data["is_active"] = workspace_data["is_active"]

            # Update workspace
            response = supabase.table("workspaces").update(update_data).eq("id", workspace_id).execute()

            if response.error:
                print(f"Error updating workspace: {response.error.message}")
                return None

            return response.data[0] if response.data else None
        except Exception as e:
            print(f"Error updating workspace: {e}")
            return None

    @staticmethod
    async def delete_workspace(workspace_id: str) -> bool:
        """
        Delete a workspace.

        Args:
            workspace_id: The ID of the workspace to delete

        Returns:
            True if deletion was successful, False otherwise
        """
        supabase = get_supabase_client()

        try:
            # Delete workspace
            response = supabase.table("workspaces").delete().eq("id", workspace_id).execute()

            if response.error:
                print(f"Error deleting workspace: {response.error.message}")
                return False

            return True
        except Exception as e:
            print(f"Error deleting workspace: {e}")
            return False

    @staticmethod
    async def get_workspace_members(workspace_id: str) -> List[Dict[str, Any]]:
        """
        Get all members of a workspace.

        Args:
            workspace_id: The ID of the workspace

        Returns:
            List of workspace members
        """
        supabase = get_supabase_client()

        try:
            # Get workspace members
            response = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).execute()

            if response.error:
                print(f"Error getting workspace members: {response.error.message}")
                return []

            return response.data or []
        except Exception as e:
            print(f"Error getting workspace members: {e}")
            return []

    @staticmethod
    async def add_workspace_member(workspace_id: str, user_id: str, role_id: str = "role-readonly") -> Optional[Dict[str, Any]]:
        """
        Add a member to a workspace.

        Args:
            workspace_id: The ID of the workspace
            user_id: The ID of the user to add
            role_id: The ID of the role to assign to the user

        Returns:
            The created workspace member data or None if creation failed
        """
        supabase = get_supabase_client()

        try:
            # Check if the user is already a member
            check_response = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", user_id).execute()

            if check_response.error:
                print(f"Error checking workspace member: {check_response.error.message}")
                return None

            if check_response.data:
                # User is already a member, update their role
                update_response = supabase.table("workspace_members").update({
                    "role_id": role_id,
                    "updated_at": datetime.utcnow().isoformat()
                }).eq("workspace_id", workspace_id).eq("user_id", user_id).execute()

                if update_response.error:
                    print(f"Error updating workspace member: {update_response.error.message}")
                    return None

                return update_response.data[0] if update_response.data else None

            # Add the user as a member
            member_data = {
                "id": f"wsm-{str(uuid.uuid4())}",
                "workspace_id": workspace_id,
                "user_id": user_id,
                "role_id": role_id,
                "joined_at": datetime.utcnow().isoformat(),
                "status": "active"
            }

            response = supabase.table("workspace_members").insert(member_data).execute()

            if response.error:
                print(f"Error adding workspace member: {response.error.message}")
                return None

            return response.data[0] if response.data else None
        except Exception as e:
            print(f"Error adding workspace member: {e}")
            return None
