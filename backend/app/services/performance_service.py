"""
Performance Optimization Service for Averum Contracts
Implements response time optimization, parallel processing, caching, and resource management
"""

import asyncio
import logging
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Awaitable
from functools import wraps
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, ProcessPoolExecutor
import threading
from dataclasses import dataclass

from app.core.config import settings
from app.services.cache_service import ai_cache

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics tracking."""
    response_time: float
    memory_usage: float
    cpu_usage: float
    cache_hit_rate: float
    concurrent_requests: int
    timestamp: datetime


class ResourceMonitor:
    """Monitor system resources and performance."""
    
    def __init__(self):
        self.metrics_history: List[PerformanceMetrics] = []
        self.max_history = 1000
        self.monitoring_active = False
        self.monitor_task = None
    
    async def start_monitoring(self, interval: float = 30.0):
        """Start continuous resource monitoring."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop resource monitoring."""
        self.monitoring_active = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("Performance monitoring stopped")
    
    async def _monitor_loop(self, interval: float):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                metrics = await self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Keep only recent metrics
                if len(self.metrics_history) > self.max_history:
                    self.metrics_history = self.metrics_history[-self.max_history:]
                
                # Check for performance issues
                await self._check_performance_alerts(metrics)
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in performance monitoring: {e}")
                await asyncio.sleep(interval)
    
    async def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        # Get system metrics
        memory_info = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Get cache metrics
        cache_stats = await ai_cache.get_stats()
        cache_hit_rate = cache_stats.get("hit_rate", 0.0)
        
        return PerformanceMetrics(
            response_time=0.0,  # Will be updated by request tracking
            memory_usage=memory_info.percent,
            cpu_usage=cpu_percent,
            cache_hit_rate=cache_hit_rate,
            concurrent_requests=performance_service.active_requests,
            timestamp=datetime.utcnow()
        )
    
    async def _check_performance_alerts(self, metrics: PerformanceMetrics):
        """Check for performance issues and log alerts."""
        if metrics.memory_usage > 85:
            logger.warning(f"High memory usage: {metrics.memory_usage:.1f}%")
        
        if metrics.cpu_usage > 80:
            logger.warning(f"High CPU usage: {metrics.cpu_usage:.1f}%")
        
        if metrics.concurrent_requests > 100:
            logger.warning(f"High concurrent requests: {metrics.concurrent_requests}")
        
        if metrics.cache_hit_rate < 0.5:
            logger.warning(f"Low cache hit rate: {metrics.cache_hit_rate:.2f}")
    
    def get_recent_metrics(self, minutes: int = 30) -> List[PerformanceMetrics]:
        """Get metrics from the last N minutes."""
        cutoff = datetime.utcnow() - timedelta(minutes=minutes)
        return [m for m in self.metrics_history if m.timestamp >= cutoff]
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary statistics."""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.get_recent_metrics(30)
        if not recent_metrics:
            recent_metrics = self.metrics_history[-10:]  # Last 10 entries
        
        return {
            "avg_memory_usage": sum(m.memory_usage for m in recent_metrics) / len(recent_metrics),
            "avg_cpu_usage": sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics),
            "avg_cache_hit_rate": sum(m.cache_hit_rate for m in recent_metrics) / len(recent_metrics),
            "max_concurrent_requests": max(m.concurrent_requests for m in recent_metrics),
            "total_metrics_collected": len(self.metrics_history),
            "monitoring_active": self.monitoring_active
        }


class ParallelProcessor:
    """Handle parallel processing for CPU and I/O bound tasks."""
    
    def __init__(self):
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
        self.process_pool = ProcessPoolExecutor(max_workers=4)
        self.semaphore = asyncio.Semaphore(20)  # Limit concurrent operations
    
    async def run_parallel_io(self, tasks: List[Callable[[], Awaitable[Any]]]) -> List[Any]:
        """Run I/O bound tasks in parallel with concurrency control."""
        async def limited_task(task):
            async with self.semaphore:
                return await task()
        
        return await asyncio.gather(*[limited_task(task) for task in tasks])
    
    async def run_parallel_cpu(self, func: Callable, items: List[Any]) -> List[Any]:
        """Run CPU bound tasks in parallel using process pool."""
        loop = asyncio.get_event_loop()
        
        # Split items into chunks for parallel processing
        chunk_size = max(1, len(items) // self.process_pool._max_workers)
        chunks = [items[i:i + chunk_size] for i in range(0, len(items), chunk_size)]
        
        # Process chunks in parallel
        futures = []
        for chunk in chunks:
            future = loop.run_in_executor(self.process_pool, func, chunk)
            futures.append(future)
        
        results = await asyncio.gather(*futures)
        
        # Flatten results
        flattened = []
        for result in results:
            if isinstance(result, list):
                flattened.extend(result)
            else:
                flattened.append(result)
        
        return flattened
    
    async def run_with_timeout(self, coro: Awaitable[Any], timeout: float) -> Any:
        """Run coroutine with timeout."""
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            logger.warning(f"Operation timed out after {timeout} seconds")
            raise
    
    def cleanup(self):
        """Cleanup thread and process pools."""
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)


class ResponseTimeOptimizer:
    """Optimize response times through various techniques."""
    
    def __init__(self):
        self.request_times: Dict[str, List[float]] = {}
        self.slow_endpoints: Dict[str, int] = {}
        self.optimization_cache = {}
    
    def track_request_time(self, endpoint: str, duration: float):
        """Track request duration for analysis."""
        if endpoint not in self.request_times:
            self.request_times[endpoint] = []
        
        self.request_times[endpoint].append(duration)
        
        # Keep only recent times (last 100 requests)
        if len(self.request_times[endpoint]) > 100:
            self.request_times[endpoint] = self.request_times[endpoint][-100:]
        
        # Track slow endpoints
        if duration > 5.0:  # 5 second threshold
            self.slow_endpoints[endpoint] = self.slow_endpoints.get(endpoint, 0) + 1
    
    def get_endpoint_stats(self, endpoint: str) -> Dict[str, Any]:
        """Get performance statistics for an endpoint."""
        times = self.request_times.get(endpoint, [])
        if not times:
            return {}
        
        return {
            "avg_response_time": sum(times) / len(times),
            "min_response_time": min(times),
            "max_response_time": max(times),
            "total_requests": len(times),
            "slow_requests": self.slow_endpoints.get(endpoint, 0),
            "p95_response_time": sorted(times)[int(len(times) * 0.95)] if len(times) > 20 else max(times)
        }
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        report = {
            "endpoints": {},
            "slowest_endpoints": [],
            "total_endpoints": len(self.request_times)
        }
        
        # Analyze each endpoint
        endpoint_stats = []
        for endpoint in self.request_times:
            stats = self.get_endpoint_stats(endpoint)
            stats["endpoint"] = endpoint
            endpoint_stats.append(stats)
            report["endpoints"][endpoint] = stats
        
        # Find slowest endpoints
        endpoint_stats.sort(key=lambda x: x.get("avg_response_time", 0), reverse=True)
        report["slowest_endpoints"] = endpoint_stats[:5]
        
        return report


class PerformanceService:
    """Main performance optimization service."""
    
    def __init__(self):
        self.resource_monitor = ResourceMonitor()
        self.parallel_processor = ParallelProcessor()
        self.response_optimizer = ResponseTimeOptimizer()
        self.active_requests = 0
        self.request_lock = threading.Lock()
    
    async def start(self):
        """Start performance monitoring and optimization."""
        await self.resource_monitor.start_monitoring()
        logger.info("Performance service started")
    
    async def stop(self):
        """Stop performance monitoring."""
        await self.resource_monitor.stop_monitoring()
        self.parallel_processor.cleanup()
        logger.info("Performance service stopped")
    
    def track_request(self, endpoint: str):
        """Decorator to track request performance."""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                
                with self.request_lock:
                    self.active_requests += 1
                
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    duration = time.time() - start_time
                    self.response_optimizer.track_request_time(endpoint, duration)
                    
                    with self.request_lock:
                        self.active_requests -= 1
            
            return wrapper
        return decorator
    
    async def optimize_parallel_processing(self, tasks: List[Callable], task_type: str = "io") -> List[Any]:
        """Optimize parallel processing based on task type."""
        if task_type == "io":
            return await self.parallel_processor.run_parallel_io(tasks)
        elif task_type == "cpu":
            # For CPU tasks, we need a different approach
            return await self.parallel_processor.run_parallel_cpu(tasks[0], tasks[1:])
        else:
            raise ValueError(f"Unknown task type: {task_type}")
    
    async def get_performance_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive performance dashboard data."""
        return {
            "resource_metrics": self.resource_monitor.get_performance_summary(),
            "response_times": self.response_optimizer.get_performance_report(),
            "active_requests": self.active_requests,
            "cache_stats": await ai_cache.get_stats(),
            "system_info": {
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total,
                "disk_usage": psutil.disk_usage('/').percent
            }
        }
    
    async def optimize_memory_usage(self):
        """Perform memory optimization tasks."""
        # Clear old cache entries
        await ai_cache.cleanup_expired()
        
        # Clear old metrics
        if len(self.resource_monitor.metrics_history) > 500:
            self.resource_monitor.metrics_history = self.resource_monitor.metrics_history[-500:]
        
        # Clear old request times
        for endpoint in list(self.response_optimizer.request_times.keys()):
            times = self.response_optimizer.request_times[endpoint]
            if len(times) > 50:
                self.response_optimizer.request_times[endpoint] = times[-50:]
        
        logger.info("Memory optimization completed")


# Global performance service instance
performance_service = PerformanceService()
