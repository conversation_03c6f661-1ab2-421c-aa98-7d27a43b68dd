"""
Approval Workflow Service for Averum Contracts

This service provides comprehensive approval workflow management including:
- Sequential and parallel approval processes
- Conditional routing based on contract attributes
- Escalation and timeout handling
- Workflow template management
- AI-enhanced approval routing
"""

import uuid
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import logging

from supabase import Client
from app.db.database import get_supabase_client
from app.services.notification_service import NotificationService
from app.services.ai_service import ai_service

logger = logging.getLogger(__name__)


class WorkflowType(str, Enum):
    """Types of approval workflows."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    HYBRID = "hybrid"


class ApprovalStatus(str, Enum):
    """Status of individual approvals."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    ESCALATED = "escalated"
    EXPIRED = "expired"
    SKIPPED = "skipped"


class WorkflowStatus(str, Enum):
    """Status of entire workflow."""
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    REJECTED = "rejected"
    CANCELLED = "cancelled"
    ESCALATED = "escalated"


class EscalationType(str, Enum):
    """Types of escalation."""
    TIMEOUT = "timeout"
    MANUAL = "manual"
    AUTOMATIC = "automatic"
    CONDITIONAL = "conditional"


class WorkflowTemplate:
    """Workflow template for reusable workflow configurations."""
    def __init__(self, template_data: Dict[str, Any]):
        self.template_id = template_data.get("template_id", str(uuid.uuid4()))
        self.name = template_data["name"]
        self.description = template_data.get("description", "")
        self.workflow_type = WorkflowType(template_data["workflow_type"])
        self.steps = template_data["steps"]
        self.escalation_rules = template_data.get("escalation_rules", [])
        self.timeout_settings = template_data.get("timeout_settings", {})
        self.conditions = template_data.get("conditions", {})
        self.created_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.is_active = template_data.get("is_active", True)


class ApprovalWorkflowService:
    """Service for managing approval workflows."""

    def __init__(self, supabase_client: Optional[Client] = None):
        self.supabase = supabase_client or get_supabase_client()
        self.notification_service = NotificationService(supabase_client)

    async def create_workflow(
        self,
        contract_id: str,
        workspace_id: str,
        workflow_type: WorkflowType,
        approvers: List[Dict[str, Any]],
        created_by: str,
        template_id: Optional[str] = None,
        due_date: Optional[datetime] = None,
        escalation_rules: Optional[Dict[str, Any]] = None,
        conditions: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a new approval workflow."""
        try:
            workflow_id = str(uuid.uuid4())
            
            # Prepare workflow data
            workflow_data = {
                "id": workflow_id,
                "contract_id": contract_id,
                "workspace_id": workspace_id,
                "workflow_type": workflow_type.value,
                "status": WorkflowStatus.DRAFT.value,
                "created_by": created_by,
                "created_at": datetime.utcnow().isoformat(),
                "due_date": due_date.isoformat() if due_date else None,
                "escalation_rules": escalation_rules or {},
                "conditions": conditions or {},
                "template_id": template_id
            }

            # Insert workflow
            response = self.supabase.table("approval_workflows").insert(workflow_data).execute()
            
            if hasattr(response, 'error') and response.error:
                raise Exception(f"Failed to create workflow: {response.error}")

            workflow = response.data[0]

            # Create individual approvals
            approvals = []
            for i, approver in enumerate(approvers):
                approval_data = {
                    "id": str(uuid.uuid4()),
                    "workflow_id": workflow_id,
                    "user_id": approver["user_id"],
                    "order": approver.get("order", i + 1),
                    "status": ApprovalStatus.PENDING.value,
                    "required": approver.get("required", True),
                    "role": approver.get("role", "approver"),
                    "conditions": approver.get("conditions", {}),
                    "created_at": datetime.utcnow().isoformat()
                }
                approvals.append(approval_data)

            if approvals:
                response = self.supabase.table("approvals").insert(approvals).execute()
                
                if hasattr(response, 'error') and response.error:
                    raise Exception(f"Failed to create approvals: {response.error}")

            # Start workflow if not draft
            if workflow_type != WorkflowType.CONDITIONAL:
                await self.start_workflow(workflow_id)

            return workflow

        except Exception as e:
            logger.error(f"Error creating approval workflow: {str(e)}")
            raise

    async def start_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Start an approval workflow."""
        try:
            # Get workflow details
            workflow_response = self.supabase.table("approval_workflows").select("*").eq("id", workflow_id).execute()
            
            if not workflow_response.data:
                raise Exception("Workflow not found")

            workflow = workflow_response.data[0]
            
            # Update workflow status to active
            update_data = {
                "status": WorkflowStatus.ACTIVE.value,
                "started_at": datetime.utcnow().isoformat()
            }
            
            self.supabase.table("approval_workflows").update(update_data).eq("id", workflow_id).execute()

            # Determine which approvers to notify based on workflow type
            if workflow["workflow_type"] == WorkflowType.SEQUENTIAL.value:
                await self._start_sequential_workflow(workflow_id)
            elif workflow["workflow_type"] == WorkflowType.PARALLEL.value:
                await self._start_parallel_workflow(workflow_id)
            elif workflow["workflow_type"] == WorkflowType.CONDITIONAL.value:
                await self._start_conditional_workflow(workflow_id)
            elif workflow["workflow_type"] == WorkflowType.HYBRID.value:
                await self._start_hybrid_workflow(workflow_id)

            return workflow

        except Exception as e:
            logger.error(f"Error starting workflow: {str(e)}")
            raise

    async def _start_sequential_workflow(self, workflow_id: str):
        """Start sequential workflow - notify first approver only."""
        approvals_response = self.supabase.table("approvals").select("*").eq("workflow_id", workflow_id).order("order").execute()
        
        if approvals_response.data:
            first_approval = approvals_response.data[0]
            await self._notify_approver(first_approval)

    async def _start_parallel_workflow(self, workflow_id: str):
        """Start parallel workflow - notify all approvers."""
        approvals_response = self.supabase.table("approvals").select("*").eq("workflow_id", workflow_id).execute()
        
        for approval in approvals_response.data:
            await self._notify_approver(approval)

    async def _start_conditional_workflow(self, workflow_id: str):
        """Start conditional workflow - use AI to determine routing."""
        # Get workflow and contract details
        workflow_response = self.supabase.table("approval_workflows").select("*, contracts(*)").eq("id", workflow_id).execute()
        
        if not workflow_response.data:
            return

        workflow = workflow_response.data[0]
        contract = workflow.get("contracts")

        if contract:
            # Use AI to determine approval routing
            routing_decision = await self._ai_determine_routing(contract, workflow_id)
            
            # Apply routing decision
            await self._apply_routing_decision(workflow_id, routing_decision)

    async def _start_hybrid_workflow(self, workflow_id: str):
        """Start hybrid workflow - combination of sequential and parallel."""
        # Implementation for hybrid workflows
        # This could involve grouping approvers and processing groups sequentially
        # while processing approvers within each group in parallel
        pass

    async def _ai_determine_routing(self, contract: Dict[str, Any], workflow_id: str) -> Dict[str, Any]:
        """Use AI to determine optimal approval routing."""
        try:
            # Analyze contract for risk and complexity
            analysis_prompt = f"""
            Analyze this contract and determine the optimal approval routing:
            
            Contract Title: {contract.get('title', 'N/A')}
            Contract Type: {contract.get('type', 'N/A')}
            Contract Value: {contract.get('value', 'N/A')}
            Description: {contract.get('description', 'N/A')}
            
            Based on the contract details, recommend:
            1. Required approval levels (legal, finance, operations, executive)
            2. Approval order (sequential vs parallel)
            3. Priority level (low, medium, high, urgent)
            4. Estimated approval time
            
            Return a JSON response with routing recommendations.
            """

            # Get AI analysis
            ai_response = await ai_service.analyze_contract(analysis_prompt, "approval_routing")
            
            # Parse AI response and create routing decision
            routing_decision = {
                "required_approvers": ["legal", "finance"],  # Default fallback
                "workflow_type": "sequential",
                "priority": "medium",
                "estimated_time_hours": 48,
                "ai_confidence": ai_response.confidence if hasattr(ai_response, 'confidence') else 0.7
            }

            return routing_decision

        except Exception as e:
            logger.error(f"AI routing determination failed: {str(e)}")
            # Return default routing
            return {
                "required_approvers": ["legal"],
                "workflow_type": "sequential",
                "priority": "medium",
                "estimated_time_hours": 24,
                "ai_confidence": 0.0
            }

    async def _apply_routing_decision(self, workflow_id: str, routing_decision: Dict[str, Any]):
        """Apply AI routing decision to workflow."""
        # Update workflow based on AI decision
        # This would involve updating approver assignments, order, etc.
        pass

    async def _notify_approver(self, approval: Dict[str, Any]):
        """Send notification to an approver."""
        try:
            # Get workflow and contract details
            workflow_response = self.supabase.table("approval_workflows").select("*, contracts(*)").eq("id", approval["workflow_id"]).execute()
            
            if not workflow_response.data:
                return

            workflow = workflow_response.data[0]
            contract = workflow.get("contracts")

            # Create notification
            notification_data = {
                "user_id": approval["user_id"],
                "workspace_id": workflow["workspace_id"],
                "title": "Contract Approval Required",
                "message": f"Please review and approve contract: {contract.get('title', 'Unknown') if contract else 'Unknown'}",
                "type": "approval_request",
                "entity_id": workflow["contract_id"],
                "entity_type": "contract",
                "action_url": f"/app/contracts/{workflow['contract_id']}/approve",
                "metadata": {
                    "workflow_id": workflow["id"],
                    "approval_id": approval["id"],
                    "due_date": workflow.get("due_date"),
                    "priority": "medium"
                }
            }

            await self.notification_service.create_notification(notification_data)

        except Exception as e:
            logger.error(f"Error notifying approver: {str(e)}")

    async def approve_contract(
        self,
        approval_id: str,
        user_id: str,
        comments: Optional[str] = None
    ) -> Dict[str, Any]:
        """Approve a contract in the workflow."""
        try:
            # Update approval status
            approval_data = {
                "status": ApprovalStatus.APPROVED.value,
                "approved_at": datetime.utcnow().isoformat(),
                "comments": comments,
                "approved_by": user_id
            }

            response = self.supabase.table("approvals").update(approval_data).eq("id", approval_id).eq("user_id", user_id).execute()
            
            if not response.data:
                raise Exception("Approval not found or unauthorized")

            approval = response.data[0]

            # Check if workflow is complete
            await self._check_workflow_completion(approval["workflow_id"])

            return approval

        except Exception as e:
            logger.error(f"Error approving contract: {str(e)}")
            raise

    async def reject_contract(
        self,
        approval_id: str,
        user_id: str,
        reason: str
    ) -> Dict[str, Any]:
        """Reject a contract in the workflow."""
        try:
            # Update approval status
            approval_data = {
                "status": ApprovalStatus.REJECTED.value,
                "rejected_at": datetime.utcnow().isoformat(),
                "comments": reason,
                "rejected_by": user_id
            }

            response = self.supabase.table("approvals").update(approval_data).eq("id", approval_id).eq("user_id", user_id).execute()
            
            if not response.data:
                raise Exception("Approval not found or unauthorized")

            approval = response.data[0]

            # Mark entire workflow as rejected
            workflow_data = {
                "status": WorkflowStatus.REJECTED.value,
                "completed_at": datetime.utcnow().isoformat(),
                "rejection_reason": reason
            }

            self.supabase.table("approval_workflows").update(workflow_data).eq("id", approval["workflow_id"]).execute()

            return approval

        except Exception as e:
            logger.error(f"Error rejecting contract: {str(e)}")
            raise

    async def _check_workflow_completion(self, workflow_id: str):
        """Check if workflow is complete and update status accordingly."""
        try:
            # Get all approvals for this workflow
            approvals_response = self.supabase.table("approvals").select("*").eq("workflow_id", workflow_id).execute()
            
            if not approvals_response.data:
                return

            approvals = approvals_response.data
            required_approvals = [a for a in approvals if a.get("required", True)]
            
            # Check completion based on workflow type
            workflow_response = self.supabase.table("approval_workflows").select("*").eq("id", workflow_id).execute()
            workflow = workflow_response.data[0] if workflow_response.data else None
            
            if not workflow:
                return

            is_complete = False
            
            if workflow["workflow_type"] == WorkflowType.SEQUENTIAL.value:
                # All required approvals must be approved in order
                is_complete = all(a["status"] == ApprovalStatus.APPROVED.value for a in required_approvals)
            elif workflow["workflow_type"] == WorkflowType.PARALLEL.value:
                # All required approvals must be approved
                is_complete = all(a["status"] == ApprovalStatus.APPROVED.value for a in required_approvals)

            if is_complete:
                # Mark workflow as complete
                workflow_data = {
                    "status": WorkflowStatus.COMPLETED.value,
                    "completed_at": datetime.utcnow().isoformat()
                }

                self.supabase.table("approval_workflows").update(workflow_data).eq("id", workflow_id).execute()

                # Update contract status
                contract_data = {
                    "status": "approved"
                }

                self.supabase.table("contracts").update(contract_data).eq("id", workflow["contract_id"]).execute()

                # Notify contract creator
                await self._notify_workflow_completion(workflow_id)

        except Exception as e:
            logger.error(f"Error checking workflow completion: {str(e)}")

    async def _notify_workflow_completion(self, workflow_id: str):
        """Notify relevant parties of workflow completion."""
        try:
            # Get workflow details
            workflow_response = self.supabase.table("approval_workflows").select("*, contracts(*)").eq("id", workflow_id).execute()
            
            if not workflow_response.data:
                return

            workflow = workflow_response.data[0]
            contract = workflow.get("contracts")

            # Notify contract creator
            notification_data = {
                "user_id": workflow["created_by"],
                "workspace_id": workflow["workspace_id"],
                "title": "Contract Approved",
                "message": f"Contract '{contract.get('title', 'Unknown') if contract else 'Unknown'}' has been approved and is now active.",
                "type": "approval_completed",
                "entity_id": workflow["contract_id"],
                "entity_type": "contract",
                "action_url": f"/app/contracts/{workflow['contract_id']}"
            }

            await self.notification_service.create_notification(notification_data)

        except Exception as e:
            logger.error(f"Error notifying workflow completion: {str(e)}")

    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get the current status of a workflow."""
        try:
            # Get workflow with approvals
            workflow_response = self.supabase.table("approval_workflows").select("*, approvals(*), contracts(*)").eq("id", workflow_id).execute()
            
            if not workflow_response.data:
                raise Exception("Workflow not found")

            workflow = workflow_response.data[0]
            
            # Calculate progress
            approvals = workflow.get("approvals", [])
            total_approvals = len([a for a in approvals if a.get("required", True)])
            completed_approvals = len([a for a in approvals if a["status"] == ApprovalStatus.APPROVED.value])
            
            progress = (completed_approvals / total_approvals * 100) if total_approvals > 0 else 0

            return {
                "workflow": workflow,
                "progress": progress,
                "total_approvals": total_approvals,
                "completed_approvals": completed_approvals,
                "pending_approvals": total_approvals - completed_approvals
            }

        except Exception as e:
            logger.error(f"Error getting workflow status: {str(e)}")
            raise

    async def handle_escalation(self, workflow_id: str, escalation_type: EscalationType, reason: str = None) -> Dict[str, Any]:
        """Handle workflow escalation."""
        try:
            # Get workflow details
            workflow_response = self.supabase.table("approval_workflows").select("*").eq("id", workflow_id).execute()

            if not workflow_response.data:
                return {"error": "Workflow not found"}

            workflow = workflow_response.data[0]

            # Create escalation record
            escalation_data = {
                "id": str(uuid.uuid4()),
                "workflow_id": workflow_id,
                "escalation_type": escalation_type.value,
                "reason": reason or f"Escalated due to {escalation_type.value}",
                "escalated_at": datetime.utcnow().isoformat(),
                "escalated_by": "system",
                "original_approvers": workflow.get("approvers", []),
                "new_approvers": self._get_escalation_approvers(workflow, escalation_type)
            }

            # Store escalation
            self.supabase.table("workflow_escalations").insert(escalation_data).execute()

            # Update workflow status
            self.supabase.table("approval_workflows").update({
                "status": WorkflowStatus.ESCALATED.value,
                "escalated_at": datetime.utcnow().isoformat(),
                "escalation_reason": reason
            }).eq("id", workflow_id).execute()

            # Create new approval steps for escalated approvers
            await self._create_escalation_approvals(workflow_id, escalation_data["new_approvers"])

            # Send escalation notifications
            await self._notify_escalation(workflow_id, escalation_data)

            logger.info(f"Workflow {workflow_id} escalated: {escalation_type.value}")
            return {"success": True, "escalation_id": escalation_data["id"]}

        except Exception as e:
            logger.error(f"Error handling escalation: {str(e)}")
            return {"error": str(e)}

    async def check_timeouts(self) -> Dict[str, Any]:
        """Check for and handle workflow timeouts."""
        try:
            # Get active workflows with pending approvals
            active_workflows = self.supabase.table("approval_workflows").select(
                "*, approval_steps(*)"
            ).eq("status", WorkflowStatus.ACTIVE.value).execute()

            timeout_count = 0
            escalated_workflows = []

            for workflow in active_workflows.data or []:
                # Check each pending approval step for timeout
                for step in workflow.get("approval_steps", []):
                    if step["status"] == ApprovalStatus.PENDING.value:
                        # Calculate timeout
                        created_at = datetime.fromisoformat(step["created_at"].replace('Z', '+00:00'))
                        timeout_hours = step.get("timeout_hours", 24)  # Default 24 hours
                        timeout_at = created_at + timedelta(hours=timeout_hours)

                        if datetime.utcnow() > timeout_at.replace(tzinfo=None):
                            # Handle timeout
                            await self._handle_step_timeout(workflow["id"], step["id"])
                            timeout_count += 1

                            # Check if escalation is needed
                            escalation_needed = await self._check_escalation_needed(workflow["id"])
                            if escalation_needed:
                                escalation_result = await self.handle_escalation(
                                    workflow["id"],
                                    EscalationType.TIMEOUT,
                                    f"Approval step timed out after {timeout_hours} hours"
                                )
                                if escalation_result.get("success"):
                                    escalated_workflows.append(workflow["id"])

            return {
                "success": True,
                "timeouts_processed": timeout_count,
                "escalated_workflows": escalated_workflows
            }

        except Exception as e:
            logger.error(f"Error checking timeouts: {str(e)}")
            return {"error": str(e)}

    def _get_escalation_approvers(self, workflow: Dict[str, Any], escalation_type: EscalationType) -> List[str]:
        """Get approvers for escalation based on type and workflow configuration."""
        # Default escalation hierarchy
        escalation_hierarchy = {
            "manager": ["department_head", "director"],
            "department_head": ["director", "vp"],
            "director": ["vp", "ceo"],
            "vp": ["ceo"],
            "ceo": ["board"]
        }

        current_approvers = workflow.get("approvers", [])
        escalated_approvers = []

        # Get escalation rules from workflow configuration
        escalation_rules = workflow.get("escalation_rules", {})

        if escalation_type == EscalationType.TIMEOUT:
            # For timeout escalation, move up the hierarchy
            for approver in current_approvers:
                approver_role = approver.get("role", "manager")
                next_level = escalation_hierarchy.get(approver_role, ["director"])
                escalated_approvers.extend(next_level)

        elif escalation_type == EscalationType.MANUAL:
            # For manual escalation, use configured escalation approvers
            escalated_approvers = escalation_rules.get("manual_escalation_approvers", ["director", "ceo"])

        # Remove duplicates and return
        return list(set(escalated_approvers))

    async def _create_escalation_approvals(self, workflow_id: str, escalated_approvers: List[str]):
        """Create new approval steps for escalated approvers."""
        try:
            for i, approver_role in enumerate(escalated_approvers):
                approval_data = {
                    "id": str(uuid.uuid4()),
                    "workflow_id": workflow_id,
                    "approver_role": approver_role,
                    "step_order": 1000 + i,  # High order to ensure they come after original steps
                    "status": ApprovalStatus.PENDING.value,
                    "is_required": True,
                    "timeout_hours": 48,  # Escalated approvals get more time
                    "created_at": datetime.utcnow().isoformat(),
                    "escalated": True
                }

                self.supabase.table("approval_steps").insert(approval_data).execute()

        except Exception as e:
            logger.error(f"Error creating escalation approvals: {str(e)}")

    async def _handle_step_timeout(self, workflow_id: str, step_id: str):
        """Handle individual approval step timeout."""
        try:
            # Update step status to expired
            self.supabase.table("approval_steps").update({
                "status": ApprovalStatus.EXPIRED.value,
                "expired_at": datetime.utcnow().isoformat(),
                "timeout_reason": "Approval timeout exceeded"
            }).eq("id", step_id).execute()

            # Log timeout event
            logger.warning(f"Approval step {step_id} in workflow {workflow_id} timed out")

        except Exception as e:
            logger.error(f"Error handling step timeout: {str(e)}")

    async def _check_escalation_needed(self, workflow_id: str) -> bool:
        """Check if workflow needs escalation based on timeout rules."""
        try:
            # Get workflow configuration
            workflow_response = self.supabase.table("approval_workflows").select("*").eq("id", workflow_id).execute()

            if not workflow_response.data:
                return False

            workflow = workflow_response.data[0]
            escalation_rules = workflow.get("escalation_rules", {})

            # Check if auto-escalation is enabled
            auto_escalate = escalation_rules.get("auto_escalate_on_timeout", True)

            if not auto_escalate:
                return False

            # Check timeout threshold
            timeout_threshold = escalation_rules.get("escalation_timeout_hours", 48)
            created_at = datetime.fromisoformat(workflow["created_at"].replace('Z', '+00:00'))

            return datetime.utcnow() > (created_at + timedelta(hours=timeout_threshold)).replace(tzinfo=None)

        except Exception as e:
            logger.error(f"Error checking escalation needed: {str(e)}")
            return False

    async def _notify_escalation(self, workflow_id: str, escalation_data: Dict[str, Any]):
        """Send notifications for workflow escalation."""
        try:
            # Get workflow details
            workflow_response = self.supabase.table("approval_workflows").select(
                "*, contracts(*)"
            ).eq("id", workflow_id).execute()

            if not workflow_response.data:
                return

            workflow = workflow_response.data[0]
            contract = workflow.get("contracts")

            # Notify new escalated approvers
            for approver_role in escalation_data["new_approvers"]:
                notification_data = {
                    "user_id": f"role:{approver_role}",  # Role-based notification
                    "workspace_id": workflow["workspace_id"],
                    "title": "Escalated Approval Required",
                    "message": f"Contract '{contract.get('title', 'Unknown') if contract else 'Unknown'}' requires your urgent approval (escalated).",
                    "type": "approval_escalated",
                    "entity_id": workflow["contract_id"],
                    "entity_type": "contract",
                    "priority": "high",
                    "action_url": f"/app/approvals/{workflow_id}"
                }

                await self.notification_service.create_notification(notification_data)

            # Notify original requestor about escalation
            notification_data = {
                "user_id": workflow["created_by"],
                "workspace_id": workflow["workspace_id"],
                "title": "Approval Escalated",
                "message": f"Your contract approval has been escalated due to: {escalation_data['reason']}",
                "type": "approval_escalated",
                "entity_id": workflow["contract_id"],
                "entity_type": "contract",
                "action_url": f"/app/contracts/{workflow['contract_id']}"
            }

            await self.notification_service.create_notification(notification_data)

        except Exception as e:
            logger.error(f"Error notifying escalation: {str(e)}")

    # Workflow Template Management Methods
    async def create_workflow_template(self, template_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Create a new workflow template."""
        try:
            template = WorkflowTemplate(template_data)

            # Store template in database
            template_record = {
                "id": template.template_id,
                "name": template.name,
                "description": template.description,
                "workflow_type": template.workflow_type.value,
                "steps": template.steps,
                "escalation_rules": template.escalation_rules,
                "timeout_settings": template.timeout_settings,
                "conditions": template.conditions,
                "created_by": user_id,
                "created_at": template.created_at.isoformat(),
                "updated_at": template.updated_at.isoformat(),
                "is_active": template.is_active
            }

            result = self.supabase.table("workflow_templates").insert(template_record).execute()

            logger.info(f"Created workflow template: {template.name}")
            return {"success": True, "template_id": template.template_id, "template": result.data[0]}

        except Exception as e:
            logger.error(f"Error creating workflow template: {str(e)}")
            return {"error": str(e)}

    async def get_workflow_templates(self, workspace_id: str = None, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get workflow templates."""
        try:
            query = self.supabase.table("workflow_templates").select("*")

            if workspace_id:
                query = query.eq("workspace_id", workspace_id)

            if active_only:
                query = query.eq("is_active", True)

            result = query.order("created_at", desc=True).execute()
            return result.data or []

        except Exception as e:
            logger.error(f"Error getting workflow templates: {str(e)}")
            return []

    async def update_workflow_template(self, template_id: str, updates: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Update a workflow template."""
        try:
            updates["updated_at"] = datetime.utcnow().isoformat()
            updates["updated_by"] = user_id

            result = self.supabase.table("workflow_templates").update(updates).eq("id", template_id).execute()

            if not result.data:
                return {"error": "Template not found"}

            logger.info(f"Updated workflow template: {template_id}")
            return {"success": True, "template": result.data[0]}

        except Exception as e:
            logger.error(f"Error updating workflow template: {str(e)}")
            return {"error": str(e)}

    async def delete_workflow_template(self, template_id: str) -> Dict[str, Any]:
        """Delete (deactivate) a workflow template."""
        try:
            # Soft delete by setting is_active to False
            result = self.supabase.table("workflow_templates").update({
                "is_active": False,
                "deleted_at": datetime.utcnow().isoformat()
            }).eq("id", template_id).execute()

            if not result.data:
                return {"error": "Template not found"}

            logger.info(f"Deleted workflow template: {template_id}")
            return {"success": True}

        except Exception as e:
            logger.error(f"Error deleting workflow template: {str(e)}")
            return {"error": str(e)}

    async def create_workflow_from_template(self, template_id: str, contract_id: str, user_id: str,
                                          workspace_id: str, overrides: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create a workflow instance from a template."""
        try:
            # Get template
            template_response = self.supabase.table("workflow_templates").select("*").eq("id", template_id).execute()

            if not template_response.data:
                return {"error": "Template not found"}

            template = template_response.data[0]

            # Create workflow data from template
            workflow_data = {
                "contract_id": contract_id,
                "workflow_type": template["workflow_type"],
                "steps": template["steps"],
                "escalation_rules": template["escalation_rules"],
                "timeout_settings": template["timeout_settings"],
                "conditions": template["conditions"],
                "template_id": template_id
            }

            # Apply any overrides
            if overrides:
                workflow_data.update(overrides)

            # Create the workflow
            result = await self.create_workflow(workflow_data, user_id, workspace_id)

            if result.get("success"):
                logger.info(f"Created workflow from template {template_id} for contract {contract_id}")

            return result

        except Exception as e:
            logger.error(f"Error creating workflow from template: {str(e)}")
            return {"error": str(e)}

    # Advanced Conditional Routing Methods
    async def evaluate_routing_conditions(self, contract_data: Dict[str, Any], conditions: Dict[str, Any]) -> bool:
        """Evaluate conditional routing rules."""
        try:
            # Contract value conditions
            if "contract_value" in conditions:
                value_conditions = conditions["contract_value"]
                contract_value = float(contract_data.get("value", 0))

                if "min_value" in value_conditions and contract_value < value_conditions["min_value"]:
                    return False
                if "max_value" in value_conditions and contract_value > value_conditions["max_value"]:
                    return False

            # Contract type conditions
            if "contract_types" in conditions:
                allowed_types = conditions["contract_types"]
                contract_type = contract_data.get("type", "").lower()
                if contract_type not in [t.lower() for t in allowed_types]:
                    return False

            # Risk score conditions
            if "risk_score" in conditions:
                risk_conditions = conditions["risk_score"]
                risk_score = float(contract_data.get("risk_score", 0))

                if "min_risk" in risk_conditions and risk_score < risk_conditions["min_risk"]:
                    return False
                if "max_risk" in risk_conditions and risk_score > risk_conditions["max_risk"]:
                    return False

            # Department conditions
            if "departments" in conditions:
                allowed_departments = conditions["departments"]
                contract_department = contract_data.get("department", "")
                if contract_department not in allowed_departments:
                    return False

            # Custom field conditions
            if "custom_fields" in conditions:
                for field_name, field_condition in conditions["custom_fields"].items():
                    field_value = contract_data.get(field_name)
                    if not self._evaluate_field_condition(field_value, field_condition):
                        return False

            return True

        except Exception as e:
            logger.error(f"Error evaluating routing conditions: {str(e)}")
            return False

    def _evaluate_field_condition(self, field_value: Any, condition: Dict[str, Any]) -> bool:
        """Evaluate a single field condition."""
        try:
            condition_type = condition.get("type", "equals")
            expected_value = condition.get("value")

            if condition_type == "equals":
                return field_value == expected_value
            elif condition_type == "not_equals":
                return field_value != expected_value
            elif condition_type == "contains":
                return expected_value in str(field_value)
            elif condition_type == "greater_than":
                return float(field_value) > float(expected_value)
            elif condition_type == "less_than":
                return float(field_value) < float(expected_value)
            elif condition_type == "in_list":
                return field_value in expected_value
            else:
                return True

        except Exception as e:
            logger.error(f"Error evaluating field condition: {str(e)}")
            return False

    async def get_optimal_workflow_route(self, contract_data: Dict[str, Any], available_templates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get the optimal workflow route based on contract data and AI analysis."""
        try:
            # Use AI to analyze contract and recommend workflow
            ai_analysis = await ai_service.analyze_contract_for_workflow(contract_data)

            # Score each template based on conditions and AI recommendations
            template_scores = []

            for template in available_templates:
                score = 0

                # Check if template conditions are met
                conditions_met = await self.evaluate_routing_conditions(contract_data, template.get("conditions", {}))
                if not conditions_met:
                    continue

                # AI recommendation score
                ai_score = ai_analysis.get("template_scores", {}).get(template["id"], 0.5)
                score += ai_score * 0.6  # 60% weight for AI recommendation

                # Contract complexity score
                complexity_score = self._calculate_complexity_score(contract_data, template)
                score += complexity_score * 0.3  # 30% weight for complexity match

                # Historical success score
                historical_score = await self._get_historical_success_score(template["id"])
                score += historical_score * 0.1  # 10% weight for historical success

                template_scores.append({
                    "template": template,
                    "score": score,
                    "ai_recommendation": ai_score,
                    "complexity_match": complexity_score,
                    "historical_success": historical_score
                })

            # Sort by score and return best match
            template_scores.sort(key=lambda x: x["score"], reverse=True)

            if template_scores:
                best_match = template_scores[0]
                return {
                    "success": True,
                    "recommended_template": best_match["template"],
                    "confidence": best_match["score"],
                    "reasoning": {
                        "ai_recommendation": best_match["ai_recommendation"],
                        "complexity_match": best_match["complexity_match"],
                        "historical_success": best_match["historical_success"]
                    },
                    "alternatives": template_scores[1:3]  # Top 2 alternatives
                }
            else:
                return {
                    "success": False,
                    "error": "No suitable workflow template found",
                    "fallback_recommendation": "Use default approval workflow"
                }

        except Exception as e:
            logger.error(f"Error getting optimal workflow route: {str(e)}")
            return {"error": str(e)}

    def _calculate_complexity_score(self, contract_data: Dict[str, Any], template: Dict[str, Any]) -> float:
        """Calculate how well a template matches contract complexity."""
        try:
            contract_value = float(contract_data.get("value", 0))
            risk_score = float(contract_data.get("risk_score", 0.5))

            # Template complexity indicators
            template_steps = len(template.get("steps", []))
            has_escalation = bool(template.get("escalation_rules"))
            has_conditions = bool(template.get("conditions"))

            # Calculate complexity scores
            value_complexity = min(contract_value / 100000, 1.0)  # Normalize to 0-1
            risk_complexity = risk_score
            template_complexity = (template_steps / 10) + (0.2 if has_escalation else 0) + (0.1 if has_conditions else 0)

            # Score based on how well template complexity matches contract complexity
            contract_complexity = (value_complexity + risk_complexity) / 2
            complexity_diff = abs(contract_complexity - template_complexity)

            return max(0, 1 - complexity_diff)

        except Exception as e:
            logger.error(f"Error calculating complexity score: {str(e)}")
            return 0.5

    async def _get_historical_success_score(self, template_id: str) -> float:
        """Get historical success rate for a template."""
        try:
            # Get workflows created from this template in the last 6 months
            six_months_ago = datetime.utcnow() - timedelta(days=180)

            workflows = self.supabase.table("approval_workflows").select("status").eq(
                "template_id", template_id
            ).gte("created_at", six_months_ago.isoformat()).execute()

            if not workflows.data:
                return 0.5  # Default score for new templates

            total_workflows = len(workflows.data)
            successful_workflows = len([w for w in workflows.data if w["status"] == WorkflowStatus.COMPLETED.value])

            return successful_workflows / total_workflows if total_workflows > 0 else 0.5

        except Exception as e:
            logger.error(f"Error getting historical success score: {str(e)}")
            return 0.5


# Global instance
approval_workflow_service = ApprovalWorkflowService()
