"""
Enhanced Contract Lifecycle Management Service for Averum Contracts
Provides advanced version control with branching, merging, and comprehensive diff tracking
"""

import asyncio
import logging
import uuid
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel
from app.db.database import get_supabase_client
from app.services.performance_service import performance_service

logger = logging.getLogger(__name__)


class BranchType(str, Enum):
    """Branch type enumeration."""
    MAIN = "main"
    FEATURE = "feature"
    HOTFIX = "hotfix"
    RELEASE = "release"


class MergeStrategy(str, Enum):
    """Merge strategy enumeration."""
    FAST_FORWARD = "fast_forward"
    THREE_WAY = "three_way"
    SQUASH = "squash"


@dataclass
class DiffChange:
    """Represents a single change in a diff."""
    type: str  # "add", "remove", "modify"
    path: str  # JSON path to the changed field
    old_value: Any
    new_value: Any
    line_number: Optional[int] = None


class ContractBranch(BaseModel):
    """Contract branch model."""
    id: str
    contract_id: str
    branch_name: str
    branch_type: BranchType
    parent_branch_id: Optional[str]
    head_version_id: str
    created_by: str
    created_at: datetime
    is_active: bool
    description: Optional[str]
    workspace_id: str


class MergeRequest(BaseModel):
    """Merge request model."""
    id: str
    source_branch_id: str
    target_branch_id: str
    title: str
    description: Optional[str]
    status: str  # "open", "merged", "closed", "conflict"
    created_by: str
    created_at: datetime
    merged_by: Optional[str]
    merged_at: Optional[datetime]
    conflicts: List[Dict[str, Any]]
    workspace_id: str


class EnhancedLifecycleService:
    """Enhanced contract lifecycle management with advanced version control."""
    
    def __init__(self):
        self.supabase = get_supabase_client()
    
    @performance_service.track_request("lifecycle_create_branch")
    async def create_branch(
        self,
        contract_id: str,
        branch_name: str,
        branch_type: BranchType,
        parent_branch_id: Optional[str],
        user_id: str,
        workspace_id: str,
        description: Optional[str] = None
    ) -> ContractBranch:
        """Create a new contract branch."""
        try:
            # Validate branch name uniqueness
            existing_branch = self.supabase.table("contract_branches").select("id").eq("contract_id", contract_id).eq("branch_name", branch_name).execute()
            
            if existing_branch.data:
                raise ValueError(f"Branch '{branch_name}' already exists for this contract")
            
            # Get parent branch head version if specified
            head_version_id = None
            if parent_branch_id:
                parent_branch = self.supabase.table("contract_branches").select("head_version_id").eq("id", parent_branch_id).single().execute()
                if parent_branch.data:
                    head_version_id = parent_branch.data["head_version_id"]
            
            # If no parent branch, get current version of contract
            if not head_version_id:
                current_version = self.supabase.table("contract_versions").select("id").eq("contract_id", contract_id).eq("is_current", True).single().execute()
                if current_version.data:
                    head_version_id = current_version.data["id"]
            
            if not head_version_id:
                raise ValueError("Cannot determine head version for new branch")
            
            # Create branch
            branch_data = {
                "id": str(uuid.uuid4()),
                "contract_id": contract_id,
                "branch_name": branch_name,
                "branch_type": branch_type.value,
                "parent_branch_id": parent_branch_id,
                "head_version_id": head_version_id,
                "created_by": user_id,
                "created_at": datetime.utcnow().isoformat(),
                "is_active": True,
                "description": description,
                "workspace_id": workspace_id
            }
            
            result = self.supabase.table("contract_branches").insert(branch_data).execute()
            
            if result.data:
                return ContractBranch(**result.data[0])
            
            raise Exception("Failed to create branch")
            
        except Exception as e:
            logger.error(f"Error creating branch: {e}")
            raise
    
    @performance_service.track_request("lifecycle_merge_branches")
    async def merge_branches(
        self,
        source_branch_id: str,
        target_branch_id: str,
        merge_strategy: MergeStrategy,
        user_id: str,
        commit_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Merge one branch into another."""
        try:
            # Get source and target branches
            source_branch = await self._get_branch(source_branch_id)
            target_branch = await self._get_branch(target_branch_id)
            
            if not source_branch or not target_branch:
                raise ValueError("Source or target branch not found")
            
            # Get versions for both branches
            source_version = await self._get_version(source_branch.head_version_id)
            target_version = await self._get_version(target_branch.head_version_id)
            
            # Check for conflicts
            conflicts = await self._detect_merge_conflicts(source_version, target_version)
            
            if conflicts and merge_strategy != MergeStrategy.SQUASH:
                return {
                    "success": False,
                    "conflicts": conflicts,
                    "message": "Merge conflicts detected. Resolve conflicts before merging."
                }
            
            # Perform merge based on strategy
            merged_content = await self._perform_merge(
                source_version, target_version, merge_strategy, conflicts
            )
            
            # Create new version on target branch
            new_version_data = {
                "contract_id": target_branch.contract_id,
                "version_number": await self._get_next_version_number(target_branch.contract_id),
                "title": f"Merge {source_branch.branch_name} into {target_branch.branch_name}",
                "content": merged_content,
                "changes_summary": commit_message or f"Merged branch {source_branch.branch_name}",
                "change_details": {
                    "merge_info": {
                        "source_branch": source_branch.branch_name,
                        "target_branch": target_branch.branch_name,
                        "merge_strategy": merge_strategy.value,
                        "source_version_id": source_version["id"],
                        "target_version_id": target_version["id"]
                    }
                },
                "workspace_id": target_branch.workspace_id
            }
            
            new_version = await self._create_version(new_version_data, user_id)
            
            # Update target branch head
            self.supabase.table("contract_branches").update({
                "head_version_id": new_version["id"]
            }).eq("id", target_branch_id).execute()
            
            # Update contract if merging to main branch
            if target_branch.branch_name == "main":
                await self._update_contract_from_version(target_branch.contract_id, new_version)
            
            return {
                "success": True,
                "new_version_id": new_version["id"],
                "message": f"Successfully merged {source_branch.branch_name} into {target_branch.branch_name}"
            }
            
        except Exception as e:
            logger.error(f"Error merging branches: {e}")
            raise
    
    @performance_service.track_request("lifecycle_advanced_diff")
    async def generate_advanced_diff(
        self,
        version1_id: str,
        version2_id: str,
        include_metadata: bool = True
    ) -> Dict[str, Any]:
        """Generate advanced diff between two versions."""
        try:
            version1 = await self._get_version(version1_id)
            version2 = await self._get_version(version2_id)
            
            if not version1 or not version2:
                raise ValueError("One or both versions not found")
            
            # Generate detailed diff
            changes = await self._generate_detailed_diff(
                version1["content"], version2["content"]
            )
            
            # Calculate statistics
            stats = {
                "total_changes": len(changes),
                "additions": len([c for c in changes if c.type == "add"]),
                "deletions": len([c for c in changes if c.type == "remove"]),
                "modifications": len([c for c in changes if c.type == "modify"])
            }
            
            # Generate summary
            summary = await self._generate_diff_summary(changes, stats)
            
            result = {
                "version1": version1,
                "version2": version2,
                "changes": [
                    {
                        "type": c.type,
                        "path": c.path,
                        "old_value": c.old_value,
                        "new_value": c.new_value,
                        "line_number": c.line_number
                    }
                    for c in changes
                ],
                "statistics": stats,
                "summary": summary
            }
            
            if include_metadata:
                result["metadata"] = {
                    "generated_at": datetime.utcnow().isoformat(),
                    "generated_by": "enhanced_lifecycle_service",
                    "diff_algorithm": "recursive_json_diff"
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Error generating advanced diff: {e}")
            raise
    
    async def _get_branch(self, branch_id: str) -> Optional[ContractBranch]:
        """Get branch by ID."""
        result = self.supabase.table("contract_branches").select("*").eq("id", branch_id).single().execute()
        if result.data:
            return ContractBranch(**result.data)
        return None
    
    async def _get_version(self, version_id: str) -> Optional[Dict[str, Any]]:
        """Get version by ID."""
        result = self.supabase.table("contract_versions").select("*").eq("id", version_id).single().execute()
        return result.data if result.data else None
    
    async def _detect_merge_conflicts(
        self, 
        source_version: Dict[str, Any], 
        target_version: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Detect conflicts between two versions."""
        conflicts = []
        
        # Compare content structures
        source_content = source_version["content"]
        target_content = target_version["content"]
        
        # Simple conflict detection - in production, this would be more sophisticated
        for key in source_content:
            if key in target_content:
                if source_content[key] != target_content[key]:
                    conflicts.append({
                        "path": key,
                        "source_value": source_content[key],
                        "target_value": target_content[key],
                        "conflict_type": "modification"
                    })
        
        return conflicts
    
    async def _perform_merge(
        self,
        source_version: Dict[str, Any],
        target_version: Dict[str, Any],
        strategy: MergeStrategy,
        conflicts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Perform merge based on strategy."""
        if strategy == MergeStrategy.FAST_FORWARD:
            # Simply use source version content
            return source_version["content"]
        
        elif strategy == MergeStrategy.THREE_WAY:
            # Merge non-conflicting changes, keep target for conflicts
            merged = target_version["content"].copy()
            source_content = source_version["content"]
            
            for key, value in source_content.items():
                if not any(c["path"] == key for c in conflicts):
                    merged[key] = value
            
            return merged
        
        elif strategy == MergeStrategy.SQUASH:
            # Combine all changes from source into target
            merged = target_version["content"].copy()
            merged.update(source_version["content"])
            return merged
        
        return target_version["content"]
    
    async def _generate_detailed_diff(
        self, 
        content1: Dict[str, Any], 
        content2: Dict[str, Any]
    ) -> List[DiffChange]:
        """Generate detailed diff between two content objects."""
        changes = []
        
        # Find additions and modifications
        for key, value in content2.items():
            if key not in content1:
                changes.append(DiffChange(
                    type="add",
                    path=key,
                    old_value=None,
                    new_value=value
                ))
            elif content1[key] != value:
                changes.append(DiffChange(
                    type="modify",
                    path=key,
                    old_value=content1[key],
                    new_value=value
                ))
        
        # Find deletions
        for key, value in content1.items():
            if key not in content2:
                changes.append(DiffChange(
                    type="remove",
                    path=key,
                    old_value=value,
                    new_value=None
                ))
        
        return changes
    
    async def _generate_diff_summary(
        self, 
        changes: List[DiffChange], 
        stats: Dict[str, int]
    ) -> str:
        """Generate human-readable diff summary."""
        if stats["total_changes"] == 0:
            return "No changes detected between versions"
        
        summary_parts = []
        
        if stats["additions"] > 0:
            summary_parts.append(f"{stats['additions']} addition(s)")
        
        if stats["deletions"] > 0:
            summary_parts.append(f"{stats['deletions']} deletion(s)")
        
        if stats["modifications"] > 0:
            summary_parts.append(f"{stats['modifications']} modification(s)")
        
        return f"Total changes: {stats['total_changes']} - " + ", ".join(summary_parts)
    
    async def _get_next_version_number(self, contract_id: str) -> int:
        """Get next version number for contract."""
        result = self.supabase.table("contract_versions").select("version_number").eq("contract_id", contract_id).order("version_number", desc=True).limit(1).execute()
        
        if result.data:
            return result.data[0]["version_number"] + 1
        return 1
    
    async def _create_version(self, version_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Create new version."""
        version_data.update({
            "id": str(uuid.uuid4()),
            "created_by": {"id": user_id, "name": "User"},
            "created_at": datetime.utcnow().isoformat(),
            "is_current": False
        })
        
        result = self.supabase.table("contract_versions").insert(version_data).execute()
        
        if result.data:
            return result.data[0]
        
        raise Exception("Failed to create version")
    
    async def _update_contract_from_version(self, contract_id: str, version: Dict[str, Any]):
        """Update main contract from version."""
        # Update contract with version content
        update_data = version["content"].copy()
        update_data["updated_at"] = datetime.utcnow().isoformat()
        
        self.supabase.table("contracts").update(update_data).eq("id", contract_id).execute()
        
        # Mark version as current
        self.supabase.table("contract_versions").update({"is_current": False}).eq("contract_id", contract_id).execute()
        self.supabase.table("contract_versions").update({"is_current": True}).eq("id", version["id"]).execute()


# Global enhanced lifecycle service instance
enhanced_lifecycle_service = EnhancedLifecycleService()
