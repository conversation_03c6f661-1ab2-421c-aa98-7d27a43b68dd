"""
Batch AI Processing Service for Averum Contracts
Handles multiple contract analysis, bulk risk assessment, and comparative analysis
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, asdict

from pydantic import BaseModel
from app.services.secure_ai_service import secure_ai_service
from app.services.clause_analysis_service import clause_analysis_service
from app.db.database import get_supabase_client

logger = logging.getLogger(__name__)


class BatchJobStatus(str, Enum):
    """Batch job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class BatchJobType(str, Enum):
    """Batch job type enumeration."""
    BULK_ANALYSIS = "bulk_analysis"
    COMPARATIVE_ANALYSIS = "comparative_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    CLAUSE_EXTRACTION = "clause_extraction"


@dataclass
class BatchJobItem:
    """Individual item in a batch job."""
    item_id: str
    contract_id: Optional[str]
    contract_text: str
    contract_name: str
    status: BatchJobStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: Optional[float] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class BatchJobProgress(BaseModel):
    """Batch job progress information."""
    job_id: str
    status: BatchJobStatus
    total_items: int
    completed_items: int
    failed_items: int
    progress_percentage: float
    estimated_completion: Optional[datetime]
    current_item: Optional[str]
    started_at: datetime
    updated_at: datetime


class BatchAnalysisResult(BaseModel):
    """Result of batch analysis."""
    job_id: str
    job_type: BatchJobType
    status: BatchJobStatus
    total_items: int
    successful_items: int
    failed_items: int
    results: List[Dict[str, Any]]
    summary_statistics: Dict[str, Any]
    comparative_insights: Optional[Dict[str, Any]]
    processing_time: float
    created_at: datetime
    completed_at: Optional[datetime]


class BatchAIService:
    """Service for handling batch AI processing operations."""
    
    def __init__(self):
        self.active_jobs: Dict[str, BatchJobProgress] = {}
        self.job_items: Dict[str, List[BatchJobItem]] = {}
        self.max_concurrent_jobs = 3
        self.max_items_per_job = 50
        self.job_timeout = timedelta(hours=2)
    
    async def create_bulk_analysis_job(
        self,
        contracts: List[Dict[str, Any]],
        user_id: str,
        workspace_id: str,
        analysis_type: str = "comprehensive",
        job_name: Optional[str] = None
    ) -> str:
        """
        Create a bulk analysis job for multiple contracts.
        
        Args:
            contracts: List of contract data with id, text, and name
            user_id: User creating the job
            workspace_id: Workspace context
            analysis_type: Type of analysis to perform
            job_name: Optional name for the job
            
        Returns:
            Job ID for tracking progress
        """
        if len(contracts) > self.max_items_per_job:
            raise ValueError(f"Too many contracts. Maximum {self.max_items_per_job} allowed per job.")
        
        job_id = f"batch_{uuid.uuid4().hex[:12]}"
        
        # Create job items
        job_items = []
        for i, contract in enumerate(contracts):
            item = BatchJobItem(
                item_id=f"{job_id}_item_{i}",
                contract_id=contract.get("id"),
                contract_text=contract["text"],
                contract_name=contract.get("name", f"Contract {i+1}"),
                status=BatchJobStatus.PENDING
            )
            job_items.append(item)
        
        # Create job progress tracker
        progress = BatchJobProgress(
            job_id=job_id,
            status=BatchJobStatus.PENDING,
            total_items=len(contracts),
            completed_items=0,
            failed_items=0,
            progress_percentage=0.0,
            estimated_completion=None,
            current_item=None,
            started_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Store job data
        self.active_jobs[job_id] = progress
        self.job_items[job_id] = job_items
        
        # Store job in database
        await self._store_job_in_database(
            job_id=job_id,
            job_type=BatchJobType.BULK_ANALYSIS,
            user_id=user_id,
            workspace_id=workspace_id,
            job_name=job_name or f"Bulk Analysis - {len(contracts)} contracts",
            analysis_type=analysis_type,
            total_items=len(contracts)
        )
        
        # Start processing asynchronously
        asyncio.create_task(self._process_bulk_analysis_job(
            job_id, user_id, workspace_id, analysis_type
        ))
        
        return job_id
    
    async def create_comparative_analysis_job(
        self,
        contracts: List[Dict[str, Any]],
        user_id: str,
        workspace_id: str,
        comparison_criteria: List[str],
        job_name: Optional[str] = None
    ) -> str:
        """
        Create a comparative analysis job for multiple contracts.
        
        Args:
            contracts: List of contract data
            user_id: User creating the job
            workspace_id: Workspace context
            comparison_criteria: Criteria for comparison
            job_name: Optional name for the job
            
        Returns:
            Job ID for tracking progress
        """
        if len(contracts) < 2:
            raise ValueError("Comparative analysis requires at least 2 contracts.")
        
        if len(contracts) > self.max_items_per_job:
            raise ValueError(f"Too many contracts. Maximum {self.max_items_per_job} allowed per job.")
        
        job_id = f"comp_{uuid.uuid4().hex[:12]}"
        
        # Create job items
        job_items = []
        for i, contract in enumerate(contracts):
            item = BatchJobItem(
                item_id=f"{job_id}_item_{i}",
                contract_id=contract.get("id"),
                contract_text=contract["text"],
                contract_name=contract.get("name", f"Contract {i+1}"),
                status=BatchJobStatus.PENDING
            )
            job_items.append(item)
        
        # Create job progress tracker
        progress = BatchJobProgress(
            job_id=job_id,
            status=BatchJobStatus.PENDING,
            total_items=len(contracts),
            completed_items=0,
            failed_items=0,
            progress_percentage=0.0,
            estimated_completion=None,
            current_item=None,
            started_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Store job data
        self.active_jobs[job_id] = progress
        self.job_items[job_id] = job_items
        
        # Store job in database
        await self._store_job_in_database(
            job_id=job_id,
            job_type=BatchJobType.COMPARATIVE_ANALYSIS,
            user_id=user_id,
            workspace_id=workspace_id,
            job_name=job_name or f"Comparative Analysis - {len(contracts)} contracts",
            analysis_type="comparative",
            total_items=len(contracts),
            metadata={"comparison_criteria": comparison_criteria}
        )
        
        # Start processing asynchronously
        asyncio.create_task(self._process_comparative_analysis_job(
            job_id, user_id, workspace_id, comparison_criteria
        ))
        
        return job_id
    
    async def get_job_progress(self, job_id: str) -> Optional[BatchJobProgress]:
        """Get progress information for a batch job."""
        if job_id in self.active_jobs:
            return self.active_jobs[job_id]
        
        # Try to load from database if not in memory
        return await self._load_job_progress_from_database(job_id)
    
    async def get_job_results(self, job_id: str) -> Optional[BatchAnalysisResult]:
        """Get results for a completed batch job."""
        return await self._load_job_results_from_database(job_id)
    
    async def cancel_job(self, job_id: str, user_id: str) -> bool:
        """Cancel a running batch job."""
        if job_id not in self.active_jobs:
            return False
        
        progress = self.active_jobs[job_id]
        if progress.status in [BatchJobStatus.COMPLETED, BatchJobStatus.FAILED, BatchJobStatus.CANCELLED]:
            return False
        
        # Update status
        progress.status = BatchJobStatus.CANCELLED
        progress.updated_at = datetime.utcnow()
        
        # Update database
        await self._update_job_status_in_database(job_id, BatchJobStatus.CANCELLED)
        
        logger.info(f"Batch job {job_id} cancelled by user {user_id}")
        return True
    
    async def list_user_jobs(
        self,
        user_id: str,
        workspace_id: Optional[str] = None,
        status: Optional[BatchJobStatus] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """List batch jobs for a user."""
        return await self._load_user_jobs_from_database(user_id, workspace_id, status, limit)
    
    async def _process_bulk_analysis_job(
        self,
        job_id: str,
        user_id: str,
        workspace_id: str,
        analysis_type: str
    ):
        """Process a bulk analysis job."""
        try:
            progress = self.active_jobs[job_id]
            job_items = self.job_items[job_id]
            
            # Update status to running
            progress.status = BatchJobStatus.RUNNING
            progress.updated_at = datetime.utcnow()
            await self._update_job_status_in_database(job_id, BatchJobStatus.RUNNING)
            
            results = []
            start_time = datetime.utcnow()
            
            # Process each contract
            for item in job_items:
                try:
                    progress.current_item = item.contract_name
                    progress.updated_at = datetime.utcnow()
                    
                    item.status = BatchJobStatus.RUNNING
                    item.started_at = datetime.utcnow()
                    
                    # Perform AI analysis
                    analysis_result = await secure_ai_service.analyze_contract_securely(
                        contract_text=item.contract_text,
                        user_id=user_id,
                        workspace_id=workspace_id,
                        contract_id=item.contract_id,
                        analysis_type=analysis_type
                    )
                    
                    item.result = analysis_result
                    item.status = BatchJobStatus.COMPLETED
                    item.completed_at = datetime.utcnow()
                    item.processing_time = (item.completed_at - item.started_at).total_seconds()
                    
                    results.append({
                        "item_id": item.item_id,
                        "contract_id": item.contract_id,
                        "contract_name": item.contract_name,
                        "result": analysis_result,
                        "processing_time": item.processing_time
                    })
                    
                    progress.completed_items += 1
                    
                except Exception as e:
                    logger.error(f"Error processing item {item.item_id}: {e}")
                    item.status = BatchJobStatus.FAILED
                    item.error = str(e)
                    item.completed_at = datetime.utcnow()
                    
                    progress.failed_items += 1
                
                # Update progress
                progress.progress_percentage = (progress.completed_items + progress.failed_items) / progress.total_items * 100
                progress.updated_at = datetime.utcnow()
            
            # Calculate summary statistics
            summary_stats = await self._calculate_bulk_analysis_summary(results)
            
            # Complete the job
            total_time = (datetime.utcnow() - start_time).total_seconds()
            
            batch_result = BatchAnalysisResult(
                job_id=job_id,
                job_type=BatchJobType.BULK_ANALYSIS,
                status=BatchJobStatus.COMPLETED,
                total_items=len(job_items),
                successful_items=progress.completed_items,
                failed_items=progress.failed_items,
                results=results,
                summary_statistics=summary_stats,
                comparative_insights=None,
                processing_time=total_time,
                created_at=progress.started_at,
                completed_at=datetime.utcnow()
            )
            
            # Store results in database
            await self._store_job_results_in_database(job_id, batch_result)
            
            # Update progress
            progress.status = BatchJobStatus.COMPLETED
            progress.updated_at = datetime.utcnow()
            await self._update_job_status_in_database(job_id, BatchJobStatus.COMPLETED)
            
            logger.info(f"Bulk analysis job {job_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Error in bulk analysis job {job_id}: {e}")
            
            # Mark job as failed
            if job_id in self.active_jobs:
                self.active_jobs[job_id].status = BatchJobStatus.FAILED
                self.active_jobs[job_id].updated_at = datetime.utcnow()
            
            await self._update_job_status_in_database(job_id, BatchJobStatus.FAILED)


    async def _process_comparative_analysis_job(
        self,
        job_id: str,
        user_id: str,
        workspace_id: str,
        comparison_criteria: List[str]
    ):
        """Process a comparative analysis job."""
        try:
            progress = self.active_jobs[job_id]
            job_items = self.job_items[job_id]

            # Update status to running
            progress.status = BatchJobStatus.RUNNING
            progress.updated_at = datetime.utcnow()
            await self._update_job_status_in_database(job_id, BatchJobStatus.RUNNING)

            results = []
            start_time = datetime.utcnow()

            # First, analyze each contract individually
            for item in job_items:
                try:
                    progress.current_item = item.contract_name
                    progress.updated_at = datetime.utcnow()

                    item.status = BatchJobStatus.RUNNING
                    item.started_at = datetime.utcnow()

                    # Perform comprehensive analysis
                    analysis_result = await secure_ai_service.analyze_contract_securely(
                        contract_text=item.contract_text,
                        user_id=user_id,
                        workspace_id=workspace_id,
                        contract_id=item.contract_id,
                        analysis_type="comprehensive"
                    )

                    # Also perform clause analysis
                    clause_analysis = await clause_analysis_service.analyze_contract_comprehensively(
                        item.contract_text
                    )

                    combined_result = {
                        "ai_analysis": analysis_result,
                        "clause_analysis": clause_analysis
                    }

                    item.result = combined_result
                    item.status = BatchJobStatus.COMPLETED
                    item.completed_at = datetime.utcnow()
                    item.processing_time = (item.completed_at - item.started_at).total_seconds()

                    results.append({
                        "item_id": item.item_id,
                        "contract_id": item.contract_id,
                        "contract_name": item.contract_name,
                        "result": combined_result,
                        "processing_time": item.processing_time
                    })

                    progress.completed_items += 1

                except Exception as e:
                    logger.error(f"Error processing comparative item {item.item_id}: {e}")
                    item.status = BatchJobStatus.FAILED
                    item.error = str(e)
                    item.completed_at = datetime.utcnow()

                    progress.failed_items += 1

                # Update progress
                progress.progress_percentage = (progress.completed_items + progress.failed_items) / progress.total_items * 100
                progress.updated_at = datetime.utcnow()

            # Perform comparative analysis
            comparative_insights = await self._perform_comparative_analysis(results, comparison_criteria)

            # Calculate summary statistics
            summary_stats = await self._calculate_comparative_analysis_summary(results)

            # Complete the job
            total_time = (datetime.utcnow() - start_time).total_seconds()

            batch_result = BatchAnalysisResult(
                job_id=job_id,
                job_type=BatchJobType.COMPARATIVE_ANALYSIS,
                status=BatchJobStatus.COMPLETED,
                total_items=len(job_items),
                successful_items=progress.completed_items,
                failed_items=progress.failed_items,
                results=results,
                summary_statistics=summary_stats,
                comparative_insights=comparative_insights,
                processing_time=total_time,
                created_at=progress.started_at,
                completed_at=datetime.utcnow()
            )

            # Store results in database
            await self._store_job_results_in_database(job_id, batch_result)

            # Update progress
            progress.status = BatchJobStatus.COMPLETED
            progress.updated_at = datetime.utcnow()
            await self._update_job_status_in_database(job_id, BatchJobStatus.COMPLETED)

            logger.info(f"Comparative analysis job {job_id} completed successfully")

        except Exception as e:
            logger.error(f"Error in comparative analysis job {job_id}: {e}")

            # Mark job as failed
            if job_id in self.active_jobs:
                self.active_jobs[job_id].status = BatchJobStatus.FAILED
                self.active_jobs[job_id].updated_at = datetime.utcnow()

            await self._update_job_status_in_database(job_id, BatchJobStatus.FAILED)

    async def _calculate_bulk_analysis_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate summary statistics for bulk analysis."""
        if not results:
            return {}

        # Extract metrics from successful results
        successful_results = [r for r in results if r.get("result", {}).get("success")]

        if not successful_results:
            return {"error": "No successful analyses to summarize"}

        # Calculate averages
        risk_scores = []
        compliance_scores = []
        processing_times = []
        contract_types = {}

        for result in successful_results:
            analysis = result["result"].get("analysis_result", {})
            risk_scores.append(analysis.get("risk_score", 0))
            compliance_scores.append(analysis.get("compliance_score", 0))
            processing_times.append(result.get("processing_time", 0))

            contract_type = analysis.get("contract_type", "Unknown")
            contract_types[contract_type] = contract_types.get(contract_type, 0) + 1

        return {
            "total_contracts": len(results),
            "successful_analyses": len(successful_results),
            "average_risk_score": sum(risk_scores) / len(risk_scores) if risk_scores else 0,
            "average_compliance_score": sum(compliance_scores) / len(compliance_scores) if compliance_scores else 0,
            "average_processing_time": sum(processing_times) / len(processing_times) if processing_times else 0,
            "contract_type_distribution": contract_types,
            "highest_risk_score": max(risk_scores) if risk_scores else 0,
            "lowest_risk_score": min(risk_scores) if risk_scores else 0,
            "highest_compliance_score": max(compliance_scores) if compliance_scores else 0,
            "lowest_compliance_score": min(compliance_scores) if compliance_scores else 0
        }

    async def _calculate_comparative_analysis_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate summary statistics for comparative analysis."""
        summary = await self._calculate_bulk_analysis_summary(results)

        # Add comparative-specific metrics
        if len(results) >= 2:
            successful_results = [r for r in results if r.get("result", {}).get("ai_analysis", {}).get("success")]

            if len(successful_results) >= 2:
                # Calculate variance in risk scores
                risk_scores = [r["result"]["ai_analysis"]["analysis_result"]["risk_score"] for r in successful_results]
                risk_variance = sum((x - summary["average_risk_score"]) ** 2 for x in risk_scores) / len(risk_scores)

                summary.update({
                    "risk_score_variance": risk_variance,
                    "contracts_compared": len(successful_results),
                    "comparison_completed": True
                })

        return summary

    async def _perform_comparative_analysis(
        self,
        results: List[Dict[str, Any]],
        comparison_criteria: List[str]
    ) -> Dict[str, Any]:
        """Perform comparative analysis across contracts."""
        if len(results) < 2:
            return {"error": "Insufficient contracts for comparison"}

        successful_results = [r for r in results if r.get("result", {}).get("ai_analysis", {}).get("success")]

        if len(successful_results) < 2:
            return {"error": "Insufficient successful analyses for comparison"}

        insights = {
            "comparison_criteria": comparison_criteria,
            "contracts_compared": len(successful_results),
            "risk_comparison": {},
            "clause_comparison": {},
            "compliance_comparison": {},
            "recommendations": []
        }

        # Risk score comparison
        risk_scores = [(r["contract_name"], r["result"]["ai_analysis"]["analysis_result"]["risk_score"])
                      for r in successful_results]
        risk_scores.sort(key=lambda x: x[1], reverse=True)

        insights["risk_comparison"] = {
            "highest_risk": risk_scores[0],
            "lowest_risk": risk_scores[-1],
            "risk_ranking": risk_scores
        }

        # Clause comparison
        clause_counts = {}
        missing_clause_counts = {}

        for result in successful_results:
            contract_name = result["contract_name"]
            clause_analysis = result["result"].get("clause_analysis", {})

            clause_counts[contract_name] = clause_analysis.get("total_clauses_found", 0)
            missing_clause_counts[contract_name] = len(clause_analysis.get("missing_clauses", []))

        insights["clause_comparison"] = {
            "clause_counts": clause_counts,
            "missing_clause_counts": missing_clause_counts,
            "most_complete_contract": min(missing_clause_counts.items(), key=lambda x: x[1])[0] if missing_clause_counts else None,
            "least_complete_contract": max(missing_clause_counts.items(), key=lambda x: x[1])[0] if missing_clause_counts else None
        }

        # Generate recommendations
        if risk_scores:
            highest_risk_contract = risk_scores[0][0]
            lowest_risk_contract = risk_scores[-1][0]

            insights["recommendations"].append(
                f"Review {highest_risk_contract} for risk mitigation opportunities"
            )
            insights["recommendations"].append(
                f"Consider using {lowest_risk_contract} as a template for lower-risk contracts"
            )

        return insights


    async def _store_job_in_database(
        self,
        job_id: str,
        job_type: BatchJobType,
        user_id: str,
        workspace_id: str,
        job_name: str,
        analysis_type: str,
        total_items: int,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Store batch job information in database."""
        try:
            supabase = get_supabase_client()

            job_data = {
                "id": job_id,
                "job_type": job_type.value,
                "job_name": job_name,
                "user_id": user_id,
                "workspace_id": workspace_id,
                "status": BatchJobStatus.PENDING.value,
                "analysis_type": analysis_type,
                "total_items": total_items,
                "completed_items": 0,
                "failed_items": 0,
                "progress_percentage": 0.0,
                "metadata": metadata or {},
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat()
            }

            result = supabase.table("batch_jobs").insert(job_data).execute()

            if not result.data:
                logger.error(f"Failed to store batch job {job_id} in database")

        except Exception as e:
            logger.error(f"Error storing batch job {job_id} in database: {e}")

    async def _update_job_status_in_database(self, job_id: str, status: BatchJobStatus):
        """Update job status in database."""
        try:
            supabase = get_supabase_client()

            progress = self.active_jobs.get(job_id)
            update_data = {
                "status": status.value,
                "updated_at": datetime.utcnow().isoformat()
            }

            if progress:
                update_data.update({
                    "completed_items": progress.completed_items,
                    "failed_items": progress.failed_items,
                    "progress_percentage": progress.progress_percentage,
                    "current_item": progress.current_item
                })

            if status == BatchJobStatus.COMPLETED:
                update_data["completed_at"] = datetime.utcnow().isoformat()

            result = supabase.table("batch_jobs").update(update_data).eq("id", job_id).execute()

            if not result.data:
                logger.error(f"Failed to update batch job {job_id} status in database")

        except Exception as e:
            logger.error(f"Error updating batch job {job_id} status in database: {e}")

    async def _store_job_results_in_database(self, job_id: str, batch_result: BatchAnalysisResult):
        """Store batch job results in database."""
        try:
            supabase = get_supabase_client()

            # Store main results
            result_data = {
                "job_id": job_id,
                "results": batch_result.results,
                "summary_statistics": batch_result.summary_statistics,
                "comparative_insights": batch_result.comparative_insights,
                "processing_time": batch_result.processing_time,
                "created_at": batch_result.created_at.isoformat(),
                "completed_at": batch_result.completed_at.isoformat() if batch_result.completed_at else None
            }

            result = supabase.table("batch_job_results").insert(result_data).execute()

            if not result.data:
                logger.error(f"Failed to store batch job {job_id} results in database")

        except Exception as e:
            logger.error(f"Error storing batch job {job_id} results in database: {e}")

    async def _load_job_progress_from_database(self, job_id: str) -> Optional[BatchJobProgress]:
        """Load job progress from database."""
        try:
            supabase = get_supabase_client()

            result = supabase.table("batch_jobs").select("*").eq("id", job_id).execute()

            if result.data:
                job_data = result.data[0]
                return BatchJobProgress(
                    job_id=job_data["id"],
                    status=BatchJobStatus(job_data["status"]),
                    total_items=job_data["total_items"],
                    completed_items=job_data["completed_items"],
                    failed_items=job_data["failed_items"],
                    progress_percentage=job_data["progress_percentage"],
                    estimated_completion=None,  # Could be calculated
                    current_item=job_data.get("current_item"),
                    started_at=datetime.fromisoformat(job_data["created_at"]),
                    updated_at=datetime.fromisoformat(job_data["updated_at"])
                )

            return None

        except Exception as e:
            logger.error(f"Error loading job progress {job_id} from database: {e}")
            return None

    async def _load_job_results_from_database(self, job_id: str) -> Optional[BatchAnalysisResult]:
        """Load job results from database."""
        try:
            supabase = get_supabase_client()

            # Get job info
            job_result = supabase.table("batch_jobs").select("*").eq("id", job_id).execute()

            if not job_result.data:
                return None

            job_data = job_result.data[0]

            # Get results
            results_result = supabase.table("batch_job_results").select("*").eq("job_id", job_id).execute()

            if not results_result.data:
                return None

            results_data = results_result.data[0]

            return BatchAnalysisResult(
                job_id=job_id,
                job_type=BatchJobType(job_data["job_type"]),
                status=BatchJobStatus(job_data["status"]),
                total_items=job_data["total_items"],
                successful_items=job_data["completed_items"],
                failed_items=job_data["failed_items"],
                results=results_data["results"],
                summary_statistics=results_data["summary_statistics"],
                comparative_insights=results_data.get("comparative_insights"),
                processing_time=results_data["processing_time"],
                created_at=datetime.fromisoformat(job_data["created_at"]),
                completed_at=datetime.fromisoformat(results_data["completed_at"]) if results_data.get("completed_at") else None
            )

        except Exception as e:
            logger.error(f"Error loading job results {job_id} from database: {e}")
            return None

    async def _load_user_jobs_from_database(
        self,
        user_id: str,
        workspace_id: Optional[str] = None,
        status: Optional[BatchJobStatus] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Load user's batch jobs from database."""
        try:
            supabase = get_supabase_client()

            query = supabase.table("batch_jobs").select("*").eq("user_id", user_id)

            if workspace_id:
                query = query.eq("workspace_id", workspace_id)

            if status:
                query = query.eq("status", status.value)

            result = query.order("created_at", desc=True).limit(limit).execute()

            return result.data or []

        except Exception as e:
            logger.error(f"Error loading user jobs from database: {e}")
            return []


# Global batch AI service instance
batch_ai_service = BatchAIService()
