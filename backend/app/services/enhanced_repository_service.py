"""
Enhanced Document Repository Service for Averum Contracts
Provides advanced search, semantic search, organization tools, and access control
"""

import asyncio
import logging
import json
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel
from app.db.database import get_supabase_client
from app.services.performance_service import performance_service
from app.services.security_service import security_service

logger = logging.getLogger(__name__)


class SearchType(str, Enum):
    """Search type enumeration."""
    TEXT = "text"
    SEMANTIC = "semantic"
    HYBRID = "hybrid"
    FUZZY = "fuzzy"


class AccessLevel(str, Enum):
    """Access level enumeration."""
    READ = "read"
    WRITE = "write"
    ADMIN = "admin"
    OWNER = "owner"


@dataclass
class SearchResult:
    """Search result with relevance scoring."""
    document_id: str
    title: str
    content_snippet: str
    relevance_score: float
    match_type: str
    highlighted_text: str
    metadata: Dict[str, Any]


class DocumentTag(BaseModel):
    """Document tag model."""
    id: str
    name: str
    color: str
    description: Optional[str]
    workspace_id: str
    created_by: str
    created_at: datetime
    usage_count: int


class DocumentCollection(BaseModel):
    """Document collection model."""
    id: str
    name: str
    description: Optional[str]
    workspace_id: str
    parent_collection_id: Optional[str]
    created_by: str
    created_at: datetime
    document_count: int
    access_level: AccessLevel
    is_public: bool


class EnhancedRepositoryService:
    """Enhanced document repository service with advanced features."""
    
    def __init__(self):
        self.supabase = get_supabase_client()
        self.search_cache = {}
        self.cache_ttl = 300  # 5 minutes
    
    @performance_service.track_request("repository_advanced_search")
    async def advanced_search(
        self,
        workspace_id: str,
        query: str,
        search_type: SearchType = SearchType.HYBRID,
        filters: Optional[Dict[str, Any]] = None,
        user_id: str = None,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Perform advanced search with multiple strategies.
        
        Args:
            workspace_id: Workspace to search in
            query: Search query
            search_type: Type of search to perform
            filters: Additional filters
            user_id: User performing the search
            limit: Maximum results
            offset: Pagination offset
            
        Returns:
            Search results with metadata
        """
        try:
            # Check cache first
            cache_key = f"{workspace_id}:{query}:{search_type.value}:{json.dumps(filters, sort_keys=True)}"
            cached_result = self._get_cached_search(cache_key)
            if cached_result:
                return cached_result
            
            results = []
            
            if search_type in [SearchType.TEXT, SearchType.HYBRID]:
                text_results = await self._perform_text_search(workspace_id, query, filters, limit, offset)
                results.extend(text_results)
            
            if search_type in [SearchType.SEMANTIC, SearchType.HYBRID]:
                semantic_results = await self._perform_semantic_search(workspace_id, query, filters, limit, offset)
                results.extend(semantic_results)
            
            if search_type == SearchType.FUZZY:
                fuzzy_results = await self._perform_fuzzy_search(workspace_id, query, filters, limit, offset)
                results.extend(fuzzy_results)
            
            # Merge and rank results for hybrid search
            if search_type == SearchType.HYBRID:
                results = self._merge_hybrid_results(results)
            
            # Apply access control filtering
            if user_id:
                results = await self._filter_by_access_control(results, user_id, workspace_id)
            
            # Sort by relevance
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            
            # Apply pagination
            paginated_results = results[offset:offset + limit]
            
            # Generate search suggestions
            suggestions = await self._generate_search_suggestions(query, workspace_id)
            
            search_result = {
                "results": [self._format_search_result(r) for r in paginated_results],
                "total_count": len(results),
                "search_type": search_type.value,
                "query": query,
                "suggestions": suggestions,
                "filters_applied": filters or {},
                "search_time": 0.0,  # Will be calculated
                "facets": await self._generate_search_facets(results)
            }
            
            # Cache the result
            self._cache_search_result(cache_key, search_result)
            
            # Log search activity
            await security_service.log_audit_event(
                event_type="document_search",
                user_id=user_id or "system",
                workspace_id=workspace_id,
                action="advanced_search_performed",
                resource_type="documents",
                details={
                    "query": query,
                    "search_type": search_type.value,
                    "results_count": len(paginated_results),
                    "filters": filters
                }
            )
            
            return search_result
            
        except Exception as e:
            logger.error(f"Error in advanced search: {e}")
            raise
    
    async def _perform_text_search(
        self,
        workspace_id: str,
        query: str,
        filters: Optional[Dict[str, Any]],
        limit: int,
        offset: int
    ) -> List[SearchResult]:
        """Perform full-text search using PostgreSQL capabilities."""
        try:
            # Build search query with PostgreSQL full-text search
            search_query = self.supabase.table("documents").select(
                "id, title, content, filename, file_info, folder, created_at, created_by"
            ).eq("workspace_id", workspace_id)
            
            # Apply text search using PostgreSQL's text search
            if query:
                # Use ilike for case-insensitive search across multiple fields
                search_terms = query.split()
                for term in search_terms:
                    search_query = search_query.or_(
                        f"title.ilike.%{term}%,"
                        f"content.ilike.%{term}%,"
                        f"filename.ilike.%{term}%"
                    )
            
            # Apply filters
            if filters:
                search_query = self._apply_search_filters(search_query, filters)
            
            result = search_query.limit(limit).offset(offset).execute()
            
            search_results = []
            for doc in result.data or []:
                # Calculate relevance score based on matches
                relevance_score = self._calculate_text_relevance(doc, query)
                
                # Generate content snippet
                snippet = self._generate_content_snippet(doc.get("content", ""), query)
                
                # Highlight matches
                highlighted = self._highlight_matches(snippet, query)
                
                search_results.append(SearchResult(
                    document_id=doc["id"],
                    title=doc["title"],
                    content_snippet=snippet,
                    relevance_score=relevance_score,
                    match_type="text",
                    highlighted_text=highlighted,
                    metadata={
                        "filename": doc["filename"],
                        "folder": doc.get("folder"),
                        "created_at": doc["created_at"],
                        "created_by": doc["created_by"],
                        "file_info": doc.get("file_info", {})
                    }
                ))
            
            return search_results
            
        except Exception as e:
            logger.error(f"Error in text search: {e}")
            return []
    
    async def _perform_semantic_search(
        self,
        workspace_id: str,
        query: str,
        filters: Optional[Dict[str, Any]],
        limit: int,
        offset: int
    ) -> List[SearchResult]:
        """Perform semantic search using AI embeddings."""
        try:
            # For now, implement a basic semantic search
            # In production, this would use vector embeddings and similarity search
            
            # Get all documents in workspace
            result = self.supabase.table("documents").select(
                "id, title, content, filename, file_info, folder, created_at, created_by"
            ).eq("workspace_id", workspace_id).execute()
            
            search_results = []
            query_lower = query.lower()
            
            for doc in result.data or []:
                # Simple semantic matching based on related terms
                semantic_score = self._calculate_semantic_relevance(doc, query_lower)
                
                if semantic_score > 0.1:  # Threshold for semantic relevance
                    snippet = self._generate_content_snippet(doc.get("content", ""), query)
                    highlighted = self._highlight_semantic_matches(snippet, query)
                    
                    search_results.append(SearchResult(
                        document_id=doc["id"],
                        title=doc["title"],
                        content_snippet=snippet,
                        relevance_score=semantic_score,
                        match_type="semantic",
                        highlighted_text=highlighted,
                        metadata={
                            "filename": doc["filename"],
                            "folder": doc.get("folder"),
                            "created_at": doc["created_at"],
                            "created_by": doc["created_by"],
                            "file_info": doc.get("file_info", {})
                        }
                    ))
            
            # Sort by semantic relevance
            search_results.sort(key=lambda x: x.relevance_score, reverse=True)
            
            return search_results[offset:offset + limit]
            
        except Exception as e:
            logger.error(f"Error in semantic search: {e}")
            return []
    
    async def _perform_fuzzy_search(
        self,
        workspace_id: str,
        query: str,
        filters: Optional[Dict[str, Any]],
        limit: int,
        offset: int
    ) -> List[SearchResult]:
        """Perform fuzzy search for typo tolerance."""
        try:
            # Implement basic fuzzy search using edit distance
            result = self.supabase.table("documents").select(
                "id, title, content, filename, file_info, folder, created_at, created_by"
            ).eq("workspace_id", workspace_id).execute()
            
            search_results = []
            
            for doc in result.data or []:
                fuzzy_score = self._calculate_fuzzy_relevance(doc, query)
                
                if fuzzy_score > 0.3:  # Threshold for fuzzy matching
                    snippet = self._generate_content_snippet(doc.get("content", ""), query)
                    highlighted = self._highlight_fuzzy_matches(snippet, query)
                    
                    search_results.append(SearchResult(
                        document_id=doc["id"],
                        title=doc["title"],
                        content_snippet=snippet,
                        relevance_score=fuzzy_score,
                        match_type="fuzzy",
                        highlighted_text=highlighted,
                        metadata={
                            "filename": doc["filename"],
                            "folder": doc.get("folder"),
                            "created_at": doc["created_at"],
                            "created_by": doc["created_by"],
                            "file_info": doc.get("file_info", {})
                        }
                    ))
            
            search_results.sort(key=lambda x: x.relevance_score, reverse=True)
            return search_results[offset:offset + limit]
            
        except Exception as e:
            logger.error(f"Error in fuzzy search: {e}")
            return []


    def _apply_search_filters(self, query, filters: Dict[str, Any]):
        """Apply search filters to query."""
        if "file_type" in filters:
            file_types = filters["file_type"]
            if isinstance(file_types, list):
                type_conditions = [f"file_info->>content_type.ilike.%{ft}%" for ft in file_types]
                query = query.or_(",".join(type_conditions))

        if "folder" in filters and filters["folder"] != "all":
            query = query.eq("folder", filters["folder"])

        if "date_range" in filters:
            date_range = filters["date_range"]
            if "start" in date_range:
                query = query.gte("created_at", date_range["start"])
            if "end" in date_range:
                query = query.lte("created_at", date_range["end"])

        if "created_by" in filters:
            query = query.eq("created_by", filters["created_by"])

        return query

    def _calculate_text_relevance(self, doc: Dict[str, Any], query: str) -> float:
        """Calculate text relevance score."""
        score = 0.0
        query_lower = query.lower()

        # Title matches get higher score
        if query_lower in doc.get("title", "").lower():
            score += 0.8

        # Content matches
        content = doc.get("content", "")
        if content and query_lower in content.lower():
            score += 0.6

        # Filename matches
        if query_lower in doc.get("filename", "").lower():
            score += 0.4

        # Boost score based on query term frequency
        query_terms = query_lower.split()
        for term in query_terms:
            title_count = doc.get("title", "").lower().count(term)
            content_count = content.lower().count(term) if content else 0
            score += (title_count * 0.1) + (content_count * 0.05)

        return min(score, 1.0)

    def _calculate_semantic_relevance(self, doc: Dict[str, Any], query: str) -> float:
        """Calculate semantic relevance score."""
        # Simple semantic matching - in production, use embeddings
        semantic_keywords = {
            "contract": ["agreement", "deal", "terms", "conditions", "legal"],
            "payment": ["money", "fee", "cost", "price", "billing"],
            "termination": ["end", "cancel", "expire", "finish", "close"],
            "liability": ["responsibility", "fault", "blame", "risk", "damage"],
            "confidential": ["secret", "private", "proprietary", "sensitive"]
        }

        score = 0.0
        doc_text = f"{doc.get('title', '')} {doc.get('content', '')}".lower()

        for main_term, related_terms in semantic_keywords.items():
            if main_term in query:
                for related_term in related_terms:
                    if related_term in doc_text:
                        score += 0.2

        return min(score, 1.0)

    def _calculate_fuzzy_relevance(self, doc: Dict[str, Any], query: str) -> float:
        """Calculate fuzzy relevance score using edit distance."""
        def levenshtein_distance(s1: str, s2: str) -> int:
            if len(s1) < len(s2):
                return levenshtein_distance(s2, s1)

            if len(s2) == 0:
                return len(s1)

            previous_row = list(range(len(s2) + 1))
            for i, c1 in enumerate(s1):
                current_row = [i + 1]
                for j, c2 in enumerate(s2):
                    insertions = previous_row[j + 1] + 1
                    deletions = current_row[j] + 1
                    substitutions = previous_row[j] + (c1 != c2)
                    current_row.append(min(insertions, deletions, substitutions))
                previous_row = current_row

            return previous_row[-1]

        score = 0.0
        query_lower = query.lower()

        # Check title fuzzy match
        title = doc.get("title", "").lower()
        if title:
            distance = levenshtein_distance(query_lower, title)
            max_len = max(len(query_lower), len(title))
            if max_len > 0:
                similarity = 1 - (distance / max_len)
                score = max(score, similarity * 0.8)

        # Check content words fuzzy match
        content = doc.get("content", "")
        if content:
            content_words = content.lower().split()
            query_words = query_lower.split()

            for query_word in query_words:
                best_match = 0
                for content_word in content_words:
                    if len(content_word) > 3:  # Only check longer words
                        distance = levenshtein_distance(query_word, content_word)
                        max_len = max(len(query_word), len(content_word))
                        if max_len > 0:
                            similarity = 1 - (distance / max_len)
                            best_match = max(best_match, similarity)

                if best_match > 0.7:  # Good fuzzy match
                    score += best_match * 0.3

        return min(score, 1.0)

    def _generate_content_snippet(self, content: str, query: str, max_length: int = 200) -> str:
        """Generate content snippet around query matches."""
        if not content or not query:
            return content[:max_length] if content else ""

        query_lower = query.lower()
        content_lower = content.lower()

        # Find first occurrence of query
        index = content_lower.find(query_lower)
        if index == -1:
            # No exact match, return beginning
            return content[:max_length]

        # Calculate snippet boundaries
        start = max(0, index - max_length // 3)
        end = min(len(content), index + len(query) + max_length // 3)

        snippet = content[start:end]

        # Add ellipsis if truncated
        if start > 0:
            snippet = "..." + snippet
        if end < len(content):
            snippet = snippet + "..."

        return snippet

    def _highlight_matches(self, text: str, query: str) -> str:
        """Highlight query matches in text."""
        if not query:
            return text

        # Simple highlighting - in production, use more sophisticated highlighting
        query_terms = query.split()
        highlighted = text

        for term in query_terms:
            pattern = re.compile(re.escape(term), re.IGNORECASE)
            highlighted = pattern.sub(f"<mark>{term}</mark>", highlighted)

        return highlighted

    def _highlight_semantic_matches(self, text: str, query: str) -> str:
        """Highlight semantic matches in text."""
        # For semantic search, highlight related terms
        return self._highlight_matches(text, query)

    def _highlight_fuzzy_matches(self, text: str, query: str) -> str:
        """Highlight fuzzy matches in text."""
        # For fuzzy search, highlight approximate matches
        return self._highlight_matches(text, query)

    def _merge_hybrid_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """Merge results from different search types."""
        # Group by document ID and combine scores
        merged = {}

        for result in results:
            doc_id = result.document_id
            if doc_id in merged:
                # Combine scores (weighted average)
                existing = merged[doc_id]
                combined_score = (existing.relevance_score + result.relevance_score) / 2

                # Keep the result with higher individual score but use combined score
                if result.relevance_score > existing.relevance_score:
                    merged[doc_id] = result

                merged[doc_id].relevance_score = combined_score
                merged[doc_id].match_type = "hybrid"
            else:
                merged[doc_id] = result

        return list(merged.values())

    async def _filter_by_access_control(
        self,
        results: List[SearchResult],
        user_id: str,
        workspace_id: str
    ) -> List[SearchResult]:
        """Filter results based on user access control."""
        # For now, basic filtering - in production, implement proper ACL
        # All users in workspace can see all documents (basic RLS handles this)
        return results

    async def _generate_search_suggestions(self, query: str, workspace_id: str) -> List[str]:
        """Generate search suggestions based on query and workspace content."""
        suggestions = []

        # Get common terms from documents
        result = self.supabase.table("documents").select("title, content").eq("workspace_id", workspace_id).limit(100).execute()

        # Extract common terms (simplified)
        all_text = " ".join([doc.get("title", "") + " " + doc.get("content", "")[:500] for doc in result.data or []])
        words = re.findall(r'\b\w{4,}\b', all_text.lower())

        # Find words similar to query
        query_lower = query.lower()
        for word in set(words):
            if query_lower in word or word.startswith(query_lower[:3]):
                suggestions.append(word)

        return suggestions[:5]

    async def _generate_search_facets(self, results: List[SearchResult]) -> Dict[str, Any]:
        """Generate search facets for filtering."""
        facets = {
            "file_types": {},
            "folders": {},
            "date_ranges": {},
            "match_types": {}
        }

        for result in results:
            # File type facets
            file_info = result.metadata.get("file_info", {})
            content_type = file_info.get("content_type", "unknown")
            facets["file_types"][content_type] = facets["file_types"].get(content_type, 0) + 1

            # Folder facets
            folder = result.metadata.get("folder", "root")
            facets["folders"][folder] = facets["folders"].get(folder, 0) + 1

            # Match type facets
            match_type = result.match_type
            facets["match_types"][match_type] = facets["match_types"].get(match_type, 0) + 1

        return facets

    def _get_cached_search(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached search result."""
        if cache_key in self.search_cache:
            cached_data, timestamp = self.search_cache[cache_key]
            if datetime.utcnow().timestamp() - timestamp < self.cache_ttl:
                return cached_data
            else:
                del self.search_cache[cache_key]
        return None

    def _cache_search_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache search result."""
        self.search_cache[cache_key] = (result, datetime.utcnow().timestamp())

        # Clean old cache entries
        current_time = datetime.utcnow().timestamp()
        expired_keys = [k for k, (_, ts) in self.search_cache.items() if current_time - ts > self.cache_ttl]
        for key in expired_keys:
            del self.search_cache[key]

    def _format_search_result(self, result: SearchResult) -> Dict[str, Any]:
        """Format search result for API response."""
        return {
            "document_id": result.document_id,
            "title": result.title,
            "content_snippet": result.content_snippet,
            "relevance_score": result.relevance_score,
            "match_type": result.match_type,
            "highlighted_text": result.highlighted_text,
            "metadata": result.metadata
        }


# Global enhanced repository service instance
enhanced_repository_service = EnhancedRepositoryService()
