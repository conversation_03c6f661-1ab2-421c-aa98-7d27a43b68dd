"""
Security Service for Averum Contracts
Handles data encryption, PII detection/anonymization, audit logging, and GDPR compliance
"""

import hashlib
import hmac
import logging
import re
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

from pydantic import BaseModel
from app.core.config import settings
from app.db.database import get_supabase_client

logger = logging.getLogger(__name__)


class PIIDetectionResult(BaseModel):
    """Result of PII detection."""
    pii_found: bool
    pii_types: List[str]
    pii_count: int
    anonymized_text: str
    redaction_map: Dict[str, str]


class AuditLogEntry(BaseModel):
    """Audit log entry structure."""
    event_type: str
    user_id: str
    workspace_id: Optional[str]
    resource_type: str
    resource_id: Optional[str]
    action: str
    details: Dict[str, Any]
    ip_address: Optional[str]
    user_agent: Optional[str]
    timestamp: datetime
    session_id: Optional[str]


class SecurityService:
    """Main security service for AI operations and data protection."""
    
    def __init__(self):
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
        # PII detection patterns
        self.pii_patterns = {
            'ssn': re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
            'credit_card': re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'),
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'phone': re.compile(r'\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b'),
            'ip_address': re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
            'bank_account': re.compile(r'\b\d{8,17}\b'),
            'passport': re.compile(r'\b[A-Z]{1,2}\d{6,9}\b'),
            'driver_license': re.compile(r'\b[A-Z]{1,2}\d{6,8}\b'),
            'tax_id': re.compile(r'\b\d{2}-\d{7}\b'),
            'medical_record': re.compile(r'\bMRN[-\s]?\d{6,10}\b', re.IGNORECASE)
        }
        
        # Sensitive data patterns for contracts
        self.sensitive_patterns = {
            'salary': re.compile(r'\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?(?:\s*(?:per|/)\s*(?:year|month|hour|annum))?', re.IGNORECASE),
            'compensation': re.compile(r'\b(?:salary|wage|compensation|payment)\s*:?\s*\$?\d{1,3}(?:,\d{3})*(?:\.\d{2})?\b', re.IGNORECASE),
            'confidential_info': re.compile(r'\b(?:confidential|proprietary|trade\s+secret|classified)\b', re.IGNORECASE),
            'personal_address': re.compile(r'\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)\b', re.IGNORECASE)
        }
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for data protection."""
        # In production, this should be stored securely (e.g., AWS KMS, Azure Key Vault)
        key_env = getattr(settings, 'ENCRYPTION_KEY', None)
        
        if key_env:
            try:
                return base64.urlsafe_b64decode(key_env.encode())
            except Exception as e:
                logger.warning(f"Invalid encryption key in environment: {e}")
        
        # Generate a new key (for development only)
        logger.warning("Generating new encryption key - this should not happen in production")
        password = settings.SECRET_KEY.encode()
        salt = os.urandom(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        return key
    
    async def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data."""
        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise SecurityError(f"Failed to encrypt data: {str(e)}")
    
    async def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise SecurityError(f"Failed to decrypt data: {str(e)}")
    
    async def detect_and_anonymize_pii(
        self,
        text: str,
        anonymize: bool = True,
        preserve_structure: bool = True
    ) -> PIIDetectionResult:
        """
        Detect and optionally anonymize PII in text.
        
        Args:
            text: Text to analyze
            anonymize: Whether to anonymize detected PII
            preserve_structure: Whether to preserve text structure when anonymizing
            
        Returns:
            PIIDetectionResult with detection and anonymization results
        """
        pii_found = False
        pii_types = []
        pii_count = 0
        anonymized_text = text
        redaction_map = {}
        
        # Check for PII patterns
        for pii_type, pattern in self.pii_patterns.items():
            matches = list(pattern.finditer(text))
            if matches:
                pii_found = True
                pii_types.append(pii_type)
                pii_count += len(matches)
                
                if anonymize:
                    for i, match in enumerate(matches):
                        original_value = match.group()
                        
                        if preserve_structure:
                            # Create structured replacement
                            if pii_type == 'ssn':
                                replacement = 'XXX-XX-XXXX'
                            elif pii_type == 'credit_card':
                                replacement = 'XXXX-XXXX-XXXX-XXXX'
                            elif pii_type == 'email':
                                replacement = '<EMAIL>'
                            elif pii_type == 'phone':
                                replacement = '(XXX) XXX-XXXX'
                            else:
                                replacement = f'[{pii_type.upper()}]'
                        else:
                            replacement = f'[{pii_type.upper()}_{i+1}]'
                        
                        redaction_map[original_value] = replacement
                        anonymized_text = anonymized_text.replace(original_value, replacement)
        
        # Check for sensitive contract data
        for sensitive_type, pattern in self.sensitive_patterns.items():
            matches = list(pattern.finditer(anonymized_text))
            if matches and anonymize:
                for i, match in enumerate(matches):
                    original_value = match.group()
                    if sensitive_type == 'salary' or sensitive_type == 'compensation':
                        replacement = '[COMPENSATION_REDACTED]'
                    elif sensitive_type == 'personal_address':
                        replacement = '[ADDRESS_REDACTED]'
                    else:
                        replacement = f'[{sensitive_type.upper()}_REDACTED]'
                    
                    redaction_map[original_value] = replacement
                    anonymized_text = anonymized_text.replace(original_value, replacement)
        
        return PIIDetectionResult(
            pii_found=pii_found,
            pii_types=pii_types,
            pii_count=pii_count,
            anonymized_text=anonymized_text,
            redaction_map=redaction_map
        )
    
    async def sanitize_for_ai_processing(self, text: str) -> Tuple[str, Dict[str, Any]]:
        """
        Sanitize text for AI processing by removing/anonymizing sensitive data.
        
        Args:
            text: Original text
            
        Returns:
            Tuple of (sanitized_text, metadata)
        """
        # Detect and anonymize PII
        pii_result = await self.detect_and_anonymize_pii(text, anonymize=True)
        
        # Additional sanitization for AI processing
        sanitized_text = pii_result.anonymized_text
        
        # Remove or replace potentially sensitive contract clauses
        sensitive_replacements = {
            r'\b(?:proprietary|confidential|trade\s+secret)\s+information\b': '[CONFIDENTIAL_INFO]',
            r'\b(?:salary|compensation|payment)\s*:?\s*\$[\d,]+(?:\.\d{2})?\b': '[COMPENSATION_AMOUNT]',
            r'\b(?:social\s+security|ssn)\s+number\b': '[SSN_REFERENCE]',
            r'\b(?:bank|account)\s+number\s*:?\s*\d+\b': '[ACCOUNT_NUMBER]'
        }
        
        for pattern, replacement in sensitive_replacements.items():
            sanitized_text = re.sub(pattern, replacement, sanitized_text, flags=re.IGNORECASE)
        
        # Create metadata about sanitization
        metadata = {
            'pii_detected': pii_result.pii_found,
            'pii_types': pii_result.pii_types,
            'pii_count': pii_result.pii_count,
            'redaction_count': len(pii_result.redaction_map),
            'original_length': len(text),
            'sanitized_length': len(sanitized_text),
            'sanitization_timestamp': datetime.utcnow().isoformat()
        }
        
        return sanitized_text, metadata
    
    async def log_audit_event(
        self,
        event_type: str,
        user_id: str,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        workspace_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> bool:
        """
        Log audit event for compliance and security monitoring.
        
        Args:
            event_type: Type of event (e.g., 'ai_processing', 'data_access')
            user_id: ID of the user performing the action
            action: Specific action taken
            resource_type: Type of resource accessed
            resource_id: ID of the specific resource
            workspace_id: Workspace context
            details: Additional event details
            ip_address: User's IP address
            user_agent: User's browser/client info
            session_id: Session identifier
            
        Returns:
            True if logged successfully
        """
        try:
            audit_entry = AuditLogEntry(
                event_type=event_type,
                user_id=user_id,
                workspace_id=workspace_id,
                resource_type=resource_type,
                resource_id=resource_id,
                action=action,
                details=details or {},
                ip_address=ip_address,
                user_agent=user_agent,
                timestamp=datetime.utcnow(),
                session_id=session_id
            )
            
            # Store in database
            supabase = get_supabase_client()
            result = supabase.table('activity_logs').insert({
                'event_type': audit_entry.event_type,
                'user_id': audit_entry.user_id,
                'workspace_id': audit_entry.workspace_id,
                'resource_type': audit_entry.resource_type,
                'resource_id': audit_entry.resource_id,
                'action': audit_entry.action,
                'details': audit_entry.details,
                'ip_address': audit_entry.ip_address,
                'user_agent': audit_entry.user_agent,
                'created_at': audit_entry.timestamp.isoformat(),
                'session_id': audit_entry.session_id
            }).execute()
            
            if result.data:
                logger.info(f"Audit event logged: {event_type} - {action} by {user_id}")
                return True
            else:
                logger.error("Failed to log audit event - no data returned")
                return False
                
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
            return False


class SecurityError(Exception):
    """Custom exception for security-related errors."""
    pass


# Global security service instance
security_service = SecurityService()
