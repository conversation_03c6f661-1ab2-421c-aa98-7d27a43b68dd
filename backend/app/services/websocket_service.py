"""
WebSocket Service for Real-time Notifications in Averum Contracts
Provides WebSocket infrastructure, connection management, and event broadcasting
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, asdict
from enum import Enum

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel
from app.db.database import get_supabase_client
from app.services.performance_service import performance_service

logger = logging.getLogger(__name__)


class NotificationPriority(str, Enum):
    """Notification priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class NotificationType(str, Enum):
    """Notification types."""
    CONTRACT_UPDATED = "contract_updated"
    CONTRACT_SIGNED = "contract_signed"
    CONTRACT_EXPIRED = "contract_expired"
    APPROVAL_REQUIRED = "approval_required"
    APPROVAL_COMPLETED = "approval_completed"
    AI_ANALYSIS_COMPLETE = "ai_analysis_complete"
    BATCH_JOB_COMPLETE = "batch_job_complete"
    DOCUMENT_UPLOADED = "document_uploaded"
    WORKSPACE_INVITATION = "workspace_invitation"
    SYSTEM_ALERT = "system_alert"
    USER_MENTION = "user_mention"
    DEADLINE_REMINDER = "deadline_reminder"


@dataclass
class NotificationEvent:
    """Notification event data structure."""
    id: str
    type: NotificationType
    priority: NotificationPriority
    title: str
    message: str
    data: Dict[str, Any]
    workspace_id: str
    user_id: Optional[str]  # None for broadcast to all workspace users
    created_at: datetime
    expires_at: Optional[datetime]
    read: bool = False


class ConnectionInfo:
    """WebSocket connection information."""
    
    def __init__(self, websocket: WebSocket, user_id: str, workspace_id: str):
        self.websocket = websocket
        self.user_id = user_id
        self.workspace_id = workspace_id
        self.connection_id = str(uuid.uuid4())
        self.connected_at = datetime.utcnow()
        self.last_ping = datetime.utcnow()
        self.subscriptions: Set[str] = set()
    
    async def send_message(self, message: Dict[str, Any]):
        """Send message to WebSocket connection."""
        try:
            await self.websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending message to connection {self.connection_id}: {e}")
            raise
    
    def update_ping(self):
        """Update last ping timestamp."""
        self.last_ping = datetime.utcnow()
    
    def is_stale(self, timeout_minutes: int = 5) -> bool:
        """Check if connection is stale."""
        return datetime.utcnow() - self.last_ping > timedelta(minutes=timeout_minutes)


class WebSocketManager:
    """Manages WebSocket connections and message broadcasting."""
    
    def __init__(self):
        self.connections: Dict[str, ConnectionInfo] = {}
        self.user_connections: Dict[str, List[str]] = {}  # user_id -> [connection_ids]
        self.workspace_connections: Dict[str, List[str]] = {}  # workspace_id -> [connection_ids]
        self.notification_queue: List[NotificationEvent] = []
        self.cleanup_task: Optional[asyncio.Task] = None
    
    async def connect(self, websocket: WebSocket, user_id: str, workspace_id: str) -> str:
        """Accept WebSocket connection and register it."""
        await websocket.accept()
        
        connection = ConnectionInfo(websocket, user_id, workspace_id)
        connection_id = connection.connection_id
        
        # Store connection
        self.connections[connection_id] = connection
        
        # Index by user
        if user_id not in self.user_connections:
            self.user_connections[user_id] = []
        self.user_connections[user_id].append(connection_id)
        
        # Index by workspace
        if workspace_id not in self.workspace_connections:
            self.workspace_connections[workspace_id] = []
        self.workspace_connections[workspace_id].append(connection_id)
        
        logger.info(f"WebSocket connected: {connection_id} for user {user_id} in workspace {workspace_id}")
        
        # Send connection confirmation
        await connection.send_message({
            "type": "connection_established",
            "connection_id": connection_id,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # Send any pending notifications
        await self._send_pending_notifications(connection)
        
        # Start cleanup task if not running
        if not self.cleanup_task:
            self.cleanup_task = asyncio.create_task(self._cleanup_stale_connections())
        
        return connection_id
    
    async def disconnect(self, connection_id: str):
        """Disconnect and clean up WebSocket connection."""
        if connection_id not in self.connections:
            return
        
        connection = self.connections[connection_id]
        user_id = connection.user_id
        workspace_id = connection.workspace_id
        
        # Remove from indexes
        if user_id in self.user_connections:
            self.user_connections[user_id] = [
                cid for cid in self.user_connections[user_id] if cid != connection_id
            ]
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        if workspace_id in self.workspace_connections:
            self.workspace_connections[workspace_id] = [
                cid for cid in self.workspace_connections[workspace_id] if cid != connection_id
            ]
            if not self.workspace_connections[workspace_id]:
                del self.workspace_connections[workspace_id]
        
        # Remove connection
        del self.connections[connection_id]
        
        logger.info(f"WebSocket disconnected: {connection_id}")
    
    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """Send message to all connections for a specific user."""
        if user_id not in self.user_connections:
            return
        
        connection_ids = self.user_connections[user_id].copy()
        disconnected_connections = []
        
        for connection_id in connection_ids:
            if connection_id in self.connections:
                try:
                    await self.connections[connection_id].send_message(message)
                except Exception as e:
                    logger.error(f"Failed to send message to user {user_id}, connection {connection_id}: {e}")
                    disconnected_connections.append(connection_id)
        
        # Clean up failed connections
        for connection_id in disconnected_connections:
            await self.disconnect(connection_id)
    
    async def send_to_workspace(self, workspace_id: str, message: Dict[str, Any], exclude_user: Optional[str] = None):
        """Send message to all connections in a workspace."""
        if workspace_id not in self.workspace_connections:
            return
        
        connection_ids = self.workspace_connections[workspace_id].copy()
        disconnected_connections = []
        
        for connection_id in connection_ids:
            if connection_id in self.connections:
                connection = self.connections[connection_id]
                
                # Skip excluded user
                if exclude_user and connection.user_id == exclude_user:
                    continue
                
                try:
                    await connection.send_message(message)
                except Exception as e:
                    logger.error(f"Failed to send message to workspace {workspace_id}, connection {connection_id}: {e}")
                    disconnected_connections.append(connection_id)
        
        # Clean up failed connections
        for connection_id in disconnected_connections:
            await self.disconnect(connection_id)
    
    async def broadcast_notification(self, notification: NotificationEvent):
        """Broadcast notification to appropriate recipients."""
        message = {
            "type": "notification",
            "notification": {
                "id": notification.id,
                "type": notification.type.value,
                "priority": notification.priority.value,
                "title": notification.title,
                "message": notification.message,
                "data": notification.data,
                "created_at": notification.created_at.isoformat(),
                "expires_at": notification.expires_at.isoformat() if notification.expires_at else None
            }
        }
        
        if notification.user_id:
            # Send to specific user
            await self.send_to_user(notification.user_id, message)
        else:
            # Broadcast to workspace
            await self.send_to_workspace(notification.workspace_id, message)
    
    async def handle_ping(self, connection_id: str):
        """Handle ping from client."""
        if connection_id in self.connections:
            self.connections[connection_id].update_ping()
            await self.connections[connection_id].send_message({
                "type": "pong",
                "timestamp": datetime.utcnow().isoformat()
            })
    
    async def _send_pending_notifications(self, connection: ConnectionInfo):
        """Send pending notifications to newly connected user."""
        # Get recent notifications for user from database
        supabase = get_supabase_client()
        
        try:
            # Get unread notifications for user
            result = supabase.table("notifications").select("*").eq("user_id", connection.user_id).eq("read", False).order("created_at", desc=True).limit(10).execute()
            
            for notification_data in result.data or []:
                message = {
                    "type": "notification",
                    "notification": notification_data
                }
                await connection.send_message(message)
                
        except Exception as e:
            logger.error(f"Error sending pending notifications: {e}")
    
    async def _cleanup_stale_connections(self):
        """Periodically clean up stale connections."""
        while True:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                stale_connections = []
                for connection_id, connection in self.connections.items():
                    if connection.is_stale():
                        stale_connections.append(connection_id)
                
                for connection_id in stale_connections:
                    logger.info(f"Cleaning up stale connection: {connection_id}")
                    await self.disconnect(connection_id)
                
            except Exception as e:
                logger.error(f"Error in connection cleanup: {e}")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            "total_connections": len(self.connections),
            "users_connected": len(self.user_connections),
            "workspaces_active": len(self.workspace_connections),
            "pending_notifications": len(self.notification_queue)
        }


class NotificationService:
    """Service for managing notifications and real-time events."""
    
    def __init__(self):
        self.websocket_manager = WebSocketManager()
        self.supabase = get_supabase_client()
    
    @performance_service.track_request("notification_create")
    async def create_notification(
        self,
        notification_type: NotificationType,
        title: str,
        message: str,
        workspace_id: str,
        user_id: Optional[str] = None,
        priority: NotificationPriority = NotificationPriority.MEDIUM,
        data: Optional[Dict[str, Any]] = None,
        expires_in_hours: Optional[int] = None
    ) -> str:
        """Create and broadcast a notification."""
        try:
            notification_id = str(uuid.uuid4())
            created_at = datetime.utcnow()
            expires_at = created_at + timedelta(hours=expires_in_hours) if expires_in_hours else None
            
            # Create notification event
            notification = NotificationEvent(
                id=notification_id,
                type=notification_type,
                priority=priority,
                title=title,
                message=message,
                data=data or {},
                workspace_id=workspace_id,
                user_id=user_id,
                created_at=created_at,
                expires_at=expires_at
            )
            
            # Store in database
            notification_data = {
                "id": notification_id,
                "type": notification_type.value,
                "priority": priority.value,
                "title": title,
                "message": message,
                "data": data or {},
                "workspace_id": workspace_id,
                "user_id": user_id,
                "created_at": created_at.isoformat(),
                "expires_at": expires_at.isoformat() if expires_at else None,
                "read": False
            }
            
            self.supabase.table("notifications").insert(notification_data).execute()
            
            # Broadcast via WebSocket
            await self.websocket_manager.broadcast_notification(notification)
            
            logger.info(f"Notification created and broadcast: {notification_id}")
            return notification_id
            
        except Exception as e:
            logger.error(f"Error creating notification: {e}")
            raise


# Global instances
websocket_manager = WebSocketManager()
notification_service = NotificationService()
