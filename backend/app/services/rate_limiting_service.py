"""
Enhanced Rate Limiting Service for Averum Contracts
Implements Redis-based distributed rate limiting with endpoint-specific limits, burst handling, and monitoring
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum

import redis.asyncio as redis
from fastapi import Request, HTTPException
from app.core.config import settings
from app.services.performance_service import performance_service

logger = logging.getLogger(__name__)


class RateLimitType(str, Enum):
    """Rate limit types."""
    PER_SECOND = "per_second"
    PER_MINUTE = "per_minute"
    PER_HOUR = "per_hour"
    PER_DAY = "per_day"


class RateLimitScope(str, Enum):
    """Rate limit scopes."""
    GLOBAL = "global"
    USER = "user"
    IP = "ip"
    WORKSPACE = "workspace"
    ENDPOINT = "endpoint"


@dataclass
class RateLimitRule:
    """Rate limit rule configuration."""
    name: str
    limit: int
    window: int  # seconds
    scope: RateLimitScope
    burst_limit: Optional[int] = None
    burst_window: Optional[int] = None
    enabled: bool = True


@dataclass
class RateLimitResult:
    """Rate limit check result."""
    allowed: bool
    limit: int
    remaining: int
    reset_time: int
    retry_after: Optional[int] = None
    burst_used: Optional[int] = None


class RateLimitingService:
    """Enhanced rate limiting service with Redis backend."""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.rules: Dict[str, RateLimitRule] = {}
        self.default_rules = self._get_default_rules()
        self.monitoring_stats = {}
        
    async def initialize(self):
        """Initialize Redis connection and load rules."""
        try:
            # Initialize Redis connection
            redis_url = getattr(settings, 'REDIS_URL', 'redis://localhost:6379')
            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            
            # Test connection
            await self.redis_client.ping()
            logger.info("Rate limiting service initialized with Redis")
            
            # Load default rules
            self.rules.update(self.default_rules)
            
        except Exception as e:
            logger.error(f"Failed to initialize rate limiting service: {e}")
            # Fallback to in-memory rate limiting
            self.redis_client = None
    
    def _get_default_rules(self) -> Dict[str, RateLimitRule]:
        """Get default rate limiting rules."""
        return {
            # Global API limits
            "global_per_second": RateLimitRule(
                name="Global Per Second",
                limit=100,
                window=1,
                scope=RateLimitScope.GLOBAL
            ),
            "global_per_minute": RateLimitRule(
                name="Global Per Minute",
                limit=1000,
                window=60,
                scope=RateLimitScope.GLOBAL
            ),
            
            # User-specific limits
            "user_per_minute": RateLimitRule(
                name="User Per Minute",
                limit=60,
                window=60,
                scope=RateLimitScope.USER,
                burst_limit=10,
                burst_window=5
            ),
            "user_per_hour": RateLimitRule(
                name="User Per Hour",
                limit=1000,
                window=3600,
                scope=RateLimitScope.USER
            ),
            
            # IP-based limits
            "ip_per_minute": RateLimitRule(
                name="IP Per Minute",
                limit=100,
                window=60,
                scope=RateLimitScope.IP,
                burst_limit=20,
                burst_window=10
            ),
            
            # AI endpoint specific limits
            "ai_analysis_per_minute": RateLimitRule(
                name="AI Analysis Per Minute",
                limit=10,
                window=60,
                scope=RateLimitScope.USER
            ),
            "ai_batch_per_hour": RateLimitRule(
                name="AI Batch Per Hour",
                limit=5,
                window=3600,
                scope=RateLimitScope.USER
            ),
            
            # Upload limits
            "upload_per_minute": RateLimitRule(
                name="Upload Per Minute",
                limit=20,
                window=60,
                scope=RateLimitScope.USER
            ),
            
            # Authentication limits
            "auth_per_minute": RateLimitRule(
                name="Auth Per Minute",
                limit=10,
                window=60,
                scope=RateLimitScope.IP,
                burst_limit=3,
                burst_window=5
            )
        }
    
    @performance_service.track_request("rate_limit_check")
    async def check_rate_limit(
        self,
        request: Request,
        rule_name: str,
        identifier: Optional[str] = None
    ) -> RateLimitResult:
        """
        Check if request is within rate limits.
        
        Args:
            request: FastAPI request object
            rule_name: Name of the rate limit rule to apply
            identifier: Custom identifier (overrides auto-detection)
            
        Returns:
            Rate limit check result
        """
        try:
            rule = self.rules.get(rule_name)
            if not rule or not rule.enabled:
                # No rule or disabled - allow request
                return RateLimitResult(
                    allowed=True,
                    limit=0,
                    remaining=0,
                    reset_time=0
                )
            
            # Get identifier based on scope
            key_identifier = identifier or self._get_identifier(request, rule.scope)
            
            # Create Redis key
            redis_key = f"rate_limit:{rule_name}:{key_identifier}"
            
            # Check rate limit
            if self.redis_client:
                result = await self._check_redis_rate_limit(redis_key, rule)
            else:
                result = await self._check_memory_rate_limit(redis_key, rule)
            
            # Update monitoring stats
            await self._update_monitoring_stats(rule_name, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            # On error, allow request but log the issue
            return RateLimitResult(
                allowed=True,
                limit=0,
                remaining=0,
                reset_time=0
            )
    
    async def _check_redis_rate_limit(
        self,
        redis_key: str,
        rule: RateLimitRule
    ) -> RateLimitResult:
        """Check rate limit using Redis sliding window."""
        try:
            current_time = int(time.time())
            window_start = current_time - rule.window
            
            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            
            # Remove expired entries
            pipe.zremrangebyscore(redis_key, 0, window_start)
            
            # Count current requests in window
            pipe.zcard(redis_key)
            
            # Add current request
            pipe.zadd(redis_key, {str(current_time): current_time})
            
            # Set expiration
            pipe.expire(redis_key, rule.window + 60)  # Extra buffer
            
            # Execute pipeline
            results = await pipe.execute()
            current_count = results[1] + 1  # +1 for the request we just added
            
            # Check if within limits
            allowed = current_count <= rule.limit
            remaining = max(0, rule.limit - current_count)
            reset_time = current_time + rule.window
            
            # Handle burst limits if configured
            burst_used = None
            if rule.burst_limit and rule.burst_window:
                burst_result = await self._check_burst_limit(redis_key, rule, current_time)
                if not burst_result.allowed:
                    allowed = False
                burst_used = burst_result.burst_used
            
            return RateLimitResult(
                allowed=allowed,
                limit=rule.limit,
                remaining=remaining,
                reset_time=reset_time,
                retry_after=rule.window if not allowed else None,
                burst_used=burst_used
            )
            
        except Exception as e:
            logger.error(f"Redis rate limit check failed: {e}")
            # Fallback to allowing request
            return RateLimitResult(
                allowed=True,
                limit=rule.limit,
                remaining=rule.limit,
                reset_time=int(time.time()) + rule.window
            )
    
    async def _check_burst_limit(
        self,
        redis_key: str,
        rule: RateLimitRule,
        current_time: int
    ) -> RateLimitResult:
        """Check burst rate limits."""
        burst_key = f"{redis_key}:burst"
        burst_window_start = current_time - rule.burst_window
        
        pipe = self.redis_client.pipeline()
        pipe.zremrangebyscore(burst_key, 0, burst_window_start)
        pipe.zcard(burst_key)
        pipe.zadd(burst_key, {str(current_time): current_time})
        pipe.expire(burst_key, rule.burst_window + 60)
        
        results = await pipe.execute()
        burst_count = results[1] + 1
        
        allowed = burst_count <= rule.burst_limit
        
        return RateLimitResult(
            allowed=allowed,
            limit=rule.burst_limit,
            remaining=max(0, rule.burst_limit - burst_count),
            reset_time=current_time + rule.burst_window,
            burst_used=burst_count
        )
    
    async def _check_memory_rate_limit(
        self,
        key: str,
        rule: RateLimitRule
    ) -> RateLimitResult:
        """Fallback in-memory rate limiting."""
        # Simplified in-memory implementation
        # In production, this would use a more sophisticated approach
        current_time = int(time.time())
        
        if not hasattr(self, '_memory_store'):
            self._memory_store = {}
        
        if key not in self._memory_store:
            self._memory_store[key] = []
        
        # Clean old entries
        window_start = current_time - rule.window
        self._memory_store[key] = [
            timestamp for timestamp in self._memory_store[key]
            if timestamp > window_start
        ]
        
        # Add current request
        self._memory_store[key].append(current_time)
        
        current_count = len(self._memory_store[key])
        allowed = current_count <= rule.limit
        remaining = max(0, rule.limit - current_count)
        
        return RateLimitResult(
            allowed=allowed,
            limit=rule.limit,
            remaining=remaining,
            reset_time=current_time + rule.window,
            retry_after=rule.window if not allowed else None
        )
    
    def _get_identifier(self, request: Request, scope: RateLimitScope) -> str:
        """Get identifier based on rate limit scope."""
        if scope == RateLimitScope.GLOBAL:
            return "global"
        elif scope == RateLimitScope.IP:
            return request.client.host if request.client else "unknown"
        elif scope == RateLimitScope.USER:
            # Extract user ID from request (would be set by auth middleware)
            return getattr(request.state, 'user_id', 'anonymous')
        elif scope == RateLimitScope.WORKSPACE:
            # Extract workspace ID from request
            return getattr(request.state, 'workspace_id', 'default')
        elif scope == RateLimitScope.ENDPOINT:
            return f"{request.method}:{request.url.path}"
        else:
            return "default"
    
    async def _update_monitoring_stats(self, rule_name: str, result: RateLimitResult):
        """Update monitoring statistics."""
        if rule_name not in self.monitoring_stats:
            self.monitoring_stats[rule_name] = {
                "total_requests": 0,
                "blocked_requests": 0,
                "last_reset": time.time()
            }
        
        stats = self.monitoring_stats[rule_name]
        stats["total_requests"] += 1
        
        if not result.allowed:
            stats["blocked_requests"] += 1
    
    async def get_monitoring_stats(self) -> Dict[str, Any]:
        """Get rate limiting monitoring statistics."""
        return {
            "rules": {
                name: {
                    "limit": rule.limit,
                    "window": rule.window,
                    "scope": rule.scope.value,
                    "enabled": rule.enabled
                }
                for name, rule in self.rules.items()
            },
            "stats": self.monitoring_stats,
            "redis_connected": self.redis_client is not None
        }
    
    async def update_rule(self, rule_name: str, rule: RateLimitRule):
        """Update or add a rate limiting rule."""
        self.rules[rule_name] = rule
        logger.info(f"Updated rate limiting rule: {rule_name}")
    
    async def disable_rule(self, rule_name: str):
        """Disable a rate limiting rule."""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = False
            logger.info(f"Disabled rate limiting rule: {rule_name}")
    
    async def enable_rule(self, rule_name: str):
        """Enable a rate limiting rule."""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = True
            logger.info(f"Enabled rate limiting rule: {rule_name}")
    
    async def clear_rate_limit(self, rule_name: str, identifier: str):
        """Clear rate limit for a specific identifier."""
        if self.redis_client:
            redis_key = f"rate_limit:{rule_name}:{identifier}"
            await self.redis_client.delete(redis_key)
            logger.info(f"Cleared rate limit for {rule_name}:{identifier}")


# Global rate limiting service instance
rate_limiting_service = RateLimitingService()
