"""
Notification service for managing notifications in the Averum Contracts application.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
import logging
import asyncio
from supabase import Client

from app.schemas.notification import (
    Notification,
    NotificationCreate,
    NotificationUpdate,
    NotificationFilters,
    NotificationSummary,
    NotificationTemplate,
    NotificationBroadcast,
    NotificationType,
    NotificationStatus
)
from app.db.database import get_supabase_client

logger = logging.getLogger(__name__)

class NotificationService:
    """Service for managing notifications."""
    
    def __init__(self, supabase_client: Optional[Client] = None):
        self.supabase = supabase_client or get_supabase_client()
        
        # Notification templates
        self.templates = {
            NotificationType.APPROVAL: NotificationTemplate(
                type=NotificationType.APPROVAL,
                title_template="Contract Approval Required",
                message_template="{contract_name} requires your approval",
                action_url_template="/app/contracts/{contract_id}",
                variables=["contract_name", "contract_id"]
            ),
            NotificationType.CONTRACT: NotificationTemplate(
                type=NotificationType.CONTRACT,
                title_template="Contract Updated",
                message_template="{sender_name} updated {contract_name}",
                action_url_template="/app/contracts/{contract_id}",
                variables=["sender_name", "contract_name", "contract_id"]
            ),
            NotificationType.MENTION: NotificationTemplate(
                type=NotificationType.MENTION,
                title_template="You were mentioned",
                message_template="{sender_name} mentioned you in a comment on {entity_name}",
                action_url_template="/app/{entity_type}/{entity_id}",
                variables=["sender_name", "entity_name", "entity_type", "entity_id"]
            ),
            NotificationType.SYSTEM: NotificationTemplate(
                type=NotificationType.SYSTEM,
                title_template="System Notification",
                message_template="{message}",
                action_url_template=None,
                variables=["message"]
            )
        }
    
    async def create_notification(
        self,
        notification_data: NotificationCreate,
        template_variables: Optional[Dict[str, Any]] = None
    ) -> Notification:
        """Create a new notification."""
        try:
            # Generate notification ID
            notification_id = str(uuid.uuid4())
            
            # Apply template if variables provided
            if template_variables and notification_data.type in self.templates:
                template = self.templates[notification_data.type]
                notification_data = self._apply_template(notification_data, template, template_variables)
            
            # Prepare notification data for database
            db_data = {
                "id": notification_id,
                "title": notification_data.title,
                "message": notification_data.message,
                "type": notification_data.type.value,
                "user_id": notification_data.user_id,
                "workspace_id": notification_data.workspace_id,
                "sender_id": notification_data.sender_id,
                "entity_id": notification_data.entity_id,
                "entity_type": notification_data.entity_type,
                "action_url": notification_data.action_url,
                "status": NotificationStatus.UNREAD.value,
                "created_at": datetime.utcnow().isoformat()
            }
            
            # Insert into database
            response = self.supabase.table("notifications").insert(db_data).execute()
            
            if hasattr(response, 'error') and response.error:
                logger.error(f"Failed to create notification: {response.error}")
                raise Exception(f"Failed to create notification: {response.error}")
            
            # Fetch the created notification with sender info
            notification = await self.get_notification_by_id(notification_id)

            # Broadcast notification via WebSocket (if available)
            try:
                from app.api.websocket.notifications import send_notification_to_user
                await send_notification_to_user(
                    user_id=notification.user_id,
                    workspace_id=notification.workspace_id,
                    notification_data=notification.dict()
                )
            except ImportError:
                # WebSocket module not available
                pass
            except Exception as ws_error:
                logger.warning(f"Failed to broadcast notification via WebSocket: {ws_error}")

            return notification

        except Exception as e:
            logger.error(f"Error creating notification: {str(e)}")
            raise
    
    async def get_notifications(
        self,
        user_id: str,
        filters: NotificationFilters
    ) -> List[Notification]:
        """Get notifications for a user with filtering."""
        try:
            query = self.supabase.table("notifications").select("""
                *,
                sender:sender_id(id, first_name, last_name, initials, avatar)
            """).eq("user_id", user_id).eq("workspace_id", filters.workspace_id)
            
            # Apply filters
            if filters.status:
                query = query.eq("status", filters.status.value)
            if filters.type:
                query = query.eq("type", filters.type.value)
            if filters.sender_id:
                query = query.eq("sender_id", filters.sender_id)
            if filters.entity_type:
                query = query.eq("entity_type", filters.entity_type)
            if filters.start_date:
                query = query.gte("created_at", filters.start_date.isoformat())
            if filters.end_date:
                query = query.lte("created_at", filters.end_date.isoformat())
            
            # Apply pagination and ordering
            query = query.order("created_at", desc=True).range(filters.skip, filters.skip + filters.limit - 1)
            
            response = query.execute()
            
            if hasattr(response, 'error') and response.error:
                logger.error(f"Failed to get notifications: {response.error}")
                raise Exception(f"Failed to get notifications: {response.error}")
            
            notifications = []
            for item in response.data:
                # Format sender data
                sender_data = None
                if item.get('sender'):
                    sender_info = item['sender']
                    sender_data = {
                        "id": sender_info.get('id'),
                        "name": f"{sender_info.get('first_name', '')} {sender_info.get('last_name', '')}".strip(),
                        "initials": sender_info.get('initials', ''),
                        "avatar": sender_info.get('avatar')
                    }
                
                notification = Notification(
                    id=item['id'],
                    title=item['title'],
                    message=item['message'],
                    type=item['type'],
                    status=item['status'],
                    user_id=item['user_id'],
                    workspace_id=item['workspace_id'],
                    sender_id=item.get('sender_id'),
                    entity_id=item.get('entity_id'),
                    entity_type=item.get('entity_type'),
                    action_url=item.get('action_url'),
                    created_at=datetime.fromisoformat(item['created_at'].replace('Z', '+00:00')),
                    read_at=datetime.fromisoformat(item['read_at'].replace('Z', '+00:00')) if item.get('read_at') else None,
                    sender=sender_data
                )
                notifications.append(notification)
            
            return notifications
            
        except Exception as e:
            logger.error(f"Error getting notifications: {str(e)}")
            raise
    
    async def get_notification_by_id(self, notification_id: str) -> Notification:
        """Get a specific notification by ID."""
        try:
            response = self.supabase.table("notifications").select("""
                *,
                sender:sender_id(id, first_name, last_name, initials, avatar)
            """).eq("id", notification_id).execute()
            
            if hasattr(response, 'error') and response.error:
                logger.error(f"Failed to get notification: {response.error}")
                raise Exception(f"Failed to get notification: {response.error}")
            
            if not response.data:
                raise Exception("Notification not found")
            
            item = response.data[0]
            
            # Format sender data
            sender_data = None
            if item.get('sender'):
                sender_info = item['sender']
                sender_data = {
                    "id": sender_info.get('id'),
                    "name": f"{sender_info.get('first_name', '')} {sender_info.get('last_name', '')}".strip(),
                    "initials": sender_info.get('initials', ''),
                    "avatar": sender_info.get('avatar')
                }
            
            return Notification(
                id=item['id'],
                title=item['title'],
                message=item['message'],
                type=item['type'],
                status=item['status'],
                user_id=item['user_id'],
                workspace_id=item['workspace_id'],
                sender_id=item.get('sender_id'),
                entity_id=item.get('entity_id'),
                entity_type=item.get('entity_type'),
                action_url=item.get('action_url'),
                created_at=datetime.fromisoformat(item['created_at'].replace('Z', '+00:00')),
                read_at=datetime.fromisoformat(item['read_at'].replace('Z', '+00:00')) if item.get('read_at') else None,
                sender=sender_data
            )
            
        except Exception as e:
            logger.error(f"Error getting notification by ID: {str(e)}")
            raise
    
    def _apply_template(
        self,
        notification_data: NotificationCreate,
        template: NotificationTemplate,
        variables: Dict[str, Any]
    ) -> NotificationCreate:
        """Apply template to notification data."""
        # Apply title template
        title = template.title_template
        for var, value in variables.items():
            title = title.replace(f"{{{var}}}", str(value))
        
        # Apply message template
        message = template.message_template
        for var, value in variables.items():
            message = message.replace(f"{{{var}}}", str(value))
        
        # Apply action URL template if provided
        action_url = notification_data.action_url
        if template.action_url_template:
            action_url = template.action_url_template
            for var, value in variables.items():
                action_url = action_url.replace(f"{{{var}}}", str(value))
        
        # Create updated notification data
        return NotificationCreate(
            title=title,
            message=message,
            type=notification_data.type,
            workspace_id=notification_data.workspace_id,
            user_id=notification_data.user_id,
            sender_id=notification_data.sender_id,
            entity_id=notification_data.entity_id,
            entity_type=notification_data.entity_type,
            action_url=action_url
        )

    async def update_notification(
        self,
        notification_id: str,
        user_id: str,
        update_data: NotificationUpdate
    ) -> Notification:
        """Update a notification."""
        try:
            db_data = {}

            if update_data.status:
                db_data["status"] = update_data.status.value
                if update_data.status == NotificationStatus.READ and not update_data.read_at:
                    db_data["read_at"] = datetime.utcnow().isoformat()

            if update_data.read_at:
                db_data["read_at"] = update_data.read_at.isoformat()

            response = self.supabase.table("notifications").update(db_data).eq("id", notification_id).eq("user_id", user_id).execute()

            if hasattr(response, 'error') and response.error:
                logger.error(f"Failed to update notification: {response.error}")
                raise Exception(f"Failed to update notification: {response.error}")

            return await self.get_notification_by_id(notification_id)

        except Exception as e:
            logger.error(f"Error updating notification: {str(e)}")
            raise

    async def mark_as_read(self, notification_id: str, user_id: str) -> Notification:
        """Mark a notification as read."""
        update_data = NotificationUpdate(
            status=NotificationStatus.READ,
            read_at=datetime.utcnow()
        )
        return await self.update_notification(notification_id, user_id, update_data)

    async def mark_all_as_read(self, user_id: str, workspace_id: str) -> int:
        """Mark all notifications as read for a user in a workspace."""
        try:
            update_data = {
                "status": NotificationStatus.READ.value,
                "read_at": datetime.utcnow().isoformat()
            }

            response = self.supabase.table("notifications").update(update_data).eq("user_id", user_id).eq("workspace_id", workspace_id).eq("status", NotificationStatus.UNREAD.value).execute()

            if hasattr(response, 'error') and response.error:
                logger.error(f"Failed to mark all notifications as read: {response.error}")
                raise Exception(f"Failed to mark all notifications as read: {response.error}")

            return len(response.data) if response.data else 0

        except Exception as e:
            logger.error(f"Error marking all notifications as read: {str(e)}")
            raise

    async def delete_notification(self, notification_id: str, user_id: str) -> bool:
        """Delete a notification."""
        try:
            response = self.supabase.table("notifications").delete().eq("id", notification_id).eq("user_id", user_id).execute()

            if hasattr(response, 'error') and response.error:
                logger.error(f"Failed to delete notification: {response.error}")
                raise Exception(f"Failed to delete notification: {response.error}")

            return len(response.data) > 0 if response.data else False

        except Exception as e:
            logger.error(f"Error deleting notification: {str(e)}")
            raise

    async def delete_all_notifications(self, user_id: str, workspace_id: str) -> int:
        """Delete all notifications for a user in a workspace."""
        try:
            response = self.supabase.table("notifications").delete().eq("user_id", user_id).eq("workspace_id", workspace_id).execute()

            if hasattr(response, 'error') and response.error:
                logger.error(f"Failed to delete all notifications: {response.error}")
                raise Exception(f"Failed to delete all notifications: {response.error}")

            return len(response.data) if response.data else 0

        except Exception as e:
            logger.error(f"Error deleting all notifications: {str(e)}")
            raise

    async def get_notification_summary(self, user_id: str, workspace_id: str) -> NotificationSummary:
        """Get notification summary for a user in a workspace."""
        try:
            # Get total count
            total_response = self.supabase.table("notifications").select("id", count="exact").eq("user_id", user_id).eq("workspace_id", workspace_id).execute()

            # Get unread count
            unread_response = self.supabase.table("notifications").select("id", count="exact").eq("user_id", user_id).eq("workspace_id", workspace_id).eq("status", NotificationStatus.UNREAD.value).execute()

            # Get type counts
            type_response = self.supabase.table("notifications").select("type").eq("user_id", user_id).eq("workspace_id", workspace_id).execute()

            total_count = total_response.count if hasattr(total_response, 'count') else 0
            unread_count = unread_response.count if hasattr(unread_response, 'count') else 0

            # Calculate type counts
            type_counts = {}
            if type_response.data:
                for item in type_response.data:
                    notification_type = item['type']
                    type_counts[notification_type] = type_counts.get(notification_type, 0) + 1

            return NotificationSummary(
                total_count=total_count,
                unread_count=unread_count,
                type_counts=type_counts
            )

        except Exception as e:
            logger.error(f"Error getting notification summary: {str(e)}")
            raise

    async def broadcast_notification(self, broadcast_data: NotificationBroadcast) -> List[Notification]:
        """Broadcast a notification to multiple users."""
        try:
            created_notifications = []

            for user_id in broadcast_data.user_ids:
                # Create notification for each user
                notification_create = NotificationCreate(
                    user_id=user_id,
                    **broadcast_data.notification_data.dict()
                )

                notification = await self.create_notification(notification_create)
                created_notifications.append(notification)

            return created_notifications

        except Exception as e:
            logger.error(f"Error broadcasting notification: {str(e)}")
            raise

    async def create_system_notification(
        self,
        workspace_id: str,
        title: str,
        message: str,
        user_ids: Optional[List[str]] = None,
        entity_id: Optional[str] = None,
        entity_type: Optional[str] = None,
        action_url: Optional[str] = None
    ) -> List[Notification]:
        """Create a system notification for specified users or all workspace members."""
        try:
            # If no user_ids specified, get all workspace members
            if not user_ids:
                response = self.supabase.table("workspace_members").select("user_id").eq("workspace_id", workspace_id).execute()

                if hasattr(response, 'error') and response.error:
                    logger.error(f"Failed to get workspace members: {response.error}")
                    raise Exception(f"Failed to get workspace members: {response.error}")

                user_ids = [member["user_id"] for member in response.data]

            # Create broadcast data
            broadcast_data = NotificationBroadcast(
                user_ids=user_ids,
                notification_data=NotificationCreate(
                    title=title,
                    message=message,
                    type=NotificationType.SYSTEM,
                    workspace_id=workspace_id,
                    user_id="",  # Will be set for each user
                    entity_id=entity_id,
                    entity_type=entity_type,
                    action_url=action_url
                )
            )

            return await self.broadcast_notification(broadcast_data)

        except Exception as e:
            logger.error(f"Error creating system notification: {str(e)}")
            raise
