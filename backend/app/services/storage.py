"""
Storage service for handling file operations with Supabase Storage.
"""
from typing import BinaryIO, Optional, Dict, Any
from fastapi import UploadFile
import uuid
from app.db.database import get_supabase_client
import os
from datetime import datetime, timedelta

# Constants
DOCUMENTS_BUCKET = "documents"

class StorageService:
    """Service for handling file storage operations with Supabase Storage."""

    @staticmethod
    async def initialize_storage():
        """
        Initialize the storage buckets if they don't exist.
        This should be called during application startup.
        """
        supabase = get_supabase_client()

        try:
            # Check if documents bucket exists
            buckets_response = supabase.storage.list_buckets()

            # Handle different response formats
            if hasattr(buckets_response, 'data'):
                buckets = buckets_response.data or []
            else:
                buckets = buckets_response or []

            # Extract bucket names safely
            bucket_names = []
            for bucket in buckets:
                if isinstance(bucket, dict) and "name" in bucket:
                    bucket_names.append(bucket["name"])
                elif hasattr(bucket, 'name'):
                    bucket_names.append(bucket.name)

            # Create documents bucket if it doesn't exist
            if DOCUMENTS_BUCKET not in bucket_names:
                try:
                    supabase.storage.create_bucket(
                        id=DOCUMENTS_BUCKET,
                        options={"public": False}  # Not publicly accessible by default
                    )
                    print(f"Created storage bucket: {DOCUMENTS_BUCKET}")
                except Exception as create_error:
                    print(f"Error creating bucket (may already exist): {create_error}")

            print(f"Storage initialization completed. Available buckets: {bucket_names}")
            return True
        except Exception as e:
            print(f"Error initializing storage: {e}")
            # Don't fail the application startup if storage initialization fails
            return True

    @staticmethod
    async def upload_file(
        file: UploadFile,
        folder: str = "",
        workspace_id: str = "",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Upload a file to the storage bucket.

        Args:
            file: The file to upload
            folder: Optional folder path within the bucket
            workspace_id: Optional workspace ID to organize files
            metadata: Optional metadata to associate with the file

        Returns:
            Dict containing file information including the URL
        """
        supabase = get_supabase_client()

        # Generate a unique file path
        file_ext = os.path.splitext(file.filename)[1].lower()
        unique_filename = f"{uuid.uuid4()}{file_ext}"

        # Create path with workspace and folder if provided
        path_parts = []
        if workspace_id:
            path_parts.append(workspace_id)
        if folder:
            path_parts.append(folder)
        path_parts.append(unique_filename)

        file_path = "/".join(path_parts)

        # Read file content
        file_content = await file.read()

        # Upload file to Supabase Storage
        response = supabase.storage.from_(DOCUMENTS_BUCKET).upload(
            file_path,
            file_content,
            {"content-type": file.content_type}
        )

        # Reset file cursor for potential future reads
        await file.seek(0)

        # Get public URL
        file_url = supabase.storage.from_(DOCUMENTS_BUCKET).get_public_url(file_path)

        # Store metadata if provided
        if metadata:
            # In a real implementation, you might store this in a separate table
            # or use Supabase Storage metadata features
            pass

        return {
            "id": unique_filename,
            "filename": file.filename,
            "content_type": file.content_type,
            "size": len(file_content),
            "path": file_path,
            "url": file_url,
            "uploaded_at": datetime.utcnow().isoformat()
        }

    @staticmethod
    def get_file_url(file_path: str, expires_in: int = 3600) -> str:
        """
        Get a signed URL for a file that expires after a certain time.

        Args:
            file_path: The path to the file in the bucket
            expires_in: Number of seconds until the URL expires

        Returns:
            Signed URL for the file
        """
        supabase = get_supabase_client()

        # Calculate expiry time
        expires_at = datetime.utcnow() + timedelta(seconds=expires_in)

        # Get signed URL
        signed_url = supabase.storage.from_(DOCUMENTS_BUCKET).create_signed_url(
            file_path,
            expires_in
        )

        return signed_url

    @staticmethod
    def delete_file(file_path: str) -> bool:
        """
        Delete a file from the storage bucket.

        Args:
            file_path: The path to the file in the bucket

        Returns:
            True if deletion was successful, False otherwise
        """
        supabase = get_supabase_client()

        try:
            supabase.storage.from_(DOCUMENTS_BUCKET).remove(file_path)
            return True
        except Exception as e:
            print(f"Error deleting file: {e}")
            return False

    @staticmethod
    def list_files(folder: str = "", workspace_id: str = "") -> list:
        """
        List files in a folder.

        Args:
            folder: The folder to list files from
            workspace_id: Optional workspace ID to filter files

        Returns:
            List of files in the folder
        """
        supabase = get_supabase_client()

        # Create path with workspace and folder if provided
        path_parts = []
        if workspace_id:
            path_parts.append(workspace_id)
        if folder:
            path_parts.append(folder)

        path = "/".join(path_parts)

        try:
            files = supabase.storage.from_(DOCUMENTS_BUCKET).list(path)
            return files
        except Exception as e:
            print(f"Error listing files: {e}")
            return []
