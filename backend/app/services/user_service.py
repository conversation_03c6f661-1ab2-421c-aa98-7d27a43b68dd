"""
User service for managing both demo users and real Clerk users
Provides a unified interface for user operations
"""

from typing import List, Dict, Any, Optional
from app.db.database import get_supabase_client
from app.core.config import settings
import requests

def handle_supabase_response(response, error_message: str = "Database operation failed"):
    """Helper function to handle Supabase response objects consistently."""
    has_error = hasattr(response, 'error') and response.error
    has_data = hasattr(response, 'data') and response.data is not None

    if has_error:
        raise Exception(f"{error_message}: {response.error}")

    return response.data if has_data else []

class UserService:
    def __init__(self):
        self.supabase = get_supabase_client()
    
    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get a user by their ID."""
        response = self.supabase.table("users").select("*").eq("id", user_id).execute()
        users = handle_supabase_response(response, "Failed to retrieve user")
        return users[0] if users else None
    
    async def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get a user by their email address."""
        response = self.supabase.table("users").select("*").eq("email", email).execute()
        users = handle_supabase_response(response, "Failed to retrieve user")
        return users[0] if users else None
    
    async def get_user_workspaces(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all workspaces for a user with their roles."""
        # Get workspace memberships
        members_response = self.supabase.table("workspace_members").select("*").eq("user_id", user_id).execute()
        members = handle_supabase_response(members_response, "Failed to retrieve user workspace memberships")
        
        if not members:
            return []
        
        # Get workspace details
        workspace_ids = [member["workspace_id"] for member in members]
        workspaces_response = self.supabase.table("workspaces").select("*").in_("id", workspace_ids).execute()
        workspaces = handle_supabase_response(workspaces_response, "Failed to retrieve workspaces")
        
        # Combine workspace data with role information
        workspace_data = []
        for workspace in workspaces:
            member_info = next((m for m in members if m["workspace_id"] == workspace["id"]), None)
            workspace_with_role = {
                **workspace,
                "user_role": member_info["role_id"] if member_info else None,
                "joined_at": member_info["joined_at"] if member_info else None
            }
            workspace_data.append(workspace_with_role)
        
        return workspace_data
    
    async def get_user_contracts(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all contracts accessible to a user through their workspaces."""
        workspaces = await self.get_user_workspaces(user_id)
        workspace_ids = [ws["id"] for ws in workspaces]
        
        if not workspace_ids:
            return []
        
        # Get contracts from user's workspaces
        contracts_response = self.supabase.table("contracts").select("*").in_("workspace_id", workspace_ids).execute()
        contracts = handle_supabase_response(contracts_response, "Failed to retrieve user contracts")
        
        return contracts
    
    async def get_user_templates(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all templates accessible to a user through their workspaces."""
        workspaces = await self.get_user_workspaces(user_id)
        workspace_ids = [ws["id"] for ws in workspaces]
        
        if not workspace_ids:
            return []
        
        # Get templates from user's workspaces
        templates_response = self.supabase.table("templates").select("*").in_("workspace_id", workspace_ids).execute()
        templates = handle_supabase_response(templates_response, "Failed to retrieve user templates")
        
        return templates
    
    async def create_user_from_clerk(self, clerk_user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a user record from Clerk user data."""
        user_record = {
            "id": clerk_user_data["id"],
            "email": clerk_user_data["email"],
            "first_name": clerk_user_data.get("first_name", ""),
            "last_name": clerk_user_data.get("last_name", ""),
            "title": "",
            "company": "",
            "timezone": "America/New_York",
            "bio": "",
            "initials": f"{clerk_user_data.get('first_name', '')[:1]}{clerk_user_data.get('last_name', '')[:1]}".upper(),
            "notification_preferences": {
                "email_approvals": True,
                "email_updates": True,
                "email_reminders": True,
                "email_comments": True,
                "system_approvals": True,
                "browser_notifications": True,
                "email_digest_frequency": "daily"
            },
            "workspaces": [],
            "workspace_roles": {}
        }
        
        response = self.supabase.table("users").insert(user_record).execute()
        created_user = handle_supabase_response(response, "Failed to create user")
        
        return created_user[0] if created_user else None
    
    async def update_user(self, user_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a user record."""
        response = self.supabase.table("users").update(update_data).eq("id", user_id).execute()
        updated_user = handle_supabase_response(response, "Failed to update user")
        
        return updated_user[0] if updated_user else None
    
    async def add_user_to_workspace(self, user_id: str, workspace_id: str, role_id: str = "role-viewer") -> bool:
        """Add a user to a workspace with a specific role."""
        membership_data = {
            "workspace_id": workspace_id,
            "user_id": user_id,
            "role_id": role_id
        }
        
        try:
            response = self.supabase.table("workspace_members").insert(membership_data).execute()
            handle_supabase_response(response, "Failed to add user to workspace")
            return True
        except Exception:
            return False
    
    async def remove_user_from_workspace(self, user_id: str, workspace_id: str) -> bool:
        """Remove a user from a workspace."""
        try:
            response = self.supabase.table("workspace_members").delete().eq("user_id", user_id).eq("workspace_id", workspace_id).execute()
            handle_supabase_response(response, "Failed to remove user from workspace")
            return True
        except Exception:
            return False
    
    async def get_demo_users(self) -> List[Dict[str, Any]]:
        """Get all demo users."""
        response = self.supabase.table("users").select("*").like("email", "%@demo.legalai.com").execute()
        return handle_supabase_response(response, "Failed to retrieve demo users")
    
    async def is_demo_user(self, user_id: str) -> bool:
        """Check if a user is a demo user."""
        user = await self.get_user_by_id(user_id)
        return user and user.get("email", "").endswith("@demo.legalai.com")
    
    async def get_user_permissions(self, user_id: str, workspace_id: str) -> Dict[str, bool]:
        """Get user permissions for a specific workspace."""
        # Get user's role in the workspace
        response = self.supabase.table("workspace_members").select("role_id").eq("user_id", user_id).eq("workspace_id", workspace_id).execute()
        members = handle_supabase_response(response, "Failed to retrieve user workspace membership")
        
        if not members:
            return {
                "can_view": False,
                "can_edit": False,
                "can_admin": False,
                "can_create_contracts": False,
                "can_approve_contracts": False,
                "can_manage_users": False
            }
        
        role_id = members[0]["role_id"]
        
        # Define permissions based on role
        permissions = {
            "role-viewer": {
                "can_view": True,
                "can_edit": False,
                "can_admin": False,
                "can_create_contracts": False,
                "can_approve_contracts": False,
                "can_manage_users": False
            },
            "role-editor": {
                "can_view": True,
                "can_edit": True,
                "can_admin": False,
                "can_create_contracts": True,
                "can_approve_contracts": False,
                "can_manage_users": False
            },
            "role-admin": {
                "can_view": True,
                "can_edit": True,
                "can_admin": True,
                "can_create_contracts": True,
                "can_approve_contracts": True,
                "can_manage_users": True
            }
        }
        
        return permissions.get(role_id, permissions["role-viewer"])
    
    async def get_user_dashboard_data(self, user_id: str) -> Dict[str, Any]:
        """Get dashboard data for a user."""
        workspaces = await self.get_user_workspaces(user_id)
        contracts = await self.get_user_contracts(user_id)
        templates = await self.get_user_templates(user_id)
        
        # Calculate statistics
        active_contracts = [c for c in contracts if c.get("status") == "active"]
        pending_contracts = [c for c in contracts if c.get("status") == "pending_signature"]
        
        return {
            "user_id": user_id,
            "workspaces_count": len(workspaces),
            "contracts_count": len(contracts),
            "templates_count": len(templates),
            "active_contracts_count": len(active_contracts),
            "pending_contracts_count": len(pending_contracts),
            "workspaces": workspaces,
            "recent_contracts": contracts[:5],  # Last 5 contracts
            "pending_approvals": pending_contracts
        }

# Create a global instance
user_service = UserService()
