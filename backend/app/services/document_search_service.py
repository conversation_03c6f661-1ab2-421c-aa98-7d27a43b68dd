import uuid
import json
import re
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from supabase import Client
from app.services.ai_service import ai_service
import logging

logger = logging.getLogger(__name__)

class DocumentSearchService:
    def __init__(self, supabase_client: Client):
        self.supabase = supabase_client

    async def advanced_search(
        self,
        workspace_id: str,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        search_type: str = "hybrid",  # "text", "semantic", "hybrid"
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Perform advanced search across documents with multiple search strategies.
        
        Args:
            workspace_id: The workspace to search in
            query: The search query
            filters: Additional filters (file_type, date_range, folder, etc.)
            search_type: Type of search to perform
            limit: Maximum number of results
            offset: Pagination offset
            
        Returns:
            Dictionary containing search results and metadata
        """
        try:
            results = []
            
            if search_type in ["text", "hybrid"]:
                text_results = await self._text_search(workspace_id, query, filters, limit, offset)
                results.extend(text_results)
            
            if search_type in ["semantic", "hybrid"]:
                semantic_results = await self._semantic_search(workspace_id, query, filters, limit, offset)
                results.extend(semantic_results)
            
            # Remove duplicates and merge scores for hybrid search
            if search_type == "hybrid":
                results = self._merge_hybrid_results(results)
            
            # Sort by relevance score
            results.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            
            # Apply final limit
            results = results[:limit]
            
            return {
                "results": results,
                "total_count": len(results),
                "search_type": search_type,
                "query": query,
                "filters": filters or {},
                "suggestions": await self._generate_search_suggestions(query, workspace_id)
            }
            
        except Exception as e:
            logger.error(f"Error in advanced search: {str(e)}")
            raise

    async def _text_search(
        self,
        workspace_id: str,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Perform full-text search using PostgreSQL text search capabilities."""
        try:
            # Build the base query
            search_query = self.supabase.table("documents").select(
                "id, title, filename, content, file_url, folder, created_at, created_by, file_info"
            ).eq("workspace_id", workspace_id)
            
            # Apply text search
            if query:
                # Use PostgreSQL full-text search
                search_terms = self._prepare_search_terms(query)
                search_query = search_query.or_(
                    f"title.ilike.%{query}%,"
                    f"content.ilike.%{query}%,"
                    f"filename.ilike.%{query}%"
                )
            
            # Apply filters
            if filters:
                search_query = self._apply_filters(search_query, filters)
            
            # Execute query with pagination
            response = search_query.range(offset, offset + limit - 1).execute()
            
            if not response.data:
                return []
            
            # Calculate relevance scores for text search
            results = []
            for doc in response.data:
                score = self._calculate_text_relevance_score(doc, query)
                results.append({
                    **doc,
                    "relevance_score": score,
                    "search_type": "text",
                    "highlights": self._generate_highlights(doc, query)
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error in text search: {str(e)}")
            return []

    async def _semantic_search(
        self,
        workspace_id: str,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Perform semantic search using AI embeddings."""
        try:
            # Get all documents for semantic analysis
            response = self.supabase.table("documents").select(
                "id, title, filename, content, file_url, folder, created_at, created_by, file_info"
            ).eq("workspace_id", workspace_id).execute()
            
            if not response.data:
                return []
            
            documents = response.data
            
            # Apply filters before semantic analysis
            if filters:
                documents = self._filter_documents(documents, filters)
            
            # Use AI service for semantic similarity
            semantic_results = await self._analyze_semantic_similarity(query, documents)
            
            # Apply pagination
            start_idx = offset
            end_idx = offset + limit
            return semantic_results[start_idx:end_idx]
            
        except Exception as e:
            logger.error(f"Error in semantic search: {str(e)}")
            return []

    async def _analyze_semantic_similarity(
        self,
        query: str,
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Use AI to analyze semantic similarity between query and documents."""
        try:
            # Prepare documents for AI analysis
            doc_summaries = []
            for doc in documents:
                summary = {
                    "id": doc["id"],
                    "title": doc["title"],
                    "content_preview": (doc.get("content", "") or "")[:500],  # First 500 chars
                    "filename": doc["filename"]
                }
                doc_summaries.append(summary)
            
            # Use AI service to find semantic matches
            analysis_prompt = f"""
            Analyze the semantic similarity between the search query and the following documents.
            
            Search Query: "{query}"
            
            Documents: {json.dumps(doc_summaries, indent=2)}
            
            For each document, provide:
            1. A relevance score from 0.0 to 1.0
            2. A brief explanation of why it matches
            3. Key matching concepts
            
            Return the results as a JSON array with this structure:
            [
                {{
                    "document_id": "doc_id",
                    "relevance_score": 0.85,
                    "explanation": "explanation text",
                    "matching_concepts": ["concept1", "concept2"]
                }}
            ]
            
            Only include documents with relevance_score > 0.3.
            """
            
            ai_response = await ai_service.analyze_contract(analysis_prompt, "semantic_search")
            
            # Parse AI response and merge with document data
            results = []
            if hasattr(ai_response, 'suggestions') and ai_response.suggestions:
                try:
                    semantic_scores = json.loads(ai_response.suggestions[0] if ai_response.suggestions else "[]")
                    
                    for score_data in semantic_scores:
                        doc_id = score_data.get("document_id")
                        doc = next((d for d in documents if d["id"] == doc_id), None)
                        
                        if doc:
                            results.append({
                                **doc,
                                "relevance_score": score_data.get("relevance_score", 0.0),
                                "search_type": "semantic",
                                "explanation": score_data.get("explanation", ""),
                                "matching_concepts": score_data.get("matching_concepts", []),
                                "highlights": []  # Semantic search doesn't have text highlights
                            })
                except json.JSONDecodeError:
                    logger.warning("Failed to parse AI semantic search response")
            
            # Sort by relevance score
            results.sort(key=lambda x: x["relevance_score"], reverse=True)
            return results
            
        except Exception as e:
            logger.error(f"Error in semantic similarity analysis: {str(e)}")
            return []

    def _merge_hybrid_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Merge results from text and semantic search, combining scores for duplicates."""
        merged = {}
        
        for result in results:
            doc_id = result["id"]
            
            if doc_id in merged:
                # Combine scores (weighted average)
                existing = merged[doc_id]
                text_weight = 0.6
                semantic_weight = 0.4
                
                if existing["search_type"] == "text" and result["search_type"] == "semantic":
                    combined_score = (existing["relevance_score"] * text_weight + 
                                    result["relevance_score"] * semantic_weight)
                elif existing["search_type"] == "semantic" and result["search_type"] == "text":
                    combined_score = (existing["relevance_score"] * semantic_weight + 
                                    result["relevance_score"] * text_weight)
                else:
                    combined_score = max(existing["relevance_score"], result["relevance_score"])
                
                merged[doc_id]["relevance_score"] = combined_score
                merged[doc_id]["search_type"] = "hybrid"
                
                # Merge highlights and explanations
                if result.get("highlights"):
                    merged[doc_id]["highlights"].extend(result["highlights"])
                if result.get("explanation"):
                    merged[doc_id]["explanation"] = result["explanation"]
                if result.get("matching_concepts"):
                    merged[doc_id]["matching_concepts"] = result["matching_concepts"]
            else:
                merged[doc_id] = result
        
        return list(merged.values())

    def _calculate_text_relevance_score(self, document: Dict[str, Any], query: str) -> float:
        """Calculate relevance score for text search results."""
        score = 0.0
        query_lower = query.lower()
        
        # Title matches (highest weight)
        title = (document.get("title", "") or "").lower()
        if query_lower in title:
            score += 0.5
        
        # Filename matches
        filename = (document.get("filename", "") or "").lower()
        if query_lower in filename:
            score += 0.3
        
        # Content matches
        content = (document.get("content", "") or "").lower()
        if query_lower in content:
            score += 0.2
            # Bonus for multiple occurrences
            occurrences = content.count(query_lower)
            score += min(occurrences * 0.1, 0.3)
        
        return min(score, 1.0)

    def _generate_highlights(self, document: Dict[str, Any], query: str) -> List[str]:
        """Generate text highlights for search results."""
        highlights = []
        query_lower = query.lower()
        
        # Highlight in title
        title = document.get("title", "") or ""
        if query_lower in title.lower():
            highlighted = re.sub(
                f"({re.escape(query)})",
                r"<mark>\1</mark>",
                title,
                flags=re.IGNORECASE
            )
            highlights.append(f"Title: {highlighted}")
        
        # Highlight in content (show context)
        content = document.get("content", "") or ""
        if query_lower in content.lower():
            # Find the first occurrence and show context
            start_idx = content.lower().find(query_lower)
            if start_idx != -1:
                context_start = max(0, start_idx - 50)
                context_end = min(len(content), start_idx + len(query) + 50)
                context = content[context_start:context_end]
                
                highlighted = re.sub(
                    f"({re.escape(query)})",
                    r"<mark>\1</mark>",
                    context,
                    flags=re.IGNORECASE
                )
                highlights.append(f"...{highlighted}...")
        
        return highlights

    async def _generate_search_suggestions(self, query: str, workspace_id: str) -> List[str]:
        """Generate search suggestions based on query and workspace content."""
        try:
            # Get common terms from documents in workspace
            response = self.supabase.table("documents").select("title, content").eq("workspace_id", workspace_id).limit(100).execute()
            
            if not response.data:
                return []
            
            # Extract common terms (simplified implementation)
            suggestions = []
            
            # Add query variations
            if len(query) > 3:
                suggestions.extend([
                    f"{query} contract",
                    f"{query} agreement",
                    f"{query} document"
                ])
            
            return suggestions[:5]
            
        except Exception as e:
            logger.error(f"Error generating search suggestions: {str(e)}")
            return []

    def _prepare_search_terms(self, query: str) -> str:
        """Prepare search terms for PostgreSQL full-text search."""
        # Remove special characters and split into terms
        terms = re.findall(r'\w+', query.lower())
        return ' & '.join(terms) if terms else query

    def _apply_filters(self, query, filters: Dict[str, Any]):
        """Apply filters to the database query."""
        for key, value in filters.items():
            if value is None or value == "":
                continue
                
            if key == "file_type":
                if isinstance(value, list):
                    # Multiple file types
                    conditions = [f"filename.ilike.%.{ext}" for ext in value]
                    query = query.or_(",".join(conditions))
                else:
                    query = query.ilike("filename", f"%.{value}")
            
            elif key == "folder":
                if value != "all":
                    query = query.eq("folder", value)
            
            elif key == "date_range":
                if isinstance(value, dict):
                    if value.get("start"):
                        query = query.gte("created_at", value["start"])
                    if value.get("end"):
                        query = query.lte("created_at", value["end"])
            
            elif key == "created_by":
                query = query.eq("created_by", value)
        
        return query

    def _filter_documents(self, documents: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter documents list based on criteria."""
        filtered = documents
        
        for key, value in filters.items():
            if value is None or value == "":
                continue
            
            if key == "file_type":
                if isinstance(value, list):
                    filtered = [d for d in filtered if any(d["filename"].lower().endswith(f".{ext.lower()}") for ext in value)]
                else:
                    filtered = [d for d in filtered if d["filename"].lower().endswith(f".{value.lower()}")]
            
            elif key == "folder" and value != "all":
                filtered = [d for d in filtered if d.get("folder") == value]
            
            elif key == "date_range" and isinstance(value, dict):
                if value.get("start"):
                    start_date = datetime.fromisoformat(value["start"].replace('Z', '+00:00'))
                    filtered = [d for d in filtered if datetime.fromisoformat(d["created_at"].replace('Z', '+00:00')) >= start_date]
                if value.get("end"):
                    end_date = datetime.fromisoformat(value["end"].replace('Z', '+00:00'))
                    filtered = [d for d in filtered if datetime.fromisoformat(d["created_at"].replace('Z', '+00:00')) <= end_date]
        
        return filtered
