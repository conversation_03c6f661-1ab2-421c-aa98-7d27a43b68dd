"""
Notification Integration Service for Averum Contracts
Integrates real-time notifications with other system components
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from app.services.websocket_service import notification_service, NotificationType, NotificationPriority
from app.services.performance_service import performance_service

logger = logging.getLogger(__name__)


class NotificationIntegrationService:
    """Service for integrating notifications with system events."""
    
    def __init__(self):
        self.notification_service = notification_service
    
    @performance_service.track_request("notification_contract_event")
    async def notify_contract_event(
        self,
        event_type: str,
        contract_id: str,
        contract_title: str,
        workspace_id: str,
        user_id: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """Notify about contract-related events."""
        try:
            event_mapping = {
                "created": {
                    "type": NotificationType.CONTRACT_UPDATED,
                    "title": "New Contract Created",
                    "message": f"Contract '{contract_title}' has been created",
                    "priority": NotificationPriority.MEDIUM
                },
                "updated": {
                    "type": NotificationType.CONTRACT_UPDATED,
                    "title": "Contract Updated",
                    "message": f"Contract '{contract_title}' has been updated",
                    "priority": NotificationPriority.MEDIUM
                },
                "signed": {
                    "type": NotificationType.CONTRACT_SIGNED,
                    "title": "Contract Signed",
                    "message": f"Contract '{contract_title}' has been signed",
                    "priority": NotificationPriority.HIGH
                },
                "expired": {
                    "type": NotificationType.CONTRACT_EXPIRED,
                    "title": "Contract Expired",
                    "message": f"Contract '{contract_title}' has expired",
                    "priority": NotificationPriority.URGENT
                },
                "expiring_soon": {
                    "type": NotificationType.DEADLINE_REMINDER,
                    "title": "Contract Expiring Soon",
                    "message": f"Contract '{contract_title}' will expire soon",
                    "priority": NotificationPriority.HIGH
                }
            }
            
            if event_type not in event_mapping:
                logger.warning(f"Unknown contract event type: {event_type}")
                return
            
            event_config = event_mapping[event_type]
            
            notification_data = {
                "contract_id": contract_id,
                "contract_title": contract_title,
                "event_type": event_type,
                **(additional_data or {})
            }
            
            await self.notification_service.create_notification(
                notification_type=event_config["type"],
                title=event_config["title"],
                message=event_config["message"],
                workspace_id=workspace_id,
                user_id=user_id,
                priority=event_config["priority"],
                data=notification_data,
                expires_in_hours=24 if event_type != "expired" else 72
            )
            
        except Exception as e:
            logger.error(f"Error notifying contract event: {e}")
    
    @performance_service.track_request("notification_approval_event")
    async def notify_approval_event(
        self,
        event_type: str,
        approval_id: str,
        contract_title: str,
        workspace_id: str,
        approver_id: str,
        requester_id: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """Notify about approval workflow events."""
        try:
            if event_type == "required":
                await self.notification_service.create_notification(
                    notification_type=NotificationType.APPROVAL_REQUIRED,
                    title="Approval Required",
                    message=f"Your approval is required for contract '{contract_title}'",
                    workspace_id=workspace_id,
                    user_id=approver_id,
                    priority=NotificationPriority.HIGH,
                    data={
                        "approval_id": approval_id,
                        "contract_title": contract_title,
                        "requester_id": requester_id,
                        **(additional_data or {})
                    },
                    expires_in_hours=48
                )
            
            elif event_type == "completed":
                # Notify requester if different from approver
                if requester_id and requester_id != approver_id:
                    await self.notification_service.create_notification(
                        notification_type=NotificationType.APPROVAL_COMPLETED,
                        title="Approval Completed",
                        message=f"Contract '{contract_title}' has been approved",
                        workspace_id=workspace_id,
                        user_id=requester_id,
                        priority=NotificationPriority.MEDIUM,
                        data={
                            "approval_id": approval_id,
                            "contract_title": contract_title,
                            "approver_id": approver_id,
                            **(additional_data or {})
                        },
                        expires_in_hours=24
                    )
            
        except Exception as e:
            logger.error(f"Error notifying approval event: {e}")
    
    @performance_service.track_request("notification_ai_event")
    async def notify_ai_analysis_complete(
        self,
        analysis_id: str,
        contract_title: str,
        workspace_id: str,
        user_id: str,
        analysis_type: str = "comprehensive",
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """Notify when AI analysis is complete."""
        try:
            await self.notification_service.create_notification(
                notification_type=NotificationType.AI_ANALYSIS_COMPLETE,
                title="AI Analysis Complete",
                message=f"AI analysis for contract '{contract_title}' is ready",
                workspace_id=workspace_id,
                user_id=user_id,
                priority=NotificationPriority.MEDIUM,
                data={
                    "analysis_id": analysis_id,
                    "contract_title": contract_title,
                    "analysis_type": analysis_type,
                    **(additional_data or {})
                },
                expires_in_hours=48
            )
            
        except Exception as e:
            logger.error(f"Error notifying AI analysis complete: {e}")
    
    @performance_service.track_request("notification_batch_job_event")
    async def notify_batch_job_complete(
        self,
        job_id: str,
        job_type: str,
        workspace_id: str,
        user_id: str,
        total_items: int,
        successful_items: int,
        failed_items: int,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """Notify when batch job is complete."""
        try:
            success_rate = (successful_items / total_items * 100) if total_items > 0 else 0
            
            if failed_items == 0:
                message = f"Batch {job_type} completed successfully for {total_items} items"
                priority = NotificationPriority.MEDIUM
            elif success_rate >= 80:
                message = f"Batch {job_type} completed with {failed_items} failures out of {total_items} items"
                priority = NotificationPriority.MEDIUM
            else:
                message = f"Batch {job_type} completed with significant failures: {failed_items}/{total_items} failed"
                priority = NotificationPriority.HIGH
            
            await self.notification_service.create_notification(
                notification_type=NotificationType.BATCH_JOB_COMPLETE,
                title="Batch Job Complete",
                message=message,
                workspace_id=workspace_id,
                user_id=user_id,
                priority=priority,
                data={
                    "job_id": job_id,
                    "job_type": job_type,
                    "total_items": total_items,
                    "successful_items": successful_items,
                    "failed_items": failed_items,
                    "success_rate": success_rate,
                    **(additional_data or {})
                },
                expires_in_hours=72
            )
            
        except Exception as e:
            logger.error(f"Error notifying batch job complete: {e}")
    
    @performance_service.track_request("notification_document_event")
    async def notify_document_uploaded(
        self,
        document_id: str,
        document_title: str,
        workspace_id: str,
        uploader_id: str,
        file_type: str,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """Notify about document upload."""
        try:
            # Broadcast to workspace (excluding uploader)
            await self.notification_service.create_notification(
                notification_type=NotificationType.DOCUMENT_UPLOADED,
                title="New Document Uploaded",
                message=f"New document '{document_title}' has been uploaded",
                workspace_id=workspace_id,
                user_id=None,  # Broadcast to workspace
                priority=NotificationPriority.LOW,
                data={
                    "document_id": document_id,
                    "document_title": document_title,
                    "uploader_id": uploader_id,
                    "file_type": file_type,
                    **(additional_data or {})
                },
                expires_in_hours=24
            )
            
        except Exception as e:
            logger.error(f"Error notifying document upload: {e}")
    
    @performance_service.track_request("notification_workspace_event")
    async def notify_workspace_invitation(
        self,
        workspace_id: str,
        workspace_name: str,
        invited_user_id: str,
        inviter_id: str,
        role: str,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """Notify about workspace invitation."""
        try:
            await self.notification_service.create_notification(
                notification_type=NotificationType.WORKSPACE_INVITATION,
                title="Workspace Invitation",
                message=f"You've been invited to join workspace '{workspace_name}' as {role}",
                workspace_id=workspace_id,
                user_id=invited_user_id,
                priority=NotificationPriority.HIGH,
                data={
                    "workspace_id": workspace_id,
                    "workspace_name": workspace_name,
                    "inviter_id": inviter_id,
                    "role": role,
                    **(additional_data or {})
                },
                expires_in_hours=168  # 1 week
            )
            
        except Exception as e:
            logger.error(f"Error notifying workspace invitation: {e}")
    
    @performance_service.track_request("notification_system_alert")
    async def notify_system_alert(
        self,
        alert_type: str,
        message: str,
        workspace_id: Optional[str] = None,
        user_id: Optional[str] = None,
        priority: NotificationPriority = NotificationPriority.MEDIUM,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """Send system alerts."""
        try:
            await self.notification_service.create_notification(
                notification_type=NotificationType.SYSTEM_ALERT,
                title=f"System Alert: {alert_type}",
                message=message,
                workspace_id=workspace_id or "system",
                user_id=user_id,
                priority=priority,
                data={
                    "alert_type": alert_type,
                    **(additional_data or {})
                },
                expires_in_hours=24
            )
            
        except Exception as e:
            logger.error(f"Error sending system alert: {e}")
    
    async def cleanup_expired_notifications(self):
        """Clean up expired notifications."""
        try:
            # Delete expired notifications
            cutoff_time = datetime.utcnow()
            
            result = self.notification_service.supabase.table("notifications").delete().lt("expires_at", cutoff_time.isoformat()).execute()
            
            deleted_count = len(result.data or [])
            if deleted_count > 0:
                logger.info(f"Cleaned up {deleted_count} expired notifications")
            
        except Exception as e:
            logger.error(f"Error cleaning up expired notifications: {e}")


# Global notification integration service instance
notification_integration_service = NotificationIntegrationService()
