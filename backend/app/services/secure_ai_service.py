"""
Secure AI Service for Averum Contracts
Integrates AI analysis with security, encryption, and audit logging
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from app.services.ai_service import ai_service, AIAnalysisResult
from app.services.security_service import security_service, SecurityError
from app.services.document_processor import document_processor
from app.core.config import settings

logger = logging.getLogger(__name__)


class SecureAIService:
    """Secure AI service that handles contract analysis with security measures."""
    
    def __init__(self):
        self.ai_service = ai_service
        self.security_service = security_service
        self.document_processor = document_processor
        
    async def analyze_contract_securely(
        self,
        contract_text: str,
        user_id: str,
        workspace_id: str,
        contract_id: Optional[str] = None,
        analysis_type: str = "comprehensive",
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Perform secure contract analysis with PII protection and audit logging.
        
        Args:
            contract_text: Raw contract text
            user_id: ID of the user requesting analysis
            workspace_id: Workspace context
            contract_id: Optional contract ID for audit trail
            analysis_type: Type of analysis to perform
            ip_address: User's IP address for audit
            user_agent: User's browser/client info
            session_id: Session identifier
            
        Returns:
            Analysis results with security metadata
        """
        start_time = datetime.utcnow()
        
        try:
            # Log the start of AI processing
            await self.security_service.log_audit_event(
                event_type="ai_processing",
                user_id=user_id,
                workspace_id=workspace_id,
                action="analysis_started",
                resource_type="contract",
                resource_id=contract_id,
                details={
                    "analysis_type": analysis_type,
                    "text_length": len(contract_text),
                    "start_time": start_time.isoformat()
                },
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            # Sanitize text for AI processing
            sanitized_text, sanitization_metadata = await self.security_service.sanitize_for_ai_processing(
                contract_text
            )
            
            # Log PII detection results if any PII was found
            if sanitization_metadata.get('pii_detected'):
                await self.security_service.log_audit_event(
                    event_type="pii_detection",
                    user_id=user_id,
                    workspace_id=workspace_id,
                    action="pii_anonymized",
                    resource_type="contract",
                    resource_id=contract_id,
                    details=sanitization_metadata,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    session_id=session_id
                )
            
            # Encrypt the sanitized text for AI processing
            encrypted_text = await self.security_service.encrypt_data(sanitized_text)
            
            # Decrypt for AI processing (in production, this would be done in a secure enclave)
            decrypted_text = await self.security_service.decrypt_data(encrypted_text)
            
            # Perform AI analysis on sanitized text
            ai_result = await self.ai_service.analyze_contract(decrypted_text, analysis_type)
            
            # Calculate processing time
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Create secure response
            secure_result = {
                "success": True,
                "analysis_result": {
                    "contract_type": ai_result.contract_type,
                    "risk_score": ai_result.risk_score,
                    "compliance_score": ai_result.compliance_score,
                    "language_clarity": ai_result.language_clarity,
                    "key_risks": ai_result.key_risks,
                    "suggestions": ai_result.suggestions,
                    "extracted_clauses": ai_result.extracted_clauses,
                    "compliance_issues": ai_result.compliance_issues,
                    "obligations": ai_result.obligations,
                    "confidence": ai_result.confidence,
                    "provider": ai_result.provider,
                    "processing_time": ai_result.processing_time
                },
                "security_metadata": {
                    "pii_detected": sanitization_metadata.get('pii_detected', False),
                    "pii_types": sanitization_metadata.get('pii_types', []),
                    "redaction_count": sanitization_metadata.get('redaction_count', 0),
                    "data_encrypted": True,
                    "audit_logged": True,
                    "total_processing_time": processing_time
                },
                "compliance_info": {
                    "gdpr_compliant": True,
                    "data_minimization_applied": True,
                    "encryption_used": True,
                    "audit_trail_created": True
                }
            }
            
            # Log successful completion
            await self.security_service.log_audit_event(
                event_type="ai_processing",
                user_id=user_id,
                workspace_id=workspace_id,
                action="analysis_completed",
                resource_type="contract",
                resource_id=contract_id,
                details={
                    "analysis_type": analysis_type,
                    "processing_time": processing_time,
                    "ai_provider": ai_result.provider,
                    "confidence": ai_result.confidence,
                    "risk_score": ai_result.risk_score,
                    "pii_detected": sanitization_metadata.get('pii_detected', False)
                },
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            return secure_result
            
        except SecurityError as e:
            # Log security error
            await self.security_service.log_audit_event(
                event_type="security_error",
                user_id=user_id,
                workspace_id=workspace_id,
                action="analysis_failed",
                resource_type="contract",
                resource_id=contract_id,
                details={
                    "error_type": "SecurityError",
                    "error_message": str(e),
                    "analysis_type": analysis_type
                },
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            logger.error(f"Security error in AI analysis: {e}")
            return {
                "success": False,
                "error": "Security error occurred during analysis",
                "error_type": "security_error"
            }
            
        except Exception as e:
            # Log general error
            await self.security_service.log_audit_event(
                event_type="ai_processing",
                user_id=user_id,
                workspace_id=workspace_id,
                action="analysis_error",
                resource_type="contract",
                resource_id=contract_id,
                details={
                    "error_type": type(e).__name__,
                    "error_message": str(e),
                    "analysis_type": analysis_type
                },
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            logger.error(f"Error in secure AI analysis: {e}")
            return {
                "success": False,
                "error": "An error occurred during analysis",
                "error_type": "processing_error"
            }
    
    async def process_document_securely(
        self,
        file_path: str,
        file_name: str,
        user_id: str,
        workspace_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a document with security measures and audit logging.
        
        Args:
            file_path: Path to the document file
            file_name: Original file name
            user_id: ID of the user uploading the document
            workspace_id: Workspace context
            ip_address: User's IP address
            user_agent: User's browser/client info
            session_id: Session identifier
            
        Returns:
            Processing results with security metadata
        """
        try:
            # Log document processing start
            await self.security_service.log_audit_event(
                event_type="document_processing",
                user_id=user_id,
                workspace_id=workspace_id,
                action="processing_started",
                resource_type="document",
                details={
                    "file_name": file_name,
                    "file_path": file_path
                },
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            # Process the document
            processed_doc = await self.document_processor.process_document(
                file_path=file_path,
                file_name=file_name,
                extract_entities=True,
                chunk_document=True,
                validate_content=True
            )
            
            # Check for PII in the processed text
            pii_result = await self.security_service.detect_and_anonymize_pii(
                processed_doc.cleaned_text,
                anonymize=False  # Just detect, don't anonymize for document processing
            )
            
            # Log PII detection results
            if pii_result.pii_found:
                await self.security_service.log_audit_event(
                    event_type="pii_detection",
                    user_id=user_id,
                    workspace_id=workspace_id,
                    action="pii_detected_in_document",
                    resource_type="document",
                    details={
                        "file_name": file_name,
                        "pii_types": pii_result.pii_types,
                        "pii_count": pii_result.pii_count
                    },
                    ip_address=ip_address,
                    user_agent=user_agent,
                    session_id=session_id
                )
            
            # Create secure response
            result = {
                "success": True,
                "file_name": file_name,
                "metadata": processed_doc.metadata.dict(),
                "text_length": len(processed_doc.cleaned_text),
                "chunk_count": len(processed_doc.chunks),
                "entity_count": len(processed_doc.extracted_entities),
                "section_count": len(processed_doc.sections),
                "processing_time": processed_doc.processing_time,
                "processing_errors": processed_doc.processing_errors,
                "security_metadata": {
                    "pii_detected": pii_result.pii_found,
                    "pii_types": pii_result.pii_types,
                    "pii_count": pii_result.pii_count,
                    "audit_logged": True
                },
                # Only return first 1000 characters for security
                "extracted_text": processed_doc.cleaned_text[:1000] + "..." if len(processed_doc.cleaned_text) > 1000 else processed_doc.cleaned_text,
                "entities": processed_doc.extracted_entities,
                "sections": [{"id": s["id"], "title": s["title"], "word_count": s["word_count"]} for s in processed_doc.sections],
                "chunks": [{"chunk_id": c.chunk_id, "word_count": c.word_count, "start_position": c.start_position} for c in processed_doc.chunks]
            }
            
            # Log successful completion
            await self.security_service.log_audit_event(
                event_type="document_processing",
                user_id=user_id,
                workspace_id=workspace_id,
                action="processing_completed",
                resource_type="document",
                details={
                    "file_name": file_name,
                    "processing_time": processed_doc.processing_time,
                    "text_length": len(processed_doc.cleaned_text),
                    "pii_detected": pii_result.pii_found
                },
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            return result
            
        except Exception as e:
            # Log error
            await self.security_service.log_audit_event(
                event_type="document_processing",
                user_id=user_id,
                workspace_id=workspace_id,
                action="processing_error",
                resource_type="document",
                details={
                    "file_name": file_name,
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                },
                ip_address=ip_address,
                user_agent=user_agent,
                session_id=session_id
            )
            
            logger.error(f"Error in secure document processing: {e}")
            return {
                "success": False,
                "error": "An error occurred during document processing",
                "error_type": "processing_error"
            }


# Global secure AI service instance
secure_ai_service = SecureAIService()
