"""
Workflow Scheduler Service for Averum Contracts

Provides background task scheduling for workflow management including:
- Automatic timeout checking
- Escalation processing
- Performance metrics collection
- Workflow cleanup
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import schedule
import time
from threading import Thread

from app.services.approval_workflow_service import approval_workflow_service
from app.services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class WorkflowScheduler:
    """Background scheduler for workflow-related tasks."""
    
    def __init__(self):
        self.notification_service = NotificationService()
        self.is_running = False
        self.scheduler_thread = None
        
    def start(self):
        """Start the background scheduler."""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
            
        self.is_running = True
        
        # Schedule tasks
        schedule.every(15).minutes.do(self._run_async_task, self.check_timeouts)
        schedule.every(1).hours.do(self._run_async_task, self.collect_performance_metrics)
        schedule.every(6).hours.do(self._run_async_task, self.cleanup_old_data)
        schedule.every().day.at("09:00").do(self._run_async_task, self.send_daily_summary)
        
        # Start scheduler in background thread
        self.scheduler_thread = Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("Workflow scheduler started")
    
    def stop(self):
        """Stop the background scheduler."""
        self.is_running = False
        schedule.clear()
        logger.info("Workflow scheduler stopped")
    
    def _run_scheduler(self):
        """Run the scheduler loop."""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Error in scheduler loop: {str(e)}")
                time.sleep(60)
    
    def _run_async_task(self, coro_func):
        """Run an async task in the scheduler."""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(coro_func())
            loop.close()
        except Exception as e:
            logger.error(f"Error running async task {coro_func.__name__}: {str(e)}")
    
    async def check_timeouts(self):
        """Check for and handle workflow timeouts."""
        try:
            logger.info("Starting timeout check...")
            result = await approval_workflow_service.check_timeouts()
            
            if result.get("success"):
                timeout_count = result.get("timeouts_processed", 0)
                escalated_count = len(result.get("escalated_workflows", []))
                
                logger.info(f"Timeout check completed: {timeout_count} timeouts, {escalated_count} escalations")
                
                # Send notifications for critical timeouts
                if timeout_count > 0:
                    await self._notify_timeout_summary(timeout_count, escalated_count)
            else:
                logger.error(f"Timeout check failed: {result.get('error')}")
                
        except Exception as e:
            logger.error(f"Error in timeout check: {str(e)}")
    
    async def collect_performance_metrics(self):
        """Collect and update workflow performance metrics."""
        try:
            logger.info("Collecting workflow performance metrics...")
            
            from app.db.database import get_supabase_client
            supabase = get_supabase_client()
            
            # Get completed workflows from the last hour
            one_hour_ago = datetime.utcnow() - timedelta(hours=1)
            
            workflows_response = supabase.table("approval_workflows").select(
                "*, approval_steps(*)"
            ).in_("status", ["completed", "rejected"]).gte(
                "updated_at", one_hour_ago.isoformat()
            ).execute()
            
            workflows = workflows_response.data or []
            metrics_updated = 0
            
            for workflow in workflows:
                try:
                    # Calculate performance metrics
                    metrics = await self._calculate_workflow_metrics(workflow)
                    
                    # Update or insert performance metrics
                    await self._update_performance_metrics(workflow["id"], metrics)
                    metrics_updated += 1
                    
                except Exception as e:
                    logger.error(f"Error calculating metrics for workflow {workflow['id']}: {str(e)}")
            
            logger.info(f"Performance metrics updated for {metrics_updated} workflows")
            
        except Exception as e:
            logger.error(f"Error collecting performance metrics: {str(e)}")
    
    async def cleanup_old_data(self):
        """Clean up old workflow data and logs."""
        try:
            logger.info("Starting workflow data cleanup...")
            
            from app.db.database import get_supabase_client
            supabase = get_supabase_client()
            
            # Clean up old escalations (older than 6 months)
            six_months_ago = datetime.utcnow() - timedelta(days=180)
            
            # Soft delete old resolved escalations
            escalations_result = supabase.table("workflow_escalations").update({
                "deleted_at": datetime.utcnow().isoformat()
            }).lt("created_at", six_months_ago.isoformat()).is_("resolved_at", "not.null").execute()
            
            escalations_cleaned = len(escalations_result.data or [])
            
            # Clean up old performance metrics (older than 1 year)
            one_year_ago = datetime.utcnow() - timedelta(days=365)
            
            metrics_result = supabase.table("workflow_performance_metrics").delete().lt(
                "created_at", one_year_ago.isoformat()
            ).execute()
            
            metrics_cleaned = len(metrics_result.data or [])
            
            logger.info(f"Cleanup completed: {escalations_cleaned} escalations, {metrics_cleaned} metrics")
            
        except Exception as e:
            logger.error(f"Error in data cleanup: {str(e)}")
    
    async def send_daily_summary(self):
        """Send daily workflow summary to administrators."""
        try:
            logger.info("Generating daily workflow summary...")
            
            from app.db.database import get_supabase_client
            supabase = get_supabase_client()
            
            # Get yesterday's data
            yesterday = datetime.utcnow() - timedelta(days=1)
            today = datetime.utcnow()
            
            # Get workflow statistics
            workflows_response = supabase.table("approval_workflows").select("*").gte(
                "created_at", yesterday.isoformat()
            ).lt("created_at", today.isoformat()).execute()
            
            workflows = workflows_response.data or []
            
            # Get escalation statistics
            escalations_response = supabase.table("workflow_escalations").select("*").gte(
                "created_at", yesterday.isoformat()
            ).lt("created_at", today.isoformat()).execute()
            
            escalations = escalations_response.data or []
            
            # Calculate summary statistics
            summary = {
                "date": yesterday.strftime("%Y-%m-%d"),
                "total_workflows": len(workflows),
                "completed_workflows": len([w for w in workflows if w["status"] == "completed"]),
                "pending_workflows": len([w for w in workflows if w["status"] == "active"]),
                "total_escalations": len(escalations),
                "timeout_escalations": len([e for e in escalations if e["escalation_type"] == "timeout"]),
                "manual_escalations": len([e for e in escalations if e["escalation_type"] == "manual"])
            }
            
            # Send summary notification to administrators
            await self._send_admin_summary(summary)
            
            logger.info(f"Daily summary sent: {summary}")
            
        except Exception as e:
            logger.error(f"Error sending daily summary: {str(e)}")
    
    async def _calculate_workflow_metrics(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate performance metrics for a workflow."""
        try:
            created_at = datetime.fromisoformat(workflow["created_at"].replace('Z', '+00:00'))
            updated_at = datetime.fromisoformat(workflow["updated_at"].replace('Z', '+00:00'))
            
            # Calculate total duration
            total_duration = (updated_at - created_at).total_seconds() / 3600  # hours
            
            # Calculate approval-specific metrics
            approval_steps = workflow.get("approval_steps", [])
            completed_steps = len([s for s in approval_steps if s["status"] in ["approved", "rejected"]])
            escalated_steps = len([s for s in approval_steps if s.get("escalated", False)])
            expired_steps = len([s for s in approval_steps if s["status"] == "expired"])
            
            # Calculate completion rate
            total_steps = len(approval_steps)
            completion_rate = completed_steps / total_steps if total_steps > 0 else 0
            
            return {
                "total_duration_hours": round(total_duration, 2),
                "approval_duration_hours": round(total_duration, 2),  # Same for now
                "escalation_count": escalated_steps,
                "timeout_count": expired_steps,
                "completed_successfully": workflow["status"] == "completed",
                "completion_rate": round(completion_rate, 4),
                "steps_completed": completed_steps,
                "steps_escalated": escalated_steps,
                "workflow_started_at": workflow["created_at"],
                "workflow_completed_at": workflow["updated_at"]
            }
            
        except Exception as e:
            logger.error(f"Error calculating workflow metrics: {str(e)}")
            return {}
    
    async def _update_performance_metrics(self, workflow_id: str, metrics: Dict[str, Any]):
        """Update performance metrics in the database."""
        try:
            from app.db.database import get_supabase_client
            supabase = get_supabase_client()
            
            # Get workflow details
            workflow_response = supabase.table("approval_workflows").select(
                "template_id, contract_id, workspace_id"
            ).eq("id", workflow_id).execute()
            
            if not workflow_response.data:
                return
            
            workflow = workflow_response.data[0]
            
            # Prepare metrics data
            metrics_data = {
                "workflow_id": workflow_id,
                "template_id": workflow.get("template_id"),
                "contract_id": workflow["contract_id"],
                "workspace_id": workflow["workspace_id"],
                **metrics,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            # Upsert metrics
            supabase.table("workflow_performance_metrics").upsert(
                metrics_data, on_conflict="workflow_id"
            ).execute()
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {str(e)}")
    
    async def _notify_timeout_summary(self, timeout_count: int, escalated_count: int):
        """Send notification about timeout summary."""
        try:
            # This would typically send to administrators
            # For now, just log the information
            logger.info(f"Timeout summary: {timeout_count} timeouts processed, {escalated_count} workflows escalated")
            
        except Exception as e:
            logger.error(f"Error sending timeout notification: {str(e)}")
    
    async def _send_admin_summary(self, summary: Dict[str, Any]):
        """Send daily summary to administrators."""
        try:
            # This would typically send to administrators
            # For now, just log the summary
            logger.info(f"Daily workflow summary: {summary}")
            
        except Exception as e:
            logger.error(f"Error sending admin summary: {str(e)}")


# Global scheduler instance
workflow_scheduler = WorkflowScheduler()


def start_workflow_scheduler():
    """Start the workflow scheduler."""
    workflow_scheduler.start()


def stop_workflow_scheduler():
    """Stop the workflow scheduler."""
    workflow_scheduler.stop()
