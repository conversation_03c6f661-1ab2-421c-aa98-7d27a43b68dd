"""
Contract Analytics Engine for Averum Contracts
Provides comprehensive analytics, metrics, trend analysis, and predictive insights
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import statistics

from pydantic import BaseModel
from app.db.database import get_supabase_client
from app.services.performance_service import performance_service

logger = logging.getLogger(__name__)


class MetricType(str, Enum):
    """Analytics metric types."""
    COUNT = "count"
    SUM = "sum"
    AVERAGE = "average"
    PERCENTAGE = "percentage"
    TREND = "trend"
    DISTRIBUTION = "distribution"


class TimeRange(str, Enum):
    """Time range options for analytics."""
    LAST_7_DAYS = "last_7_days"
    LAST_30_DAYS = "last_30_days"
    LAST_90_DAYS = "last_90_days"
    LAST_6_MONTHS = "last_6_months"
    LAST_YEAR = "last_year"
    ALL_TIME = "all_time"


@dataclass
class AnalyticsMetric:
    """Analytics metric data structure."""
    name: str
    value: Any
    metric_type: MetricType
    change_percentage: Optional[float] = None
    trend_direction: Optional[str] = None  # "up", "down", "stable"
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class TrendDataPoint:
    """Trend analysis data point."""
    date: datetime
    value: float
    label: str


class ContractAnalyticsEngine:
    """Main analytics engine for contract data analysis."""
    
    def __init__(self):
        self.supabase = get_supabase_client()
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
    
    @performance_service.track_request("analytics_overview")
    async def get_overview_metrics(
        self,
        workspace_id: str,
        time_range: TimeRange = TimeRange.LAST_30_DAYS
    ) -> Dict[str, Any]:
        """Get overview analytics metrics for the dashboard."""
        try:
            cache_key = f"overview_{workspace_id}_{time_range.value}"
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result
            
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = self._calculate_start_date(end_date, time_range)
            
            # Get contract data
            contracts_result = self.supabase.table("contracts").select(
                "id, status, created_at, value, type, expires_at"
            ).eq("workspace_id", workspace_id).execute()
            
            contracts = contracts_result.data or []
            
            # Calculate metrics
            metrics = {
                "total_contracts": await self._calculate_total_contracts(contracts),
                "active_contracts": await self._calculate_active_contracts(contracts),
                "contract_value": await self._calculate_contract_value(contracts),
                "expiring_soon": await self._calculate_expiring_soon(contracts),
                "status_distribution": await self._calculate_status_distribution(contracts),
                "monthly_trends": await self._calculate_monthly_trends(contracts, start_date, end_date),
                "performance_metrics": await self._calculate_performance_metrics(workspace_id, start_date, end_date),
                "risk_analysis": await self._calculate_risk_analysis(contracts)
            }
            
            # Add metadata
            result = {
                "metrics": metrics,
                "time_range": time_range.value,
                "generated_at": datetime.utcnow().isoformat(),
                "workspace_id": workspace_id
            }
            
            # Cache result
            self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error generating overview metrics: {e}")
            raise
    
    @performance_service.track_request("analytics_trends")
    async def get_trend_analysis(
        self,
        workspace_id: str,
        metric_name: str,
        time_range: TimeRange = TimeRange.LAST_90_DAYS,
        granularity: str = "daily"
    ) -> Dict[str, Any]:
        """Get detailed trend analysis for a specific metric."""
        try:
            end_date = datetime.utcnow()
            start_date = self._calculate_start_date(end_date, time_range)
            
            # Get contracts data
            contracts_result = self.supabase.table("contracts").select("*").eq("workspace_id", workspace_id).execute()
            contracts = contracts_result.data or []
            
            # Generate trend data based on metric
            trend_data = []
            
            if metric_name == "contract_creation":
                trend_data = await self._generate_creation_trend(contracts, start_date, end_date, granularity)
            elif metric_name == "contract_value":
                trend_data = await self._generate_value_trend(contracts, start_date, end_date, granularity)
            elif metric_name == "approval_time":
                trend_data = await self._generate_approval_time_trend(workspace_id, start_date, end_date, granularity)
            elif metric_name == "contract_status":
                trend_data = await self._generate_status_trend(contracts, start_date, end_date, granularity)
            
            # Calculate trend statistics
            values = [point.value for point in trend_data]
            trend_stats = {
                "average": statistics.mean(values) if values else 0,
                "median": statistics.median(values) if values else 0,
                "min": min(values) if values else 0,
                "max": max(values) if values else 0,
                "trend_direction": self._calculate_trend_direction(values),
                "volatility": statistics.stdev(values) if len(values) > 1 else 0
            }
            
            return {
                "metric_name": metric_name,
                "time_range": time_range.value,
                "granularity": granularity,
                "trend_data": [
                    {
                        "date": point.date.isoformat(),
                        "value": point.value,
                        "label": point.label
                    }
                    for point in trend_data
                ],
                "statistics": trend_stats,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating trend analysis: {e}")
            raise
    
    @performance_service.track_request("analytics_predictive")
    async def get_predictive_insights(
        self,
        workspace_id: str,
        prediction_type: str = "contract_volume"
    ) -> Dict[str, Any]:
        """Generate predictive analytics insights."""
        try:
            # Get historical data
            contracts_result = self.supabase.table("contracts").select("*").eq("workspace_id", workspace_id).execute()
            contracts = contracts_result.data or []
            
            insights = {}
            
            if prediction_type == "contract_volume":
                insights = await self._predict_contract_volume(contracts)
            elif prediction_type == "expiration_risk":
                insights = await self._predict_expiration_risk(contracts)
            elif prediction_type == "value_forecast":
                insights = await self._predict_value_forecast(contracts)
            elif prediction_type == "approval_bottlenecks":
                insights = await self._predict_approval_bottlenecks(workspace_id)
            
            return {
                "prediction_type": prediction_type,
                "insights": insights,
                "confidence_level": "medium",  # Simplified for MVP
                "generated_at": datetime.utcnow().isoformat(),
                "workspace_id": workspace_id
            }
            
        except Exception as e:
            logger.error(f"Error generating predictive insights: {e}")
            raise
    
    async def _calculate_total_contracts(self, contracts: List[Dict[str, Any]]) -> AnalyticsMetric:
        """Calculate total contracts metric."""
        total = len(contracts)
        return AnalyticsMetric(
            name="total_contracts",
            value=total,
            metric_type=MetricType.COUNT,
            metadata={"description": "Total number of contracts"}
        )
    
    async def _calculate_active_contracts(self, contracts: List[Dict[str, Any]]) -> AnalyticsMetric:
        """Calculate active contracts metric."""
        active_count = len([c for c in contracts if c.get("status") == "active"])
        total_count = len(contracts)
        percentage = (active_count / total_count * 100) if total_count > 0 else 0
        
        return AnalyticsMetric(
            name="active_contracts",
            value=active_count,
            metric_type=MetricType.COUNT,
            metadata={
                "percentage": percentage,
                "description": "Number of active contracts"
            }
        )
    
    async def _calculate_contract_value(self, contracts: List[Dict[str, Any]]) -> AnalyticsMetric:
        """Calculate total contract value metric."""
        total_value = sum(float(c.get("value", 0)) for c in contracts if c.get("value"))
        
        return AnalyticsMetric(
            name="contract_value",
            value=total_value,
            metric_type=MetricType.SUM,
            metadata={
                "currency": "USD",
                "description": "Total value of all contracts"
            }
        )
    
    async def _calculate_expiring_soon(self, contracts: List[Dict[str, Any]]) -> AnalyticsMetric:
        """Calculate contracts expiring soon metric."""
        now = datetime.utcnow()
        thirty_days_from_now = now + timedelta(days=30)
        
        expiring_count = 0
        for contract in contracts:
            if contract.get("expires_at"):
                try:
                    expires_at = datetime.fromisoformat(contract["expires_at"].replace("Z", "+00:00"))
                    if now <= expires_at <= thirty_days_from_now:
                        expiring_count += 1
                except (ValueError, TypeError):
                    continue
        
        return AnalyticsMetric(
            name="expiring_soon",
            value=expiring_count,
            metric_type=MetricType.COUNT,
            metadata={
                "timeframe": "30 days",
                "description": "Contracts expiring within 30 days"
            }
        )
    
    async def _calculate_status_distribution(self, contracts: List[Dict[str, Any]]) -> AnalyticsMetric:
        """Calculate contract status distribution."""
        status_counts = {}
        for contract in contracts:
            status = contract.get("status", "unknown")
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return AnalyticsMetric(
            name="status_distribution",
            value=status_counts,
            metric_type=MetricType.DISTRIBUTION,
            metadata={"description": "Distribution of contract statuses"}
        )
    
    async def _calculate_monthly_trends(
        self,
        contracts: List[Dict[str, Any]],
        start_date: datetime,
        end_date: datetime
    ) -> AnalyticsMetric:
        """Calculate monthly contract creation trends."""
        monthly_counts = {}
        
        for contract in contracts:
            if contract.get("created_at"):
                try:
                    created_at = datetime.fromisoformat(contract["created_at"].replace("Z", "+00:00"))
                    if start_date <= created_at <= end_date:
                        month_key = created_at.strftime("%Y-%m")
                        monthly_counts[month_key] = monthly_counts.get(month_key, 0) + 1
                except (ValueError, TypeError):
                    continue
        
        return AnalyticsMetric(
            name="monthly_trends",
            value=monthly_counts,
            metric_type=MetricType.TREND,
            metadata={"description": "Monthly contract creation trends"}
        )
    
    async def _calculate_performance_metrics(
        self,
        workspace_id: str,
        start_date: datetime,
        end_date: datetime
    ) -> AnalyticsMetric:
        """Calculate performance metrics."""
        # Get approval workflow data
        approvals_result = self.supabase.table("approval_workflows").select(
            "created_at, completed_at, status"
        ).eq("workspace_id", workspace_id).execute()
        
        approvals = approvals_result.data or []
        
        # Calculate average approval time
        approval_times = []
        for approval in approvals:
            if approval.get("created_at") and approval.get("completed_at"):
                try:
                    created = datetime.fromisoformat(approval["created_at"].replace("Z", "+00:00"))
                    completed = datetime.fromisoformat(approval["completed_at"].replace("Z", "+00:00"))
                    if start_date <= created <= end_date:
                        approval_time = (completed - created).total_seconds() / 3600  # hours
                        approval_times.append(approval_time)
                except (ValueError, TypeError):
                    continue
        
        avg_approval_time = statistics.mean(approval_times) if approval_times else 0
        
        return AnalyticsMetric(
            name="performance_metrics",
            value={
                "avg_approval_time_hours": avg_approval_time,
                "total_approvals": len(approval_times)
            },
            metric_type=MetricType.AVERAGE,
            metadata={"description": "Performance metrics for contract processing"}
        )
    
    async def _calculate_risk_analysis(self, contracts: List[Dict[str, Any]]) -> AnalyticsMetric:
        """Calculate risk analysis metrics."""
        now = datetime.utcnow()
        risk_factors = {
            "high_risk": 0,
            "medium_risk": 0,
            "low_risk": 0
        }
        
        for contract in contracts:
            risk_score = 0
            
            # Check expiration risk
            if contract.get("expires_at"):
                try:
                    expires_at = datetime.fromisoformat(contract["expires_at"].replace("Z", "+00:00"))
                    days_to_expiry = (expires_at - now).days
                    if days_to_expiry < 30:
                        risk_score += 2
                    elif days_to_expiry < 90:
                        risk_score += 1
                except (ValueError, TypeError):
                    pass
            
            # Check contract value risk
            value = float(contract.get("value", 0))
            if value > 100000:  # High value contracts
                risk_score += 1
            
            # Categorize risk
            if risk_score >= 3:
                risk_factors["high_risk"] += 1
            elif risk_score >= 1:
                risk_factors["medium_risk"] += 1
            else:
                risk_factors["low_risk"] += 1
        
        return AnalyticsMetric(
            name="risk_analysis",
            value=risk_factors,
            metric_type=MetricType.DISTRIBUTION,
            metadata={"description": "Risk analysis of contract portfolio"}
        )
    
    def _calculate_start_date(self, end_date: datetime, time_range: TimeRange) -> datetime:
        """Calculate start date based on time range."""
        if time_range == TimeRange.LAST_7_DAYS:
            return end_date - timedelta(days=7)
        elif time_range == TimeRange.LAST_30_DAYS:
            return end_date - timedelta(days=30)
        elif time_range == TimeRange.LAST_90_DAYS:
            return end_date - timedelta(days=90)
        elif time_range == TimeRange.LAST_6_MONTHS:
            return end_date - timedelta(days=180)
        elif time_range == TimeRange.LAST_YEAR:
            return end_date - timedelta(days=365)
        else:  # ALL_TIME
            return datetime(2020, 1, 1)  # Reasonable start date
    
    def _calculate_trend_direction(self, values: List[float]) -> str:
        """Calculate trend direction from values."""
        if len(values) < 2:
            return "stable"
        
        # Simple linear trend calculation
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]
        
        first_avg = statistics.mean(first_half) if first_half else 0
        second_avg = statistics.mean(second_half) if second_half else 0
        
        if second_avg > first_avg * 1.05:  # 5% threshold
            return "up"
        elif second_avg < first_avg * 0.95:
            return "down"
        else:
            return "stable"
    
    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached result if still valid."""
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if datetime.utcnow().timestamp() - timestamp < self.cache_ttl:
                return cached_data
            else:
                del self.cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache result with timestamp."""
        self.cache[cache_key] = (result, datetime.utcnow().timestamp())


    async def _generate_creation_trend(
        self,
        contracts: List[Dict[str, Any]],
        start_date: datetime,
        end_date: datetime,
        granularity: str
    ) -> List[TrendDataPoint]:
        """Generate contract creation trend data."""
        trend_data = []

        # Create date buckets based on granularity
        current_date = start_date
        while current_date <= end_date:
            if granularity == "daily":
                next_date = current_date + timedelta(days=1)
                label = current_date.strftime("%Y-%m-%d")
            elif granularity == "weekly":
                next_date = current_date + timedelta(weeks=1)
                label = f"Week of {current_date.strftime('%Y-%m-%d')}"
            else:  # monthly
                next_date = current_date.replace(month=current_date.month + 1) if current_date.month < 12 else current_date.replace(year=current_date.year + 1, month=1)
                label = current_date.strftime("%Y-%m")

            # Count contracts created in this period
            count = 0
            for contract in contracts:
                if contract.get("created_at"):
                    try:
                        created_at = datetime.fromisoformat(contract["created_at"].replace("Z", "+00:00"))
                        if current_date <= created_at < next_date:
                            count += 1
                    except (ValueError, TypeError):
                        continue

            trend_data.append(TrendDataPoint(
                date=current_date,
                value=float(count),
                label=label
            ))

            current_date = next_date

        return trend_data

    async def _generate_value_trend(
        self,
        contracts: List[Dict[str, Any]],
        start_date: datetime,
        end_date: datetime,
        granularity: str
    ) -> List[TrendDataPoint]:
        """Generate contract value trend data."""
        trend_data = []

        current_date = start_date
        while current_date <= end_date:
            if granularity == "daily":
                next_date = current_date + timedelta(days=1)
                label = current_date.strftime("%Y-%m-%d")
            elif granularity == "weekly":
                next_date = current_date + timedelta(weeks=1)
                label = f"Week of {current_date.strftime('%Y-%m-%d')}"
            else:  # monthly
                next_date = current_date.replace(month=current_date.month + 1) if current_date.month < 12 else current_date.replace(year=current_date.year + 1, month=1)
                label = current_date.strftime("%Y-%m")

            # Sum contract values created in this period
            total_value = 0
            for contract in contracts:
                if contract.get("created_at") and contract.get("value"):
                    try:
                        created_at = datetime.fromisoformat(contract["created_at"].replace("Z", "+00:00"))
                        if current_date <= created_at < next_date:
                            total_value += float(contract["value"])
                    except (ValueError, TypeError):
                        continue

            trend_data.append(TrendDataPoint(
                date=current_date,
                value=total_value,
                label=label
            ))

            current_date = next_date

        return trend_data

    async def _generate_approval_time_trend(
        self,
        workspace_id: str,
        start_date: datetime,
        end_date: datetime,
        granularity: str
    ) -> List[TrendDataPoint]:
        """Generate approval time trend data."""
        # Get approval data
        approvals_result = self.supabase.table("approval_workflows").select(
            "created_at, completed_at"
        ).eq("workspace_id", workspace_id).execute()

        approvals = approvals_result.data or []
        trend_data = []

        current_date = start_date
        while current_date <= end_date:
            if granularity == "daily":
                next_date = current_date + timedelta(days=1)
                label = current_date.strftime("%Y-%m-%d")
            elif granularity == "weekly":
                next_date = current_date + timedelta(weeks=1)
                label = f"Week of {current_date.strftime('%Y-%m-%d')}"
            else:  # monthly
                next_date = current_date.replace(month=current_date.month + 1) if current_date.month < 12 else current_date.replace(year=current_date.year + 1, month=1)
                label = current_date.strftime("%Y-%m")

            # Calculate average approval time for this period
            approval_times = []
            for approval in approvals:
                if approval.get("created_at") and approval.get("completed_at"):
                    try:
                        created_at = datetime.fromisoformat(approval["created_at"].replace("Z", "+00:00"))
                        completed_at = datetime.fromisoformat(approval["completed_at"].replace("Z", "+00:00"))
                        if current_date <= created_at < next_date:
                            approval_time = (completed_at - created_at).total_seconds() / 3600  # hours
                            approval_times.append(approval_time)
                    except (ValueError, TypeError):
                        continue

            avg_time = statistics.mean(approval_times) if approval_times else 0

            trend_data.append(TrendDataPoint(
                date=current_date,
                value=avg_time,
                label=label
            ))

            current_date = next_date

        return trend_data

    async def _generate_status_trend(
        self,
        contracts: List[Dict[str, Any]],
        start_date: datetime,
        end_date: datetime,
        granularity: str
    ) -> List[TrendDataPoint]:
        """Generate contract status trend data."""
        # For simplicity, track active contracts over time
        trend_data = []

        current_date = start_date
        while current_date <= end_date:
            if granularity == "daily":
                next_date = current_date + timedelta(days=1)
                label = current_date.strftime("%Y-%m-%d")
            elif granularity == "weekly":
                next_date = current_date + timedelta(weeks=1)
                label = f"Week of {current_date.strftime('%Y-%m-%d')}"
            else:  # monthly
                next_date = current_date.replace(month=current_date.month + 1) if current_date.month < 12 else current_date.replace(year=current_date.year + 1, month=1)
                label = current_date.strftime("%Y-%m")

            # Count active contracts at this point in time
            active_count = 0
            for contract in contracts:
                if contract.get("status") == "active" and contract.get("created_at"):
                    try:
                        created_at = datetime.fromisoformat(contract["created_at"].replace("Z", "+00:00"))
                        if created_at <= current_date:
                            active_count += 1
                    except (ValueError, TypeError):
                        continue

            trend_data.append(TrendDataPoint(
                date=current_date,
                value=float(active_count),
                label=label
            ))

            current_date = next_date

        return trend_data

    async def _predict_contract_volume(self, contracts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Predict future contract volume based on historical data."""
        # Simple prediction based on recent trends
        now = datetime.utcnow()
        last_30_days = now - timedelta(days=30)
        last_60_days = now - timedelta(days=60)

        recent_count = len([c for c in contracts if c.get("created_at") and
                          datetime.fromisoformat(c["created_at"].replace("Z", "+00:00")) >= last_30_days])

        previous_count = len([c for c in contracts if c.get("created_at") and
                            last_60_days <= datetime.fromisoformat(c["created_at"].replace("Z", "+00:00")) < last_30_days])

        # Calculate growth rate
        growth_rate = ((recent_count - previous_count) / previous_count) if previous_count > 0 else 0

        # Predict next 30 days
        predicted_volume = int(recent_count * (1 + growth_rate))

        return {
            "predicted_volume_next_30_days": predicted_volume,
            "current_30_day_volume": recent_count,
            "growth_rate": growth_rate,
            "confidence": "medium",
            "factors": ["historical_trend", "seasonal_patterns"]
        }

    async def _predict_expiration_risk(self, contracts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Predict expiration risk for contracts."""
        now = datetime.utcnow()
        risk_levels = {"high": 0, "medium": 0, "low": 0}

        for contract in contracts:
            if contract.get("expires_at") and contract.get("status") == "active":
                try:
                    expires_at = datetime.fromisoformat(contract["expires_at"].replace("Z", "+00:00"))
                    days_to_expiry = (expires_at - now).days

                    if days_to_expiry <= 30:
                        risk_levels["high"] += 1
                    elif days_to_expiry <= 90:
                        risk_levels["medium"] += 1
                    else:
                        risk_levels["low"] += 1
                except (ValueError, TypeError):
                    continue

        total_active = sum(risk_levels.values())
        risk_percentage = (risk_levels["high"] / total_active * 100) if total_active > 0 else 0

        return {
            "risk_distribution": risk_levels,
            "high_risk_percentage": risk_percentage,
            "total_active_contracts": total_active,
            "recommendation": "high" if risk_percentage > 20 else "medium" if risk_percentage > 10 else "low"
        }

    async def _predict_value_forecast(self, contracts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Predict future contract value trends."""
        # Calculate monthly value trends
        monthly_values = {}

        for contract in contracts:
            if contract.get("created_at") and contract.get("value"):
                try:
                    created_at = datetime.fromisoformat(contract["created_at"].replace("Z", "+00:00"))
                    month_key = created_at.strftime("%Y-%m")
                    value = float(contract["value"])
                    monthly_values[month_key] = monthly_values.get(month_key, 0) + value
                except (ValueError, TypeError):
                    continue

        # Simple trend analysis
        values = list(monthly_values.values())
        if len(values) >= 3:
            recent_avg = statistics.mean(values[-3:])
            overall_avg = statistics.mean(values)
            trend = "increasing" if recent_avg > overall_avg else "decreasing"
        else:
            trend = "stable"
            recent_avg = statistics.mean(values) if values else 0

        return {
            "trend": trend,
            "average_monthly_value": recent_avg,
            "predicted_next_month": recent_avg * 1.1 if trend == "increasing" else recent_avg * 0.9,
            "confidence": "medium"
        }

    async def _predict_approval_bottlenecks(self, workspace_id: str) -> Dict[str, Any]:
        """Predict potential approval bottlenecks."""
        # Get approval data
        approvals_result = self.supabase.table("approval_workflows").select("*").eq("workspace_id", workspace_id).execute()
        approvals = approvals_result.data or []

        # Analyze approval patterns
        pending_count = len([a for a in approvals if a.get("status") == "pending"])
        total_count = len(approvals)

        # Calculate average approval time
        approval_times = []
        for approval in approvals:
            if approval.get("created_at") and approval.get("completed_at"):
                try:
                    created = datetime.fromisoformat(approval["created_at"].replace("Z", "+00:00"))
                    completed = datetime.fromisoformat(approval["completed_at"].replace("Z", "+00:00"))
                    approval_time = (completed - created).total_seconds() / 3600  # hours
                    approval_times.append(approval_time)
                except (ValueError, TypeError):
                    continue

        avg_approval_time = statistics.mean(approval_times) if approval_times else 0
        bottleneck_risk = "high" if pending_count > 10 or avg_approval_time > 48 else "low"

        return {
            "pending_approvals": pending_count,
            "average_approval_time_hours": avg_approval_time,
            "bottleneck_risk": bottleneck_risk,
            "recommendation": "Review approval processes" if bottleneck_risk == "high" else "Current process is efficient"
        }


# Global analytics engine instance
analytics_engine = ContractAnalyticsEngine()
