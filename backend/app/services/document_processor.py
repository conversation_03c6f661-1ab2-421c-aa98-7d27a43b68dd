"""
Document Processing Service for Averum Contracts
Handles text extraction, preprocessing, chunking, and validation for various document formats
"""

import asyncio
import hashlib
import logging
import re
import tempfile
import os
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple, Union
from pathlib import Path
import mimetypes

# PDF processing
try:
    import PyPDF2
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

# DOCX processing
try:
    from docx import Document as DocxDocument
    import python_docx
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

# Text processing
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

from pydantic import BaseModel
from app.core.config import settings

logger = logging.getLogger(__name__)


class DocumentMetadata(BaseModel):
    """Document metadata structure."""
    file_name: str
    file_size: int
    file_type: str
    mime_type: str
    page_count: Optional[int] = None
    word_count: Optional[int] = None
    character_count: Optional[int] = None
    language: Optional[str] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    author: Optional[str] = None
    title: Optional[str] = None
    subject: Optional[str] = None


class TextChunk(BaseModel):
    """Text chunk structure for processing large documents."""
    chunk_id: str
    content: str
    start_position: int
    end_position: int
    word_count: int
    overlap_with_previous: int = 0
    overlap_with_next: int = 0
    metadata: Dict[str, Any] = {}


class ProcessedDocument(BaseModel):
    """Processed document structure."""
    original_text: str
    cleaned_text: str
    chunks: List[TextChunk]
    metadata: DocumentMetadata
    extracted_entities: List[Dict[str, Any]] = []
    sections: List[Dict[str, Any]] = []
    processing_time: float
    processing_errors: List[str] = []


class DocumentProcessingError(Exception):
    """Custom exception for document processing errors."""
    pass


class DocumentProcessor:
    """Main document processing service."""
    
    def __init__(self):
        self.max_file_size = settings.MAX_FILE_SIZE if hasattr(settings, 'MAX_FILE_SIZE') else 50 * 1024 * 1024  # 50MB
        self.chunk_size = settings.CHUNK_SIZE if hasattr(settings, 'CHUNK_SIZE') else 1000  # words
        self.chunk_overlap = settings.CHUNK_OVERLAP if hasattr(settings, 'CHUNK_OVERLAP') else 100  # words
        self.supported_formats = ['pdf', 'docx', 'doc', 'txt', 'html', 'rtf']
        
        # Initialize NLP components
        self.lemmatizer = WordNetLemmatizer()
        self.stop_words = set(stopwords.words('english'))
        
        # Validation patterns
        self.validation_patterns = {
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'phone': re.compile(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'),
            'date': re.compile(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b'),
            'currency': re.compile(r'\$\d{1,3}(?:,\d{3})*(?:\.\d{2})?'),
            'ssn': re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
            'contract_number': re.compile(r'\b(?:contract|agreement)\s*#?\s*([A-Z0-9-]+)\b', re.IGNORECASE)
        }
    
    async def process_document(
        self,
        file_path: str,
        file_name: str,
        extract_entities: bool = True,
        chunk_document: bool = True,
        validate_content: bool = True
    ) -> ProcessedDocument:
        """
        Process a document file and extract structured information.
        
        Args:
            file_path: Path to the document file
            file_name: Original file name
            extract_entities: Whether to extract named entities
            chunk_document: Whether to chunk the document for processing
            validate_content: Whether to validate document content
            
        Returns:
            ProcessedDocument with extracted and processed information
        """
        start_time = datetime.now()
        processing_errors = []
        
        try:
            # Validate file
            await self._validate_file(file_path, file_name)
            
            # Extract text based on file type
            raw_text, metadata = await self._extract_text(file_path, file_name)
            
            # Clean and preprocess text
            cleaned_text = await self._clean_text(raw_text)
            
            # Validate content if requested
            if validate_content:
                validation_errors = await self._validate_content(cleaned_text)
                processing_errors.extend(validation_errors)
            
            # Extract entities if requested
            entities = []
            if extract_entities:
                entities = await self._extract_entities(cleaned_text)
            
            # Extract sections
            sections = await self._extract_sections(cleaned_text)
            
            # Chunk document if requested
            chunks = []
            if chunk_document:
                chunks = await self._chunk_document(cleaned_text)
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return ProcessedDocument(
                original_text=raw_text,
                cleaned_text=cleaned_text,
                chunks=chunks,
                metadata=metadata,
                extracted_entities=entities,
                sections=sections,
                processing_time=processing_time,
                processing_errors=processing_errors
            )
            
        except Exception as e:
            logger.error(f"Error processing document {file_name}: {e}")
            raise DocumentProcessingError(f"Failed to process document: {str(e)}")

    async def process_text(
        self,
        text: str,
        file_name: str = "text_input",
        extract_entities: bool = True,
        chunk_document: bool = True,
        validate_content: bool = True
    ) -> ProcessedDocument:
        """
        Process raw text input.
        
        Args:
            text: Raw text content
            file_name: Name for the text input
            extract_entities: Whether to extract named entities
            chunk_document: Whether to chunk the text
            validate_content: Whether to validate content
            
        Returns:
            ProcessedDocument with processed information
        """
        start_time = datetime.now()
        processing_errors = []
        
        try:
            # Create metadata for text input
            metadata = DocumentMetadata(
                file_name=file_name,
                file_size=len(text.encode('utf-8')),
                file_type='txt',
                mime_type='text/plain',
                word_count=len(text.split()),
                character_count=len(text),
                created_at=datetime.now()
            )
            
            # Clean and preprocess text
            cleaned_text = await self._clean_text(text)
            
            # Validate content if requested
            if validate_content:
                validation_errors = await self._validate_content(cleaned_text)
                processing_errors.extend(validation_errors)
            
            # Extract entities if requested
            entities = []
            if extract_entities:
                entities = await self._extract_entities(cleaned_text)
            
            # Extract sections
            sections = await self._extract_sections(cleaned_text)
            
            # Chunk document if requested
            chunks = []
            if chunk_document:
                chunks = await self._chunk_document(cleaned_text)
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return ProcessedDocument(
                original_text=text,
                cleaned_text=cleaned_text,
                chunks=chunks,
                metadata=metadata,
                extracted_entities=entities,
                sections=sections,
                processing_time=processing_time,
                processing_errors=processing_errors
            )
            
        except Exception as e:
            logger.error(f"Error processing text: {e}")
            raise DocumentProcessingError(f"Failed to process text: {str(e)}")

    async def _validate_file(self, file_path: str, file_name: str) -> None:
        """Validate file before processing."""
        if not os.path.exists(file_path):
            raise DocumentProcessingError(f"File not found: {file_path}")

        # Check file size
        file_size = os.path.getsize(file_path)
        if file_size > self.max_file_size:
            raise DocumentProcessingError(f"File too large: {file_size} bytes (max: {self.max_file_size})")

        if file_size == 0:
            raise DocumentProcessingError("File is empty")

        # Check file extension
        file_ext = Path(file_name).suffix.lower().lstrip('.')
        if file_ext not in self.supported_formats:
            raise DocumentProcessingError(f"Unsupported file format: {file_ext}")

        # Check MIME type
        mime_type, _ = mimetypes.guess_type(file_name)
        if mime_type and not self._is_supported_mime_type(mime_type):
            logger.warning(f"Potentially unsupported MIME type: {mime_type}")

    def _is_supported_mime_type(self, mime_type: str) -> bool:
        """Check if MIME type is supported."""
        supported_mime_types = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'text/plain',
            'text/html',
            'application/rtf'
        ]
        return mime_type in supported_mime_types

    async def _extract_text(self, file_path: str, file_name: str) -> Tuple[str, DocumentMetadata]:
        """Extract text from document based on file type."""
        file_ext = Path(file_name).suffix.lower().lstrip('.')
        file_size = os.path.getsize(file_path)
        mime_type, _ = mimetypes.guess_type(file_name)

        # Create base metadata
        metadata = DocumentMetadata(
            file_name=file_name,
            file_size=file_size,
            file_type=file_ext,
            mime_type=mime_type or 'application/octet-stream'
        )

        try:
            if file_ext == 'pdf':
                return await self._extract_pdf_text(file_path, metadata)
            elif file_ext in ['docx', 'doc']:
                return await self._extract_docx_text(file_path, metadata)
            elif file_ext == 'txt':
                return await self._extract_txt_text(file_path, metadata)
            elif file_ext == 'html':
                return await self._extract_html_text(file_path, metadata)
            elif file_ext == 'rtf':
                return await self._extract_rtf_text(file_path, metadata)
            else:
                # Fallback to text extraction
                return await self._extract_txt_text(file_path, metadata)

        except Exception as e:
            logger.error(f"Error extracting text from {file_name}: {e}")
            raise DocumentProcessingError(f"Failed to extract text: {str(e)}")

    async def _extract_pdf_text(self, file_path: str, metadata: DocumentMetadata) -> Tuple[str, DocumentMetadata]:
        """Extract text from PDF file."""
        if not PDF_AVAILABLE:
            raise DocumentProcessingError("PDF processing libraries not available")

        text_content = []
        page_count = 0

        try:
            # Try pdfplumber first (better text extraction)
            with pdfplumber.open(file_path) as pdf:
                page_count = len(pdf.pages)
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_content.append(page_text)

                # Extract metadata if available
                if pdf.metadata:
                    metadata.title = pdf.metadata.get('Title')
                    metadata.author = pdf.metadata.get('Author')
                    metadata.subject = pdf.metadata.get('Subject')
                    if pdf.metadata.get('CreationDate'):
                        metadata.created_at = pdf.metadata['CreationDate']
                    if pdf.metadata.get('ModDate'):
                        metadata.modified_at = pdf.metadata['ModDate']

        except Exception as e:
            logger.warning(f"pdfplumber failed, trying PyPDF2: {e}")
            # Fallback to PyPDF2
            try:
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    page_count = len(pdf_reader.pages)

                    for page in pdf_reader.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text_content.append(page_text)

                    # Extract metadata
                    if pdf_reader.metadata:
                        metadata.title = pdf_reader.metadata.get('/Title')
                        metadata.author = pdf_reader.metadata.get('/Author')
                        metadata.subject = pdf_reader.metadata.get('/Subject')
                        if pdf_reader.metadata.get('/CreationDate'):
                            metadata.created_at = pdf_reader.metadata['/CreationDate']
                        if pdf_reader.metadata.get('/ModDate'):
                            metadata.modified_at = pdf_reader.metadata['/ModDate']

            except Exception as e2:
                logger.error(f"Both PDF extraction methods failed: {e2}")
                raise DocumentProcessingError(f"Failed to extract PDF text: {str(e2)}")

        metadata.page_count = page_count
        full_text = '\n\n'.join(text_content)
        metadata.word_count = len(full_text.split())
        metadata.character_count = len(full_text)

        return full_text, metadata

    async def _extract_docx_text(self, file_path: str, metadata: DocumentMetadata) -> Tuple[str, DocumentMetadata]:
        """Extract text from DOCX file."""
        if not DOCX_AVAILABLE:
            raise DocumentProcessingError("DOCX processing libraries not available")

        try:
            doc = DocxDocument(file_path)

            # Extract text from paragraphs
            text_content = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(' | '.join(row_text))

            full_text = '\n\n'.join(text_content)

            # Extract metadata from document properties
            if hasattr(doc, 'core_properties'):
                props = doc.core_properties
                metadata.title = props.title
                metadata.author = props.author
                metadata.subject = props.subject
                metadata.created_at = props.created
                metadata.modified_at = props.modified

            metadata.word_count = len(full_text.split())
            metadata.character_count = len(full_text)

            return full_text, metadata

        except Exception as e:
            logger.error(f"Error extracting DOCX text: {e}")
            raise DocumentProcessingError(f"Failed to extract DOCX text: {str(e)}")

    async def _extract_txt_text(self, file_path: str, metadata: DocumentMetadata) -> Tuple[str, DocumentMetadata]:
        """Extract text from plain text file."""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        text = file.read()
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise DocumentProcessingError("Could not decode text file with any supported encoding")

            metadata.word_count = len(text.split())
            metadata.character_count = len(text)

            return text, metadata

        except Exception as e:
            logger.error(f"Error extracting text file: {e}")
            raise DocumentProcessingError(f"Failed to extract text: {str(e)}")

    async def _extract_html_text(self, file_path: str, metadata: DocumentMetadata) -> Tuple[str, DocumentMetadata]:
        """Extract text from HTML file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()

            # Simple HTML tag removal (for production, use BeautifulSoup)
            import re
            text = re.sub(r'<[^>]+>', '', html_content)
            text = re.sub(r'\s+', ' ', text).strip()

            metadata.word_count = len(text.split())
            metadata.character_count = len(text)

            return text, metadata

        except Exception as e:
            logger.error(f"Error extracting HTML text: {e}")
            raise DocumentProcessingError(f"Failed to extract HTML text: {str(e)}")

    async def _extract_rtf_text(self, file_path: str, metadata: DocumentMetadata) -> Tuple[str, DocumentMetadata]:
        """Extract text from RTF file."""
        try:
            # Basic RTF text extraction (for production, use striprtf library)
            with open(file_path, 'r', encoding='utf-8') as file:
                rtf_content = file.read()

            # Simple RTF control word removal
            import re
            text = re.sub(r'\\[a-z]+\d*\s?', '', rtf_content)
            text = re.sub(r'[{}]', '', text)
            text = re.sub(r'\s+', ' ', text).strip()

            metadata.word_count = len(text.split())
            metadata.character_count = len(text)

            return text, metadata

        except Exception as e:
            logger.error(f"Error extracting RTF text: {e}")
            raise DocumentProcessingError(f"Failed to extract RTF text: {str(e)}")

    async def _clean_text(self, text: str) -> str:
        """Clean and preprocess text."""
        if not text:
            return ""

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove control characters
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # Normalize line endings
        text = text.replace('\r\n', '\n').replace('\r', '\n')

        # Remove excessive newlines
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)

        # Strip leading/trailing whitespace
        text = text.strip()

        return text

    async def _validate_content(self, text: str) -> List[str]:
        """Validate document content and return list of issues."""
        errors = []

        if not text or len(text.strip()) == 0:
            errors.append("Document appears to be empty")
            return errors

        # Check for minimum content length
        if len(text) < 100:
            errors.append("Document content is very short (less than 100 characters)")

        # Check for potential OCR errors (too many single characters)
        words = text.split()
        single_chars = sum(1 for word in words if len(word) == 1 and word.isalpha())
        if len(words) > 0 and single_chars / len(words) > 0.1:
            errors.append("Document may contain OCR errors (many single characters)")

        # Check for suspicious patterns
        if re.search(r'[^\x00-\x7F]{10,}', text):
            errors.append("Document contains non-ASCII characters that may indicate encoding issues")

        # Check for PII that should be redacted
        pii_patterns = {
            'SSN': self.validation_patterns['ssn'],
            'Email': self.validation_patterns['email'],
            'Phone': self.validation_patterns['phone']
        }

        for pii_type, pattern in pii_patterns.items():
            matches = pattern.findall(text)
            if matches:
                errors.append(f"Document contains {len(matches)} {pii_type} instances that may need redaction")

        return errors

    async def _extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract named entities from text."""
        entities = []

        for entity_type, pattern in self.validation_patterns.items():
            matches = pattern.finditer(text)
            for match in matches:
                entities.append({
                    'type': entity_type,
                    'value': match.group(),
                    'start_position': match.start(),
                    'end_position': match.end(),
                    'confidence': 0.8  # Basic pattern matching confidence
                })

        # Extract potential party names (basic implementation)
        party_patterns = [
            r'\b([A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b',  # Person names
            r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:Inc|LLC|Corp|Corporation|Ltd|Limited)\.?)\b'  # Company names
        ]

        for pattern in party_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                entities.append({
                    'type': 'party',
                    'value': match.group(1),
                    'start_position': match.start(),
                    'end_position': match.end(),
                    'confidence': 0.6
                })

        return entities

    async def _extract_sections(self, text: str) -> List[Dict[str, Any]]:
        """Extract document sections."""
        sections = []

        # Common section patterns for legal documents
        section_patterns = [
            r'(?:^|\n)\s*(\d+\.\s+[A-Z][A-Za-z\s]+)(?:\n|$)',  # "1. Introduction"
            r'(?:^|\n)\s*(Article\s+\d+[\.:]\s*[A-Z][A-Za-z\s]+)(?:\n|$)',  # "Article 1: Definitions"
            r'(?:^|\n)\s*([A-Z][A-Z\s]{3,})(?:\n|$)',  # "INTRODUCTION"
            r'(?:^|\n)\s*(Section\s+\d+[\.:]\s*[A-Z][A-Za-z\s]+)(?:\n|$)',  # "Section 1: Overview"
        ]

        section_id = 1
        for pattern in section_patterns:
            matches = re.finditer(pattern, text, re.MULTILINE)
            for match in matches:
                title = match.group(1).strip()
                start_pos = match.start()

                # Find the end of this section (start of next section or end of text)
                end_pos = len(text)
                for end_pattern in section_patterns:
                    next_matches = re.finditer(end_pattern, text[start_pos + len(match.group()):], re.MULTILINE)
                    for next_match in next_matches:
                        potential_end = start_pos + len(match.group()) + next_match.start()
                        if potential_end < end_pos:
                            end_pos = potential_end
                        break

                content = text[start_pos:end_pos].strip()

                sections.append({
                    'id': f'section_{section_id}',
                    'title': title,
                    'content': content,
                    'start_position': start_pos,
                    'end_position': end_pos,
                    'word_count': len(content.split()),
                    'level': 1  # Basic level detection
                })
                section_id += 1

        return sections

    async def _chunk_document(self, text: str) -> List[TextChunk]:
        """Chunk document into smaller pieces for processing."""
        if not text:
            return []

        # Split text into sentences
        sentences = sent_tokenize(text)
        if not sentences:
            return []

        chunks = []
        current_chunk = []
        current_word_count = 0
        chunk_id = 1
        start_position = 0

        for sentence in sentences:
            sentence_words = len(sentence.split())

            # If adding this sentence would exceed chunk size, create a new chunk
            if current_word_count + sentence_words > self.chunk_size and current_chunk:
                # Create chunk with overlap
                chunk_text = ' '.join(current_chunk)
                end_position = start_position + len(chunk_text)

                chunks.append(TextChunk(
                    chunk_id=f'chunk_{chunk_id}',
                    content=chunk_text,
                    start_position=start_position,
                    end_position=end_position,
                    word_count=current_word_count,
                    overlap_with_previous=self._calculate_overlap(chunks, chunk_text) if chunks else 0,
                    metadata={'sentence_count': len(current_chunk)}
                ))

                # Start new chunk with overlap
                overlap_sentences = current_chunk[-self.chunk_overlap//50:] if self.chunk_overlap > 0 else []
                current_chunk = overlap_sentences + [sentence]
                current_word_count = sum(len(s.split()) for s in current_chunk)
                start_position = end_position - sum(len(s) for s in overlap_sentences)
                chunk_id += 1
            else:
                current_chunk.append(sentence)
                current_word_count += sentence_words

        # Add the last chunk if it has content
        if current_chunk:
            chunk_text = ' '.join(current_chunk)
            end_position = start_position + len(chunk_text)

            chunks.append(TextChunk(
                chunk_id=f'chunk_{chunk_id}',
                content=chunk_text,
                start_position=start_position,
                end_position=end_position,
                word_count=current_word_count,
                overlap_with_previous=self._calculate_overlap(chunks, chunk_text) if chunks else 0,
                metadata={'sentence_count': len(current_chunk)}
            ))

        # Set overlap_with_next for all chunks except the last
        for i in range(len(chunks) - 1):
            chunks[i].overlap_with_next = self._calculate_overlap([chunks[i]], chunks[i + 1].content)

        return chunks

    def _calculate_overlap(self, existing_chunks: List[TextChunk], new_content: str) -> int:
        """Calculate word overlap between chunks."""
        if not existing_chunks:
            return 0

        last_chunk = existing_chunks[-1]
        last_words = set(last_chunk.content.lower().split())
        new_words = set(new_content.lower().split())

        overlap = len(last_words.intersection(new_words))
        return overlap


# Global document processor instance
document_processor = DocumentProcessor()
