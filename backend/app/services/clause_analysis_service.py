"""
Advanced Clause Analysis Service for Averum Contracts
Provides comprehensive clause extraction, missing clause detection, and risk assessment
"""

import re
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass

from pydantic import BaseModel

logger = logging.getLogger(__name__)


@dataclass
class ClausePattern:
    """Pattern for detecting specific clause types."""
    name: str
    category: str
    patterns: List[str]
    importance: str
    risk_level: str
    description: str


class ExtractedClause(BaseModel):
    """Extracted clause with metadata."""
    type: str
    title: str
    content: str
    importance: str
    risk_level: str
    confidence: float
    position: int
    start_char: int
    end_char: int
    category: str
    keywords: List[str]
    risk_factors: List[str]


class MissingClause(BaseModel):
    """Missing clause information."""
    type: str
    title: str
    importance: str
    risk_level: str
    description: str
    recommendation: str
    category: str


class ClauseRiskAssessment(BaseModel):
    """Risk assessment for a clause."""
    clause_type: str
    risk_score: float
    risk_factors: List[str]
    mitigation_suggestions: List[str]
    compliance_issues: List[str]
    severity: str


class ClauseAnalysisService:
    """Advanced clause analysis and risk assessment service."""
    
    def __init__(self):
        self.clause_patterns = self._initialize_clause_patterns()
        self.standard_clauses = self._get_standard_clauses()
        self.risk_keywords = self._initialize_risk_keywords()
    
    def _initialize_clause_patterns(self) -> List[ClausePattern]:
        """Initialize patterns for detecting different clause types."""
        return [
            ClausePattern(
                name="termination",
                category="contract_lifecycle",
                patterns=[
                    r"(?i)(?:termination|terminate|end|expir[ey]|dissolution).*(?:agreement|contract)",
                    r"(?i)(?:either party|party).*(?:may|shall).*(?:terminate|end)",
                    r"(?i)(?:notice of termination|termination notice)",
                    r"(?i)(?:breach|default).*(?:terminate|termination)"
                ],
                importance="critical",
                risk_level="high",
                description="Defines conditions and procedures for contract termination"
            ),
            ClausePattern(
                name="confidentiality",
                category="data_protection",
                patterns=[
                    r"(?i)(?:confidential|proprietary|trade secret).*(?:information|data)",
                    r"(?i)(?:non-disclosure|nda)",
                    r"(?i)(?:shall not|will not).*(?:disclose|reveal|share)",
                    r"(?i)(?:confidentiality|secrecy).*(?:obligation|duty|agreement)"
                ],
                importance="high",
                risk_level="medium",
                description="Protects confidential information and trade secrets"
            ),
            ClausePattern(
                name="liability",
                category="risk_management",
                patterns=[
                    r"(?i)(?:liability|liable|responsible).*(?:damages|loss|harm)",
                    r"(?i)(?:limitation of liability|liability limitation)",
                    r"(?i)(?:indemnif|hold harmless)",
                    r"(?i)(?:consequential|indirect|incidental).*damages"
                ],
                importance="critical",
                risk_level="high",
                description="Defines liability limits and indemnification terms"
            ),
            ClausePattern(
                name="intellectual_property",
                category="ip_rights",
                patterns=[
                    r"(?i)(?:intellectual property|ip rights?)",
                    r"(?i)(?:copyright|trademark|patent|trade secret)",
                    r"(?i)(?:ownership|title).*(?:work|invention|creation)",
                    r"(?i)(?:work for hire|work made for hire)"
                ],
                importance="high",
                risk_level="medium",
                description="Defines intellectual property ownership and rights"
            ),
            ClausePattern(
                name="payment",
                category="financial",
                patterns=[
                    r"(?i)(?:payment|compensation|fee|salary|wage)",
                    r"(?i)(?:invoice|billing|due date)",
                    r"(?i)(?:late payment|penalty|interest)",
                    r"(?i)\$[\d,]+(?:\.\d{2})?"
                ],
                importance="high",
                risk_level="medium",
                description="Defines payment terms and conditions"
            ),
            ClausePattern(
                name="force_majeure",
                category="risk_management",
                patterns=[
                    r"(?i)force majeure",
                    r"(?i)(?:act of god|natural disaster)",
                    r"(?i)(?:unforeseeable|beyond.*control)",
                    r"(?i)(?:war|terrorism|pandemic|epidemic)"
                ],
                importance="medium",
                risk_level="low",
                description="Addresses unforeseeable circumstances"
            ),
            ClausePattern(
                name="governing_law",
                category="legal_framework",
                patterns=[
                    r"(?i)(?:governing law|applicable law)",
                    r"(?i)(?:jurisdiction|venue)",
                    r"(?i)(?:courts? of|state of|laws? of)",
                    r"(?i)(?:dispute resolution|arbitration)"
                ],
                importance="medium",
                risk_level="low",
                description="Defines applicable law and jurisdiction"
            ),
            ClausePattern(
                name="data_protection",
                category="compliance",
                patterns=[
                    r"(?i)(?:data protection|privacy|gdpr|ccpa)",
                    r"(?i)(?:personal data|personal information)",
                    r"(?i)(?:data processing|data controller)",
                    r"(?i)(?:consent|data subject rights)"
                ],
                importance="high",
                risk_level="high",
                description="Addresses data protection and privacy requirements"
            ),
            ClausePattern(
                name="non_compete",
                category="employment",
                patterns=[
                    r"(?i)(?:non-compete|non compete|restraint of trade)",
                    r"(?i)(?:shall not|will not).*(?:compete|solicit)",
                    r"(?i)(?:competitive business|competing)",
                    r"(?i)(?:restriction|covenant).*(?:employment|business)"
                ],
                importance="high",
                risk_level="high",
                description="Restricts competitive activities"
            ),
            ClausePattern(
                name="warranty",
                category="quality_assurance",
                patterns=[
                    r"(?i)(?:warrant|warranty|guarantee)",
                    r"(?i)(?:represent|representation)",
                    r"(?i)(?:as is|without warranty)",
                    r"(?i)(?:fitness for purpose|merchantability)"
                ],
                importance="medium",
                risk_level="medium",
                description="Defines warranties and representations"
            )
        ]
    
    def _get_standard_clauses(self) -> Dict[str, Dict[str, Any]]:
        """Get standard clauses that should be present in contracts."""
        return {
            "termination": {
                "importance": "critical",
                "risk_if_missing": "high",
                "description": "Without termination clauses, parties may be locked into indefinite agreements"
            },
            "liability": {
                "importance": "critical", 
                "risk_if_missing": "high",
                "description": "Missing liability clauses can expose parties to unlimited damages"
            },
            "confidentiality": {
                "importance": "high",
                "risk_if_missing": "medium",
                "description": "Lack of confidentiality protection may lead to information disclosure"
            },
            "governing_law": {
                "importance": "medium",
                "risk_if_missing": "medium",
                "description": "Unclear jurisdiction can complicate dispute resolution"
            },
            "force_majeure": {
                "importance": "medium",
                "risk_if_missing": "low",
                "description": "May limit protection against unforeseeable events"
            }
        }
    
    def _initialize_risk_keywords(self) -> Dict[str, List[str]]:
        """Initialize risk-indicating keywords."""
        return {
            "high_risk": [
                "unlimited liability", "personal guarantee", "liquidated damages",
                "automatic renewal", "non-compete", "exclusive", "irrevocable",
                "waive", "waiver", "indemnify", "hold harmless"
            ],
            "medium_risk": [
                "penalty", "late fee", "interest", "breach", "default",
                "confidential", "proprietary", "assignment", "delegation"
            ],
            "compliance_risk": [
                "gdpr", "hipaa", "sox", "pci", "data protection", "privacy",
                "personal data", "sensitive information"
            ]
        }
    
    async def extract_clauses(self, contract_text: str) -> List[ExtractedClause]:
        """Extract clauses from contract text using pattern matching and NLP."""
        extracted_clauses = []
        
        # Split text into sentences for better analysis
        sentences = self._split_into_sentences(contract_text)
        
        for pattern in self.clause_patterns:
            clause_matches = self._find_clause_matches(contract_text, pattern, sentences)
            extracted_clauses.extend(clause_matches)
        
        # Remove duplicates and merge overlapping clauses
        extracted_clauses = self._deduplicate_clauses(extracted_clauses)
        
        return extracted_clauses
    
    def _split_into_sentences(self, text: str) -> List[Tuple[str, int, int]]:
        """Split text into sentences with position information."""
        sentences = []
        # Simple sentence splitting - in production, use NLTK or spaCy
        sentence_pattern = r'[.!?]+\s+'
        parts = re.split(sentence_pattern, text)
        
        position = 0
        for part in parts:
            if part.strip():
                start = text.find(part, position)
                end = start + len(part)
                sentences.append((part.strip(), start, end))
                position = end
        
        return sentences
    
    def _find_clause_matches(
        self, 
        text: str, 
        pattern: ClausePattern, 
        sentences: List[Tuple[str, int, int]]
    ) -> List[ExtractedClause]:
        """Find matches for a specific clause pattern."""
        matches = []
        
        for regex_pattern in pattern.patterns:
            for sentence, start_pos, end_pos in sentences:
                if re.search(regex_pattern, sentence):
                    # Extract surrounding context
                    context = self._extract_context(text, start_pos, end_pos)
                    
                    # Calculate confidence based on pattern strength
                    confidence = self._calculate_confidence(sentence, pattern)
                    
                    # Identify risk factors
                    risk_factors = self._identify_risk_factors(context, pattern)
                    
                    clause = ExtractedClause(
                        type=pattern.name,
                        title=f"{pattern.name.replace('_', ' ').title()} Clause",
                        content=context,
                        importance=pattern.importance,
                        risk_level=pattern.risk_level,
                        confidence=confidence,
                        position=len(matches) + 1,
                        start_char=start_pos,
                        end_char=end_pos,
                        category=pattern.category,
                        keywords=self._extract_keywords(sentence, pattern),
                        risk_factors=risk_factors
                    )
                    
                    matches.append(clause)
                    break  # Avoid multiple matches for the same pattern in the same sentence
        
        return matches
    
    def _extract_context(self, text: str, start_pos: int, end_pos: int, context_size: int = 500) -> str:
        """Extract context around a clause match."""
        context_start = max(0, start_pos - context_size)
        context_end = min(len(text), end_pos + context_size)
        return text[context_start:context_end].strip()
    
    def _calculate_confidence(self, sentence: str, pattern: ClausePattern) -> float:
        """Calculate confidence score for clause detection."""
        base_confidence = 0.6
        
        # Boost confidence for exact keyword matches
        exact_keywords = {
            "termination": ["termination", "terminate"],
            "confidentiality": ["confidential", "non-disclosure"],
            "liability": ["liability", "liable", "indemnify"],
            "intellectual_property": ["intellectual property", "copyright", "patent"],
            "payment": ["payment", "compensation", "invoice"],
            "force_majeure": ["force majeure", "act of god"],
            "governing_law": ["governing law", "jurisdiction"],
            "data_protection": ["gdpr", "data protection", "privacy"],
            "non_compete": ["non-compete", "non compete"],
            "warranty": ["warranty", "warrant", "guarantee"]
        }
        
        if pattern.name in exact_keywords:
            for keyword in exact_keywords[pattern.name]:
                if keyword.lower() in sentence.lower():
                    base_confidence += 0.2
                    break
        
        return min(1.0, base_confidence)
    
    def _identify_risk_factors(self, context: str, pattern: ClausePattern) -> List[str]:
        """Identify risk factors in clause context."""
        risk_factors = []
        context_lower = context.lower()
        
        # Check for high-risk keywords
        for risk_keyword in self.risk_keywords["high_risk"]:
            if risk_keyword in context_lower:
                risk_factors.append(f"Contains high-risk term: {risk_keyword}")
        
        # Check for compliance-related risks
        for compliance_keyword in self.risk_keywords["compliance_risk"]:
            if compliance_keyword in context_lower:
                risk_factors.append(f"Compliance consideration: {compliance_keyword}")
        
        # Pattern-specific risk checks
        if pattern.name == "liability" and "unlimited" in context_lower:
            risk_factors.append("Unlimited liability exposure")
        
        if pattern.name == "termination" and "without cause" in context_lower:
            risk_factors.append("Termination without cause allowed")
        
        return risk_factors
    
    def _extract_keywords(self, sentence: str, pattern: ClausePattern) -> List[str]:
        """Extract relevant keywords from sentence."""
        keywords = []
        sentence_lower = sentence.lower()
        
        # Extract words that match pattern themes
        keyword_patterns = {
            "termination": ["terminate", "end", "expire", "dissolution", "breach"],
            "confidentiality": ["confidential", "proprietary", "secret", "disclose"],
            "liability": ["liable", "damages", "indemnify", "responsible"],
            "payment": ["payment", "fee", "invoice", "compensation", "due"]
        }
        
        if pattern.name in keyword_patterns:
            for keyword in keyword_patterns[pattern.name]:
                if keyword in sentence_lower:
                    keywords.append(keyword)
        
        return keywords
    
    def _deduplicate_clauses(self, clauses: List[ExtractedClause]) -> List[ExtractedClause]:
        """Remove duplicate and overlapping clauses."""
        if not clauses:
            return []
        
        # Sort by position
        clauses.sort(key=lambda x: x.start_char)
        
        deduplicated = []
        for clause in clauses:
            # Check for overlap with existing clauses
            is_duplicate = False
            for existing in deduplicated:
                if (clause.type == existing.type and 
                    abs(clause.start_char - existing.start_char) < 100):
                    # Keep the one with higher confidence
                    if clause.confidence > existing.confidence:
                        deduplicated.remove(existing)
                        deduplicated.append(clause)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                deduplicated.append(clause)
        
        return deduplicated

    async def detect_missing_clauses(self, contract_text: str, extracted_clauses: List[ExtractedClause]) -> List[MissingClause]:
        """Detect missing standard clauses that should be present."""
        missing_clauses = []

        # Get types of extracted clauses
        extracted_types = {clause.type for clause in extracted_clauses}

        # Check for missing standard clauses
        for clause_type, clause_info in self.standard_clauses.items():
            if clause_type not in extracted_types:
                missing_clause = MissingClause(
                    type=clause_type,
                    title=f"{clause_type.replace('_', ' ').title()} Clause",
                    importance=clause_info["importance"],
                    risk_level=clause_info["risk_if_missing"],
                    description=clause_info["description"],
                    recommendation=self._get_missing_clause_recommendation(clause_type),
                    category=self._get_clause_category(clause_type)
                )
                missing_clauses.append(missing_clause)

        # Check for contract-type specific missing clauses
        contract_type = self._detect_contract_type(contract_text)
        type_specific_missing = self._get_contract_type_missing_clauses(contract_type, extracted_types)
        missing_clauses.extend(type_specific_missing)

        return missing_clauses

    def _get_missing_clause_recommendation(self, clause_type: str) -> str:
        """Get recommendation for missing clause."""
        recommendations = {
            "termination": "Add clear termination conditions, notice periods, and post-termination obligations",
            "liability": "Include liability limitations and indemnification provisions to protect against excessive damages",
            "confidentiality": "Add confidentiality provisions to protect sensitive information shared during the relationship",
            "governing_law": "Specify governing law and jurisdiction to avoid disputes over applicable legal framework",
            "force_majeure": "Consider adding force majeure clause to address unforeseeable circumstances",
            "intellectual_property": "Define IP ownership and usage rights to prevent future disputes",
            "payment": "Specify payment terms, due dates, and late payment penalties",
            "data_protection": "Add data protection clauses to ensure compliance with privacy regulations",
            "warranty": "Include appropriate warranties and disclaimers for the goods/services provided"
        }
        return recommendations.get(clause_type, f"Consider adding {clause_type.replace('_', ' ')} provisions")

    def _get_clause_category(self, clause_type: str) -> str:
        """Get category for clause type."""
        categories = {
            "termination": "contract_lifecycle",
            "liability": "risk_management",
            "confidentiality": "data_protection",
            "governing_law": "legal_framework",
            "force_majeure": "risk_management",
            "intellectual_property": "ip_rights",
            "payment": "financial",
            "data_protection": "compliance",
            "warranty": "quality_assurance"
        }
        return categories.get(clause_type, "general")

    def _detect_contract_type(self, contract_text: str) -> str:
        """Detect the type of contract based on content."""
        text_lower = contract_text.lower()

        # Employment contract indicators
        if any(term in text_lower for term in ["employment", "employee", "employer", "salary", "job title", "work schedule"]):
            return "employment"

        # Service agreement indicators
        if any(term in text_lower for term in ["service", "services", "provider", "client", "deliverables", "scope of work"]):
            return "service_agreement"

        # NDA indicators
        if any(term in text_lower for term in ["non-disclosure", "confidential information", "proprietary", "trade secret"]):
            return "nda"

        # Sales contract indicators
        if any(term in text_lower for term in ["purchase", "sale", "goods", "products", "delivery", "shipping"]):
            return "sales"

        # Lease agreement indicators
        if any(term in text_lower for term in ["lease", "rent", "tenant", "landlord", "premises", "property"]):
            return "lease"

        return "general"

    def _get_contract_type_missing_clauses(self, contract_type: str, extracted_types: set) -> List[MissingClause]:
        """Get missing clauses specific to contract type."""
        missing = []

        type_specific_clauses = {
            "employment": {
                "non_compete": {
                    "importance": "medium",
                    "risk_level": "medium",
                    "description": "Non-compete clauses may be necessary to protect business interests",
                    "recommendation": "Consider adding reasonable non-compete restrictions if appropriate for the role"
                },
                "benefits": {
                    "importance": "medium",
                    "risk_level": "low",
                    "description": "Benefits and compensation details should be clearly specified",
                    "recommendation": "Include comprehensive benefits and compensation information"
                }
            },
            "service_agreement": {
                "deliverables": {
                    "importance": "high",
                    "risk_level": "medium",
                    "description": "Clear deliverables and acceptance criteria prevent scope disputes",
                    "recommendation": "Define specific deliverables, timelines, and acceptance criteria"
                },
                "change_orders": {
                    "importance": "medium",
                    "risk_level": "medium",
                    "description": "Change order process prevents scope creep and disputes",
                    "recommendation": "Include process for handling changes to scope of work"
                }
            },
            "sales": {
                "delivery": {
                    "importance": "high",
                    "risk_level": "medium",
                    "description": "Delivery terms and risk of loss should be clearly defined",
                    "recommendation": "Specify delivery terms, shipping responsibilities, and risk transfer"
                },
                "returns": {
                    "importance": "medium",
                    "risk_level": "low",
                    "description": "Return and refund policies protect both parties",
                    "recommendation": "Include clear return, refund, and exchange policies"
                }
            }
        }

        if contract_type in type_specific_clauses:
            for clause_type, clause_info in type_specific_clauses[contract_type].items():
                if clause_type not in extracted_types:
                    missing_clause = MissingClause(
                        type=clause_type,
                        title=f"{clause_type.replace('_', ' ').title()} Clause",
                        importance=clause_info["importance"],
                        risk_level=clause_info["risk_level"],
                        description=clause_info["description"],
                        recommendation=clause_info["recommendation"],
                        category=self._get_clause_category(clause_type)
                    )
                    missing.append(missing_clause)

        return missing

    async def assess_clause_risks(self, clauses: List[ExtractedClause]) -> List[ClauseRiskAssessment]:
        """Assess risks for extracted clauses."""
        risk_assessments = []

        for clause in clauses:
            assessment = await self._assess_individual_clause_risk(clause)
            risk_assessments.append(assessment)

        return risk_assessments

    async def _assess_individual_clause_risk(self, clause: ExtractedClause) -> ClauseRiskAssessment:
        """Assess risk for an individual clause."""
        risk_score = 0.0
        risk_factors = []
        mitigation_suggestions = []
        compliance_issues = []

        # Base risk score from clause type
        base_risks = {
            "liability": 0.8,
            "termination": 0.6,
            "non_compete": 0.7,
            "confidentiality": 0.4,
            "payment": 0.5,
            "intellectual_property": 0.6,
            "data_protection": 0.7,
            "warranty": 0.4,
            "governing_law": 0.2,
            "force_majeure": 0.1
        }

        risk_score = base_risks.get(clause.type, 0.3)

        # Analyze clause content for specific risks
        content_lower = clause.content.lower()

        # High-risk terms increase score
        for high_risk_term in self.risk_keywords["high_risk"]:
            if high_risk_term in content_lower:
                risk_score += 0.1
                risk_factors.append(f"Contains high-risk term: {high_risk_term}")

        # Compliance risks
        for compliance_term in self.risk_keywords["compliance_risk"]:
            if compliance_term in content_lower:
                compliance_issues.append(f"Compliance consideration: {compliance_term}")

        # Clause-specific risk analysis
        if clause.type == "liability":
            if "unlimited" in content_lower:
                risk_score += 0.2
                risk_factors.append("Unlimited liability exposure")
                mitigation_suggestions.append("Consider adding liability caps or limitations")

            if "consequential" in content_lower and "exclude" not in content_lower:
                risk_score += 0.1
                risk_factors.append("Consequential damages not excluded")
                mitigation_suggestions.append("Consider excluding consequential damages")

        elif clause.type == "termination":
            if "without cause" in content_lower:
                risk_factors.append("Termination without cause permitted")
                mitigation_suggestions.append("Ensure adequate notice periods are specified")

            if "immediate" in content_lower:
                risk_score += 0.1
                risk_factors.append("Immediate termination allowed")

        elif clause.type == "non_compete":
            if re.search(r'\d+\s*(?:year|month)', content_lower):
                # Extract time period
                time_match = re.search(r'(\d+)\s*(year|month)', content_lower)
                if time_match:
                    duration = int(time_match.group(1))
                    unit = time_match.group(2)
                    if unit == "year" and duration > 2:
                        risk_score += 0.2
                        risk_factors.append(f"Long non-compete period: {duration} {unit}s")
                        mitigation_suggestions.append("Consider reducing non-compete duration")

        # Cap risk score at 1.0
        risk_score = min(1.0, risk_score)

        # Determine severity
        if risk_score >= 0.8:
            severity = "critical"
        elif risk_score >= 0.6:
            severity = "high"
        elif risk_score >= 0.4:
            severity = "medium"
        else:
            severity = "low"

        return ClauseRiskAssessment(
            clause_type=clause.type,
            risk_score=risk_score,
            risk_factors=risk_factors,
            mitigation_suggestions=mitigation_suggestions,
            compliance_issues=compliance_issues,
            severity=severity
        )

    async def analyze_contract_comprehensively(self, contract_text: str) -> Dict[str, Any]:
        """Perform comprehensive clause analysis on contract."""
        # Extract clauses
        extracted_clauses = await self.extract_clauses(contract_text)

        # Detect missing clauses
        missing_clauses = await self.detect_missing_clauses(contract_text, extracted_clauses)

        # Assess risks
        risk_assessments = await self.assess_clause_risks(extracted_clauses)

        # Calculate overall scores
        overall_risk_score = self._calculate_overall_risk_score(risk_assessments)
        completeness_score = self._calculate_completeness_score(extracted_clauses, missing_clauses)

        return {
            "extracted_clauses": [clause.dict() for clause in extracted_clauses],
            "missing_clauses": [clause.dict() for clause in missing_clauses],
            "risk_assessments": [assessment.dict() for assessment in risk_assessments],
            "overall_risk_score": overall_risk_score,
            "completeness_score": completeness_score,
            "contract_type": self._detect_contract_type(contract_text),
            "total_clauses_found": len(extracted_clauses),
            "critical_missing_clauses": len([c for c in missing_clauses if c.importance == "critical"]),
            "high_risk_clauses": len([r for r in risk_assessments if r.severity in ["critical", "high"]])
        }

    def _calculate_overall_risk_score(self, risk_assessments: List[ClauseRiskAssessment]) -> float:
        """Calculate overall risk score from individual assessments."""
        if not risk_assessments:
            return 0.5  # Default medium risk if no clauses found

        # Weight by severity
        total_weighted_score = 0.0
        total_weight = 0.0

        for assessment in risk_assessments:
            weight = {"critical": 3.0, "high": 2.0, "medium": 1.0, "low": 0.5}[assessment.severity]
            total_weighted_score += assessment.risk_score * weight
            total_weight += weight

        return total_weighted_score / total_weight if total_weight > 0 else 0.5

    def _calculate_completeness_score(self, extracted_clauses: List[ExtractedClause], missing_clauses: List[MissingClause]) -> float:
        """Calculate completeness score based on standard clauses."""
        total_standard_clauses = len(self.standard_clauses)
        missing_critical = len([c for c in missing_clauses if c.importance == "critical"])
        missing_high = len([c for c in missing_clauses if c.importance == "high"])

        # Penalize missing critical clauses more heavily
        penalty = (missing_critical * 0.3) + (missing_high * 0.2) + (len(missing_clauses) * 0.1)

        completeness = max(0.0, 1.0 - penalty)
        return completeness


# Global clause analysis service instance
clause_analysis_service = ClauseAnalysisService()
