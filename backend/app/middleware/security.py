"""
Security middleware for Averum Contracts API.
Implements comprehensive security headers and input sanitization.
"""

import re
import html
import logging
from typing import Dict, Any, List, Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import time

logger = logging.getLogger(__name__)


class SecurityConfig:
    """Security configuration constants."""
    
    # Content Security Policy
    CSP_POLICY = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; "
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
        "font-src 'self' https://fonts.gstatic.com; "
        "img-src 'self' data: https:; "
        "connect-src 'self' https://api.clerk.dev https://api.openai.com https://api-inference.huggingface.co https://generativelanguage.googleapis.com; "
        "frame-ancestors 'none'; "
        "base-uri 'self'; "
        "form-action 'self'"
    )
    
    # Security headers
    SECURITY_HEADERS = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains; preload",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": (
            "geolocation=(), microphone=(), camera=(), "
            "payment=(), usb=(), magnetometer=(), gyroscope=(), "
            "accelerometer=(), ambient-light-sensor=()"
        ),
        "Content-Security-Policy": CSP_POLICY,
        "X-Permitted-Cross-Domain-Policies": "none",
        "Cross-Origin-Embedder-Policy": "require-corp",
        "Cross-Origin-Opener-Policy": "same-origin",
        "Cross-Origin-Resource-Policy": "same-origin"
    }
    
    # Dangerous patterns for input sanitization
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'onload\s*=',
        r'onerror\s*=',
        r'onclick\s*=',
        r'onmouseover\s*=',
        r'onfocus\s*=',
        r'onblur\s*=',
        r'onchange\s*=',
        r'onsubmit\s*=',
        r'<iframe[^>]*>.*?</iframe>',
        r'<object[^>]*>.*?</object>',
        r'<embed[^>]*>.*?</embed>',
        r'<link[^>]*>',
        r'<meta[^>]*>',
        r'<style[^>]*>.*?</style>',
        r'expression\s*\(',
        r'url\s*\(',
        r'@import',
        r'<svg[^>]*>.*?</svg>',
    ]
    
    # SQL injection patterns
    SQL_PATTERNS = [
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\b)',
        r'(\b(OR|AND)\s+\d+\s*=\s*\d+)',
        r'(\b(OR|AND)\s+[\'"][^\'"]*[\'"])',
        r'(--|#|/\*|\*/)',
        r'(\bUNION\s+(ALL\s+)?SELECT\b)',
        r'(\bINTO\s+(OUTFILE|DUMPFILE)\b)',
        r'(\bLOAD_FILE\s*\()',
        r'(\bINTO\s+OUTFILE\b)',
    ]
    
    # File upload restrictions
    ALLOWED_MIME_TYPES = {
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'text/html',
        'application/rtf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/json',
        'text/csv'
    }
    
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_REQUEST_SIZE = 50 * 1024 * 1024  # 50MB


class InputSanitizer:
    """Input sanitization utilities."""
    
    @staticmethod
    def sanitize_string(value: str, allow_html: bool = False) -> str:
        """Sanitize string input to prevent XSS and injection attacks."""
        if not isinstance(value, str):
            return str(value)
        
        # Remove null bytes
        value = value.replace('\x00', '')
        
        # Check for SQL injection patterns
        for pattern in SecurityConfig.SQL_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                logger.warning(f"Potential SQL injection attempt detected: {pattern}")
                # Replace with safe placeholder
                value = re.sub(pattern, '[FILTERED]', value, flags=re.IGNORECASE)
        
        # Check for XSS patterns
        for pattern in SecurityConfig.XSS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE | re.DOTALL):
                logger.warning(f"Potential XSS attempt detected: {pattern}")
                # Remove the dangerous content
                value = re.sub(pattern, '', value, flags=re.IGNORECASE | re.DOTALL)
        
        # HTML encode if HTML is not allowed
        if not allow_html:
            value = html.escape(value)
        
        # Normalize whitespace
        value = re.sub(r'\s+', ' ', value).strip()
        
        return value
    
    @staticmethod
    def sanitize_dict(data: Dict[str, Any], allow_html_fields: List[str] = None) -> Dict[str, Any]:
        """Recursively sanitize dictionary data."""
        if allow_html_fields is None:
            allow_html_fields = ['content', 'description', 'message', 'body']
        
        sanitized = {}
        
        for key, value in data.items():
            if isinstance(value, str):
                allow_html = key.lower() in allow_html_fields
                sanitized[key] = InputSanitizer.sanitize_string(value, allow_html=allow_html)
            elif isinstance(value, dict):
                sanitized[key] = InputSanitizer.sanitize_dict(value, allow_html_fields)
            elif isinstance(value, list):
                sanitized[key] = [
                    InputSanitizer.sanitize_dict(item, allow_html_fields) if isinstance(item, dict)
                    else InputSanitizer.sanitize_string(str(item), allow_html=key.lower() in allow_html_fields) if isinstance(item, str)
                    else item
                    for item in value
                ]
            else:
                sanitized[key] = value
        
        return sanitized
    
    @staticmethod
    def validate_file_upload(filename: str, content_type: str, file_size: int) -> tuple[bool, str]:
        """Validate file upload parameters."""
        # Check file size
        if file_size > SecurityConfig.MAX_FILE_SIZE:
            return False, f"File size exceeds maximum allowed size of {SecurityConfig.MAX_FILE_SIZE // (1024*1024)}MB"
        
        # Check MIME type
        if content_type not in SecurityConfig.ALLOWED_MIME_TYPES:
            return False, f"File type '{content_type}' is not allowed"
        
        # Check filename for dangerous patterns
        dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com', '.jar', '.js', '.vbs', '.ps1']
        filename_lower = filename.lower()
        
        for ext in dangerous_extensions:
            if filename_lower.endswith(ext):
                return False, f"File extension '{ext}' is not allowed"
        
        # Check for path traversal attempts
        if '..' in filename or '/' in filename or '\\' in filename:
            return False, "Invalid filename: path traversal detected"
        
        return True, "File validation passed"


class SecurityMiddleware(BaseHTTPMiddleware):
    """Security middleware that adds security headers and input sanitization."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.sanitizer = InputSanitizer()
    
    async def dispatch(self, request: Request, call_next):
        """Process request with security measures."""
        start_time = time.time()
        
        try:
            # Check request size
            content_length = request.headers.get('content-length')
            if content_length and int(content_length) > SecurityConfig.MAX_REQUEST_SIZE:
                return JSONResponse(
                    status_code=413,
                    content={"detail": "Request entity too large"}
                )
            
            # Sanitize request data for POST/PUT/PATCH requests
            if request.method in ['POST', 'PUT', 'PATCH']:
                await self._sanitize_request_body(request)
            
            # Process the request
            response = await call_next(request)
            
            # Add security headers
            self._add_security_headers(response)
            
            # Add performance header
            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(round(process_time * 1000, 2))
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {e}")
            # Return a generic error response
            response = JSONResponse(
                status_code=500,
                content={"detail": "Internal server error"}
            )
            self._add_security_headers(response)
            return response
    
    def _add_security_headers(self, response: Response):
        """Add security headers to response."""
        for header, value in SecurityConfig.SECURITY_HEADERS.items():
            response.headers[header] = value
        
        # Add server header obfuscation
        response.headers["Server"] = "Averum-API"
    
    async def _sanitize_request_body(self, request: Request):
        """Sanitize request body data."""
        try:
            # Skip sanitization for certain content types
            content_type = request.headers.get('content-type', '')
            
            # Skip file uploads and binary data
            if any(ct in content_type.lower() for ct in ['multipart/form-data', 'application/octet-stream', 'image/', 'video/', 'audio/']):
                return
            
            # Only sanitize JSON data
            if 'application/json' in content_type.lower():
                # Note: In FastAPI, the request body is consumed by the framework
                # This is a placeholder for where sanitization would occur
                # Actual sanitization happens in the endpoint handlers
                pass
                
        except Exception as e:
            logger.error(f"Error sanitizing request body: {e}")


# Utility functions for use in endpoints
def sanitize_input_data(data: Dict[str, Any], allow_html_fields: List[str] = None) -> Dict[str, Any]:
    """Sanitize input data in endpoint handlers."""
    return InputSanitizer.sanitize_dict(data, allow_html_fields)


def validate_file_upload(filename: str, content_type: str, file_size: int) -> tuple[bool, str]:
    """Validate file upload in endpoint handlers."""
    return InputSanitizer.validate_file_upload(filename, content_type, file_size)


# Create middleware instance
security_middleware = SecurityMiddleware
