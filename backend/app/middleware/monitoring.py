"""
Application Performance Monitoring middleware for Averum Contracts API.

This module provides comprehensive monitoring including:
- Request/response logging
- Performance metrics
- Error tracking
- Health checks
- Custom metrics collection
"""

import time
import json
import logging
import traceback
from typing import Dict, Any, Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from datetime import datetime, timedelta
import uuid
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
import threading

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RequestMetrics:
    """Data class for storing request metrics."""
    request_id: str
    method: str
    path: str
    user_id: Optional[str]
    workspace_id: Optional[str]
    start_time: float
    end_time: Optional[float] = None
    duration_ms: Optional[float] = None
    status_code: Optional[int] = None
    response_size: Optional[int] = None
    error: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None

class PerformanceMonitor:
    """
    Performance monitoring system that tracks metrics and provides insights.
    """
    
    def __init__(self, max_metrics_history: int = 10000):
        self.max_metrics_history = max_metrics_history
        self.metrics_history = deque(maxlen=max_metrics_history)
        self.error_counts = defaultdict(int)
        self.endpoint_stats = defaultdict(lambda: {
            'count': 0,
            'total_duration': 0,
            'avg_duration': 0,
            'min_duration': float('inf'),
            'max_duration': 0,
            'error_count': 0
        })
        self.active_requests = {}
        self.lock = threading.Lock()
        
    def start_request(self, request: Request) -> str:
        """Start tracking a request and return a unique request ID."""
        request_id = str(uuid.uuid4())
        
        # Extract user information if available
        user_id = getattr(request.state, 'user_id', None)
        workspace_id = request.headers.get('X-Workspace-ID')
        
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        metrics = RequestMetrics(
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            user_id=user_id,
            workspace_id=workspace_id,
            start_time=time.time(),
            user_agent=request.headers.get('User-Agent'),
            ip_address=client_ip
        )
        
        with self.lock:
            self.active_requests[request_id] = metrics
            
        return request_id
        
    def end_request(self, request_id: str, response: Response, error: Optional[Exception] = None):
        """End tracking a request and update metrics."""
        with self.lock:
            if request_id not in self.active_requests:
                return
                
            metrics = self.active_requests.pop(request_id)
            metrics.end_time = time.time()
            metrics.duration_ms = (metrics.end_time - metrics.start_time) * 1000
            metrics.status_code = response.status_code if response else 500
            
            # Get response size if available
            if hasattr(response, 'body') and response.body:
                metrics.response_size = len(response.body)
            
            # Handle errors
            if error:
                metrics.error = str(error)
                self.error_counts[type(error).__name__] += 1
                
            # Update endpoint statistics
            endpoint_key = f"{metrics.method} {metrics.path}"
            stats = self.endpoint_stats[endpoint_key]
            stats['count'] += 1
            stats['total_duration'] += metrics.duration_ms
            stats['avg_duration'] = stats['total_duration'] / stats['count']
            stats['min_duration'] = min(stats['min_duration'], metrics.duration_ms)
            stats['max_duration'] = max(stats['max_duration'], metrics.duration_ms)
            
            if error or (metrics.status_code and metrics.status_code >= 400):
                stats['error_count'] += 1
                
            # Add to history
            self.metrics_history.append(metrics)
            
            # Log the request
            self._log_request(metrics, error)
            
    def _log_request(self, metrics: RequestMetrics, error: Optional[Exception] = None):
        """Log request details."""
        log_data = {
            "request_id": metrics.request_id,
            "method": metrics.method,
            "path": metrics.path,
            "duration_ms": round(metrics.duration_ms, 2),
            "status_code": metrics.status_code,
            "user_id": metrics.user_id,
            "workspace_id": metrics.workspace_id,
            "ip_address": metrics.ip_address,
            "timestamp": datetime.fromtimestamp(metrics.start_time).isoformat()
        }
        
        if error:
            log_data["error"] = str(error)
            log_data["error_type"] = type(error).__name__
            logger.error(f"Request failed: {json.dumps(log_data)}")
        elif metrics.status_code >= 400:
            logger.warning(f"Request error: {json.dumps(log_data)}")
        else:
            logger.info(f"Request completed: {json.dumps(log_data)}")
            
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of current metrics."""
        with self.lock:
            total_requests = len(self.metrics_history)
            
            if total_requests == 0:
                return {
                    "total_requests": 0,
                    "active_requests": len(self.active_requests),
                    "error_rate": 0,
                    "avg_response_time": 0
                }
            
            # Calculate metrics from recent history
            recent_metrics = list(self.metrics_history)[-1000:]  # Last 1000 requests
            
            total_duration = sum(m.duration_ms for m in recent_metrics if m.duration_ms)
            error_count = sum(1 for m in recent_metrics if m.error or (m.status_code and m.status_code >= 400))
            
            avg_response_time = total_duration / len(recent_metrics) if recent_metrics else 0
            error_rate = (error_count / len(recent_metrics)) * 100 if recent_metrics else 0
            
            # Get top endpoints by request count
            top_endpoints = sorted(
                self.endpoint_stats.items(),
                key=lambda x: x[1]['count'],
                reverse=True
            )[:10]
            
            return {
                "total_requests": total_requests,
                "active_requests": len(self.active_requests),
                "error_rate": round(error_rate, 2),
                "avg_response_time": round(avg_response_time, 2),
                "error_counts": dict(self.error_counts),
                "top_endpoints": [
                    {
                        "endpoint": endpoint,
                        "count": stats['count'],
                        "avg_duration": round(stats['avg_duration'], 2),
                        "error_rate": round((stats['error_count'] / stats['count']) * 100, 2)
                    }
                    for endpoint, stats in top_endpoints
                ]
            }
            
    def get_health_status(self) -> Dict[str, Any]:
        """Get application health status."""
        with self.lock:
            recent_errors = sum(1 for m in list(self.metrics_history)[-100:] 
                              if m.error or (m.status_code and m.status_code >= 500))
            
            # Determine health status
            if recent_errors > 10:  # More than 10% error rate in last 100 requests
                status = "unhealthy"
            elif recent_errors > 5:
                status = "degraded"
            else:
                status = "healthy"
                
            return {
                "status": status,
                "timestamp": datetime.utcnow().isoformat(),
                "active_requests": len(self.active_requests),
                "recent_errors": recent_errors,
                "uptime_seconds": time.time() - (self.metrics_history[0].start_time if self.metrics_history else time.time())
            }

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

async def monitoring_middleware(request: Request, call_next):
    """
    Monitoring middleware that tracks all requests and responses.
    """
    # Start tracking the request
    request_id = performance_monitor.start_request(request)
    
    # Add request ID to request state for use in other parts of the application
    request.state.request_id = request_id
    
    response = None
    error = None
    
    try:
        # Process the request
        response = await call_next(request)
        
        # Add monitoring headers
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Response-Time"] = str(round((time.time() - performance_monitor.active_requests[request_id].start_time) * 1000, 2))
        
        return response
        
    except Exception as e:
        error = e
        logger.error(f"Request {request_id} failed with error: {str(e)}\n{traceback.format_exc()}")
        
        # Create error response
        response = JSONResponse(
            status_code=500,
            content={
                "detail": "Internal server error",
                "request_id": request_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        response.headers["X-Request-ID"] = request_id
        
        return response
        
    finally:
        # End tracking the request
        performance_monitor.end_request(request_id, response, error)
