"""
Rate Limiting Middleware for Averum Contracts
Provides automatic rate limiting enforcement across all API endpoints
"""

import logging
from typing import Callable, Dict, Any, Optional
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.services.rate_limiting_service import rate_limiting_service, RateLimitResult

logger = logging.getLogger(__name__)


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Middleware for automatic rate limiting enforcement."""
    
    def __init__(self, app, endpoint_rules: Optional[Dict[str, str]] = None):
        super().__init__(app)
        self.endpoint_rules = endpoint_rules or self._get_default_endpoint_rules()
    
    def _get_default_endpoint_rules(self) -> Dict[str, str]:
        """Get default rate limiting rules for different endpoints."""
        return {
            # Authentication endpoints
            "/api/v1/auth/": "auth_per_minute",
            "/api/v1/clerk/": "auth_per_minute",
            
            # AI endpoints
            "/api/v1/ai/": "ai_analysis_per_minute",
            "/api/v1/secure-ai/": "ai_analysis_per_minute",
            "/api/v1/clause-analysis/": "ai_analysis_per_minute",
            "/api/v1/batch-ai/": "ai_batch_per_hour",
            
            # Upload endpoints
            "/api/v1/storage/upload": "upload_per_minute",
            "/api/v1/documents/upload": "upload_per_minute",
            
            # General API endpoints
            "/api/v1/": "user_per_minute",
            
            # WebSocket connections
            "/api/v1/realtime/ws": "ip_per_minute"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through rate limiting."""
        try:
            # Skip rate limiting for certain paths
            if self._should_skip_rate_limiting(request):
                return await call_next(request)
            
            # Determine which rate limiting rule to apply
            rule_name = self._get_rule_for_endpoint(request.url.path)
            
            if rule_name:
                # Check rate limit
                result = await rate_limiting_service.check_rate_limit(
                    request=request,
                    rule_name=rule_name
                )
                
                # Add rate limit headers to response
                response = await self._process_rate_limit_result(request, call_next, result)
                self._add_rate_limit_headers(response, result)
                
                return response
            else:
                # No specific rule, apply default user rate limiting
                result = await rate_limiting_service.check_rate_limit(
                    request=request,
                    rule_name="user_per_minute"
                )
                
                response = await self._process_rate_limit_result(request, call_next, result)
                self._add_rate_limit_headers(response, result)
                
                return response
                
        except Exception as e:
            logger.error(f"Rate limiting middleware error: {e}")
            # On error, allow request to proceed
            return await call_next(request)
    
    def _should_skip_rate_limiting(self, request: Request) -> bool:
        """Determine if rate limiting should be skipped for this request."""
        skip_paths = [
            "/health",
            "/docs",
            "/openapi.json",
            "/favicon.ico",
            "/static/",
            "/api/v1/monitoring/health",
            "/api/v1/performance/health"
        ]
        
        path = request.url.path
        return any(path.startswith(skip_path) for skip_path in skip_paths)
    
    def _get_rule_for_endpoint(self, path: str) -> Optional[str]:
        """Get the appropriate rate limiting rule for an endpoint."""
        # Check for exact matches first
        if path in self.endpoint_rules:
            return self.endpoint_rules[path]
        
        # Check for prefix matches
        for endpoint_prefix, rule_name in self.endpoint_rules.items():
            if path.startswith(endpoint_prefix):
                return rule_name
        
        return None
    
    async def _process_rate_limit_result(
        self,
        request: Request,
        call_next: Callable,
        result: RateLimitResult
    ) -> Response:
        """Process the rate limit result and either allow or block the request."""
        if result.allowed:
            # Request is within limits, proceed
            return await call_next(request)
        else:
            # Request exceeds limits, return 429 Too Many Requests
            error_response = {
                "error": "Rate limit exceeded",
                "message": f"Too many requests. Limit: {result.limit} requests per window.",
                "limit": result.limit,
                "remaining": result.remaining,
                "reset_time": result.reset_time,
                "retry_after": result.retry_after
            }
            
            if result.burst_used is not None:
                error_response["burst_used"] = result.burst_used
            
            return JSONResponse(
                status_code=429,
                content=error_response,
                headers={
                    "Retry-After": str(result.retry_after) if result.retry_after else "60"
                }
            )
    
    def _add_rate_limit_headers(self, response: Response, result: RateLimitResult):
        """Add rate limiting headers to the response."""
        response.headers["X-RateLimit-Limit"] = str(result.limit)
        response.headers["X-RateLimit-Remaining"] = str(result.remaining)
        response.headers["X-RateLimit-Reset"] = str(result.reset_time)
        
        if result.retry_after is not None:
            response.headers["X-RateLimit-Retry-After"] = str(result.retry_after)
        
        if result.burst_used is not None:
            response.headers["X-RateLimit-Burst-Used"] = str(result.burst_used)


class EndpointRateLimiter:
    """Decorator for applying specific rate limits to individual endpoints."""
    
    def __init__(self, rule_name: str, identifier_func: Optional[Callable] = None):
        self.rule_name = rule_name
        self.identifier_func = identifier_func
    
    def __call__(self, func):
        """Decorator function."""
        async def wrapper(*args, **kwargs):
            # Extract request from function arguments
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                # If no request found, proceed without rate limiting
                return await func(*args, **kwargs)
            
            # Get custom identifier if function provided
            identifier = None
            if self.identifier_func:
                identifier = self.identifier_func(request, *args, **kwargs)
            
            # Check rate limit
            result = await rate_limiting_service.check_rate_limit(
                request=request,
                rule_name=self.rule_name,
                identifier=identifier
            )
            
            if not result.allowed:
                raise HTTPException(
                    status_code=429,
                    detail={
                        "error": "Rate limit exceeded",
                        "message": f"Too many requests for {self.rule_name}",
                        "limit": result.limit,
                        "remaining": result.remaining,
                        "reset_time": result.reset_time,
                        "retry_after": result.retry_after
                    },
                    headers={
                        "Retry-After": str(result.retry_after) if result.retry_after else "60"
                    }
                )
            
            # Proceed with the original function
            response = await func(*args, **kwargs)
            
            # Add rate limit headers if response supports it
            if hasattr(response, 'headers'):
                response.headers["X-RateLimit-Limit"] = str(result.limit)
                response.headers["X-RateLimit-Remaining"] = str(result.remaining)
                response.headers["X-RateLimit-Reset"] = str(result.reset_time)
            
            return response
        
        return wrapper


def rate_limit(rule_name: str, identifier_func: Optional[Callable] = None):
    """
    Decorator for applying rate limits to specific endpoints.
    
    Args:
        rule_name: Name of the rate limiting rule to apply
        identifier_func: Optional function to generate custom identifier
        
    Usage:
        @rate_limit("ai_analysis_per_minute")
        async def analyze_contract(request: Request, ...):
            ...
        
        @rate_limit("upload_per_minute", lambda req, *args, **kwargs: req.state.user_id)
        async def upload_file(request: Request, ...):
            ...
    """
    return EndpointRateLimiter(rule_name, identifier_func)


class BurstProtection:
    """Additional burst protection for critical endpoints."""
    
    def __init__(self, max_burst: int = 5, burst_window: int = 10):
        self.max_burst = max_burst
        self.burst_window = burst_window
        self.burst_cache = {}
    
    async def check_burst(self, identifier: str) -> bool:
        """Check if request is within burst limits."""
        import time
        
        current_time = time.time()
        
        if identifier not in self.burst_cache:
            self.burst_cache[identifier] = []
        
        # Clean old entries
        self.burst_cache[identifier] = [
            timestamp for timestamp in self.burst_cache[identifier]
            if current_time - timestamp < self.burst_window
        ]
        
        # Check burst limit
        if len(self.burst_cache[identifier]) >= self.max_burst:
            return False
        
        # Add current request
        self.burst_cache[identifier].append(current_time)
        return True


def burst_protect(max_burst: int = 5, burst_window: int = 10):
    """
    Decorator for burst protection on critical endpoints.
    
    Args:
        max_burst: Maximum requests in burst window
        burst_window: Burst window in seconds
    """
    burst_protection = BurstProtection(max_burst, burst_window)
    
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract request and identifier
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if request:
                identifier = getattr(request.state, 'user_id', request.client.host)
                
                if not await burst_protection.check_burst(identifier):
                    raise HTTPException(
                        status_code=429,
                        detail={
                            "error": "Burst limit exceeded",
                            "message": f"Too many requests in {burst_window} seconds",
                            "max_burst": max_burst,
                            "burst_window": burst_window
                        }
                    )
            
            return await func(*args, **kwargs)
        
        return wrapper
    
    return decorator
