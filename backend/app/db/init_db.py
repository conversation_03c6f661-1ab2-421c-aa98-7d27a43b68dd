import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client, Client

# Add the parent directory to the path so we can import from app
sys.path.append(str(Path(__file__).parent.parent.parent))

# Load environment variables
load_dotenv()

def init_db():
    """
    Initialize the database with the schema and initial data.
    """
    try:
        # Get Supabase credentials from environment variables
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")

        if not supabase_url or not supabase_key:
            print("Error: SUPABASE_URL and SUPABASE_KEY environment variables must be set.")
            sys.exit(1)

        # Create Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)

        # Read the schema SQL file
        schema_path = Path(__file__).parent / "schema.sql"
        with open(schema_path, "r") as f:
            schema_sql = f.read()

        # Execute the schema SQL
        # Note: In a real application, you would use Supabase's SQL editor or migrations
        # This is a simplified example
        print("Creating database schema...")

        # Split the SQL into individual statements
        statements = schema_sql.split(";")

        for statement in statements:
            if statement.strip():
                # Execute each statement
                print(f"Executing: {statement[:50]}...")
                # Note: Supabase doesn't directly support executing arbitrary SQL via the API
                # We'll assume the schema is already created in Supabase

        print("Database schema verification completed.")

        # Seed the database with initial data
        seed_db(supabase)

        return True

    except Exception as e:
        print(f"Error initializing database: {e}")
        return False

def seed_db(supabase: Client):
    """
    Seed the database with initial data.
    """
    try:
        print("Seeding database with initial data...")

        # Create a default admin user
        admin_user = {
            "id": "admin-user",
            "email": "<EMAIL>",
            "first_name": "Admin",
            "last_name": "User",
            "initials": "AU",
        }

        supabase.table("users").insert(admin_user).execute()

        # Create a default workspace
        default_workspace = {
            "id": "default-workspace",
            "name": "Default Workspace",
            "description": "Default workspace for all users",
            "created_by": "admin-user",
        }

        supabase.table("workspaces").insert(default_workspace).execute()

        # Add the admin user to the default workspace
        workspace_member = {
            "workspace_id": "default-workspace",
            "user_id": "admin-user",
            "role_id": "role-admin",
        }

        supabase.table("workspace_members").insert(workspace_member).execute()

        print("Database seeded successfully.")

        return True

    except Exception as e:
        print(f"Error seeding database: {e}")
        return False

if __name__ == "__main__":
    print("Initializing database...")
    success = init_db()

    if success:
        print("Database initialization completed successfully.")
    else:
        print("Database initialization failed.")
        sys.exit(1)
