-- Database indexes for notifications table to optimize query performance

-- Index for user_id and workspace_id (most common query pattern)
CREATE INDEX IF NOT EXISTS idx_notifications_user_workspace 
ON notifications(user_id, workspace_id);

-- Index for status filtering (unread notifications)
CREATE INDEX IF NOT EXISTS idx_notifications_status 
ON notifications(status);

-- Index for type filtering
CREATE INDEX IF NOT EXISTS idx_notifications_type 
ON notifications(type);

-- Index for created_at ordering (most recent first)
CREATE INDEX IF NOT EXISTS idx_notifications_created_at 
ON notifications(created_at DESC);

-- Composite index for user, workspace, and status (common filter combination)
CREATE INDEX IF NOT EXISTS idx_notifications_user_workspace_status 
ON notifications(user_id, workspace_id, status);

-- Composite index for user, workspace, and type (filter by notification type)
CREATE INDEX IF NOT EXISTS idx_notifications_user_workspace_type 
ON notifications(user_id, workspace_id, type);

-- Index for sender_id (for filtering notifications by sender)
CREATE INDEX IF NOT EXISTS idx_notifications_sender 
ON notifications(sender_id);

-- Index for entity_id and entity_type (for entity-related notifications)
CREATE INDEX IF NOT EXISTS idx_notifications_entity 
ON notifications(entity_id, entity_type);

-- Composite index for workspace and created_at (workspace activity feeds)
CREATE INDEX IF NOT EXISTS idx_notifications_workspace_created 
ON notifications(workspace_id, created_at DESC);

-- Partial index for unread notifications only (performance optimization)
CREATE INDEX IF NOT EXISTS idx_notifications_unread 
ON notifications(user_id, workspace_id, created_at DESC) 
WHERE status = 'unread';

-- Partial index for read notifications with read_at timestamp
CREATE INDEX IF NOT EXISTS idx_notifications_read_at 
ON notifications(user_id, workspace_id, read_at DESC) 
WHERE status = 'read' AND read_at IS NOT NULL;
