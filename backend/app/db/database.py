from supabase import create_client, Client
from app.core.config import settings
from typing import Optional
import logging

logger = logging.getLogger(__name__)

def get_supabase_client() -> Client:
    """
    Create and return a Supabase client instance.
    """
    try:
        supabase_client: Client = create_client(
            settings.SUPABASE_URL,
            settings.SUPABASE_KEY
        )
        return supabase_client
    except Exception as e:
        # Log the error
        logger.error(f"Error creating Supabase client: {e}")
        raise

def set_user_context(supabase_client: Client, user_id: str) -> None:
    """
    Set the current user context for RLS policies.
    This must be called before any database operations that require user context.
    """
    try:
        # Set the user ID in the database session for RLS policies
        supabase_client.rpc('set_current_user_id', {'user_id': user_id}).execute()
        logger.debug(f"Set user context for user: {user_id}")
    except Exception as e:
        logger.error(f"Failed to set user context for {user_id}: {e}")
        raise

def get_authenticated_supabase_client(user_id: str) -> Client:
    """
    Get a Supabase client with user context set for RLS policies.
    Use this for all authenticated database operations.
    """
    client = get_supabase_client()
    set_user_context(client, user_id)
    return client
