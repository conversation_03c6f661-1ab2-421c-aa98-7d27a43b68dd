"""
Realistic contract templates for LegalAI V4 demo data
Contains comprehensive, legally-inspired contract templates for different industries
"""

from datetime import datetime
from typing import Dict, List, Any

def get_nda_template() -> Dict[str, Any]:
    """Get a comprehensive NDA template."""
    return {
        "id": "template-nda-comprehensive",
        "title": "Comprehensive Non-Disclosure Agreement",
        "description": "A thorough NDA template suitable for business partnerships, vendor relationships, and confidential discussions.",
        "type": "nda",
        "complexity": "medium",
        "industry": "Technology",
        "tags": ["confidentiality", "business", "legal", "partnership"],
        "icon": "shield",
        "content": {
            "document_title": "NON-DISCLOSURE AGREEMENT",
            "sections": [
                {
                    "title": "Parties",
                    "content": """This Non-Disclosure Agreement ("Agreement") is entered into on [EFFECTIVE_DATE] between [PARTY_1_NAME], a [PARTY_1_TYPE] ("Disclosing Party") and [PARTY_2_NAME], a [PARTY_2_TYPE] ("Receiving Party")."""
                },
                {
                    "title": "Recitals",
                    "content": """WHEREAS, the Disclosing Party possesses certain confidential and proprietary information that it wishes to disclose to the Receiving Party for the purpose of [PURPOSE];

WHEREAS, the Receiving Party agrees to maintain the confidentiality of such information and use it solely for the purposes set forth herein;

NOW, THEREFORE, in consideration of the mutual covenants contained herein and other good and valuable consideration, the receipt and sufficiency of which are hereby acknowledged, the parties agree as follows:"""
                },
                {
                    "title": "Definition of Confidential Information",
                    "content": """For purposes of this Agreement, "Confidential Information" means any and all non-public, confidential or proprietary information disclosed by the Disclosing Party to the Receiving Party, whether orally, in writing, or in any other form, including but not limited to:

a) Technical data, trade secrets, know-how, research, product plans, products, services, customers, customer lists, markets, software, developments, inventions, processes, formulas, technology, designs, drawings, engineering, hardware configuration information;
b) Marketing plans, business plans, financial information, pricing, costs, profits, markets, sales, suppliers, customers;
c) Personnel information, compensation data, and organizational structure;
d) Any other information that would reasonably be considered confidential or proprietary."""
                },
                {
                    "title": "Obligations of Receiving Party",
                    "content": """The Receiving Party agrees to:

a) Hold and maintain the Confidential Information in strict confidence;
b) Not disclose the Confidential Information to any third parties without the prior written consent of the Disclosing Party;
c) Use the Confidential Information solely for the purpose of [PURPOSE];
d) Take reasonable precautions to protect the confidentiality of the information, using at least the same degree of care that it uses to protect its own confidential information;
e) Limit access to the Confidential Information to employees, agents, or advisors who have a legitimate need to know and who have been informed of the confidential nature of such information."""
                },
                {
                    "title": "Exceptions",
                    "content": """The obligations set forth in this Agreement shall not apply to information that:

a) Is or becomes publicly available through no breach of this Agreement by the Receiving Party;
b) Was rightfully known by the Receiving Party prior to disclosure;
c) Is rightfully received by the Receiving Party from a third party without breach of any confidentiality obligation;
d) Is independently developed by the Receiving Party without use of or reference to the Confidential Information;
e) Is required to be disclosed by law or court order, provided that the Receiving Party gives the Disclosing Party prompt written notice of such requirement."""
                },
                {
                    "title": "Term and Termination",
                    "content": """This Agreement shall remain in effect for a period of [DURATION] years from the date first written above, unless terminated earlier by mutual written consent of the parties. Upon termination, the Receiving Party shall promptly return or destroy all documents, materials, and other tangible manifestations of Confidential Information and any copies thereof."""
                },
                {
                    "title": "Remedies",
                    "content": """The Receiving Party acknowledges that any breach of this Agreement may cause irreparable harm to the Disclosing Party for which monetary damages would be inadequate. Therefore, the Disclosing Party shall be entitled to seek equitable relief, including injunction and specific performance, in addition to all other remedies available at law or in equity."""
                },
                {
                    "title": "Governing Law",
                    "content": """This Agreement shall be governed by and construed in accordance with the laws of [JURISDICTION], without regard to its conflict of laws principles. Any disputes arising under this Agreement shall be subject to the exclusive jurisdiction of the courts of [JURISDICTION]."""
                },
                {
                    "title": "Miscellaneous",
                    "content": """This Agreement constitutes the entire agreement between the parties with respect to the subject matter hereof and supersedes all prior and contemporaneous agreements and understandings. This Agreement may not be amended except by a written instrument signed by both parties. If any provision of this Agreement is held to be invalid or unenforceable, the remaining provisions shall remain in full force and effect."""
                }
            ],
            "signature_block": """IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first written above.

[PARTY_1_NAME]                    [PARTY_2_NAME]

By: _____________________        By: _____________________
Name: [PARTY_1_REP_NAME]         Name: [PARTY_2_REP_NAME]
Title: [PARTY_1_REP_TITLE]       Title: [PARTY_2_REP_TITLE]
Date: ___________________        Date: ___________________"""
        }
    }

def get_service_agreement_template() -> Dict[str, Any]:
    """Get a comprehensive service agreement template."""
    return {
        "id": "template-service-comprehensive",
        "title": "Professional Services Agreement",
        "description": "Comprehensive template for professional service providers including scope, payment terms, and legal protections.",
        "type": "service",
        "complexity": "complex",
        "industry": "Technology",
        "tags": ["services", "consulting", "professional", "technology"],
        "icon": "briefcase",
        "content": {
            "document_title": "PROFESSIONAL SERVICES AGREEMENT",
            "sections": [
                {
                    "title": "Parties and Effective Date",
                    "content": """This Professional Services Agreement ("Agreement") is entered into on [EFFECTIVE_DATE] between [CLIENT_NAME], a [CLIENT_TYPE] ("Client") and [PROVIDER_NAME], a [PROVIDER_TYPE] ("Service Provider")."""
                },
                {
                    "title": "Services",
                    "content": """Service Provider agrees to provide the following services ("Services") to Client:

[SERVICE_DESCRIPTION]

The detailed scope of work, deliverables, timeline, and specifications are set forth in Exhibit A attached hereto and incorporated by reference."""
                },
                {
                    "title": "Term",
                    "content": """This Agreement shall commence on [START_DATE] and shall continue until [END_DATE], unless terminated earlier in accordance with the provisions hereof. The Agreement may be extended by mutual written consent of the parties."""
                },
                {
                    "title": "Compensation and Payment",
                    "content": """In consideration for the Services, Client shall pay Service Provider [PAYMENT_AMOUNT] [CURRENCY] according to the following schedule:

[PAYMENT_SCHEDULE]

All payments are due within [PAYMENT_TERMS] days of invoice date. Late payments may be subject to a service charge of 1.5% per month or the maximum rate permitted by law, whichever is less."""
                },
                {
                    "title": "Intellectual Property",
                    "content": """All work product, deliverables, and intellectual property created by Service Provider in the performance of the Services shall be owned by [IP_OWNER]. Service Provider hereby assigns all right, title, and interest in such work product to Client, except for Service Provider's pre-existing intellectual property and general methodologies."""
                }
            ]
        }
    }

def get_employment_contract_template() -> Dict[str, Any]:
    """Get a comprehensive employment contract template."""
    return {
        "id": "template-employment-comprehensive", 
        "title": "Employment Agreement",
        "description": "Comprehensive employment contract template with standard terms and conditions for full-time employees.",
        "type": "employment",
        "complexity": "medium",
        "industry": "All",
        "tags": ["employment", "hr", "job", "full-time"],
        "icon": "users",
        "content": {
            "document_title": "EMPLOYMENT AGREEMENT",
            "sections": [
                {
                    "title": "Employment",
                    "content": """[COMPANY_NAME] ("Company") hereby employs [EMPLOYEE_NAME] ("Employee") as [JOB_TITLE], and Employee hereby accepts such employment, subject to the terms and conditions set forth in this Agreement."""
                },
                {
                    "title": "Duties and Responsibilities", 
                    "content": """Employee shall perform the duties and responsibilities of [JOB_TITLE] as outlined in the job description attached as Exhibit A. Employee shall devote their full business time and attention to the performance of their duties and shall not engage in any other business activities without Company's prior written consent."""
                },
                {
                    "title": "Compensation and Benefits",
                    "content": """Company shall pay Employee an annual salary of [SALARY] [CURRENCY], payable in accordance with Company's standard payroll practices. Employee shall be eligible for the following benefits: [BENEFITS_LIST]"""
                },
                {
                    "title": "Confidentiality",
                    "content": """Employee acknowledges that they will have access to confidential and proprietary information of Company. Employee agrees to maintain the confidentiality of such information during and after employment."""
                }
            ]
        }
    }

# Template registry
TEMPLATE_REGISTRY = {
    "nda": get_nda_template,
    "service": get_service_agreement_template, 
    "employment": get_employment_contract_template
}

def get_template_by_type(template_type: str) -> Dict[str, Any]:
    """Get a template by type."""
    if template_type in TEMPLATE_REGISTRY:
        return TEMPLATE_REGISTRY[template_type]()
    else:
        raise ValueError(f"Unknown template type: {template_type}")

def get_all_templates() -> List[Dict[str, Any]]:
    """Get all available templates."""
    return [func() for func in TEMPLATE_REGISTRY.values()]
