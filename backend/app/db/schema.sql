-- Schema for Averum Contracts Management System
-- This file contains SQL statements to set up the database schema in Supabase

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    title TEXT,
    company TEXT,
    timezone TEXT DEFAULT 'UTC',
    bio TEXT,
    avatar TEXT,
    initials TEXT NOT NULL,
    notification_preferences JSONB NOT NULL DEFAULT '{"email_approvals": true, "email_updates": true, "email_reminders": true, "email_comments": true, "system_approvals": true, "browser_notifications": true, "email_digest_frequency": "daily"}',
    workspaces TEXT[] DEFAULT '{}',
    workspace_roles JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Workspaces table
CREATE TABLE IF NOT EXISTS workspaces (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    members INTEGER DEFAULT 0,
    contracts INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE
);

-- Workspace members table
CREATE TABLE IF NOT EXISTS workspace_members (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id TEXT NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(workspace_id, user_id)
);

-- Contracts table
CREATE TABLE IF NOT EXISTS contracts (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    type TEXT NOT NULL,
    jurisdiction TEXT,
    effective_date TIMESTAMP WITH TIME ZONE,
    expiry_date TIMESTAMP WITH TIME ZONE,
    description TEXT,
    counterparty TEXT,
    value TEXT,
    currency TEXT DEFAULT 'USD',
    status TEXT NOT NULL DEFAULT 'draft',
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_by JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    parties JSONB,
    clauses JSONB,
    attachments JSONB,
    approvers JSONB,
    approval_process TEXT DEFAULT 'sequential',
    tags TEXT[],
    custom_fields JSONB,
    starred BOOLEAN DEFAULT FALSE,
    folder_id TEXT
);

-- Templates table
CREATE TABLE IF NOT EXISTS templates (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL,
    complexity TEXT NOT NULL,
    industry TEXT,
    tags TEXT[],
    icon TEXT,
    created_by JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    rating FLOAT,
    is_user_created BOOLEAN DEFAULT FALSE,
    folder_id TEXT,
    content JSONB NOT NULL,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE
);

-- Folders table
CREATE TABLE IF NOT EXISTS folders (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    parent_id TEXT REFERENCES folders(id) ON DELETE CASCADE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Contract Signers table
CREATE TABLE IF NOT EXISTS contract_signers (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    role TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    initials TEXT NOT NULL,
    order INTEGER NOT NULL,
    avatar TEXT,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    signed_at TIMESTAMP WITH TIME ZONE
);

-- Clauses table
CREATE TABLE IF NOT EXISTS clauses (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    category TEXT NOT NULL,
    tags TEXT[] DEFAULT '{}',
    version TEXT NOT NULL,
    approved BOOLEAN DEFAULT FALSE,
    author TEXT,
    is_favorite BOOLEAN DEFAULT FALSE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE
);

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    filename TEXT NOT NULL,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    content TEXT,
    file_url TEXT,
    file_path TEXT,
    file_info JSONB,
    folder TEXT,
    status TEXT NOT NULL DEFAULT 'draft',
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Document Signers table
CREATE TABLE IF NOT EXISTS document_signers (
    id TEXT PRIMARY KEY,
    document_id TEXT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    role TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    order INTEGER NOT NULL,
    avatar TEXT,
    signature_data JSONB,
    completed_at TIMESTAMP WITH TIME ZONE,
    declined_at TIMESTAMP WITH TIME ZONE,
    decline_reason TEXT
);

-- Permissions table (system-wide permission definitions)
CREATE TABLE IF NOT EXISTS permissions (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Roles table (workspace-specific role definitions)
CREATE TABLE IF NOT EXISTS roles (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    permissions TEXT[] DEFAULT '{}',
    is_system BOOLEAN DEFAULT FALSE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(name, workspace_id)
);

-- Notifications table (user notification system)
CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL, -- 'approval', 'contract', 'system', 'mention'
    status TEXT NOT NULL DEFAULT 'unread', -- 'unread', 'read'
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    sender_id TEXT REFERENCES users(id),
    entity_id TEXT,
    entity_type TEXT,
    action_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Activity Logs table (audit trail and activity tracking)
CREATE TABLE IF NOT EXISTS activity_logs (
    id TEXT PRIMARY KEY,
    event_type TEXT NOT NULL,
    user_id TEXT NOT NULL REFERENCES users(id),
    user_name TEXT NOT NULL,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    workspace_name TEXT NOT NULL,
    target_id TEXT,
    target_type TEXT,
    target_name TEXT,
    details JSONB,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Export History table (document export tracking)
CREATE TABLE IF NOT EXISTS export_history (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    contract_title TEXT NOT NULL,
    format TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    download_url TEXT NOT NULL,
    template_used TEXT,
    branding_settings JSONB,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    exported_by JSONB NOT NULL,
    exported_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    download_count INTEGER DEFAULT 0,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- AI Analysis Results table (AI contract analysis storage)
CREATE TABLE IF NOT EXISTS ai_analysis_results (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    risk_score FLOAT NOT NULL,
    compliance_score FLOAT NOT NULL,
    language_clarity FLOAT NOT NULL,
    key_risks JSONB DEFAULT '[]',
    suggestions JSONB DEFAULT '[]',
    extracted_clauses JSONB DEFAULT '[]',
    compliance_issues JSONB DEFAULT '[]',
    obligations JSONB DEFAULT '[]',
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Performance Indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_workspace ON notifications(user_id, workspace_id);
CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);
CREATE INDEX IF NOT EXISTS idx_activity_logs_workspace_created ON activity_logs(workspace_id, created_at);
CREATE INDEX IF NOT EXISTS idx_activity_logs_user ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_export_history_workspace_contract ON export_history(workspace_id, contract_id);
CREATE INDEX IF NOT EXISTS idx_export_history_status ON export_history(status);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_contract ON ai_analysis_results(contract_id);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_workspace ON ai_analysis_results(workspace_id);
CREATE INDEX IF NOT EXISTS idx_roles_workspace ON roles(workspace_id);
CREATE INDEX IF NOT EXISTS idx_clauses_workspace ON clauses(workspace_id);

-- Batch Jobs table for AI processing
CREATE TABLE IF NOT EXISTS batch_jobs (
    id TEXT PRIMARY KEY,
    job_type TEXT NOT NULL CHECK (job_type IN ('bulk_analysis', 'comparative_analysis', 'risk_assessment', 'clause_extraction')),
    job_name TEXT NOT NULL,
    user_id TEXT NOT NULL REFERENCES users(id),
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    analysis_type TEXT NOT NULL,
    total_items INTEGER NOT NULL DEFAULT 0,
    completed_items INTEGER NOT NULL DEFAULT 0,
    failed_items INTEGER NOT NULL DEFAULT 0,
    progress_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.0,
    current_item TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Batch Job Results table
CREATE TABLE IF NOT EXISTS batch_job_results (
    id SERIAL PRIMARY KEY,
    job_id TEXT NOT NULL REFERENCES batch_jobs(id) ON DELETE CASCADE,
    results JSONB NOT NULL DEFAULT '[]',
    summary_statistics JSONB NOT NULL DEFAULT '{}',
    comparative_insights JSONB,
    processing_time DECIMAL(10,3) NOT NULL DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for batch jobs
CREATE INDEX IF NOT EXISTS idx_batch_jobs_user_id ON batch_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_batch_jobs_workspace_id ON batch_jobs(workspace_id);
CREATE INDEX IF NOT EXISTS idx_batch_jobs_status ON batch_jobs(status);
CREATE INDEX IF NOT EXISTS idx_batch_jobs_created_at ON batch_jobs(created_at);
CREATE INDEX IF NOT EXISTS idx_batch_job_results_job_id ON batch_job_results(job_id);

-- Enhanced Contract Lifecycle Management Tables

-- Contract Branches table for advanced version control
CREATE TABLE IF NOT EXISTS contract_branches (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    branch_name TEXT NOT NULL,
    branch_type TEXT NOT NULL CHECK (branch_type IN ('main', 'feature', 'hotfix', 'release')),
    parent_branch_id TEXT REFERENCES contract_branches(id),
    head_version_id TEXT NOT NULL REFERENCES contract_versions(id),
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,

    -- Ensure unique branch names per contract
    UNIQUE(contract_id, branch_name)
);

-- Merge Requests table
CREATE TABLE IF NOT EXISTS merge_requests (
    id TEXT PRIMARY KEY,
    source_branch_id TEXT NOT NULL REFERENCES contract_branches(id) ON DELETE CASCADE,
    target_branch_id TEXT NOT NULL REFERENCES contract_branches(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'merged', 'closed', 'conflict')),
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    merged_by TEXT REFERENCES users(id),
    merged_at TIMESTAMP WITH TIME ZONE,
    conflicts JSONB DEFAULT '[]',
    merge_strategy TEXT CHECK (merge_strategy IN ('fast_forward', 'three_way', 'squash')),
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE
);

-- Version Diffs table for storing detailed change information
CREATE TABLE IF NOT EXISTS version_diffs (
    id SERIAL PRIMARY KEY,
    from_version_id TEXT NOT NULL REFERENCES contract_versions(id) ON DELETE CASCADE,
    to_version_id TEXT NOT NULL REFERENCES contract_versions(id) ON DELETE CASCADE,
    diff_data JSONB NOT NULL,
    statistics JSONB NOT NULL DEFAULT '{}',
    summary TEXT,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,

    -- Ensure unique diff pairs
    UNIQUE(from_version_id, to_version_id)
);

-- Indexes for enhanced lifecycle tables
CREATE INDEX IF NOT EXISTS idx_contract_branches_contract_id ON contract_branches(contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_branches_workspace_id ON contract_branches(workspace_id);
CREATE INDEX IF NOT EXISTS idx_contract_branches_created_by ON contract_branches(created_by);
CREATE INDEX IF NOT EXISTS idx_contract_branches_is_active ON contract_branches(is_active);
CREATE INDEX IF NOT EXISTS idx_merge_requests_source_branch ON merge_requests(source_branch_id);
CREATE INDEX IF NOT EXISTS idx_merge_requests_target_branch ON merge_requests(target_branch_id);
CREATE INDEX IF NOT EXISTS idx_merge_requests_status ON merge_requests(status);
CREATE INDEX IF NOT EXISTS idx_merge_requests_workspace_id ON merge_requests(workspace_id);
CREATE INDEX IF NOT EXISTS idx_version_diffs_from_version ON version_diffs(from_version_id);
CREATE INDEX IF NOT EXISTS idx_version_diffs_to_version ON version_diffs(to_version_id);
CREATE INDEX IF NOT EXISTS idx_version_diffs_workspace_id ON version_diffs(workspace_id);

-- Enhanced Repository Management Tables

-- Document Tags table for organizing documents
CREATE TABLE IF NOT EXISTS document_tags (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    color TEXT NOT NULL,
    description TEXT,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    usage_count INTEGER DEFAULT 0,

    -- Ensure unique tag names per workspace
    UNIQUE(workspace_id, name)
);

-- Document Collections table for hierarchical organization
CREATE TABLE IF NOT EXISTS document_collections (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    parent_collection_id TEXT REFERENCES document_collections(id),
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    document_count INTEGER DEFAULT 0,
    access_level TEXT NOT NULL DEFAULT 'read' CHECK (access_level IN ('read', 'write', 'admin', 'owner')),
    is_public BOOLEAN DEFAULT FALSE,

    -- Ensure unique collection names per workspace and parent
    UNIQUE(workspace_id, parent_collection_id, name)
);

-- Document Tag Assignments table (many-to-many)
CREATE TABLE IF NOT EXISTS document_tag_assignments (
    id SERIAL PRIMARY KEY,
    document_id TEXT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    tag_id TEXT NOT NULL REFERENCES document_tags(id) ON DELETE CASCADE,
    assigned_by TEXT NOT NULL REFERENCES users(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique tag assignments per document
    UNIQUE(document_id, tag_id)
);

-- Document Collection Assignments table (many-to-many)
CREATE TABLE IF NOT EXISTS document_collection_assignments (
    id SERIAL PRIMARY KEY,
    document_id TEXT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    collection_id TEXT NOT NULL REFERENCES document_collections(id) ON DELETE CASCADE,
    assigned_by TEXT NOT NULL REFERENCES users(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique collection assignments per document
    UNIQUE(document_id, collection_id)
);

-- Search Analytics table for tracking search patterns
CREATE TABLE IF NOT EXISTS search_analytics (
    id SERIAL PRIMARY KEY,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES users(id),
    search_query TEXT NOT NULL,
    search_type TEXT NOT NULL,
    results_count INTEGER NOT NULL DEFAULT 0,
    search_time DECIMAL(10,3) NOT NULL DEFAULT 0.0,
    filters_used JSONB DEFAULT '{}',
    clicked_results JSONB DEFAULT '[]',
    searched_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for enhanced repository tables
CREATE INDEX IF NOT EXISTS idx_document_tags_workspace_id ON document_tags(workspace_id);
CREATE INDEX IF NOT EXISTS idx_document_tags_usage_count ON document_tags(usage_count);
CREATE INDEX IF NOT EXISTS idx_document_collections_workspace_id ON document_collections(workspace_id);
CREATE INDEX IF NOT EXISTS idx_document_collections_parent_id ON document_collections(parent_collection_id);
CREATE INDEX IF NOT EXISTS idx_document_tag_assignments_document_id ON document_tag_assignments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_tag_assignments_tag_id ON document_tag_assignments(tag_id);
CREATE INDEX IF NOT EXISTS idx_document_collection_assignments_document_id ON document_collection_assignments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_collection_assignments_collection_id ON document_collection_assignments(collection_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_workspace_id ON search_analytics(workspace_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_user_id ON search_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_searched_at ON search_analytics(searched_at);

-- Real-time Notifications Tables

-- Enhanced Notifications table for real-time system
CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL,
    priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    user_id TEXT REFERENCES users(id) ON DELETE CASCADE, -- NULL for workspace broadcasts
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    delivered BOOLEAN DEFAULT FALSE,
    delivered_at TIMESTAMP WITH TIME ZONE
);

-- WebSocket Connections table for tracking active connections
CREATE TABLE IF NOT EXISTS websocket_connections (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    connection_id TEXT NOT NULL UNIQUE,
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_ping TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    subscriptions JSONB DEFAULT '[]',
    user_agent TEXT,
    ip_address INET
);

-- Notification Delivery Log table for tracking delivery status
CREATE TABLE IF NOT EXISTS notification_delivery_log (
    id SERIAL PRIMARY KEY,
    notification_id TEXT NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    delivery_method TEXT NOT NULL CHECK (delivery_method IN ('websocket', 'email', 'push')),
    delivered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivery_status TEXT NOT NULL DEFAULT 'sent' CHECK (delivery_status IN ('sent', 'delivered', 'failed', 'bounced')),
    error_message TEXT,
    metadata JSONB DEFAULT '{}'
);

-- Notification Preferences table for user notification settings
CREATE TABLE IF NOT EXISTS notification_preferences (
    id SERIAL PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    delivery_methods JSONB DEFAULT '["websocket"]',
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    timezone TEXT DEFAULT 'UTC',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique preferences per user, workspace, and type
    UNIQUE(user_id, workspace_id, notification_type)
);

-- Indexes for real-time notifications tables
CREATE INDEX IF NOT EXISTS idx_notifications_workspace_id ON notifications(workspace_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_priority ON notifications(priority);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON notifications(expires_at);
CREATE INDEX IF NOT EXISTS idx_websocket_connections_user_id ON websocket_connections(user_id);
CREATE INDEX IF NOT EXISTS idx_websocket_connections_workspace_id ON websocket_connections(workspace_id);
CREATE INDEX IF NOT EXISTS idx_websocket_connections_last_ping ON websocket_connections(last_ping);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_notification_id ON notification_delivery_log(notification_id);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_user_id ON notification_delivery_log(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_delivery_log_delivered_at ON notification_delivery_log(delivered_at);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_workspace ON notification_preferences(user_id, workspace_id);

-- Contract Analytics Tables

-- Analytics Dashboards table for custom dashboard configurations
CREATE TABLE IF NOT EXISTS analytics_dashboards (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    configuration JSONB NOT NULL DEFAULT '{}',
    is_default BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE
);

-- Analytics Metrics Cache table for performance optimization
CREATE TABLE IF NOT EXISTS analytics_metrics_cache (
    id SERIAL PRIMARY KEY,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    metric_type TEXT NOT NULL,
    time_range TEXT NOT NULL,
    cache_key TEXT NOT NULL UNIQUE,
    cached_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- Analytics Reports table for scheduled and saved reports
CREATE TABLE IF NOT EXISTS analytics_reports (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    report_type TEXT NOT NULL CHECK (report_type IN ('overview', 'trends', 'performance', 'risk', 'custom')),
    configuration JSONB NOT NULL DEFAULT '{}',
    schedule JSONB, -- For scheduled reports
    last_generated TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE
);

-- Analytics Insights table for storing AI-generated insights
CREATE TABLE IF NOT EXISTS analytics_insights (
    id SERIAL PRIMARY KEY,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    insight_type TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    confidence_score DECIMAL(3,2) DEFAULT 0.5,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_actionable BOOLEAN DEFAULT FALSE,
    action_taken BOOLEAN DEFAULT FALSE
);

-- Indexes for analytics tables
CREATE INDEX IF NOT EXISTS idx_analytics_dashboards_workspace_id ON analytics_dashboards(workspace_id);
CREATE INDEX IF NOT EXISTS idx_analytics_dashboards_created_by ON analytics_dashboards(created_by);
CREATE INDEX IF NOT EXISTS idx_analytics_dashboards_is_default ON analytics_dashboards(is_default);
CREATE INDEX IF NOT EXISTS idx_analytics_metrics_cache_workspace_id ON analytics_metrics_cache(workspace_id);
CREATE INDEX IF NOT EXISTS idx_analytics_metrics_cache_expires_at ON analytics_metrics_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_analytics_metrics_cache_cache_key ON analytics_metrics_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_analytics_reports_workspace_id ON analytics_reports(workspace_id);
CREATE INDEX IF NOT EXISTS idx_analytics_reports_created_by ON analytics_reports(created_by);
CREATE INDEX IF NOT EXISTS idx_analytics_reports_report_type ON analytics_reports(report_type);
CREATE INDEX IF NOT EXISTS idx_analytics_insights_workspace_id ON analytics_insights(workspace_id);
CREATE INDEX IF NOT EXISTS idx_analytics_insights_insight_type ON analytics_insights(insight_type);
CREATE INDEX IF NOT EXISTS idx_analytics_insights_created_at ON analytics_insights(created_at);

-- Comprehensive Audit Logging Tables

-- Main audit logs table with immutable storage design
CREATE TABLE IF NOT EXISTS audit_logs (
    event_id TEXT PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    event_type TEXT NOT NULL CHECK (event_type IN ('authentication', 'authorization', 'data_access', 'data_modification', 'system_configuration', 'ai_operation', 'file_operation', 'security_event', 'compliance_event', 'admin_action')),
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    outcome TEXT NOT NULL CHECK (outcome IN ('success', 'failure', 'partial', 'error')),
    user_id TEXT REFERENCES users(id),
    workspace_id TEXT REFERENCES workspaces(id),
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id TEXT,
    details JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    hash_chain TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Immutable audit logs archive for long-term retention
CREATE TABLE IF NOT EXISTS audit_logs_archive (
    event_id TEXT PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL,
    outcome TEXT NOT NULL,
    user_id TEXT,
    workspace_id TEXT,
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id TEXT,
    details JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    hash_chain TEXT NOT NULL,
    archived_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Security alerts table for critical events
CREATE TABLE IF NOT EXISTS security_alerts (
    alert_id TEXT PRIMARY KEY,
    event_id TEXT NOT NULL REFERENCES audit_logs(event_id),
    alert_type TEXT NOT NULL,
    severity TEXT NOT NULL,
    message TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by TEXT REFERENCES users(id),
    acknowledged_at TIMESTAMP WITH TIME ZONE
);

-- Compliance reports table for audit compliance tracking
CREATE TABLE IF NOT EXISTS compliance_reports (
    report_id TEXT PRIMARY KEY,
    report_type TEXT NOT NULL,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    generated_by TEXT NOT NULL REFERENCES users(id),
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    report_data JSONB NOT NULL,
    compliance_score DECIMAL(5,2),
    violations_count INTEGER DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'generated' CHECK (status IN ('generated', 'reviewed', 'approved', 'archived'))
);

-- Audit retention policies table for compliance management
CREATE TABLE IF NOT EXISTS audit_retention_policies (
    policy_id SERIAL PRIMARY KEY,
    event_type TEXT NOT NULL,
    retention_days INTEGER NOT NULL,
    archive_after_days INTEGER,
    delete_after_days INTEGER,
    compliance_requirement TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(event_type)
);

-- Indexes for audit logging tables
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_workspace_id ON audit_logs(workspace_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_severity ON audit_logs(severity);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON audit_logs(resource_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_id ON audit_logs(resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_outcome ON audit_logs(outcome);
CREATE INDEX IF NOT EXISTS idx_audit_logs_session_id ON audit_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs(ip_address);
CREATE INDEX IF NOT EXISTS idx_audit_logs_hash_chain ON audit_logs(hash_chain);

CREATE INDEX IF NOT EXISTS idx_audit_logs_archive_timestamp ON audit_logs_archive(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_archive_event_type ON audit_logs_archive(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_archive_user_id ON audit_logs_archive(user_id);

CREATE INDEX IF NOT EXISTS idx_security_alerts_event_id ON security_alerts(event_id);
CREATE INDEX IF NOT EXISTS idx_security_alerts_severity ON security_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_security_alerts_acknowledged ON security_alerts(acknowledged);
CREATE INDEX IF NOT EXISTS idx_security_alerts_created_at ON security_alerts(created_at);

CREATE INDEX IF NOT EXISTS idx_compliance_reports_generated_at ON compliance_reports(generated_at);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_period ON compliance_reports(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_status ON compliance_reports(status);

-- Row Level Security Policies

-- Users table policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY users_select_policy ON users
    FOR SELECT USING (true);  -- Anyone can view users

CREATE POLICY users_insert_policy ON users
    FOR INSERT WITH CHECK (auth.uid()::text = id);  -- Only the user can insert their own record

CREATE POLICY users_update_policy ON users
    FOR UPDATE USING (auth.uid()::text = id);  -- Only the user can update their own record

-- Workspaces table policies
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;

CREATE POLICY workspaces_select_policy ON workspaces
    FOR SELECT USING (
        id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view workspaces they are members of

CREATE POLICY workspaces_insert_policy ON workspaces
    FOR INSERT WITH CHECK (created_by = auth.uid()::text);  -- Only the creator can insert a workspace

CREATE POLICY workspaces_update_policy ON workspaces
    FOR UPDATE USING (
        id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can update workspaces

CREATE POLICY workspaces_delete_policy ON workspaces
    FOR DELETE USING (
        id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete workspaces

-- Workspace members table policies
ALTER TABLE workspace_members ENABLE ROW LEVEL SECURITY;

CREATE POLICY workspace_members_select_policy ON workspace_members
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view members of workspaces they are members of

CREATE POLICY workspace_members_insert_policy ON workspace_members
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) OR
        (user_id = auth.uid()::text AND EXISTS (
            SELECT 1 FROM workspaces WHERE id = workspace_id AND created_by = auth.uid()::text
        ))
    );  -- Only workspace admins can add members, or users can add themselves to workspaces they created

-- Contracts table policies
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;

CREATE POLICY contracts_select_policy ON contracts
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view contracts in workspaces they are members of

CREATE POLICY contracts_insert_policy ON contracts
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create contracts in workspaces they are members of

CREATE POLICY contracts_update_policy ON contracts
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update contracts in workspaces they are members of

CREATE POLICY contracts_delete_policy ON contracts
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) OR
        (created_by->>'id' = auth.uid()::text)
    );  -- Only workspace admins or the creator can delete contracts

-- Templates table policies
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;

CREATE POLICY templates_select_policy ON templates
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view templates in workspaces they are members of

CREATE POLICY templates_insert_policy ON templates
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create templates in workspaces they are members of

CREATE POLICY templates_update_policy ON templates
    FOR UPDATE USING (
        (created_by->>'id' = auth.uid()::text) OR
        (workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ))
    );  -- Only the creator or workspace admins can update templates

CREATE POLICY templates_delete_policy ON templates
    FOR DELETE USING (
        (created_by->>'id' = auth.uid()::text) OR
        (workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ))
    );  -- Only the creator or workspace admins can delete templates

-- Folders table policies
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;

CREATE POLICY folders_select_policy ON folders
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view folders in workspaces they are members of

CREATE POLICY folders_insert_policy ON folders
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create folders in workspaces they are members of

CREATE POLICY folders_update_policy ON folders
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update folders in workspaces they are members of

CREATE POLICY folders_delete_policy ON folders
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) OR
        (created_by = auth.uid()::text)
    );  -- Only workspace admins or the creator can delete folders

-- Contract signers table policies
ALTER TABLE contract_signers ENABLE ROW LEVEL SECURITY;

CREATE POLICY contract_signers_select_policy ON contract_signers
    FOR SELECT USING (
        contract_id IN (
            SELECT id FROM contracts WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only view signers for contracts in workspaces they are members of

CREATE POLICY contract_signers_insert_policy ON contract_signers
    FOR INSERT WITH CHECK (
        contract_id IN (
            SELECT id FROM contracts WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only add signers to contracts in workspaces they are members of

CREATE POLICY contract_signers_update_policy ON contract_signers
    FOR UPDATE USING (
        contract_id IN (
            SELECT id FROM contracts WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only update signers for contracts in workspaces they are members of

CREATE POLICY contract_signers_delete_policy ON contract_signers
    FOR DELETE USING (
        contract_id IN (
            SELECT id FROM contracts WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only delete signers for contracts in workspaces they are members of

-- Clauses table policies
ALTER TABLE clauses ENABLE ROW LEVEL SECURITY;

CREATE POLICY clauses_select_policy ON clauses
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view clauses in workspaces they are members of

CREATE POLICY clauses_insert_policy ON clauses
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create clauses in workspaces they are members of

CREATE POLICY clauses_update_policy ON clauses
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND (
            created_by = auth.uid()::text OR
            auth.uid() IN (
                SELECT user_id FROM workspace_members
                WHERE workspace_id = clauses.workspace_id AND role_id = 'role-admin'
            )
        )
    );  -- Only the creator or workspace admins can update clauses in their workspace

CREATE POLICY clauses_delete_policy ON clauses
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND (
            created_by = auth.uid()::text OR
            auth.uid() IN (
                SELECT user_id FROM workspace_members
                WHERE workspace_id = clauses.workspace_id AND role_id = 'role-admin'
            )
        )
    );  -- Only the creator or workspace admins can delete clauses in their workspace

-- Documents table policies
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

CREATE POLICY documents_select_policy ON documents
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view documents in workspaces they are members of

CREATE POLICY documents_insert_policy ON documents
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create documents in workspaces they are members of

CREATE POLICY documents_update_policy ON documents
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update documents in workspaces they are members of

CREATE POLICY documents_delete_policy ON documents
    FOR DELETE USING (
        (created_by = auth.uid()::text) OR
        (workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ))
    );  -- Only the creator or workspace admins can delete documents

-- Document Signers table policies
ALTER TABLE document_signers ENABLE ROW LEVEL SECURITY;

CREATE POLICY document_signers_select_policy ON document_signers
    FOR SELECT USING (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only view signers for documents in workspaces they are members of

CREATE POLICY document_signers_insert_policy ON document_signers
    FOR INSERT WITH CHECK (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only add signers to documents in workspaces they are members of

CREATE POLICY document_signers_update_policy ON document_signers
    FOR UPDATE USING (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only update signers for documents in workspaces they are members of

CREATE POLICY document_signers_delete_policy ON document_signers
    FOR DELETE USING (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can only delete signers for documents in workspaces they are members of

-- Permissions table policies
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;

CREATE POLICY permissions_select_policy ON permissions
    FOR SELECT USING (true);  -- All authenticated users can view permissions

CREATE POLICY permissions_insert_policy ON permissions
    FOR INSERT WITH CHECK (false);  -- Only system can insert permissions

CREATE POLICY permissions_update_policy ON permissions
    FOR UPDATE USING (false);  -- Only system can update permissions

CREATE POLICY permissions_delete_policy ON permissions
    FOR DELETE USING (false);  -- Only system can delete permissions

-- Roles table policies
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY roles_select_policy ON roles
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view roles in workspaces they are members of

CREATE POLICY roles_insert_policy ON roles
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can create roles

CREATE POLICY roles_update_policy ON roles
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) AND is_system = false
    );  -- Only workspace admins can update non-system roles

CREATE POLICY roles_delete_policy ON roles
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) AND is_system = false
    );  -- Only workspace admins can delete non-system roles

-- Notifications table policies
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY notifications_select_policy ON notifications
    FOR SELECT USING (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view their own notifications in workspaces they are members of

CREATE POLICY notifications_insert_policy ON notifications
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create notifications in workspaces they are members of

CREATE POLICY notifications_update_policy ON notifications
    FOR UPDATE USING (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update their own notifications

CREATE POLICY notifications_delete_policy ON notifications
    FOR DELETE USING (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only delete their own notifications

-- Activity Logs table policies
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY activity_logs_select_policy ON activity_logs
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view activity logs in workspaces they are members of

CREATE POLICY activity_logs_insert_policy ON activity_logs
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create activity logs in workspaces they are members of

CREATE POLICY activity_logs_update_policy ON activity_logs
    FOR UPDATE USING (false);  -- Activity logs cannot be updated

CREATE POLICY activity_logs_delete_policy ON activity_logs
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete activity logs

-- Export History table policies
ALTER TABLE export_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY export_history_select_policy ON export_history
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view export history in workspaces they are members of

CREATE POLICY export_history_insert_policy ON export_history
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create export history in workspaces they are members of

CREATE POLICY export_history_update_policy ON export_history
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update export history in workspaces they are members of

CREATE POLICY export_history_delete_policy ON export_history
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete export history

-- AI Analysis Results table policies
ALTER TABLE ai_analysis_results ENABLE ROW LEVEL SECURITY;

CREATE POLICY ai_analysis_results_select_policy ON ai_analysis_results
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only view AI analysis results in workspaces they are members of

CREATE POLICY ai_analysis_results_insert_policy ON ai_analysis_results
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only create AI analysis results in workspaces they are members of

CREATE POLICY ai_analysis_results_update_policy ON ai_analysis_results
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only update AI analysis results in workspaces they are members of

CREATE POLICY ai_analysis_results_delete_policy ON ai_analysis_results
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can only delete AI analysis results in workspaces they are members of

-- Batch Jobs table policies
ALTER TABLE batch_jobs ENABLE ROW LEVEL SECURITY;

CREATE POLICY batch_jobs_select_policy ON batch_jobs
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) OR user_id = auth.uid()::text
    );  -- Users can view batch jobs in their workspaces or their own jobs

CREATE POLICY batch_jobs_insert_policy ON batch_jobs
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND user_id = auth.uid()::text
    );  -- Users can create batch jobs in workspaces they are members of

CREATE POLICY batch_jobs_update_policy ON batch_jobs
    FOR UPDATE USING (
        user_id = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Users can update their own jobs or workspace admins can update any job

CREATE POLICY batch_jobs_delete_policy ON batch_jobs
    FOR DELETE USING (
        user_id = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Users can delete their own jobs or workspace admins can delete any job

-- Batch Job Results table policies
ALTER TABLE batch_job_results ENABLE ROW LEVEL SECURITY;

CREATE POLICY batch_job_results_select_policy ON batch_job_results
    FOR SELECT USING (
        job_id IN (
            SELECT id FROM batch_jobs WHERE
            workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            ) OR user_id = auth.uid()::text
        )
    );  -- Users can view results for jobs in their workspaces or their own jobs

CREATE POLICY batch_job_results_insert_policy ON batch_job_results
    FOR INSERT WITH CHECK (
        job_id IN (
            SELECT id FROM batch_jobs WHERE user_id = auth.uid()::text
        )
    );  -- Only job owners can insert results

CREATE POLICY batch_job_results_update_policy ON batch_job_results
    FOR UPDATE USING (
        job_id IN (
            SELECT id FROM batch_jobs WHERE user_id = auth.uid()::text
        )
    );  -- Only job owners can update results

CREATE POLICY batch_job_results_delete_policy ON batch_job_results
    FOR DELETE USING (
        job_id IN (
            SELECT id FROM batch_jobs WHERE
            user_id = auth.uid()::text OR
            workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
            )
        )
    );  -- Job owners or workspace admins can delete results

-- Enhanced Lifecycle Management table policies
ALTER TABLE contract_branches ENABLE ROW LEVEL SECURITY;

CREATE POLICY contract_branches_select_policy ON contract_branches
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can view branches in their workspaces

CREATE POLICY contract_branches_insert_policy ON contract_branches
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND created_by = auth.uid()::text
    );  -- Users can create branches in workspaces they are members of

CREATE POLICY contract_branches_update_policy ON contract_branches
    FOR UPDATE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Branch creators or workspace admins can update branches

CREATE POLICY contract_branches_delete_policy ON contract_branches
    FOR DELETE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Branch creators or workspace admins can delete branches

-- Merge Requests table policies
ALTER TABLE merge_requests ENABLE ROW LEVEL SECURITY;

CREATE POLICY merge_requests_select_policy ON merge_requests
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can view merge requests in their workspaces

CREATE POLICY merge_requests_insert_policy ON merge_requests
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND created_by = auth.uid()::text
    );  -- Users can create merge requests in workspaces they are members of

CREATE POLICY merge_requests_update_policy ON merge_requests
    FOR UPDATE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Merge request creators or workspace admins can update merge requests

CREATE POLICY merge_requests_delete_policy ON merge_requests
    FOR DELETE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Merge request creators or workspace admins can delete merge requests

-- Version Diffs table policies
ALTER TABLE version_diffs ENABLE ROW LEVEL SECURITY;

CREATE POLICY version_diffs_select_policy ON version_diffs
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can view version diffs in their workspaces

CREATE POLICY version_diffs_insert_policy ON version_diffs
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can create version diffs in workspaces they are members of

CREATE POLICY version_diffs_update_policy ON version_diffs
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can update version diffs in their workspaces

CREATE POLICY version_diffs_delete_policy ON version_diffs
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete version diffs

-- Enhanced Repository table policies
ALTER TABLE document_tags ENABLE ROW LEVEL SECURITY;

CREATE POLICY document_tags_select_policy ON document_tags
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can view tags in their workspaces

CREATE POLICY document_tags_insert_policy ON document_tags
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND created_by = auth.uid()::text
    );  -- Users can create tags in workspaces they are members of

CREATE POLICY document_tags_update_policy ON document_tags
    FOR UPDATE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Tag creators or workspace admins can update tags

CREATE POLICY document_tags_delete_policy ON document_tags
    FOR DELETE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Tag creators or workspace admins can delete tags

-- Document Collections table policies
ALTER TABLE document_collections ENABLE ROW LEVEL SECURITY;

CREATE POLICY document_collections_select_policy ON document_collections
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can view collections in their workspaces

CREATE POLICY document_collections_insert_policy ON document_collections
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND created_by = auth.uid()::text
    );  -- Users can create collections in workspaces they are members of

CREATE POLICY document_collections_update_policy ON document_collections
    FOR UPDATE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Collection creators or workspace admins can update collections

CREATE POLICY document_collections_delete_policy ON document_collections
    FOR DELETE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Collection creators or workspace admins can delete collections

-- Document Tag Assignments table policies
ALTER TABLE document_tag_assignments ENABLE ROW LEVEL SECURITY;

CREATE POLICY document_tag_assignments_select_policy ON document_tag_assignments
    FOR SELECT USING (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can view tag assignments for documents in their workspaces

CREATE POLICY document_tag_assignments_insert_policy ON document_tag_assignments
    FOR INSERT WITH CHECK (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        ) AND assigned_by = auth.uid()::text
    );  -- Users can assign tags to documents in their workspaces

CREATE POLICY document_tag_assignments_delete_policy ON document_tag_assignments
    FOR DELETE USING (
        assigned_by = auth.uid()::text OR
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
            )
        )
    );  -- Tag assigners or workspace admins can remove tag assignments

-- Document Collection Assignments table policies
ALTER TABLE document_collection_assignments ENABLE ROW LEVEL SECURITY;

CREATE POLICY document_collection_assignments_select_policy ON document_collection_assignments
    FOR SELECT USING (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Users can view collection assignments for documents in their workspaces

CREATE POLICY document_collection_assignments_insert_policy ON document_collection_assignments
    FOR INSERT WITH CHECK (
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        ) AND assigned_by = auth.uid()::text
    );  -- Users can assign documents to collections in their workspaces

CREATE POLICY document_collection_assignments_delete_policy ON document_collection_assignments
    FOR DELETE USING (
        assigned_by = auth.uid()::text OR
        document_id IN (
            SELECT id FROM documents WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
            )
        )
    );  -- Collection assigners or workspace admins can remove collection assignments

-- Search Analytics table policies
ALTER TABLE search_analytics ENABLE ROW LEVEL SECURITY;

CREATE POLICY search_analytics_select_policy ON search_analytics
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can view search analytics

CREATE POLICY search_analytics_insert_policy ON search_analytics
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND user_id = auth.uid()::text
    );  -- Users can create search analytics in workspaces they are members of

-- Real-time Notifications table policies
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY notifications_select_policy ON notifications
    FOR SELECT USING (
        user_id = auth.uid()::text OR
        (user_id IS NULL AND workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ))
    );  -- Users can view their own notifications or workspace broadcasts

CREATE POLICY notifications_insert_policy ON notifications
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can create notifications in workspaces they are members of

CREATE POLICY notifications_update_policy ON notifications
    FOR UPDATE USING (
        user_id = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Users can update their own notifications or workspace admins can update any

CREATE POLICY notifications_delete_policy ON notifications
    FOR DELETE USING (
        user_id = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Users can delete their own notifications or workspace admins can delete any

-- WebSocket Connections table policies
ALTER TABLE websocket_connections ENABLE ROW LEVEL SECURITY;

CREATE POLICY websocket_connections_select_policy ON websocket_connections
    FOR SELECT USING (
        user_id = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Users can view their own connections or workspace admins can view all

CREATE POLICY websocket_connections_insert_policy ON websocket_connections
    FOR INSERT WITH CHECK (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can create their own connections in workspaces they are members of

CREATE POLICY websocket_connections_update_policy ON websocket_connections
    FOR UPDATE USING (
        user_id = auth.uid()::text
    );  -- Users can update their own connections

CREATE POLICY websocket_connections_delete_policy ON websocket_connections
    FOR DELETE USING (
        user_id = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Users can delete their own connections or workspace admins can delete any

-- Notification Delivery Log table policies
ALTER TABLE notification_delivery_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY notification_delivery_log_select_policy ON notification_delivery_log
    FOR SELECT USING (
        user_id = auth.uid()::text OR
        notification_id IN (
            SELECT id FROM notifications WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members
                WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
            )
        )
    );  -- Users can view their own delivery logs or workspace admins can view all

CREATE POLICY notification_delivery_log_insert_policy ON notification_delivery_log
    FOR INSERT WITH CHECK (
        notification_id IN (
            SELECT id FROM notifications WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
            )
        )
    );  -- Can create delivery logs for notifications in accessible workspaces

-- Notification Preferences table policies
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY notification_preferences_select_policy ON notification_preferences
    FOR SELECT USING (
        user_id = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Users can view their own preferences or workspace admins can view all

CREATE POLICY notification_preferences_insert_policy ON notification_preferences
    FOR INSERT WITH CHECK (
        user_id = auth.uid()::text AND
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can create their own preferences in workspaces they are members of

CREATE POLICY notification_preferences_update_policy ON notification_preferences
    FOR UPDATE USING (
        user_id = auth.uid()::text
    );  -- Users can update their own preferences

CREATE POLICY notification_preferences_delete_policy ON notification_preferences
    FOR DELETE USING (
        user_id = auth.uid()::text
    );  -- Users can delete their own preferences

-- Analytics tables policies
ALTER TABLE analytics_dashboards ENABLE ROW LEVEL SECURITY;

CREATE POLICY analytics_dashboards_select_policy ON analytics_dashboards
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can view dashboards in their workspaces

CREATE POLICY analytics_dashboards_insert_policy ON analytics_dashboards
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND created_by = auth.uid()::text
    );  -- Users can create dashboards in workspaces they are members of

CREATE POLICY analytics_dashboards_update_policy ON analytics_dashboards
    FOR UPDATE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Dashboard creators or workspace admins can update dashboards

CREATE POLICY analytics_dashboards_delete_policy ON analytics_dashboards
    FOR DELETE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Dashboard creators or workspace admins can delete dashboards

-- Analytics Metrics Cache table policies
ALTER TABLE analytics_metrics_cache ENABLE ROW LEVEL SECURITY;

CREATE POLICY analytics_metrics_cache_select_policy ON analytics_metrics_cache
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can view cached metrics for their workspaces

CREATE POLICY analytics_metrics_cache_insert_policy ON analytics_metrics_cache
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can cache metrics for workspaces they are members of

CREATE POLICY analytics_metrics_cache_delete_policy ON analytics_metrics_cache
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can delete cached metrics for their workspaces

-- Analytics Reports table policies
ALTER TABLE analytics_reports ENABLE ROW LEVEL SECURITY;

CREATE POLICY analytics_reports_select_policy ON analytics_reports
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can view reports in their workspaces

CREATE POLICY analytics_reports_insert_policy ON analytics_reports
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        ) AND created_by = auth.uid()::text
    );  -- Users can create reports in workspaces they are members of

CREATE POLICY analytics_reports_update_policy ON analytics_reports
    FOR UPDATE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Report creators or workspace admins can update reports

CREATE POLICY analytics_reports_delete_policy ON analytics_reports
    FOR DELETE USING (
        created_by = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Report creators or workspace admins can delete reports

-- Analytics Insights table policies
ALTER TABLE analytics_insights ENABLE ROW LEVEL SECURITY;

CREATE POLICY analytics_insights_select_policy ON analytics_insights
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can view insights for their workspaces

CREATE POLICY analytics_insights_insert_policy ON analytics_insights
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can create insights for workspaces they are members of

CREATE POLICY analytics_insights_update_policy ON analytics_insights
    FOR UPDATE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()::text
        )
    );  -- Users can update insights for their workspaces

CREATE POLICY analytics_insights_delete_policy ON analytics_insights
    FOR DELETE USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        )
    );  -- Only workspace admins can delete insights

-- Audit Logging table policies
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY audit_logs_select_policy ON audit_logs
    FOR SELECT USING (
        user_id = auth.uid()::text OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()::text AND role_id = 'role-admin'
        ) OR
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Users can view their own audit logs, workspace admins can view workspace logs, system admins can view all

CREATE POLICY audit_logs_insert_policy ON audit_logs
    FOR INSERT WITH CHECK (true);  -- Allow all inserts for audit logging (system-generated)

-- Audit logs are immutable - no update or delete policies

-- Audit Logs Archive table policies
ALTER TABLE audit_logs_archive ENABLE ROW LEVEL SECURITY;

CREATE POLICY audit_logs_archive_select_policy ON audit_logs_archive
    FOR SELECT USING (
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Only system admins can view archived audit logs

CREATE POLICY audit_logs_archive_insert_policy ON audit_logs_archive
    FOR INSERT WITH CHECK (true);  -- Allow all inserts for audit archiving (system-generated)

-- Security Alerts table policies
ALTER TABLE security_alerts ENABLE ROW LEVEL SECURITY;

CREATE POLICY security_alerts_select_policy ON security_alerts
    FOR SELECT USING (
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Only system admins can view security alerts

CREATE POLICY security_alerts_insert_policy ON security_alerts
    FOR INSERT WITH CHECK (true);  -- Allow all inserts for security alerts (system-generated)

CREATE POLICY security_alerts_update_policy ON security_alerts
    FOR UPDATE USING (
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Only system admins can acknowledge security alerts

-- Compliance Reports table policies
ALTER TABLE compliance_reports ENABLE ROW LEVEL SECURITY;

CREATE POLICY compliance_reports_select_policy ON compliance_reports
    FOR SELECT USING (
        generated_by = auth.uid()::text OR
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Report generators and system admins can view compliance reports

CREATE POLICY compliance_reports_insert_policy ON compliance_reports
    FOR INSERT WITH CHECK (
        generated_by = auth.uid()::text AND
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Only system admins can generate compliance reports

CREATE POLICY compliance_reports_update_policy ON compliance_reports
    FOR UPDATE USING (
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Only system admins can update compliance reports

-- Audit Retention Policies table policies
ALTER TABLE audit_retention_policies ENABLE ROW LEVEL SECURITY;

CREATE POLICY audit_retention_policies_select_policy ON audit_retention_policies
    FOR SELECT USING (
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Only system admins can view retention policies

CREATE POLICY audit_retention_policies_insert_policy ON audit_retention_policies
    FOR INSERT WITH CHECK (
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Only system admins can create retention policies

CREATE POLICY audit_retention_policies_update_policy ON audit_retention_policies
    FOR UPDATE USING (
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Only system admins can update retention policies

CREATE POLICY audit_retention_policies_delete_policy ON audit_retention_policies
    FOR DELETE USING (
        auth.uid()::text IN (
            SELECT id FROM users WHERE role = 'admin'
        )
    );  -- Only system admins can delete retention policies
