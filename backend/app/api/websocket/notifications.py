"""
WebSocket endpoints for real-time notifications.
"""

import json
import asyncio
import logging
from typing import Dict, Set, Optional
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.routing import <PERSON><PERSON><PERSON><PERSON>
import jwt

from app.core.auth import verify_jwt_token
from app.schemas.notification import NotificationWebSocketMessage
from app.db.database import get_supabase_client

logger = logging.getLogger(__name__)

# WebSocket connection manager
class NotificationConnectionManager:
    def __init__(self):
        # Store connections by user_id and workspace_id
        self.connections: Dict[str, Dict[str, Set[WebSocket]]] = {}
        # Store user info for each connection
        self.connection_info: Dict[WebSocket, Dict[str, str]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str, workspace_id: str):
        """Accept a WebSocket connection and store it."""
        await websocket.accept()
        
        # Initialize user connections if not exists
        if user_id not in self.connections:
            self.connections[user_id] = {}
        
        # Initialize workspace connections if not exists
        if workspace_id not in self.connections[user_id]:
            self.connections[user_id][workspace_id] = set()
        
        # Add connection
        self.connections[user_id][workspace_id].add(websocket)
        self.connection_info[websocket] = {
            "user_id": user_id,
            "workspace_id": workspace_id,
            "connected_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"WebSocket connected: user={user_id}, workspace={workspace_id}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.connection_info:
            info = self.connection_info[websocket]
            user_id = info["user_id"]
            workspace_id = info["workspace_id"]
            
            # Remove from connections
            if (user_id in self.connections and 
                workspace_id in self.connections[user_id] and 
                websocket in self.connections[user_id][workspace_id]):
                self.connections[user_id][workspace_id].remove(websocket)
                
                # Clean up empty sets
                if not self.connections[user_id][workspace_id]:
                    del self.connections[user_id][workspace_id]
                
                # Clean up empty user dict
                if not self.connections[user_id]:
                    del self.connections[user_id]
            
            # Remove connection info
            del self.connection_info[websocket]
            
            logger.info(f"WebSocket disconnected: user={user_id}, workspace={workspace_id}")
    
    async def send_to_user(self, user_id: str, workspace_id: str, message: dict):
        """Send a message to all connections for a specific user in a workspace."""
        if user_id in self.connections and workspace_id in self.connections[user_id]:
            connections = self.connections[user_id][workspace_id].copy()
            
            for websocket in connections:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending message to WebSocket: {e}")
                    # Remove broken connection
                    self.disconnect(websocket)
    
    async def send_to_workspace(self, workspace_id: str, message: dict, exclude_user_id: Optional[str] = None):
        """Send a message to all users in a workspace."""
        for user_id, workspaces in self.connections.items():
            if exclude_user_id and user_id == exclude_user_id:
                continue
                
            if workspace_id in workspaces:
                await self.send_to_user(user_id, workspace_id, message)
    
    async def broadcast_notification(self, notification_data: dict):
        """Broadcast a notification to the appropriate users."""
        user_id = notification_data.get("user_id")
        workspace_id = notification_data.get("workspace_id")
        
        if user_id and workspace_id:
            message = NotificationWebSocketMessage(
                type="notification",
                notification=notification_data,
                timestamp=datetime.utcnow()
            ).dict()
            
            await self.send_to_user(user_id, workspace_id, message)
    
    def get_connection_count(self) -> int:
        """Get total number of active connections."""
        count = 0
        for user_connections in self.connections.values():
            for workspace_connections in user_connections.values():
                count += len(workspace_connections)
        return count
    
    def get_user_connections(self, user_id: str) -> int:
        """Get number of connections for a specific user."""
        if user_id not in self.connections:
            return 0
        
        count = 0
        for workspace_connections in self.connections[user_id].values():
            count += len(workspace_connections)
        return count

# Global connection manager instance
manager = NotificationConnectionManager()

# WebSocket router
router = APIRouter()

@router.websocket("/notifications")
async def websocket_notifications(
    websocket: WebSocket,
    token: str = Query(..., description="JWT authentication token"),
    workspace_id: str = Query(..., description="Workspace ID to connect to")
):
    """
    WebSocket endpoint for real-time notifications.
    """
    try:
        # Verify JWT token
        try:
            user_data = verify_jwt_token(token)
            user_id = user_data["id"]
        except Exception as e:
            logger.error(f"WebSocket authentication failed: {e}")
            await websocket.close(code=4001, reason="Authentication failed")
            return
        
        # Verify user has access to workspace
        supabase = get_supabase_client()
        try:
            # Check workspace membership
            response = supabase.table("workspace_members").select("user_id").eq("user_id", user_id).eq("workspace_id", workspace_id).execute()
            
            if hasattr(response, 'error') and response.error:
                logger.error(f"Database error checking workspace membership: {response.error}")
                await websocket.close(code=4003, reason="Database error")
                return
            
            if not response.data:
                logger.warning(f"User {user_id} attempted to connect to unauthorized workspace {workspace_id}")
                await websocket.close(code=4003, reason="Unauthorized workspace access")
                return
                
        except Exception as e:
            logger.error(f"Error verifying workspace access: {e}")
            await websocket.close(code=4003, reason="Authorization error")
            return
        
        # Connect to WebSocket
        await manager.connect(websocket, user_id, workspace_id)
        
        # Send welcome message
        welcome_message = NotificationWebSocketMessage(
            type="connected",
            data={"message": "Connected to notification stream", "user_id": user_id, "workspace_id": workspace_id},
            timestamp=datetime.utcnow()
        ).dict()
        await websocket.send_text(json.dumps(welcome_message))
        
        # Keep connection alive and handle messages
        try:
            while True:
                # Wait for messages from client
                data = await websocket.receive_text()
                
                try:
                    message = json.loads(data)
                    message_type = message.get("type")
                    
                    if message_type == "ping":
                        # Respond to ping with pong
                        pong_message = NotificationWebSocketMessage(
                            type="pong",
                            timestamp=datetime.utcnow()
                        ).dict()
                        await websocket.send_text(json.dumps(pong_message))
                    
                    elif message_type == "subscribe":
                        # Handle subscription to specific notification types
                        # This could be extended to allow filtering
                        pass
                    
                    else:
                        logger.warning(f"Unknown message type: {message_type}")
                
                except json.JSONDecodeError:
                    logger.error("Invalid JSON received from WebSocket client")
                    
        except WebSocketDisconnect:
            logger.info(f"WebSocket client disconnected: user={user_id}, workspace={workspace_id}")
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            manager.disconnect(websocket)
            
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        try:
            await websocket.close(code=4000, reason="Internal server error")
        except:
            pass

@router.get("/connections/stats")
async def get_connection_stats():
    """
    Get WebSocket connection statistics.
    """
    return {
        "total_connections": manager.get_connection_count(),
        "connected_users": len(manager.connections),
        "timestamp": datetime.utcnow().isoformat()
    }

# Function to broadcast notifications (to be used by notification service)
async def broadcast_notification(notification_data: dict):
    """
    Broadcast a notification to connected WebSocket clients.
    This function can be called from the notification service.
    """
    await manager.broadcast_notification(notification_data)

# Function to send notification to specific user
async def send_notification_to_user(user_id: str, workspace_id: str, notification_data: dict):
    """
    Send a notification to a specific user's WebSocket connections.
    """
    message = NotificationWebSocketMessage(
        type="notification",
        notification=notification_data,
        timestamp=datetime.utcnow()
    ).dict()
    
    await manager.send_to_user(user_id, workspace_id, message)

# Function to get connection manager (for external use)
def get_connection_manager() -> NotificationConnectionManager:
    """
    Get the global connection manager instance.
    """
    return manager
