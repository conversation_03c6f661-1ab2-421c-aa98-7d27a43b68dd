"""
Performance Monitoring and Optimization API endpoints for Averum Contracts
Provides performance metrics, optimization controls, and system monitoring
"""

from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.performance_service import performance_service

router = APIRouter()


class PerformanceOptimizationRequest(BaseModel):
    """Request model for performance optimization."""
    optimization_type: str  # "memory", "cache", "parallel"
    parameters: Optional[Dict[str, Any]] = None


def get_client_info(request: Request) -> Dict[str, Optional[str]]:
    """Extract client information for monitoring."""
    return {
        "ip_address": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent"),
        "endpoint": str(request.url.path)
    }


@router.get("/dashboard")
@performance_service.track_request("performance_dashboard")
async def get_performance_dashboard(
    current_user: dict = Depends(get_current_user)
):
    """
    Get comprehensive performance dashboard data.
    
    Returns:
        Performance metrics, resource usage, and optimization recommendations
    """
    try:
        # Check if user has admin privileges (simplified check)
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for performance dashboard")
        
        dashboard_data = await performance_service.get_performance_dashboard()
        
        return {
            "success": True,
            "dashboard": dashboard_data,
            "timestamp": dashboard_data.get("resource_metrics", {}).get("monitoring_active", False)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get performance dashboard: {str(e)}")


@router.get("/metrics")
@performance_service.track_request("performance_metrics")
async def get_performance_metrics(
    minutes: int = 30,
    current_user: dict = Depends(get_current_user)
):
    """
    Get performance metrics for the specified time period.
    
    Args:
        minutes: Number of minutes of historical data to retrieve
        current_user: Current authenticated user
        
    Returns:
        Performance metrics and trends
    """
    try:
        # Check admin access
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for performance metrics")
        
        # Get recent metrics
        recent_metrics = performance_service.resource_monitor.get_recent_metrics(minutes)
        
        # Calculate trends
        if len(recent_metrics) >= 2:
            latest = recent_metrics[-1]
            previous = recent_metrics[0]
            
            trends = {
                "memory_trend": latest.memory_usage - previous.memory_usage,
                "cpu_trend": latest.cpu_usage - previous.cpu_usage,
                "cache_trend": latest.cache_hit_rate - previous.cache_hit_rate,
                "requests_trend": latest.concurrent_requests - previous.concurrent_requests
            }
        else:
            trends = {}
        
        return {
            "success": True,
            "metrics": [
                {
                    "timestamp": m.timestamp.isoformat(),
                    "memory_usage": m.memory_usage,
                    "cpu_usage": m.cpu_usage,
                    "cache_hit_rate": m.cache_hit_rate,
                    "concurrent_requests": m.concurrent_requests,
                    "response_time": m.response_time
                }
                for m in recent_metrics
            ],
            "trends": trends,
            "summary": performance_service.resource_monitor.get_performance_summary()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get performance metrics: {str(e)}")


@router.get("/endpoints")
@performance_service.track_request("performance_endpoints")
async def get_endpoint_performance(
    current_user: dict = Depends(get_current_user)
):
    """
    Get performance statistics for all API endpoints.
    
    Returns:
        Endpoint performance data and recommendations
    """
    try:
        # Check admin access
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for endpoint performance")
        
        performance_report = performance_service.response_optimizer.get_performance_report()
        
        # Add optimization recommendations
        recommendations = []
        for endpoint_data in performance_report.get("slowest_endpoints", []):
            if endpoint_data.get("avg_response_time", 0) > 2.0:
                recommendations.append({
                    "endpoint": endpoint_data["endpoint"],
                    "issue": "Slow response time",
                    "recommendation": "Consider adding caching or optimizing database queries",
                    "priority": "high" if endpoint_data["avg_response_time"] > 5.0 else "medium"
                })
        
        return {
            "success": True,
            "performance_report": performance_report,
            "recommendations": recommendations,
            "active_requests": performance_service.active_requests
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get endpoint performance: {str(e)}")


@router.post("/optimize")
@performance_service.track_request("performance_optimize")
async def optimize_performance(
    request_data: PerformanceOptimizationRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Trigger performance optimization operations.
    
    Args:
        request_data: Optimization request parameters
        current_user: Current authenticated user
        
    Returns:
        Optimization results
    """
    try:
        # Check admin access
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for performance optimization")
        
        optimization_type = request_data.optimization_type
        
        if optimization_type == "memory":
            await performance_service.optimize_memory_usage()
            result = {"message": "Memory optimization completed", "type": "memory"}
            
        elif optimization_type == "cache":
            from app.services.cache_service import ai_cache
            await ai_cache.cleanup_expired()
            cache_stats = await ai_cache.get_stats()
            result = {
                "message": "Cache optimization completed",
                "type": "cache",
                "cache_stats": cache_stats
            }
            
        elif optimization_type == "monitoring":
            if not performance_service.resource_monitor.monitoring_active:
                await performance_service.resource_monitor.start_monitoring()
                result = {"message": "Performance monitoring started", "type": "monitoring"}
            else:
                result = {"message": "Performance monitoring already active", "type": "monitoring"}
                
        else:
            raise HTTPException(status_code=400, detail=f"Unknown optimization type: {optimization_type}")
        
        return {
            "success": True,
            "optimization_result": result,
            "timestamp": performance_service.resource_monitor.get_performance_summary()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Performance optimization failed: {str(e)}")


@router.get("/system-info")
@performance_service.track_request("performance_system_info")
async def get_system_info(
    current_user: dict = Depends(get_current_user)
):
    """
    Get system information and resource limits.
    
    Returns:
        System information and current resource usage
    """
    try:
        # Check admin access
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for system information")
        
        import psutil
        
        # Get system information
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        system_info = {
            "cpu": {
                "count": psutil.cpu_count(),
                "usage_percent": psutil.cpu_percent(interval=1),
                "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": disk.percent
            },
            "network": {
                "connections": len(psutil.net_connections()),
            }
        }
        
        # Add performance service status
        service_status = {
            "monitoring_active": performance_service.resource_monitor.monitoring_active,
            "active_requests": performance_service.active_requests,
            "metrics_collected": len(performance_service.resource_monitor.metrics_history)
        }
        
        return {
            "success": True,
            "system_info": system_info,
            "service_status": service_status
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get system information: {str(e)}")


@router.post("/monitoring/start")
@performance_service.track_request("performance_monitoring_start")
async def start_monitoring(
    current_user: dict = Depends(get_current_user)
):
    """
    Start performance monitoring.
    
    Returns:
        Monitoring start status
    """
    try:
        # Check admin access
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required to control monitoring")
        
        if performance_service.resource_monitor.monitoring_active:
            return {
                "success": True,
                "message": "Performance monitoring is already active"
            }
        
        await performance_service.resource_monitor.start_monitoring()
        
        return {
            "success": True,
            "message": "Performance monitoring started successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start monitoring: {str(e)}")


@router.post("/monitoring/stop")
@performance_service.track_request("performance_monitoring_stop")
async def stop_monitoring(
    current_user: dict = Depends(get_current_user)
):
    """
    Stop performance monitoring.
    
    Returns:
        Monitoring stop status
    """
    try:
        # Check admin access
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required to control monitoring")
        
        if not performance_service.resource_monitor.monitoring_active:
            return {
                "success": True,
                "message": "Performance monitoring is not active"
            }
        
        await performance_service.resource_monitor.stop_monitoring()
        
        return {
            "success": True,
            "message": "Performance monitoring stopped successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stop monitoring: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for performance service.
    
    Returns:
        Service health status
    """
    try:
        summary = performance_service.resource_monitor.get_performance_summary()
        
        return {
            "status": "healthy",
            "service": "performance",
            "monitoring_active": summary.get("monitoring_active", False),
            "active_requests": performance_service.active_requests,
            "metrics_available": len(performance_service.resource_monitor.metrics_history) > 0
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "performance",
            "error": str(e)
        }
