"""
Batch AI Processing API endpoints for Averum Contracts
Provides bulk contract analysis, comparative analysis, and progress tracking
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.batch_ai_service import batch_ai_service, BatchJobStatus, BatchJobType

router = APIRouter()


class BulkAnalysisRequest(BaseModel):
    """Request model for bulk analysis."""
    contracts: List[Dict[str, Any]]  # List of {id, text, name}
    analysis_type: str = "comprehensive"
    job_name: Optional[str] = None


class ComparativeAnalysisRequest(BaseModel):
    """Request model for comparative analysis."""
    contracts: List[Dict[str, Any]]  # List of {id, text, name}
    comparison_criteria: List[str]
    job_name: Optional[str] = None


class JobCancellationRequest(BaseModel):
    """Request model for job cancellation."""
    job_id: str


def get_client_info(request: Request) -> Dict[str, Optional[str]]:
    """Extract client information for audit logging."""
    return {
        "ip_address": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent"),
        "session_id": request.headers.get("x-session-id")
    }


@router.post("/bulk-analysis")
async def create_bulk_analysis_job(
    request_data: BulkAnalysisRequest,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a bulk analysis job for multiple contracts.
    
    Args:
        request_data: Bulk analysis request data
        request: FastAPI request object for client info
        current_user: Current authenticated user
        
    Returns:
        Job ID and initial status
    """
    try:
        # Get user and workspace info
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        if not request_data.contracts:
            raise HTTPException(status_code=400, detail="No contracts provided")
        
        if len(request_data.contracts) > 50:
            raise HTTPException(status_code=400, detail="Too many contracts. Maximum 50 allowed per job.")
        
        # Validate contract data
        for i, contract in enumerate(request_data.contracts):
            if not contract.get("text"):
                raise HTTPException(status_code=400, detail=f"Contract {i+1} missing text content")
        
        # Create bulk analysis job
        job_id = await batch_ai_service.create_bulk_analysis_job(
            contracts=request_data.contracts,
            user_id=user_id,
            workspace_id=workspace_id,
            analysis_type=request_data.analysis_type,
            job_name=request_data.job_name
        )
        
        return {
            "success": True,
            "job_id": job_id,
            "message": f"Bulk analysis job created for {len(request_data.contracts)} contracts",
            "total_contracts": len(request_data.contracts),
            "analysis_type": request_data.analysis_type
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create bulk analysis job: {str(e)}")


@router.post("/comparative-analysis")
async def create_comparative_analysis_job(
    request_data: ComparativeAnalysisRequest,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a comparative analysis job for multiple contracts.
    
    Args:
        request_data: Comparative analysis request data
        request: FastAPI request object for client info
        current_user: Current authenticated user
        
    Returns:
        Job ID and initial status
    """
    try:
        # Get user and workspace info
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        if len(request_data.contracts) < 2:
            raise HTTPException(status_code=400, detail="Comparative analysis requires at least 2 contracts")
        
        if len(request_data.contracts) > 50:
            raise HTTPException(status_code=400, detail="Too many contracts. Maximum 50 allowed per job.")
        
        # Validate contract data
        for i, contract in enumerate(request_data.contracts):
            if not contract.get("text"):
                raise HTTPException(status_code=400, detail=f"Contract {i+1} missing text content")
        
        # Create comparative analysis job
        job_id = await batch_ai_service.create_comparative_analysis_job(
            contracts=request_data.contracts,
            user_id=user_id,
            workspace_id=workspace_id,
            comparison_criteria=request_data.comparison_criteria,
            job_name=request_data.job_name
        )
        
        return {
            "success": True,
            "job_id": job_id,
            "message": f"Comparative analysis job created for {len(request_data.contracts)} contracts",
            "total_contracts": len(request_data.contracts),
            "comparison_criteria": request_data.comparison_criteria
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create comparative analysis job: {str(e)}")


@router.get("/jobs/{job_id}/progress")
async def get_job_progress(
    job_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get progress information for a batch job.
    
    Args:
        job_id: Batch job ID
        current_user: Current authenticated user
        
    Returns:
        Job progress information
    """
    try:
        progress = await batch_ai_service.get_job_progress(job_id)
        
        if not progress:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return {
            "success": True,
            "job_id": progress.job_id,
            "status": progress.status.value,
            "total_items": progress.total_items,
            "completed_items": progress.completed_items,
            "failed_items": progress.failed_items,
            "progress_percentage": progress.progress_percentage,
            "current_item": progress.current_item,
            "started_at": progress.started_at.isoformat(),
            "updated_at": progress.updated_at.isoformat(),
            "estimated_completion": progress.estimated_completion.isoformat() if progress.estimated_completion else None
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job progress: {str(e)}")


@router.get("/jobs/{job_id}/results")
async def get_job_results(
    job_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get results for a completed batch job.
    
    Args:
        job_id: Batch job ID
        current_user: Current authenticated user
        
    Returns:
        Job results and analysis
    """
    try:
        results = await batch_ai_service.get_job_results(job_id)
        
        if not results:
            raise HTTPException(status_code=404, detail="Job results not found")
        
        return {
            "success": True,
            "job_id": results.job_id,
            "job_type": results.job_type.value,
            "status": results.status.value,
            "total_items": results.total_items,
            "successful_items": results.successful_items,
            "failed_items": results.failed_items,
            "results": results.results,
            "summary_statistics": results.summary_statistics,
            "comparative_insights": results.comparative_insights,
            "processing_time": results.processing_time,
            "created_at": results.created_at.isoformat(),
            "completed_at": results.completed_at.isoformat() if results.completed_at else None
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job results: {str(e)}")


@router.post("/jobs/{job_id}/cancel")
async def cancel_job(
    job_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Cancel a running batch job.
    
    Args:
        job_id: Batch job ID
        current_user: Current authenticated user
        
    Returns:
        Cancellation status
    """
    try:
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        success = await batch_ai_service.cancel_job(job_id, user_id)
        
        if not success:
            raise HTTPException(status_code=400, detail="Job cannot be cancelled (not found or already completed)")
        
        return {
            "success": True,
            "message": f"Job {job_id} cancelled successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cancel job: {str(e)}")


@router.get("/jobs")
async def list_user_jobs(
    status: Optional[str] = None,
    limit: int = 50,
    current_user: dict = Depends(get_current_user)
):
    """
    List batch jobs for the current user.
    
    Args:
        status: Optional status filter
        limit: Maximum number of jobs to return
        current_user: Current authenticated user
        
    Returns:
        List of user's batch jobs
    """
    try:
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # Validate status if provided
        job_status = None
        if status:
            try:
                job_status = BatchJobStatus(status)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")
        
        jobs = await batch_ai_service.list_user_jobs(
            user_id=user_id,
            workspace_id=workspace_id,
            status=job_status,
            limit=min(limit, 100)  # Cap at 100
        )
        
        return {
            "success": True,
            "jobs": jobs,
            "total_jobs": len(jobs)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list jobs: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for batch AI service.
    
    Returns:
        Service health status
    """
    try:
        active_jobs_count = len(batch_ai_service.active_jobs)
        
        return {
            "status": "healthy",
            "service": "batch_ai",
            "active_jobs": active_jobs_count,
            "max_concurrent_jobs": batch_ai_service.max_concurrent_jobs,
            "max_items_per_job": batch_ai_service.max_items_per_job
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "batch_ai",
            "error": str(e)
        }
