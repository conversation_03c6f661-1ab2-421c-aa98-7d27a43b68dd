"""
API endpoints for approval workflow management.
"""

from fastapi import APIRout<PERSON>, Depends, HTTPException, Query, Path
from typing import List, Optional
from datetime import datetime

from app.core.auth import get_current_user, get_authenticated_db_client
from app.services.approval_workflow_service import ApprovalWorkflowService
from app.schemas.approval_workflow import (
    ApprovalWorkflow,
    ApprovalWorkflowCreate,
    ApprovalAction,
    ApprovalUpdate,
    WorkflowProgress,
    WorkflowSummary,
    WorkflowFilters,
    ApprovalFilters,
    AIRoutingRecommendation,
    BulkApprovalAction,
    WorkflowTemplate,
    WorkflowTemplateCreate,
    Approver,
    WorkflowType,
    ApprovalStatus,
    WorkflowStatus,
    Priority
)

router = APIRouter()


@router.post("/", response_model=ApprovalWorkflow)
async def create_approval_workflow(
    workflow_data: ApprovalWorkflowCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new approval workflow for a contract.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        workflow_service = ApprovalWorkflowService(supabase)
        
        workflow = await workflow_service.create_workflow(
            contract_id=workflow_data.contract_id,
            workspace_id=workflow_data.workspace_id,
            workflow_type=workflow_data.workflow_type,
            approvers=[approver.dict() for approver in workflow_data.approvers],
            created_by=current_user["id"],
            template_id=workflow_data.template_id,
            due_date=workflow_data.due_date,
            escalation_rules=workflow_data.escalation_rules,
            conditions=workflow_data.conditions
        )
        
        return workflow
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create approval workflow: {str(e)}")


@router.get("/", response_model=List[ApprovalWorkflow])
async def get_approval_workflows(
    workspace_id: str = Query(..., description="Workspace ID"),
    status: Optional[WorkflowStatus] = Query(None, description="Filter by status"),
    workflow_type: Optional[WorkflowType] = Query(None, description="Filter by workflow type"),
    priority: Optional[Priority] = Query(None, description="Filter by priority"),
    created_by: Optional[str] = Query(None, description="Filter by creator"),
    contract_id: Optional[str] = Query(None, description="Filter by contract"),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: dict = Depends(get_current_user)
):
    """
    Get approval workflows with filtering and pagination.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        workflow_service = ApprovalWorkflowService(supabase)
        
        # Build query
        query = supabase.table("approval_workflows").select("*, approvers:approvals(*), contract:contracts(*), created_by_user:users(*)")
        query = query.eq("workspace_id", workspace_id)
        
        if status:
            query = query.eq("status", status.value)
        if workflow_type:
            query = query.eq("workflow_type", workflow_type.value)
        if priority:
            query = query.eq("priority", priority.value)
        if created_by:
            query = query.eq("created_by", created_by)
        if contract_id:
            query = query.eq("contract_id", contract_id)
            
        query = query.order("created_at", desc=True).range(skip, skip + limit - 1)
        
        response = query.execute()
        
        if hasattr(response, 'error') and response.error:
            raise HTTPException(status_code=400, detail=response.error.message)
        
        return response.data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get approval workflows: {str(e)}")


@router.get("/{workflow_id}", response_model=ApprovalWorkflow)
async def get_approval_workflow(
    workflow_id: str = Path(..., description="Workflow ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get a specific approval workflow by ID.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        
        response = supabase.table("approval_workflows").select("*, approvers:approvals(*), contract:contracts(*), created_by_user:users(*)").eq("id", workflow_id).execute()
        
        if hasattr(response, 'error') and response.error:
            raise HTTPException(status_code=400, detail=response.error.message)
        
        if not response.data:
            raise HTTPException(status_code=404, detail="Workflow not found")
        
        return response.data[0]
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get approval workflow: {str(e)}")


@router.post("/{workflow_id}/start", response_model=ApprovalWorkflow)
async def start_approval_workflow(
    workflow_id: str = Path(..., description="Workflow ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Start an approval workflow.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        workflow_service = ApprovalWorkflowService(supabase)
        
        workflow = await workflow_service.start_workflow(workflow_id)
        
        return workflow
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start approval workflow: {str(e)}")


@router.get("/{workflow_id}/progress", response_model=WorkflowProgress)
async def get_workflow_progress(
    workflow_id: str = Path(..., description="Workflow ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get the progress of an approval workflow.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        workflow_service = ApprovalWorkflowService(supabase)
        
        progress = await workflow_service.get_workflow_status(workflow_id)
        
        return progress
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get workflow progress: {str(e)}")


@router.post("/approvals/{approval_id}/approve", response_model=Approver)
async def approve_contract(
    action: ApprovalAction,
    approval_id: str = Path(..., description="Approval ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Approve a contract in the workflow.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        workflow_service = ApprovalWorkflowService(supabase)
        
        approval = await workflow_service.approve_contract(
            approval_id=approval_id,
            user_id=current_user["id"],
            comments=action.comments
        )
        
        return approval
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to approve contract: {str(e)}")


@router.post("/approvals/{approval_id}/reject", response_model=Approver)
async def reject_contract(
    action: ApprovalAction,
    approval_id: str = Path(..., description="Approval ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Reject a contract in the workflow.
    """
    try:
        if not action.comments:
            raise HTTPException(status_code=400, detail="Rejection reason is required")
        
        supabase = get_authenticated_db_client(current_user)
        workflow_service = ApprovalWorkflowService(supabase)
        
        approval = await workflow_service.reject_contract(
            approval_id=approval_id,
            user_id=current_user["id"],
            reason=action.comments
        )
        
        return approval
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reject contract: {str(e)}")


@router.get("/approvals/my-approvals", response_model=List[Approver])
async def get_my_approvals(
    workspace_id: str = Query(..., description="Workspace ID"),
    status: Optional[ApprovalStatus] = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: dict = Depends(get_current_user)
):
    """
    Get approvals assigned to the current user.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        
        query = supabase.table("approvals").select("*, workflow:approval_workflows(*, contract:contracts(*))").eq("user_id", current_user["id"])
        
        # Filter by workspace through the workflow
        query = query.eq("workflow.workspace_id", workspace_id)
        
        if status:
            query = query.eq("status", status.value)
            
        query = query.order("created_at", desc=True).range(skip, skip + limit - 1)
        
        response = query.execute()
        
        if hasattr(response, 'error') and response.error:
            raise HTTPException(status_code=400, detail=response.error.message)
        
        return response.data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get user approvals: {str(e)}")


@router.get("/summary", response_model=WorkflowSummary)
async def get_workflow_summary(
    workspace_id: str = Query(..., description="Workspace ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get workflow summary statistics for a workspace.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        
        # Get workflow counts by status
        response = supabase.table("approval_workflows").select("status").eq("workspace_id", workspace_id).execute()
        
        if hasattr(response, 'error') and response.error:
            raise HTTPException(status_code=400, detail=response.error.message)
        
        workflows = response.data
        total_workflows = len(workflows)
        active_workflows = len([w for w in workflows if w["status"] == "active"])
        completed_workflows = len([w for w in workflows if w["status"] == "completed"])
        rejected_workflows = len([w for w in workflows if w["status"] == "rejected"])
        
        # Calculate overdue workflows (simplified)
        overdue_workflows = 0  # Would need more complex query with due dates
        
        # Calculate average completion time (simplified)
        average_completion_time_hours = 48.0  # Placeholder
        
        summary = WorkflowSummary(
            total_workflows=total_workflows,
            active_workflows=active_workflows,
            completed_workflows=completed_workflows,
            rejected_workflows=rejected_workflows,
            overdue_workflows=overdue_workflows,
            average_completion_time_hours=average_completion_time_hours
        )
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get workflow summary: {str(e)}")


@router.post("/ai-routing/{contract_id}", response_model=AIRoutingRecommendation)
async def get_ai_routing_recommendation(
    contract_id: str = Path(..., description="Contract ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get AI-powered routing recommendations for a contract.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        workflow_service = ApprovalWorkflowService(supabase)
        
        # Get contract details
        contract_response = supabase.table("contracts").select("*").eq("id", contract_id).execute()
        
        if not contract_response.data:
            raise HTTPException(status_code=404, detail="Contract not found")
        
        contract = contract_response.data[0]
        
        # Get AI routing recommendation
        routing_decision = await workflow_service._ai_determine_routing(contract, None)
        
        recommendation = AIRoutingRecommendation(
            recommended_approvers=routing_decision.get("required_approvers", ["legal"]),
            workflow_type=WorkflowType(routing_decision.get("workflow_type", "sequential")),
            priority=Priority(routing_decision.get("priority", "medium")),
            estimated_time_hours=routing_decision.get("estimated_time_hours", 48),
            confidence=routing_decision.get("ai_confidence", 0.7),
            reasoning="AI analysis based on contract type, value, and complexity",
            risk_factors=routing_decision.get("risk_factors", [])
        )
        
        return recommendation
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get AI routing recommendation: {str(e)}")


@router.post("/bulk-approve", response_model=dict)
async def bulk_approve_contracts(
    bulk_action: BulkApprovalAction,
    current_user: dict = Depends(get_current_user)
):
    """
    Perform bulk approval actions on multiple contracts.
    """
    try:
        supabase = get_authenticated_db_client(current_user)
        workflow_service = ApprovalWorkflowService(supabase)
        
        results = []
        
        for approval_id in bulk_action.approval_ids:
            try:
                if bulk_action.action == "approve":
                    result = await workflow_service.approve_contract(
                        approval_id=approval_id,
                        user_id=current_user["id"],
                        comments=bulk_action.comments
                    )
                elif bulk_action.action == "reject":
                    if not bulk_action.comments:
                        raise ValueError("Rejection reason is required")
                    result = await workflow_service.reject_contract(
                        approval_id=approval_id,
                        user_id=current_user["id"],
                        reason=bulk_action.comments
                    )
                else:
                    raise ValueError(f"Invalid action: {bulk_action.action}")
                
                results.append({"approval_id": approval_id, "success": True, "result": result})
                
            except Exception as e:
                results.append({"approval_id": approval_id, "success": False, "error": str(e)})
        
        successful = len([r for r in results if r["success"]])
        failed = len(results) - successful
        
        return {
            "success": True,
            "message": f"Bulk action completed: {successful} successful, {failed} failed",
            "results": results,
            "summary": {
                "total": len(results),
                "successful": successful,
                "failed": failed
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to perform bulk action: {str(e)}")
