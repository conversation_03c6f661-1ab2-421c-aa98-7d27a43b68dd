"""
Secure AI API endpoints for Averum Contracts
Provides secure AI analysis with encryption, PII protection, and audit logging
"""

import tempfile
import os
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.secure_ai_service import secure_ai_service
from app.services.security_service import SecurityError

router = APIRouter()


class SecureAnalysisRequest(BaseModel):
    """Request model for secure AI analysis."""
    text: str
    analysis_type: str = "comprehensive"
    contract_id: Optional[str] = None


class PIIDetectionRequest(BaseModel):
    """Request model for PII detection."""
    text: str
    anonymize: bool = True
    preserve_structure: bool = True


def get_client_info(request: Request) -> Dict[str, Optional[str]]:
    """Extract client information for audit logging."""
    return {
        "ip_address": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent"),
        "session_id": request.headers.get("x-session-id")
    }


@router.post("/analyze-contract")
async def analyze_contract_secure(
    request_data: SecureAnalysisRequest,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    Perform secure AI analysis on contract text with PII protection and audit logging.
    
    Args:
        request_data: Analysis request data
        request: FastAPI request object for client info
        current_user: Current authenticated user
        
    Returns:
        Secure analysis results with metadata
    """
    try:
        client_info = get_client_info(request)
        
        # Get user and workspace info
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # Perform secure analysis
        result = await secure_ai_service.analyze_contract_securely(
            contract_text=request_data.text,
            user_id=user_id,
            workspace_id=workspace_id,
            contract_id=request_data.contract_id,
            analysis_type=request_data.analysis_type,
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            session_id=client_info["session_id"]
        )
        
        return JSONResponse(content=result)
        
    except SecurityError as e:
        raise HTTPException(status_code=400, detail=f"Security error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/process-document-secure")
async def process_document_secure(
    file: UploadFile = File(...),
    request: Request = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Process an uploaded document with security measures and audit logging.
    
    Args:
        file: Uploaded document file
        request: FastAPI request object for client info
        current_user: Current authenticated user
        
    Returns:
        Secure processing results with metadata
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
        try:
            client_info = get_client_info(request)
            
            # Get user and workspace info
            user_id = current_user.get("sub") or current_user.get("user_id")
            workspace_id = current_user.get("workspace_id")
            
            if not user_id:
                raise HTTPException(status_code=401, detail="User ID not found in token")
            
            # Write uploaded file to temporary location
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            
            # Process the document securely
            result = await secure_ai_service.process_document_securely(
                file_path=temp_file.name,
                file_name=file.filename,
                user_id=user_id,
                workspace_id=workspace_id,
                ip_address=client_info["ip_address"],
                user_agent=client_info["user_agent"],
                session_id=client_info["session_id"]
            )
            
            return JSONResponse(content=result)
            
        except SecurityError as e:
            raise HTTPException(status_code=400, detail=f"Security error: {str(e)}")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)


@router.post("/detect-pii")
async def detect_pii(
    request_data: PIIDetectionRequest,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    Detect and optionally anonymize PII in text.
    
    Args:
        request_data: PII detection request
        request: FastAPI request object for client info
        current_user: Current authenticated user
        
    Returns:
        PII detection results
    """
    try:
        client_info = get_client_info(request)
        
        # Get user and workspace info
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # Detect PII
        pii_result = await secure_ai_service.security_service.detect_and_anonymize_pii(
            text=request_data.text,
            anonymize=request_data.anonymize,
            preserve_structure=request_data.preserve_structure
        )
        
        # Log PII detection activity
        await secure_ai_service.security_service.log_audit_event(
            event_type="pii_detection",
            user_id=user_id,
            workspace_id=workspace_id,
            action="pii_detection_requested",
            resource_type="text",
            details={
                "text_length": len(request_data.text),
                "anonymize": request_data.anonymize,
                "pii_found": pii_result.pii_found,
                "pii_types": pii_result.pii_types,
                "pii_count": pii_result.pii_count
            },
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            session_id=client_info["session_id"]
        )
        
        return {
            "success": True,
            "pii_found": pii_result.pii_found,
            "pii_types": pii_result.pii_types,
            "pii_count": pii_result.pii_count,
            "anonymized_text": pii_result.anonymized_text if request_data.anonymize else None,
            "redaction_map": pii_result.redaction_map if request_data.anonymize else None
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to detect PII: {str(e)}")


@router.post("/sanitize-text")
async def sanitize_text_for_ai(
    text: str = Form(...),
    request: Request = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Sanitize text for AI processing by removing/anonymizing sensitive data.
    
    Args:
        text: Text to sanitize
        request: FastAPI request object for client info
        current_user: Current authenticated user
        
    Returns:
        Sanitized text and metadata
    """
    try:
        client_info = get_client_info(request)
        
        # Get user and workspace info
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # Sanitize text
        sanitized_text, metadata = await secure_ai_service.security_service.sanitize_for_ai_processing(text)
        
        # Log sanitization activity
        await secure_ai_service.security_service.log_audit_event(
            event_type="data_sanitization",
            user_id=user_id,
            workspace_id=workspace_id,
            action="text_sanitized",
            resource_type="text",
            details={
                "original_length": len(text),
                "sanitized_length": len(sanitized_text),
                "pii_detected": metadata.get("pii_detected", False),
                "redaction_count": metadata.get("redaction_count", 0)
            },
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            session_id=client_info["session_id"]
        )
        
        return {
            "success": True,
            "sanitized_text": sanitized_text,
            "metadata": metadata
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to sanitize text: {str(e)}")


@router.get("/security-status")
async def get_security_status(
    current_user: dict = Depends(get_current_user)
):
    """
    Get security service status and configuration.
    
    Returns:
        Security service status
    """
    try:
        return {
            "status": "healthy",
            "service": "secure_ai",
            "features": {
                "encryption": True,
                "pii_detection": True,
                "audit_logging": True,
                "gdpr_compliance": True,
                "data_anonymization": True
            },
            "pii_patterns_supported": list(secure_ai_service.security_service.pii_patterns.keys()),
            "sensitive_patterns_supported": list(secure_ai_service.security_service.sensitive_patterns.keys())
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "secure_ai",
            "error": str(e)
        }


@router.get("/audit-logs/{user_id}")
async def get_user_audit_logs(
    user_id: str,
    limit: int = 100,
    current_user: dict = Depends(get_current_user)
):
    """
    Get audit logs for a specific user (admin only).
    
    Args:
        user_id: User ID to get logs for
        limit: Maximum number of logs to return
        current_user: Current authenticated user
        
    Returns:
        User audit logs
    """
    # Check if current user is admin (implement proper role checking)
    current_user_id = current_user.get("sub") or current_user.get("user_id")
    
    # For now, users can only see their own logs
    if user_id != current_user_id:
        raise HTTPException(status_code=403, detail="Access denied: Can only view your own audit logs")
    
    try:
        # This would typically query the audit logs from the database
        # For now, return a placeholder response
        return {
            "success": True,
            "user_id": user_id,
            "logs": [],
            "message": "Audit log retrieval not yet implemented - logs are being stored in activity_logs table"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve audit logs: {str(e)}")
