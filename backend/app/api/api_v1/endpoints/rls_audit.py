"""
RLS Audit API endpoints for Averum Contracts
Provides RLS policy auditing, security assessment, and compliance reporting
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.rls_audit_service import rls_audit_service

router = APIRouter()


class AuditScheduleRequest(BaseModel):
    """Request model for scheduling RLS audits."""
    schedule_type: str  # "daily", "weekly", "monthly"
    notification_emails: List[str]
    include_performance_tests: bool = True
    include_isolation_tests: bool = True


@router.post("/run-audit")
async def run_rls_audit(
    background_tasks: BackgroundTasks,
    include_performance: bool = Query(True),
    include_isolation: bool = Query(True),
    current_user: dict = Depends(get_current_user)
):
    """
    Run comprehensive RLS policy audit.
    
    Args:
        background_tasks: FastAPI background tasks
        include_performance: Whether to include performance impact assessment
        include_isolation: Whether to include data isolation testing
        current_user: Current authenticated user
        
    Returns:
        Audit results and findings
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for RLS auditing")
        
        # Run comprehensive audit
        audit_results = await rls_audit_service.run_comprehensive_audit()
        
        # Log audit activity
        user_id = current_user.get("sub") or current_user.get("user_id")
        background_tasks.add_task(
            log_audit_activity,
            user_id=user_id,
            audit_type="comprehensive",
            findings_count=audit_results.get("total_findings", 0)
        )
        
        return {
            "success": True,
            "audit_results": audit_results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to run RLS audit: {str(e)}")


@router.get("/audit-history")
async def get_audit_history(
    limit: int = Query(10, le=50),
    offset: int = Query(0, ge=0),
    current_user: dict = Depends(get_current_user)
):
    """
    Get RLS audit history.
    
    Args:
        limit: Maximum number of audit records to return
        offset: Pagination offset
        current_user: Current authenticated user
        
    Returns:
        Historical audit data
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for audit history")
        
        # In production, this would query actual audit history from database
        # For now, return simulated data
        audit_history = [
            {
                "audit_id": f"audit_{i}",
                "timestamp": "2024-01-15T10:30:00Z",
                "audit_type": "comprehensive",
                "total_findings": 3 + i,
                "security_score": 95 - i,
                "status": "completed",
                "triggered_by": "admin_user"
            }
            for i in range(limit)
        ]
        
        return {
            "success": True,
            "audit_history": audit_history,
            "total_count": 25,  # Simulated total
            "limit": limit,
            "offset": offset
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get audit history: {str(e)}")


@router.get("/security-score")
async def get_security_score(
    current_user: dict = Depends(get_current_user)
):
    """
    Get current RLS security score and status.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current security score and recommendations
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for security score")
        
        # Run quick audit to get current score
        audit_results = await rls_audit_service.run_comprehensive_audit()
        summary = audit_results.get("summary", {})
        
        return {
            "success": True,
            "security_score": summary.get("overall_security_score", 0),
            "status": summary.get("overall_status", "unknown"),
            "total_findings": summary.get("total_findings", 0),
            "severity_breakdown": summary.get("severity_breakdown", {}),
            "recommendations": summary.get("recommendations", []),
            "last_audit": audit_results.get("audit_timestamp"),
            "next_audit_recommended": summary.get("next_audit_recommended")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get security score: {str(e)}")


@router.get("/policy-coverage")
async def get_policy_coverage(
    current_user: dict = Depends(get_current_user)
):
    """
    Get RLS policy coverage analysis.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Policy coverage analysis
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for policy coverage")
        
        # Get RLS enablement status
        rls_status = await rls_audit_service._audit_rls_enablement()
        
        # Get policy completeness
        policy_completeness = await rls_audit_service._audit_policy_completeness()
        
        return {
            "success": True,
            "rls_enablement": rls_status,
            "policy_completeness": policy_completeness,
            "coverage_summary": {
                "total_tables": len(rls_audit_service.expected_tables),
                "rls_enabled": rls_status.get("rls_enabled_count", 0),
                "compliance_percentage": rls_status.get("compliance_percentage", 0),
                "tables_needing_attention": rls_status.get("rls_disabled_tables", [])
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get policy coverage: {str(e)}")


@router.get("/performance-impact")
async def get_performance_impact(
    current_user: dict = Depends(get_current_user)
):
    """
    Get RLS performance impact analysis.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Performance impact analysis
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for performance analysis")
        
        # Get performance impact assessment
        performance_results = await rls_audit_service._assess_performance_impact()
        
        return {
            "success": True,
            "performance_impact": performance_results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get performance impact: {str(e)}")


@router.post("/test-isolation")
async def test_data_isolation(
    table_name: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Test data isolation for specific table or all tables.
    
    Args:
        table_name: Optional specific table to test
        current_user: Current authenticated user
        
    Returns:
        Data isolation test results
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for isolation testing")
        
        # Run data isolation tests
        isolation_results = await rls_audit_service._test_data_isolation()
        
        # Filter by table if specified
        if table_name:
            isolation_tests = isolation_results.get("isolation_tests", [])
            filtered_tests = [test for test in isolation_tests if test["table_name"] == table_name]
            
            if not filtered_tests:
                raise HTTPException(status_code=404, detail=f"No isolation tests found for table: {table_name}")
            
            isolation_results["isolation_tests"] = filtered_tests
        
        return {
            "success": True,
            "isolation_results": isolation_results,
            "table_filter": table_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to test data isolation: {str(e)}")


@router.post("/verify-access-control")
async def verify_access_control(
    scenario: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Verify access control enforcement.
    
    Args:
        scenario: Optional specific scenario to test
        current_user: Current authenticated user
        
    Returns:
        Access control verification results
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for access control verification")
        
        # Run access control verification
        access_control_results = await rls_audit_service._verify_access_control()
        
        # Filter by scenario if specified
        if scenario:
            access_tests = access_control_results.get("access_control_tests", [])
            filtered_tests = [test for test in access_tests if scenario.lower() in test["scenario"].lower()]
            
            if not filtered_tests:
                raise HTTPException(status_code=404, detail=f"No access control tests found for scenario: {scenario}")
            
            access_control_results["access_control_tests"] = filtered_tests
        
        return {
            "success": True,
            "access_control_results": access_control_results,
            "scenario_filter": scenario
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to verify access control: {str(e)}")


@router.get("/compliance-report")
async def generate_compliance_report(
    format: str = Query("json", pattern="^(json|pdf|csv)$"),
    current_user: dict = Depends(get_current_user)
):
    """
    Generate RLS compliance report.
    
    Args:
        format: Report format (json, pdf, csv)
        current_user: Current authenticated user
        
    Returns:
        Compliance report
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for compliance reports")
        
        # Run comprehensive audit for compliance report
        audit_results = await rls_audit_service.run_comprehensive_audit()
        
        if format == "json":
            return {
                "success": True,
                "compliance_report": audit_results,
                "format": "json",
                "generated_at": audit_results.get("audit_timestamp")
            }
        
        elif format == "pdf":
            # In production, generate actual PDF
            return {
                "success": True,
                "message": "PDF compliance report generation would be implemented here",
                "format": "pdf",
                "download_url": "/api/v1/rls-audit/download/compliance-report.pdf"
            }
        
        elif format == "csv":
            # In production, generate actual CSV
            return {
                "success": True,
                "message": "CSV compliance report generation would be implemented here",
                "format": "csv",
                "download_url": "/api/v1/rls-audit/download/compliance-report.csv"
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate compliance report: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for RLS audit service.
    
    Returns:
        Service health status
    """
    try:
        return {
            "status": "healthy",
            "service": "rls_audit",
            "features": [
                "comprehensive_audit",
                "policy_coverage",
                "data_isolation_testing",
                "access_control_verification",
                "performance_assessment",
                "compliance_reporting"
            ],
            "expected_tables_count": len(rls_audit_service.expected_tables)
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "rls_audit",
            "error": str(e)
        }


async def log_audit_activity(user_id: str, audit_type: str, findings_count: int):
    """Background task to log audit activity."""
    try:
        # In production, this would log to audit trail
        logger.info(f"RLS audit completed by user {user_id}: type={audit_type}, findings={findings_count}")
    except Exception as e:
        logger.error(f"Failed to log audit activity: {e}")
