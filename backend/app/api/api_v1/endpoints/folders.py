from fastapi import APIRouter, Depends, HTTPException, Path, Query
from typing import List, Optional
from app.schemas.folder import Folder, FolderCreate, FolderUpdate
from app.core.auth import get_current_user, validate_workspace_access, get_authenticated_db_client
from app.db.database import get_supabase_client
from app.utils.response import (
    handle_supabase_response,
    create_success_response,
    create_created_response,
    not_found_error,
    forbidden_error
)
from datetime import datetime
import uuid
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/", response_model=List[Folder])
async def get_folders(
    workspace_id: Optional[str] = Query(None, description="Filter folders by workspace ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all folders accessible to the current user.
    If workspace_id is provided, filter by that workspace.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)
    
    # Build query
    query = supabase.table("folders").select("*")
    
    if workspace_id:
        # Validate workspace access
        await validate_workspace_access(current_user["id"], workspace_id, supabase)
        query = query.eq("workspace_id", workspace_id)
    
    # Execute query - RLS will automatically filter by user's accessible workspaces
    response = query.execute()
    
    # Handle response
    folders_data = handle_supabase_response(response, "Failed to fetch folders")
    
    return folders_data or []

@router.post("/", response_model=Folder)
async def create_folder(
    folder: FolderCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new folder in a workspace the user has access to.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)
    
    # Validate that the user has access to the specified workspace
    workspace_id = folder.workspace_id
    if workspace_id:
        await validate_workspace_access(current_user["id"], workspace_id, supabase)
    
    # Prepare the folder data
    folder_data = folder.model_dump()
    folder_data["id"] = str(uuid.uuid4())
    folder_data["created_by"] = current_user["id"]
    folder_data["created_at"] = datetime.utcnow().isoformat()
    
    # Insert the folder
    response = supabase.table("folders").insert(folder_data).execute()
    
    # Handle folder creation response
    folder_data_result = handle_supabase_response(response, "Failed to create folder")
    
    if not folder_data_result:
        raise HTTPException(status_code=500, detail="Folder creation failed - no data returned")
    
    return folder_data_result[0]

@router.get("/{folder_id}", response_model=Folder)
async def get_folder(
    folder_id: str = Path(..., description="The ID of the folder to retrieve"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve a specific folder by ID with workspace access validation.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)
    
    # Get the folder - RLS will automatically filter by workspace access
    response = supabase.table("folders").select("*").eq("id", folder_id).execute()
    
    # Handle response
    folder_data = handle_supabase_response(response, "Failed to fetch folder")
    
    if not folder_data:
        raise not_found_error("Folder")
    
    return folder_data[0]

@router.put("/{folder_id}", response_model=Folder)
async def update_folder(
    folder: FolderUpdate,
    folder_id: str = Path(..., description="The ID of the folder to update"),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a specific folder.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)
    
    # Check if folder exists and user has access
    check_response = supabase.table("folders").select("id, workspace_id").eq("id", folder_id).execute()
    check_data = handle_supabase_response(check_response, "Failed to check folder existence")
    
    if not check_data:
        raise not_found_error("Folder")
    
    # Prepare update data
    update_data = folder.model_dump(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow().isoformat()
    
    # Update the folder
    response = supabase.table("folders").update(update_data).eq("id", folder_id).execute()
    
    # Handle response
    folder_data = handle_supabase_response(response, "Failed to update folder")
    
    if not folder_data:
        raise HTTPException(status_code=500, detail="Folder update failed - no data returned")
    
    return folder_data[0]

@router.delete("/{folder_id}")
async def delete_folder(
    folder_id: str = Path(..., description="The ID of the folder to delete"),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a specific folder.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)
    
    # Check if folder exists and user has access
    check_response = supabase.table("folders").select("id, workspace_id").eq("id", folder_id).execute()
    check_data = handle_supabase_response(check_response, "Failed to check folder existence")
    
    if not check_data:
        raise not_found_error("Folder")
    
    # Delete the folder
    response = supabase.table("folders").delete().eq("id", folder_id).execute()
    
    # Handle response
    handle_supabase_response(response, "Failed to delete folder")
    
    return {"message": "Folder deleted successfully"}
