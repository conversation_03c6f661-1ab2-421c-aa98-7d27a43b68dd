"""
Rate Limiting Management API endpoints for Averum Contracts
Provides rate limiting configuration, monitoring, and management
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.rate_limiting_service import rate_limiting_service, RateLimitRule, RateLimitScope
from app.middleware.rate_limiting_middleware import rate_limit

router = APIRouter()


class CreateRuleRequest(BaseModel):
    """Request model for creating rate limiting rules."""
    name: str
    limit: int
    window: int
    scope: RateLimitScope
    burst_limit: Optional[int] = None
    burst_window: Optional[int] = None
    enabled: bool = True


class UpdateRuleRequest(BaseModel):
    """Request model for updating rate limiting rules."""
    limit: Optional[int] = None
    window: Optional[int] = None
    burst_limit: Optional[int] = None
    burst_window: Optional[int] = None
    enabled: Optional[bool] = None


class ClearRateLimitRequest(BaseModel):
    """Request model for clearing rate limits."""
    rule_name: str
    identifier: str


@router.get("/rules")
async def get_rate_limiting_rules(
    current_user: dict = Depends(get_current_user)
):
    """
    Get all rate limiting rules.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        List of rate limiting rules
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for rate limiting management")
        
        rules = {}
        for name, rule in rate_limiting_service.rules.items():
            rules[name] = {
                "name": rule.name,
                "limit": rule.limit,
                "window": rule.window,
                "scope": rule.scope.value,
                "burst_limit": rule.burst_limit,
                "burst_window": rule.burst_window,
                "enabled": rule.enabled
            }
        
        return {
            "success": True,
            "rules": rules,
            "total_rules": len(rules)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get rate limiting rules: {str(e)}")


@router.post("/rules")
async def create_rate_limiting_rule(
    request_data: CreateRuleRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new rate limiting rule.
    
    Args:
        request_data: Rule creation request
        current_user: Current authenticated user
        
    Returns:
        Created rule information
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:  # Only system admins can create rules
            raise HTTPException(status_code=403, detail="System admin access required")
        
        # Validate rule parameters
        if request_data.limit <= 0:
            raise HTTPException(status_code=400, detail="Limit must be positive")
        
        if request_data.window <= 0:
            raise HTTPException(status_code=400, detail="Window must be positive")
        
        if request_data.burst_limit and request_data.burst_limit > request_data.limit:
            raise HTTPException(status_code=400, detail="Burst limit cannot exceed main limit")
        
        # Create rule
        rule = RateLimitRule(
            name=request_data.name,
            limit=request_data.limit,
            window=request_data.window,
            scope=request_data.scope,
            burst_limit=request_data.burst_limit,
            burst_window=request_data.burst_window,
            enabled=request_data.enabled
        )
        
        # Generate rule key
        rule_key = request_data.name.lower().replace(" ", "_")
        
        # Add rule to service
        await rate_limiting_service.update_rule(rule_key, rule)
        
        return {
            "success": True,
            "rule_key": rule_key,
            "rule": {
                "name": rule.name,
                "limit": rule.limit,
                "window": rule.window,
                "scope": rule.scope.value,
                "burst_limit": rule.burst_limit,
                "burst_window": rule.burst_window,
                "enabled": rule.enabled
            },
            "message": f"Rate limiting rule '{request_data.name}' created successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create rate limiting rule: {str(e)}")


@router.put("/rules/{rule_key}")
async def update_rate_limiting_rule(
    rule_key: str,
    request_data: UpdateRuleRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Update an existing rate limiting rule.
    
    Args:
        rule_key: Rule key to update
        request_data: Rule update request
        current_user: Current authenticated user
        
    Returns:
        Updated rule information
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required")
        
        # Check if rule exists
        if rule_key not in rate_limiting_service.rules:
            raise HTTPException(status_code=404, detail="Rate limiting rule not found")
        
        current_rule = rate_limiting_service.rules[rule_key]
        
        # Update rule with provided values
        updated_rule = RateLimitRule(
            name=current_rule.name,
            limit=request_data.limit if request_data.limit is not None else current_rule.limit,
            window=request_data.window if request_data.window is not None else current_rule.window,
            scope=current_rule.scope,
            burst_limit=request_data.burst_limit if request_data.burst_limit is not None else current_rule.burst_limit,
            burst_window=request_data.burst_window if request_data.burst_window is not None else current_rule.burst_window,
            enabled=request_data.enabled if request_data.enabled is not None else current_rule.enabled
        )
        
        # Validate updated rule
        if updated_rule.limit <= 0:
            raise HTTPException(status_code=400, detail="Limit must be positive")
        
        if updated_rule.window <= 0:
            raise HTTPException(status_code=400, detail="Window must be positive")
        
        # Update rule in service
        await rate_limiting_service.update_rule(rule_key, updated_rule)
        
        return {
            "success": True,
            "rule_key": rule_key,
            "rule": {
                "name": updated_rule.name,
                "limit": updated_rule.limit,
                "window": updated_rule.window,
                "scope": updated_rule.scope.value,
                "burst_limit": updated_rule.burst_limit,
                "burst_window": updated_rule.burst_window,
                "enabled": updated_rule.enabled
            },
            "message": f"Rate limiting rule '{rule_key}' updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update rate limiting rule: {str(e)}")


@router.post("/rules/{rule_key}/enable")
async def enable_rate_limiting_rule(
    rule_key: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Enable a rate limiting rule.
    
    Args:
        rule_key: Rule key to enable
        current_user: Current authenticated user
        
    Returns:
        Success status
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required")
        
        if rule_key not in rate_limiting_service.rules:
            raise HTTPException(status_code=404, detail="Rate limiting rule not found")
        
        await rate_limiting_service.enable_rule(rule_key)
        
        return {
            "success": True,
            "message": f"Rate limiting rule '{rule_key}' enabled successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to enable rate limiting rule: {str(e)}")


@router.post("/rules/{rule_key}/disable")
async def disable_rate_limiting_rule(
    rule_key: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Disable a rate limiting rule.
    
    Args:
        rule_key: Rule key to disable
        current_user: Current authenticated user
        
    Returns:
        Success status
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required")
        
        if rule_key not in rate_limiting_service.rules:
            raise HTTPException(status_code=404, detail="Rate limiting rule not found")
        
        await rate_limiting_service.disable_rule(rule_key)
        
        return {
            "success": True,
            "message": f"Rate limiting rule '{rule_key}' disabled successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to disable rate limiting rule: {str(e)}")


@router.get("/stats")
@rate_limit("user_per_minute")
async def get_rate_limiting_stats(
    current_user: dict = Depends(get_current_user)
):
    """
    Get rate limiting statistics and monitoring data.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Rate limiting statistics
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for rate limiting statistics")
        
        stats = await rate_limiting_service.get_monitoring_stats()
        
        return {
            "success": True,
            "statistics": stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get rate limiting statistics: {str(e)}")


@router.post("/clear")
async def clear_rate_limit(
    request_data: ClearRateLimitRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Clear rate limit for a specific identifier.
    
    Args:
        request_data: Clear rate limit request
        current_user: Current authenticated user
        
    Returns:
        Success status
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required")
        
        await rate_limiting_service.clear_rate_limit(
            rule_name=request_data.rule_name,
            identifier=request_data.identifier
        )
        
        return {
            "success": True,
            "message": f"Rate limit cleared for {request_data.rule_name}:{request_data.identifier}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear rate limit: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for rate limiting service.
    
    Returns:
        Service health status
    """
    try:
        stats = await rate_limiting_service.get_monitoring_stats()
        
        return {
            "status": "healthy",
            "service": "rate_limiting",
            "redis_connected": stats.get("redis_connected", False),
            "total_rules": len(stats.get("rules", {})),
            "active_rules": len([
                rule for rule in stats.get("rules", {}).values()
                if rule.get("enabled", False)
            ])
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "rate_limiting",
            "error": str(e)
        }
