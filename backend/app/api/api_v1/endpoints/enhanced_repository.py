"""
Enhanced Document Repository API endpoints for Averum Contracts
Provides advanced search, semantic search, organization tools, and access control
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.enhanced_repository_service import enhanced_repository_service, SearchType

router = APIRouter()


class AdvancedSearchRequest(BaseModel):
    """Request model for advanced search."""
    query: str
    search_type: SearchType = SearchType.HYBRID
    filters: Optional[Dict[str, Any]] = None
    limit: int = 50
    offset: int = 0


class CreateTagRequest(BaseModel):
    """Request model for creating document tags."""
    name: str
    color: str
    description: Optional[str] = None


class CreateCollectionRequest(BaseModel):
    """Request model for creating document collections."""
    name: str
    description: Optional[str] = None
    parent_collection_id: Optional[str] = None
    is_public: bool = False


@router.post("/search/advanced")
async def advanced_search(
    request_data: AdvancedSearchRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Perform advanced search across documents with multiple search strategies.
    
    Args:
        request_data: Advanced search request
        current_user: Current authenticated user
        
    Returns:
        Advanced search results with facets and suggestions
    """
    try:
        workspace_id = current_user.get("workspace_id")
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        if not workspace_id or not user_id:
            raise HTTPException(status_code=401, detail="Workspace ID or user ID not found in token")
        
        if not request_data.query.strip():
            raise HTTPException(status_code=400, detail="Search query cannot be empty")
        
        # Perform advanced search
        search_results = await enhanced_repository_service.advanced_search(
            workspace_id=workspace_id,
            query=request_data.query,
            search_type=request_data.search_type,
            filters=request_data.filters,
            user_id=user_id,
            limit=request_data.limit,
            offset=request_data.offset
        )
        
        return {
            "success": True,
            "search_results": search_results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Advanced search failed: {str(e)}")


@router.get("/search/suggestions")
async def get_search_suggestions(
    query: str = Query(..., min_length=1),
    current_user: dict = Depends(get_current_user)
):
    """
    Get search suggestions based on query and workspace content.
    
    Args:
        query: Partial search query
        current_user: Current authenticated user
        
    Returns:
        Search suggestions
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        suggestions = await enhanced_repository_service._generate_search_suggestions(query, workspace_id)
        
        return {
            "success": True,
            "suggestions": suggestions,
            "query": query
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get search suggestions: {str(e)}")


@router.post("/tags")
async def create_document_tag(
    request_data: CreateTagRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new document tag.
    
    Args:
        request_data: Tag creation request
        current_user: Current authenticated user
        
    Returns:
        Created tag information
    """
    try:
        workspace_id = current_user.get("workspace_id")
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        if not workspace_id or not user_id:
            raise HTTPException(status_code=401, detail="Workspace ID or user ID not found in token")
        
        # Create tag in database
        tag_data = {
            "id": str(__import__("uuid").uuid4()),
            "name": request_data.name,
            "color": request_data.color,
            "description": request_data.description,
            "workspace_id": workspace_id,
            "created_by": user_id,
            "created_at": __import__("datetime").datetime.utcnow().isoformat(),
            "usage_count": 0
        }
        
        result = enhanced_repository_service.supabase.table("document_tags").insert(tag_data).execute()
        
        if result.data:
            return {
                "success": True,
                "tag": result.data[0],
                "message": f"Tag '{request_data.name}' created successfully"
            }
        
        raise Exception("Failed to create tag")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create tag: {str(e)}")


@router.get("/tags")
async def list_document_tags(
    current_user: dict = Depends(get_current_user)
):
    """
    List all document tags in the workspace.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        List of document tags
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        result = enhanced_repository_service.supabase.table("document_tags").select("*").eq("workspace_id", workspace_id).order("usage_count", desc=True).execute()
        
        return {
            "success": True,
            "tags": result.data or [],
            "total_tags": len(result.data or [])
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list tags: {str(e)}")


@router.post("/collections")
async def create_document_collection(
    request_data: CreateCollectionRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new document collection.
    
    Args:
        request_data: Collection creation request
        current_user: Current authenticated user
        
    Returns:
        Created collection information
    """
    try:
        workspace_id = current_user.get("workspace_id")
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        if not workspace_id or not user_id:
            raise HTTPException(status_code=401, detail="Workspace ID or user ID not found in token")
        
        # Create collection in database
        collection_data = {
            "id": str(__import__("uuid").uuid4()),
            "name": request_data.name,
            "description": request_data.description,
            "workspace_id": workspace_id,
            "parent_collection_id": request_data.parent_collection_id,
            "created_by": user_id,
            "created_at": __import__("datetime").datetime.utcnow().isoformat(),
            "document_count": 0,
            "access_level": "read",
            "is_public": request_data.is_public
        }
        
        result = enhanced_repository_service.supabase.table("document_collections").insert(collection_data).execute()
        
        if result.data:
            return {
                "success": True,
                "collection": result.data[0],
                "message": f"Collection '{request_data.name}' created successfully"
            }
        
        raise Exception("Failed to create collection")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create collection: {str(e)}")


@router.get("/collections")
async def list_document_collections(
    parent_id: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    List document collections in the workspace.
    
    Args:
        parent_id: Optional parent collection ID to filter by
        current_user: Current authenticated user
        
    Returns:
        List of document collections
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        query = enhanced_repository_service.supabase.table("document_collections").select("*").eq("workspace_id", workspace_id)
        
        if parent_id:
            query = query.eq("parent_collection_id", parent_id)
        else:
            query = query.is_("parent_collection_id", "null")
        
        result = query.order("created_at", desc=True).execute()
        
        return {
            "success": True,
            "collections": result.data or [],
            "total_collections": len(result.data or [])
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list collections: {str(e)}")


@router.get("/analytics")
async def get_repository_analytics(
    current_user: dict = Depends(get_current_user)
):
    """
    Get repository analytics and statistics.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Repository analytics data
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        # Get document statistics
        docs_result = enhanced_repository_service.supabase.table("documents").select("id, file_info, created_at, folder").eq("workspace_id", workspace_id).execute()
        
        documents = docs_result.data or []
        
        # Calculate analytics
        analytics = {
            "total_documents": len(documents),
            "file_type_distribution": {},
            "folder_distribution": {},
            "upload_trends": {},
            "storage_usage": 0
        }
        
        # Analyze file types
        for doc in documents:
            file_info = doc.get("file_info", {})
            content_type = file_info.get("content_type", "unknown")
            analytics["file_type_distribution"][content_type] = analytics["file_type_distribution"].get(content_type, 0) + 1
            
            # Calculate storage usage
            file_size = file_info.get("size", 0)
            analytics["storage_usage"] += file_size
            
            # Analyze folders
            folder = doc.get("folder", "root")
            analytics["folder_distribution"][folder] = analytics["folder_distribution"].get(folder, 0) + 1
        
        # Get tag statistics
        tags_result = enhanced_repository_service.supabase.table("document_tags").select("*").eq("workspace_id", workspace_id).execute()
        analytics["total_tags"] = len(tags_result.data or [])
        
        # Get collection statistics
        collections_result = enhanced_repository_service.supabase.table("document_collections").select("*").eq("workspace_id", workspace_id).execute()
        analytics["total_collections"] = len(collections_result.data or [])
        
        return {
            "success": True,
            "analytics": analytics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get repository analytics: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for enhanced repository service.
    
    Returns:
        Service health status
    """
    try:
        return {
            "status": "healthy",
            "service": "enhanced_repository",
            "features": [
                "advanced_search",
                "semantic_search",
                "fuzzy_search",
                "document_tags",
                "document_collections",
                "analytics"
            ],
            "search_cache_size": len(enhanced_repository_service.search_cache)
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "enhanced_repository",
            "error": str(e)
        }
