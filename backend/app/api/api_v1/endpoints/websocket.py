"""
WebSocket API endpoints for Real-time Notifications in Averum Contracts
Provides WebSocket connections and real-time event handling
"""

import json
import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user, verify_websocket_token
from app.services.websocket_service import websocket_manager, notification_service, NotificationType, NotificationPriority

router = APIRouter()
logger = logging.getLogger(__name__)


class CreateNotificationRequest(BaseModel):
    """Request model for creating notifications."""
    type: NotificationType
    title: str
    message: str
    user_id: Optional[str] = None
    priority: NotificationPriority = NotificationPriority.MEDIUM
    data: Optional[Dict[str, Any]] = None
    expires_in_hours: Optional[int] = None


class MarkNotificationReadRequest(BaseModel):
    """Request model for marking notifications as read."""
    notification_ids: list[str]


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(...),
    workspace_id: str = Query(...)
):
    """
    WebSocket endpoint for real-time notifications.
    
    Args:
        websocket: WebSocket connection
        token: Authentication token
        workspace_id: Workspace ID for the connection
    """
    connection_id = None
    
    try:
        # Verify token and get user info
        user_info = await verify_websocket_token(token)
        if not user_info:
            await websocket.close(code=4001, reason="Invalid token")
            return
        
        user_id = user_info.get("sub") or user_info.get("user_id")
        if not user_id:
            await websocket.close(code=4002, reason="User ID not found in token")
            return
        
        # Connect to WebSocket manager
        connection_id = await websocket_manager.connect(websocket, user_id, workspace_id)
        
        logger.info(f"WebSocket connection established: {connection_id}")
        
        # Handle incoming messages
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                message_type = message.get("type")
                
                if message_type == "ping":
                    await websocket_manager.handle_ping(connection_id)
                
                elif message_type == "subscribe":
                    # Handle subscription to specific event types
                    event_types = message.get("event_types", [])
                    if connection_id in websocket_manager.connections:
                        connection = websocket_manager.connections[connection_id]
                        connection.subscriptions.update(event_types)
                        await connection.send_message({
                            "type": "subscription_confirmed",
                            "event_types": list(connection.subscriptions)
                        })
                
                elif message_type == "unsubscribe":
                    # Handle unsubscription from event types
                    event_types = message.get("event_types", [])
                    if connection_id in websocket_manager.connections:
                        connection = websocket_manager.connections[connection_id]
                        connection.subscriptions.difference_update(event_types)
                        await connection.send_message({
                            "type": "unsubscription_confirmed",
                            "event_types": event_types
                        })
                
                elif message_type == "mark_read":
                    # Handle marking notifications as read
                    notification_ids = message.get("notification_ids", [])
                    await mark_notifications_read(notification_ids, user_id)
                    await websocket.send_text(json.dumps({
                        "type": "notifications_marked_read",
                        "notification_ids": notification_ids
                    }))
                
                else:
                    logger.warning(f"Unknown message type: {message_type}")
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "message": f"Unknown message type: {message_type}"
                    }))
                
            except json.JSONDecodeError:
                logger.error("Invalid JSON received from WebSocket client")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format"
                }))
            
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Internal server error"
                }))
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected: {connection_id}")
    
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    
    finally:
        if connection_id:
            await websocket_manager.disconnect(connection_id)


@router.post("/notifications")
async def create_notification(
    request_data: CreateNotificationRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new notification.
    
    Args:
        request_data: Notification creation request
        current_user: Current authenticated user
        
    Returns:
        Created notification ID
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        notification_id = await notification_service.create_notification(
            notification_type=request_data.type,
            title=request_data.title,
            message=request_data.message,
            workspace_id=workspace_id,
            user_id=request_data.user_id,
            priority=request_data.priority,
            data=request_data.data,
            expires_in_hours=request_data.expires_in_hours
        )
        
        return {
            "success": True,
            "notification_id": notification_id,
            "message": "Notification created and broadcast successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create notification: {str(e)}")


@router.get("/notifications")
async def get_notifications(
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0),
    unread_only: bool = Query(False),
    current_user: dict = Depends(get_current_user)
):
    """
    Get notifications for the current user.
    
    Args:
        limit: Maximum number of notifications to return
        offset: Pagination offset
        unread_only: Whether to return only unread notifications
        current_user: Current authenticated user
        
    Returns:
        List of notifications
    """
    try:
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id or not workspace_id:
            raise HTTPException(status_code=401, detail="User ID or workspace ID not found in token")
        
        # Build query
        query = notification_service.supabase.table("notifications").select("*")
        
        # Filter by user (personal notifications) or workspace (broadcast notifications)
        query = query.or_(f"user_id.eq.{user_id},and(user_id.is.null,workspace_id.eq.{workspace_id})")
        
        if unread_only:
            query = query.eq("read", False)
        
        # Apply pagination and ordering
        result = query.order("created_at", desc=True).range(offset, offset + limit - 1).execute()
        
        notifications = result.data or []
        
        # Get total count for pagination
        count_query = notification_service.supabase.table("notifications").select("id", count="exact")
        count_query = count_query.or_(f"user_id.eq.{user_id},and(user_id.is.null,workspace_id.eq.{workspace_id})")
        
        if unread_only:
            count_query = count_query.eq("read", False)
        
        count_result = count_query.execute()
        total_count = count_result.count or 0
        
        return {
            "success": True,
            "notifications": notifications,
            "total_count": total_count,
            "limit": limit,
            "offset": offset,
            "unread_only": unread_only
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get notifications: {str(e)}")


@router.post("/notifications/mark-read")
async def mark_notifications_read_endpoint(
    request_data: MarkNotificationReadRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Mark notifications as read.
    
    Args:
        request_data: Notification IDs to mark as read
        current_user: Current authenticated user
        
    Returns:
        Success status
    """
    try:
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        await mark_notifications_read(request_data.notification_ids, user_id)
        
        return {
            "success": True,
            "message": f"Marked {len(request_data.notification_ids)} notifications as read"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to mark notifications as read: {str(e)}")


@router.delete("/notifications/{notification_id}")
async def delete_notification(
    notification_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a notification.
    
    Args:
        notification_id: Notification ID to delete
        current_user: Current authenticated user
        
    Returns:
        Success status
    """
    try:
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # Delete notification (RLS will ensure user can only delete their own)
        result = notification_service.supabase.table("notifications").delete().eq("id", notification_id).eq("user_id", user_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Notification not found or access denied")
        
        return {
            "success": True,
            "message": "Notification deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete notification: {str(e)}")


@router.get("/websocket/stats")
async def get_websocket_stats(
    current_user: dict = Depends(get_current_user)
):
    """
    Get WebSocket connection statistics.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        WebSocket statistics
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for WebSocket statistics")
        
        stats = websocket_manager.get_connection_stats()
        
        return {
            "success": True,
            "websocket_stats": stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get WebSocket statistics: {str(e)}")


async def mark_notifications_read(notification_ids: list[str], user_id: str):
    """Helper function to mark notifications as read."""
    if not notification_ids:
        return
    
    # Update notifications as read
    notification_service.supabase.table("notifications").update({
        "read": True,
        "read_at": notification_service.supabase.functions.now()
    }).in_("id", notification_ids).eq("user_id", user_id).execute()


@router.get("/health")
async def health_check():
    """
    Health check for WebSocket service.
    
    Returns:
        Service health status
    """
    try:
        stats = websocket_manager.get_connection_stats()
        
        return {
            "status": "healthy",
            "service": "websocket",
            "active_connections": stats["total_connections"],
            "connected_users": stats["users_connected"],
            "active_workspaces": stats["workspaces_active"]
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "websocket",
            "error": str(e)
        }
