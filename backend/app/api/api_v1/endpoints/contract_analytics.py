"""
Contract Analytics API endpoints for Averum Contracts
Provides analytics dashboard, metrics, trends, and predictive insights
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.analytics_engine import analytics_engine, TimeRange

router = APIRouter()


class CustomDashboardRequest(BaseModel):
    """Request model for creating custom dashboards."""
    name: str
    description: Optional[str] = None
    metrics: List[str]
    time_range: TimeRange = TimeRange.LAST_30_DAYS
    filters: Optional[Dict[str, Any]] = None


@router.get("/overview")
async def get_analytics_overview(
    time_range: TimeRange = Query(TimeRange.LAST_30_DAYS),
    current_user: dict = Depends(get_current_user)
):
    """
    Get overview analytics metrics for the dashboard.
    
    Args:
        time_range: Time range for analytics
        current_user: Current authenticated user
        
    Returns:
        Overview analytics metrics
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        overview_metrics = await analytics_engine.get_overview_metrics(
            workspace_id=workspace_id,
            time_range=time_range
        )
        
        return {
            "success": True,
            "overview": overview_metrics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get analytics overview: {str(e)}")


@router.get("/trends/{metric_name}")
async def get_trend_analysis(
    metric_name: str,
    time_range: TimeRange = Query(TimeRange.LAST_90_DAYS),
    granularity: str = Query("daily", pattern="^(daily|weekly|monthly)$"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get detailed trend analysis for a specific metric.
    
    Args:
        metric_name: Name of the metric to analyze
        time_range: Time range for trend analysis
        granularity: Data granularity (daily, weekly, monthly)
        current_user: Current authenticated user
        
    Returns:
        Trend analysis data
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        valid_metrics = [
            "contract_creation", "contract_value", "approval_time", "contract_status"
        ]
        
        if metric_name not in valid_metrics:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid metric name. Valid options: {', '.join(valid_metrics)}"
            )
        
        trend_analysis = await analytics_engine.get_trend_analysis(
            workspace_id=workspace_id,
            metric_name=metric_name,
            time_range=time_range,
            granularity=granularity
        )
        
        return {
            "success": True,
            "trend_analysis": trend_analysis
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get trend analysis: {str(e)}")


@router.get("/predictions/{prediction_type}")
async def get_predictive_insights(
    prediction_type: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get predictive analytics insights.
    
    Args:
        prediction_type: Type of prediction to generate
        current_user: Current authenticated user
        
    Returns:
        Predictive insights
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        valid_predictions = [
            "contract_volume", "expiration_risk", "value_forecast", "approval_bottlenecks"
        ]
        
        if prediction_type not in valid_predictions:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid prediction type. Valid options: {', '.join(valid_predictions)}"
            )
        
        predictions = await analytics_engine.get_predictive_insights(
            workspace_id=workspace_id,
            prediction_type=prediction_type
        )
        
        return {
            "success": True,
            "predictions": predictions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get predictive insights: {str(e)}")


@router.get("/performance-metrics")
async def get_performance_metrics(
    time_range: TimeRange = Query(TimeRange.LAST_30_DAYS),
    current_user: dict = Depends(get_current_user)
):
    """
    Get detailed performance metrics.
    
    Args:
        time_range: Time range for performance metrics
        current_user: Current authenticated user
        
    Returns:
        Performance metrics data
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        # Get overview metrics which include performance data
        overview = await analytics_engine.get_overview_metrics(
            workspace_id=workspace_id,
            time_range=time_range
        )
        
        # Extract and enhance performance metrics
        performance_metrics = overview["metrics"].get("performance_metrics", {})
        
        # Add additional performance calculations
        enhanced_metrics = {
            **performance_metrics.get("value", {}),
            "time_range": time_range.value,
            "workspace_efficiency": {
                "contracts_per_day": overview["metrics"]["total_contracts"]["value"] / 30,  # Simplified
                "value_per_contract": (
                    overview["metrics"]["contract_value"]["value"] / 
                    overview["metrics"]["total_contracts"]["value"]
                ) if overview["metrics"]["total_contracts"]["value"] > 0 else 0
            }
        }
        
        return {
            "success": True,
            "performance_metrics": enhanced_metrics,
            "benchmarks": {
                "industry_avg_approval_time": 72,  # hours - example benchmark
                "target_approval_time": 48,
                "efficiency_score": "good" if enhanced_metrics.get("avg_approval_time_hours", 0) < 48 else "needs_improvement"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get performance metrics: {str(e)}")


@router.get("/risk-analysis")
async def get_risk_analysis(
    current_user: dict = Depends(get_current_user)
):
    """
    Get comprehensive risk analysis.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Risk analysis data
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        # Get overview metrics which include risk analysis
        overview = await analytics_engine.get_overview_metrics(
            workspace_id=workspace_id,
            time_range=TimeRange.LAST_30_DAYS
        )
        
        # Get expiration risk predictions
        expiration_predictions = await analytics_engine.get_predictive_insights(
            workspace_id=workspace_id,
            prediction_type="expiration_risk"
        )
        
        risk_analysis = {
            "portfolio_risk": overview["metrics"]["risk_analysis"]["value"],
            "expiration_risk": expiration_predictions["insights"],
            "risk_score": "medium",  # Simplified scoring
            "recommendations": [
                "Review contracts expiring within 30 days",
                "Consider renewal negotiations for high-value contracts",
                "Implement automated renewal reminders"
            ]
        }
        
        return {
            "success": True,
            "risk_analysis": risk_analysis
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get risk analysis: {str(e)}")


@router.post("/custom-dashboard")
async def create_custom_dashboard(
    request_data: CustomDashboardRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a custom analytics dashboard.
    
    Args:
        request_data: Custom dashboard configuration
        current_user: Current authenticated user
        
    Returns:
        Custom dashboard data
    """
    try:
        workspace_id = current_user.get("workspace_id")
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        if not workspace_id or not user_id:
            raise HTTPException(status_code=401, detail="Workspace ID or user ID not found in token")
        
        # Validate requested metrics
        valid_metrics = [
            "total_contracts", "active_contracts", "contract_value", "expiring_soon",
            "status_distribution", "monthly_trends", "performance_metrics", "risk_analysis"
        ]
        
        invalid_metrics = [m for m in request_data.metrics if m not in valid_metrics]
        if invalid_metrics:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid metrics: {', '.join(invalid_metrics)}"
            )
        
        # Get overview data
        overview = await analytics_engine.get_overview_metrics(
            workspace_id=workspace_id,
            time_range=request_data.time_range
        )
        
        # Filter metrics based on request
        filtered_metrics = {
            metric: overview["metrics"][metric]
            for metric in request_data.metrics
            if metric in overview["metrics"]
        }
        
        # Store custom dashboard configuration (simplified - in production, save to database)
        dashboard_config = {
            "id": f"custom_{user_id}_{len(request_data.metrics)}",
            "name": request_data.name,
            "description": request_data.description,
            "metrics": filtered_metrics,
            "time_range": request_data.time_range.value,
            "filters": request_data.filters,
            "created_by": user_id,
            "created_at": overview["generated_at"]
        }
        
        return {
            "success": True,
            "dashboard": dashboard_config,
            "message": f"Custom dashboard '{request_data.name}' created successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create custom dashboard: {str(e)}")


@router.get("/export")
async def export_analytics_data(
    format: str = Query("json", pattern="^(json|csv|xlsx)$"),
    time_range: TimeRange = Query(TimeRange.LAST_30_DAYS),
    current_user: dict = Depends(get_current_user)
):
    """
    Export analytics data in various formats.
    
    Args:
        format: Export format (json, csv, xlsx)
        time_range: Time range for exported data
        current_user: Current authenticated user
        
    Returns:
        Exported analytics data
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        # Get comprehensive analytics data
        overview = await analytics_engine.get_overview_metrics(
            workspace_id=workspace_id,
            time_range=time_range
        )
        
        if format == "json":
            return {
                "success": True,
                "data": overview,
                "format": "json",
                "exported_at": overview["generated_at"]
            }
        
        elif format == "csv":
            # Simplified CSV export - in production, generate actual CSV
            return {
                "success": True,
                "message": "CSV export functionality would be implemented here",
                "format": "csv",
                "download_url": f"/api/v1/analytics/download/{workspace_id}/analytics.csv"
            }
        
        elif format == "xlsx":
            # Simplified Excel export - in production, generate actual Excel file
            return {
                "success": True,
                "message": "Excel export functionality would be implemented here",
                "format": "xlsx",
                "download_url": f"/api/v1/analytics/download/{workspace_id}/analytics.xlsx"
            }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to export analytics data: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for analytics service.
    
    Returns:
        Service health status
    """
    try:
        return {
            "status": "healthy",
            "service": "contract_analytics",
            "features": [
                "overview_metrics",
                "trend_analysis",
                "predictive_insights",
                "performance_metrics",
                "risk_analysis",
                "custom_dashboards"
            ],
            "cache_size": len(analytics_engine.cache)
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "contract_analytics",
            "error": str(e)
        }
