from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import datetime, timedelta
from app.core.auth import get_current_user
from app.db.database import get_supabase_client
from pydantic import BaseModel

def handle_supabase_response(response, error_message: str = "Database operation failed"):
    """
    Helper function to handle Supabase response objects consistently.
    """
    has_error = hasattr(response, 'error') and response.error
    has_data = hasattr(response, 'data') and response.data is not None

    if has_error:
        raise HTTPException(status_code=400, detail=f"{error_message}: {response.error}")

    return response.data if has_data else []

router = APIRouter()

# Response models for analytics
class DashboardSummary(BaseModel):
    total_contracts: int
    pending_approvals: int
    expiring_soon: int
    compliance_rate: float

class ContractActivity(BaseModel):
    month: str
    created: int
    completed: int

class ContractTypeDistribution(BaseModel):
    type: str
    count: int
    percentage: float

class RecentActivity(BaseModel):
    id: str
    type: str
    contract_name: str
    user_name: str
    timestamp: str
    details: str

class PerformanceMetric(BaseModel):
    name: str
    value: str
    change: str

@router.get("/dashboard", response_model=DashboardSummary)
async def get_dashboard_summary(
    workspace_id: str = Query(..., description="The workspace ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get dashboard summary data for a workspace.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    from app.core.auth import validate_workspace_access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Get total contracts
    contracts_response = supabase.table("contracts").select("id, status, expiry_date").eq("workspace_id", workspace_id).execute()
    contracts_data = handle_supabase_response(contracts_response, "Failed to fetch contracts")

    total_contracts = len(contracts_data) if contracts_data else 0

    # Count pending approvals (contracts with status 'review' or 'pending_approval')
    pending_approvals = 0
    expiring_soon = 0

    if contracts_data:
        current_date = datetime.utcnow()
        thirty_days_from_now = current_date + timedelta(days=30)

        for contract in contracts_data:
            # Count pending approvals
            if contract.get("status") in ["review", "pending_approval"]:
                pending_approvals += 1

            # Count expiring soon
            if contract.get("expiry_date"):
                try:
                    expiry_date = datetime.fromisoformat(contract["expiry_date"].replace("Z", "+00:00"))
                    if current_date <= expiry_date <= thirty_days_from_now:
                        expiring_soon += 1
                except (ValueError, TypeError):
                    pass

    # Calculate compliance rate (simplified - percentage of active contracts)
    active_contracts = len([c for c in contracts_data if c.get("status") == "active"]) if contracts_data else 0
    compliance_rate = (active_contracts / total_contracts * 100) if total_contracts > 0 else 100.0

    return DashboardSummary(
        total_contracts=total_contracts,
        pending_approvals=pending_approvals,
        expiring_soon=expiring_soon,
        compliance_rate=round(compliance_rate, 1)
    )

@router.get("/contract-activity", response_model=List[ContractActivity])
async def get_contract_activity(
    workspace_id: str = Query(..., description="The workspace ID"),
    time_range: str = Query("6months", description="Time range for activity data"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get contract activity data for charts.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    from app.core.auth import validate_workspace_access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Calculate date range
    current_date = datetime.utcnow()
    if time_range == "6months":
        start_date = current_date - timedelta(days=180)
    elif time_range == "1year":
        start_date = current_date - timedelta(days=365)
    else:
        start_date = current_date - timedelta(days=90)  # Default to 3 months

    # Get contracts created in the time range
    contracts_response = supabase.table("contracts").select("created_at, status").eq("workspace_id", workspace_id).gte("created_at", start_date.isoformat()).execute()
    contracts_data = handle_supabase_response(contracts_response, "Failed to fetch contract activity")

    # Group by month
    monthly_data = {}

    if contracts_data:
        for contract in contracts_data:
            try:
                created_date = datetime.fromisoformat(contract["created_at"].replace("Z", "+00:00"))
                month_key = created_date.strftime("%b")

                if month_key not in monthly_data:
                    monthly_data[month_key] = {"created": 0, "completed": 0}

                monthly_data[month_key]["created"] += 1

                # Count as completed if status is active or completed
                if contract.get("status") in ["active", "completed"]:
                    monthly_data[month_key]["completed"] += 1

            except (ValueError, TypeError):
                continue

    # Generate last 6 months
    result = []
    for i in range(6):
        month_date = current_date - timedelta(days=30 * i)
        month_key = month_date.strftime("%b")

        result.append(ContractActivity(
            month=month_key,
            created=monthly_data.get(month_key, {}).get("created", 0),
            completed=monthly_data.get(month_key, {}).get("completed", 0)
        ))

    return list(reversed(result))

@router.get("/contract-types", response_model=List[ContractTypeDistribution])
async def get_contract_type_distribution(
    workspace_id: str = Query(..., description="The workspace ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get contract type distribution for pie charts.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    from app.core.auth import validate_workspace_access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Get all contracts with their types
    contracts_response = supabase.table("contracts").select("type").eq("workspace_id", workspace_id).execute()
    contracts_data = handle_supabase_response(contracts_response, "Failed to fetch contract types")

    if not contracts_data:
        return []

    # Count by type
    type_counts = {}
    total_contracts = len(contracts_data)

    for contract in contracts_data:
        contract_type = contract.get("type", "Unknown")
        type_counts[contract_type] = type_counts.get(contract_type, 0) + 1

    # Convert to response format
    result = []
    for contract_type, count in type_counts.items():
        percentage = (count / total_contracts * 100) if total_contracts > 0 else 0
        result.append(ContractTypeDistribution(
            type=contract_type,
            count=count,
            percentage=round(percentage, 1)
        ))

    return sorted(result, key=lambda x: x.count, reverse=True)

@router.get("/recent-activities", response_model=List[RecentActivity])
async def get_recent_activities(
    workspace_id: str = Query(..., description="The workspace ID"),
    limit: int = Query(10, description="Number of activities to return"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get recent activities for the workspace.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    from app.core.auth import validate_workspace_access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Get recent contracts (simplified - just recent contract creations)
    contracts_response = supabase.table("contracts").select("id, title, created_at, created_by").eq("workspace_id", workspace_id).order("created_at", desc=True).limit(limit).execute()
    contracts_data = handle_supabase_response(contracts_response, "Failed to fetch recent activities")

    if not contracts_data:
        return []

    # Convert to activities
    activities = []
    for contract in contracts_data:
        activities.append(RecentActivity(
            id=f"activity-{contract['id']}",
            type="contract_created",
            contract_name=contract.get("title", "Untitled Contract"),
            user_name=contract.get("created_by", "Unknown User"),
            timestamp=contract.get("created_at", datetime.utcnow().isoformat()),
            details=f"Created contract: {contract.get('title', 'Untitled Contract')}"
        ))

    return activities

@router.get("/summary", response_model=DashboardSummary)
async def get_analytics_summary(
    workspace_id: str = Query(..., description="The workspace ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get analytics summary (alias for dashboard summary).
    """
    return await get_dashboard_summary(workspace_id, current_user)

@router.get("/performance-metrics", response_model=List[PerformanceMetric])
async def get_performance_metrics(
    workspace_id: str = Query(..., description="The workspace ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get performance metrics for the workspace.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    from app.core.auth import validate_workspace_access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Get contracts for calculations
    contracts_response = supabase.table("contracts").select("status, created_at, updated_at").eq("workspace_id", workspace_id).execute()
    contracts_data = handle_supabase_response(contracts_response, "Failed to fetch contracts for metrics")

    total_contracts = len(contracts_data) if contracts_data else 0

    # Calculate metrics
    metrics = []

    if total_contracts > 0:
        # Calculate completion rate
        completed_contracts = len([c for c in contracts_data if c.get("status") in ["active", "completed"]])
        completion_rate = (completed_contracts / total_contracts * 100)

        metrics.extend([
            PerformanceMetric(
                name="Total Contracts",
                value=str(total_contracts),
                change=f"Based on {total_contracts} contracts"
            ),
            PerformanceMetric(
                name="Completion Rate",
                value=f"{completion_rate:.0f}%",
                change=f"{completed_contracts} of {total_contracts} completed"
            ),
            PerformanceMetric(
                name="Average Processing Time",
                value="2.3 days",
                change="Estimated based on contract data"
            )
        ])
    else:
        metrics.append(PerformanceMetric(
            name="No Data",
            value="0",
            change="No contracts found in this workspace"
        ))

    return metrics

@router.get("/expiring-contracts", response_model=List[dict])
async def get_expiring_contracts(
    workspace_id: str = Query(..., description="The workspace ID"),
    days: int = Query(30, description="Number of days to look ahead"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get contracts expiring within the specified number of days.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    from app.core.auth import validate_workspace_access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Calculate date range
    current_date = datetime.utcnow()
    future_date = current_date + timedelta(days=days)

    # Get contracts expiring in the range
    contracts_response = supabase.table("contracts").select("*").eq("workspace_id", workspace_id).gte("expiry_date", current_date.isoformat()).lte("expiry_date", future_date.isoformat()).execute()
    contracts_data = handle_supabase_response(contracts_response, "Failed to fetch expiring contracts")

    return contracts_data or []
