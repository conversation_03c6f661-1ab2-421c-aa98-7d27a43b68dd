from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Path, Query, Body
from typing import List, Optional
from app.schemas.signer import Signer, SignerCreate, SignerUpdate
from app.core.auth import get_current_user
from app.db.database import get_supabase_client
from datetime import datetime
import uuid

router = APIRouter()

@router.get("/contracts/{contract_id}/signers", response_model=List[Signer])
async def get_contract_signers(
    contract_id: str = Path(..., description="The ID of the contract"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve all signers for a specific contract.
    """
    supabase = get_supabase_client()

    # First check if the user has access to this contract
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()

    if contract_response.error:
        raise HTTPException(status_code=400, detail=contract_response.error.message)

    if not contract_response.data:
        raise HTTPException(status_code=404, detail="Contract not found")

    # Get the workspace ID for this contract
    workspace_id = contract_response.data[0]["workspace_id"]

    # Check if the user is a member of this workspace
    member_response = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    if member_response.error:
        raise HTTPException(status_code=400, detail=member_response.error.message)

    if not member_response.data:
        raise HTTPException(status_code=403, detail="You don't have access to this contract")

    # Get the signers for this contract
    response = supabase.table("contract_signers").select("*").eq("contract_id", contract_id).order("order").execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return response.data

@router.post("/contracts/{contract_id}/signers", response_model=Signer)
async def add_contract_signer(
    signer: SignerCreate,
    contract_id: str = Path(..., description="The ID of the contract"),
    current_user: dict = Depends(get_current_user)
):
    """
    Add a new signer to a contract.
    """
    supabase = get_supabase_client()

    # First check if the user has access to this contract
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()

    if contract_response.error:
        raise HTTPException(status_code=400, detail=contract_response.error.message)

    if not contract_response.data:
        raise HTTPException(status_code=404, detail="Contract not found")

    # Get the workspace ID for this contract
    workspace_id = contract_response.data[0]["workspace_id"]

    # Check if the user is a member of this workspace
    member_response = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    if member_response.error:
        raise HTTPException(status_code=400, detail=member_response.error.message)

    if not member_response.data:
        raise HTTPException(status_code=403, detail="You don't have access to this contract")

    # Prepare the signer data
    signer_data = signer.model_dump()
    signer_data["id"] = str(uuid.uuid4())
    signer_data["contract_id"] = contract_id
    signer_data["created_at"] = datetime.utcnow().isoformat()
    signer_data["created_by"] = current_user["id"]

    # Insert the signer
    response = supabase.table("contract_signers").insert(signer_data).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return response.data[0]

@router.delete("/contracts/{contract_id}/signers/{signer_id}")
async def remove_contract_signer(
    contract_id: str = Path(..., description="The ID of the contract"),
    signer_id: str = Path(..., description="The ID of the signer to remove"),
    current_user: dict = Depends(get_current_user)
):
    """
    Remove a signer from a contract.
    """
    supabase = get_supabase_client()

    # First check if the user has access to this contract
    contract_response = supabase.table("contracts").select("workspace_id").eq("id", contract_id).execute()

    if contract_response.error:
        raise HTTPException(status_code=400, detail=contract_response.error.message)

    if not contract_response.data:
        raise HTTPException(status_code=404, detail="Contract not found")

    # Get the workspace ID for this contract
    workspace_id = contract_response.data[0]["workspace_id"]

    # Check if the user is a member of this workspace
    member_response = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    if member_response.error:
        raise HTTPException(status_code=400, detail=member_response.error.message)

    if not member_response.data:
        raise HTTPException(status_code=403, detail="You don't have access to this contract")

    # Delete the signer
    response = supabase.table("contract_signers").delete().eq("id", signer_id).eq("contract_id", contract_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return {"message": "Signer removed successfully"}

@router.post("/contracts/{contract_id}/send-for-signature")
async def send_contract_for_signature(
    contract_id: str = Path(..., description="The ID of the contract"),
    current_user: dict = Depends(get_current_user)
):
    """
    Send a contract for signature to all signers.
    """
    supabase = get_supabase_client()

    # First check if the user has access to this contract
    contract_response = supabase.table("contracts").select("workspace_id, title").eq("id", contract_id).execute()

    if contract_response.error:
        raise HTTPException(status_code=400, detail=contract_response.error.message)

    if not contract_response.data:
        raise HTTPException(status_code=404, detail="Contract not found")

    # Get the workspace ID and title for this contract
    workspace_id = contract_response.data[0]["workspace_id"]
    contract_title = contract_response.data[0]["title"]

    # Check if the user is a member of this workspace
    member_response = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    if member_response.error:
        raise HTTPException(status_code=400, detail=member_response.error.message)

    if not member_response.data:
        raise HTTPException(status_code=403, detail="You don't have access to this contract")

    # Get the signers for this contract
    signers_response = supabase.table("contract_signers").select("*").eq("contract_id", contract_id).execute()

    if signers_response.error:
        raise HTTPException(status_code=400, detail=signers_response.error.message)

    if not signers_response.data:
        raise HTTPException(status_code=400, detail="No signers found for this contract")

    # In a real application, you would send emails to all signers here
    # For now, we'll just update the contract status
    update_response = supabase.table("contracts").update({"status": "pending_signature"}).eq("id", contract_id).execute()

    if update_response.error:
        raise HTTPException(status_code=400, detail=update_response.error.message)

    return {
        "message": f"Contract '{contract_title}' sent for signature to {len(signers_response.data)} signers",
        "signers_count": len(signers_response.data)
    }
