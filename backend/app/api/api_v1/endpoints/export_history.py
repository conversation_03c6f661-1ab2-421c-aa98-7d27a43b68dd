from fastapi import APIRouter, Depends, HTTPException, Query, Path
from typing import List, Optional
from datetime import datetime, timedelta
import uuid

from app.core.auth import get_current_user, validate_workspace_access
from app.db.database import get_supabase_client
from app.schemas.export_history import (
    ExportHistory,
    ExportHistoryCreate,
    ExportHistoryUpdate,
    ExportHistoryResponse
)

router = APIRouter()

def handle_supabase_response(response, error_message: str = "Database operation failed"):
    """
    Helper function to handle Supabase response objects consistently.
    """
    has_error = hasattr(response, 'error') and response.error
    has_data = hasattr(response, 'data') and response.data is not None

    if has_error:
        print(f"Supabase error: {response.error}")
        raise HTTPException(status_code=500, detail=f"{error_message}: {response.error}")

    if not has_data:
        return None

    return response.data

@router.get("/", response_model=List[ExportHistoryResponse])
async def get_export_history(
    workspace_id: str = Query(..., description="Workspace ID (required for security)"),
    contract_id: Optional[str] = Query(None, description="Filter by contract ID"),
    format: Optional[str] = Query(None, description="Filter by export format"),
    status: Optional[str] = Query(None, description="Filter by status"),
    days: Optional[int] = Query(30, description="Number of days to look back"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve export history for a specific workspace.
    """
    supabase = get_supabase_client()
    
    # Validate workspace access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)
    
    # Build query
    query = supabase.table("export_history").select("*").eq("workspace_id", workspace_id)
    
    # Apply filters
    if contract_id:
        query = query.eq("contract_id", contract_id)
    
    if format:
        query = query.eq("format", format.upper())
    
    if status:
        query = query.eq("status", status)
    
    # Apply date filter
    if days:
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        query = query.gte("exported_at", cutoff_date.isoformat())
    
    # Apply pagination and ordering
    query = query.order("exported_at", desc=True).range(skip, skip + limit - 1)
    
    response = query.execute()
    data = handle_supabase_response(response, "Failed to retrieve export history")
    
    return data or []

@router.post("/", response_model=ExportHistoryResponse)
async def create_export_record(
    export_data: ExportHistoryCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new export history record.
    """
    supabase = get_supabase_client()
    
    # Validate workspace access
    await validate_workspace_access(current_user["id"], export_data.workspace_id, supabase)
    
    # Prepare export record
    export_record = export_data.model_dump()
    export_record["id"] = str(uuid.uuid4())
    export_record["exported_by"] = {
        "id": current_user["id"],
        "name": f"{current_user.get('first_name', '')} {current_user.get('last_name', '')}".strip() or "User"
    }
    export_record["exported_at"] = datetime.utcnow().isoformat()
    export_record["created_at"] = datetime.utcnow().isoformat()
    
    # Set expiration date (30 days from now by default)
    export_record["expires_at"] = (datetime.utcnow() + timedelta(days=30)).isoformat()
    
    # Insert into database
    response = supabase.table("export_history").insert(export_record).execute()
    data = handle_supabase_response(response, "Failed to create export record")
    
    if not data:
        raise HTTPException(status_code=500, detail="Failed to create export record")
    
    return data[0]

@router.get("/{export_id}", response_model=ExportHistoryResponse)
async def get_export_record(
    export_id: str = Path(..., description="The ID of the export record"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve a specific export record by ID.
    """
    supabase = get_supabase_client()
    
    # Get export record
    response = supabase.table("export_history").select("*").eq("id", export_id).execute()
    data = handle_supabase_response(response, "Failed to retrieve export record")
    
    if not data:
        raise HTTPException(status_code=404, detail="Export record not found")
    
    export_record = data[0]
    
    # Validate workspace access
    await validate_workspace_access(current_user["id"], export_record["workspace_id"], supabase)
    
    return export_record

@router.put("/{export_id}", response_model=ExportHistoryResponse)
async def update_export_record(
    export_update: ExportHistoryUpdate,
    export_id: str = Path(..., description="The ID of the export record"),
    current_user: dict = Depends(get_current_user)
):
    """
    Update an export record (e.g., increment download count).
    """
    supabase = get_supabase_client()
    
    # Check if export record exists and validate access
    check_response = supabase.table("export_history").select("workspace_id").eq("id", export_id).execute()
    check_data = handle_supabase_response(check_response, "Failed to check export record")
    
    if not check_data:
        raise HTTPException(status_code=404, detail="Export record not found")
    
    # Validate workspace access
    await validate_workspace_access(current_user["id"], check_data[0]["workspace_id"], supabase)
    
    # Prepare update data
    update_data = export_update.model_dump(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow().isoformat()
    
    # Update the record
    response = supabase.table("export_history").update(update_data).eq("id", export_id).execute()
    data = handle_supabase_response(response, "Failed to update export record")
    
    if not data:
        raise HTTPException(status_code=500, detail="Failed to update export record")
    
    return data[0]

@router.delete("/{export_id}")
async def delete_export_record(
    export_id: str = Path(..., description="The ID of the export record"),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete an export record.
    """
    supabase = get_supabase_client()
    
    # Check if export record exists and validate access
    check_response = supabase.table("export_history").select("workspace_id").eq("id", export_id).execute()
    check_data = handle_supabase_response(check_response, "Failed to check export record")
    
    if not check_data:
        raise HTTPException(status_code=404, detail="Export record not found")
    
    # Validate workspace access
    await validate_workspace_access(current_user["id"], check_data[0]["workspace_id"], supabase)
    
    # Delete the record
    response = supabase.table("export_history").delete().eq("id", export_id).execute()
    handle_supabase_response(response, "Failed to delete export record")
    
    return {"message": "Export record deleted successfully"}

@router.post("/{export_id}/download")
async def track_download(
    export_id: str = Path(..., description="The ID of the export record"),
    current_user: dict = Depends(get_current_user)
):
    """
    Track a download and increment the download count.
    """
    supabase = get_supabase_client()
    
    # Get current export record
    response = supabase.table("export_history").select("*").eq("id", export_id).execute()
    data = handle_supabase_response(response, "Failed to retrieve export record")
    
    if not data:
        raise HTTPException(status_code=404, detail="Export record not found")
    
    export_record = data[0]
    
    # Validate workspace access
    await validate_workspace_access(current_user["id"], export_record["workspace_id"], supabase)
    
    # Check if export is still active
    if export_record["status"] != "active":
        raise HTTPException(status_code=400, detail="Export is no longer active")
    
    # Check if export has expired
    if export_record.get("expires_at"):
        expires_at = datetime.fromisoformat(export_record["expires_at"].replace('Z', '+00:00'))
        if expires_at < datetime.utcnow().replace(tzinfo=expires_at.tzinfo):
            # Mark as expired
            supabase.table("export_history").update({
                "status": "expired",
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", export_id).execute()
            raise HTTPException(status_code=400, detail="Export has expired")
    
    # Increment download count
    new_count = export_record.get("download_count", 0) + 1
    update_response = supabase.table("export_history").update({
        "download_count": new_count,
        "updated_at": datetime.utcnow().isoformat()
    }).eq("id", export_id).execute()
    
    handle_supabase_response(update_response, "Failed to update download count")
    
    return {
        "message": "Download tracked successfully",
        "download_count": new_count,
        "download_url": export_record["download_url"]
    }

@router.get("/stats/summary")
async def get_export_stats(
    workspace_id: str = Query(..., description="Workspace ID"),
    days: Optional[int] = Query(30, description="Number of days to analyze"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get export statistics summary for a workspace.
    """
    supabase = get_supabase_client()
    
    # Validate workspace access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)
    
    # Calculate date range
    cutoff_date = datetime.utcnow() - timedelta(days=days or 30)
    
    # Get export data
    response = supabase.table("export_history").select("*").eq("workspace_id", workspace_id).gte("exported_at", cutoff_date.isoformat()).execute()
    data = handle_supabase_response(response, "Failed to retrieve export statistics")
    
    if not data:
        return {
            "total_exports": 0,
            "total_downloads": 0,
            "formats": {},
            "status_breakdown": {},
            "most_exported_contracts": []
        }
    
    # Calculate statistics
    total_exports = len(data)
    total_downloads = sum(record.get("download_count", 0) for record in data)
    
    # Format breakdown
    formats = {}
    for record in data:
        format_type = record.get("format", "Unknown")
        formats[format_type] = formats.get(format_type, 0) + 1
    
    # Status breakdown
    status_breakdown = {}
    for record in data:
        status = record.get("status", "unknown")
        status_breakdown[status] = status_breakdown.get(status, 0) + 1
    
    # Most exported contracts
    contract_counts = {}
    for record in data:
        contract_id = record.get("contract_id")
        contract_title = record.get("contract_title", "Unknown")
        if contract_id:
            if contract_id not in contract_counts:
                contract_counts[contract_id] = {"title": contract_title, "count": 0}
            contract_counts[contract_id]["count"] += 1
    
    most_exported = sorted(
        [{"contract_id": k, **v} for k, v in contract_counts.items()],
        key=lambda x: x["count"],
        reverse=True
    )[:5]
    
    return {
        "total_exports": total_exports,
        "total_downloads": total_downloads,
        "formats": formats,
        "status_breakdown": status_breakdown,
        "most_exported_contracts": most_exported,
        "period_days": days
    }
