"""
API Testing Framework endpoints for Averum Contracts
Provides API testing, contract validation, performance testing, and load testing
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from tests.test_framework.api_testing_service import api_testing_service, TestType

router = APIRouter()


class TestSuiteRequest(BaseModel):
    """Request model for running test suites."""
    test_types: List[TestType]
    include_performance: bool = True
    include_load: bool = False
    auth_token: Optional[str] = None


class TestConfigRequest(BaseModel):
    """Request model for updating test configuration."""
    base_url: Optional[str] = None
    timeout: Optional[float] = None
    max_retries: Optional[int] = None
    performance_thresholds: Optional[Dict[str, Any]] = None


@router.post("/run-contract-tests")
async def run_contract_tests(
    background_tasks: BackgroundTasks,
    auth_token: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Run API contract tests to validate response schemas.
    
    Args:
        background_tasks: FastAPI background tasks
        auth_token: Optional authentication token for testing
        current_user: Current authenticated user
        
    Returns:
        Contract test results
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for API testing")
        
        # Use provided token or generate one for testing
        test_token = auth_token or "test-token-123"
        
        # Run contract tests
        test_results = await api_testing_service.run_contract_tests(test_token)
        
        # Generate summary
        total_tests = len(test_results)
        passed_tests = len([r for r in test_results if r.status.value == "passed"])
        failed_tests = len([r for r in test_results if r.status.value == "failed"])
        
        return {
            "success": True,
            "test_type": "contract",
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "results": [
                {
                    "test_id": r.test_id,
                    "test_name": r.test_name,
                    "status": r.status.value,
                    "duration": r.duration,
                    "error_message": r.error_message,
                    "details": r.details,
                    "timestamp": r.timestamp.isoformat()
                }
                for r in test_results
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to run contract tests: {str(e)}")


@router.post("/run-performance-tests")
async def run_performance_tests(
    background_tasks: BackgroundTasks,
    auth_token: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Run performance tests to validate response times and throughput.
    
    Args:
        background_tasks: FastAPI background tasks
        auth_token: Optional authentication token for testing
        current_user: Current authenticated user
        
    Returns:
        Performance test results
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for performance testing")
        
        # Use provided token or generate one for testing
        test_token = auth_token or "test-token-123"
        
        # Run performance tests
        test_results = await api_testing_service.run_performance_tests(test_token)
        
        # Generate summary
        total_tests = len(test_results)
        passed_tests = len([r for r in test_results if r.status.value == "passed"])
        failed_tests = len([r for r in test_results if r.status.value == "failed"])
        
        # Extract performance metrics
        performance_metrics = []
        for result in test_results:
            if result.details and "metrics" in result.details:
                performance_metrics.append({
                    "test_name": result.test_name,
                    "metrics": result.details["metrics"]
                })
        
        return {
            "success": True,
            "test_type": "performance",
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "performance_metrics": performance_metrics,
            "results": [
                {
                    "test_id": r.test_id,
                    "test_name": r.test_name,
                    "status": r.status.value,
                    "duration": r.duration,
                    "error_message": r.error_message,
                    "details": r.details,
                    "timestamp": r.timestamp.isoformat()
                }
                for r in test_results
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to run performance tests: {str(e)}")


@router.post("/run-load-tests")
async def run_load_tests(
    background_tasks: BackgroundTasks,
    auth_token: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Run load tests to validate system behavior under high load.
    
    Args:
        background_tasks: FastAPI background tasks
        auth_token: Optional authentication token for testing
        current_user: Current authenticated user
        
    Returns:
        Load test results
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for load testing")
        
        # Use provided token or generate one for testing
        test_token = auth_token or "test-token-123"
        
        # Run load tests in background due to long duration
        background_tasks.add_task(
            run_load_tests_background,
            test_token=test_token,
            user_id=current_user.get("sub") or current_user.get("user_id")
        )
        
        return {
            "success": True,
            "message": "Load tests started in background",
            "test_type": "load",
            "estimated_duration": "5-10 minutes",
            "status_endpoint": "/api/v1/api-testing/test-status"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start load tests: {str(e)}")


@router.post("/run-test-suite")
async def run_test_suite(
    request_data: TestSuiteRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """
    Run comprehensive test suite with multiple test types.
    
    Args:
        request_data: Test suite configuration
        background_tasks: FastAPI background tasks
        current_user: Current authenticated user
        
    Returns:
        Test suite results
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for comprehensive testing")
        
        test_token = request_data.auth_token or "test-token-123"
        all_results = []
        
        # Run requested test types
        for test_type in request_data.test_types:
            if test_type == TestType.CONTRACT:
                results = await api_testing_service.run_contract_tests(test_token)
                all_results.extend(results)
            elif test_type == TestType.PERFORMANCE and request_data.include_performance:
                results = await api_testing_service.run_performance_tests(test_token)
                all_results.extend(results)
            elif test_type == TestType.LOAD and request_data.include_load:
                # Run load tests in background
                background_tasks.add_task(
                    run_load_tests_background,
                    test_token=test_token,
                    user_id=current_user.get("sub") or current_user.get("user_id")
                )
        
        # Generate comprehensive report
        report = api_testing_service.generate_test_report()
        
        return {
            "success": True,
            "test_suite_report": report,
            "test_types_run": [t.value for t in request_data.test_types],
            "total_results": len(all_results)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to run test suite: {str(e)}")


@router.get("/test-report")
async def get_test_report(
    current_user: dict = Depends(get_current_user)
):
    """
    Get comprehensive test report from recent test runs.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Test report with results and recommendations
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for test reports")
        
        report = api_testing_service.generate_test_report()
        
        return {
            "success": True,
            "test_report": report
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get test report: {str(e)}")


@router.get("/test-status")
async def get_test_status(
    current_user: dict = Depends(get_current_user)
):
    """
    Get current test execution status.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Test execution status
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin", "workspace_admin"]:
            raise HTTPException(status_code=403, detail="Admin access required for test status")
        
        # Get recent test results
        recent_results = api_testing_service.test_results[-10:] if api_testing_service.test_results else []
        
        return {
            "success": True,
            "test_status": {
                "total_tests_run": len(api_testing_service.test_results),
                "recent_results": [
                    {
                        "test_name": r.test_name,
                        "test_type": r.test_type.value,
                        "status": r.status.value,
                        "duration": r.duration,
                        "timestamp": r.timestamp.isoformat()
                    }
                    for r in recent_results
                ],
                "last_test_run": recent_results[-1].timestamp.isoformat() if recent_results else None
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get test status: {str(e)}")


@router.post("/configure")
async def configure_testing(
    request_data: TestConfigRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Configure API testing parameters.
    
    Args:
        request_data: Test configuration
        current_user: Current authenticated user
        
    Returns:
        Configuration status
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for test configuration")
        
        # Update configuration
        if request_data.base_url:
            api_testing_service.base_url = request_data.base_url
        
        # In a real implementation, would update other configuration parameters
        
        return {
            "success": True,
            "message": "API testing configuration updated successfully",
            "current_config": {
                "base_url": api_testing_service.base_url,
                "test_suites_available": list(api_testing_service.test_suites.keys())
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to configure testing: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for API testing service.
    
    Returns:
        Service health status
    """
    try:
        return {
            "status": "healthy",
            "service": "api_testing",
            "features": [
                "contract_testing",
                "performance_testing", 
                "load_testing",
                "openapi_validation",
                "test_reporting"
            ],
            "test_suites_available": list(api_testing_service.test_suites.keys()),
            "total_tests_run": len(api_testing_service.test_results)
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "api_testing",
            "error": str(e)
        }


async def run_load_tests_background(test_token: str, user_id: str):
    """Background task to run load tests."""
    try:
        results = await api_testing_service.run_load_tests(test_token)
        
        # In a real implementation, would store results and notify user
        print(f"Load tests completed for user {user_id}: {len(results)} tests run")
        
    except Exception as e:
        print(f"Load tests failed for user {user_id}: {e}")
