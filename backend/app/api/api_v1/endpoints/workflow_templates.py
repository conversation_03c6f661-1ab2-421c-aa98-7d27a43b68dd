"""
Workflow Templates API endpoints for Averum Contracts

Provides endpoints for managing workflow templates including:
- Template CRUD operations
- Template recommendations
- Workflow creation from templates
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import logging

from app.auth.clerk_auth import get_current_user
from app.services.approval_workflow_service import approval_workflow_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/workflow-templates", tags=["workflow-templates"])


class WorkflowTemplateCreate(BaseModel):
    """Schema for creating workflow templates."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    workflow_type: str = Field(..., pattern="^(sequential|parallel|conditional|hybrid)$")
    steps: List[Dict[str, Any]] = Field(..., min_items=1)
    escalation_rules: Optional[Dict[str, Any]] = Field(default_factory=dict)
    timeout_settings: Optional[Dict[str, Any]] = Field(default_factory=dict)
    conditions: Optional[Dict[str, Any]] = Field(default_factory=dict)
    workspace_id: Optional[str] = None


class WorkflowTemplateUpdate(BaseModel):
    """Schema for updating workflow templates."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    workflow_type: Optional[str] = Field(None, pattern="^(sequential|parallel|conditional|hybrid)$")
    steps: Optional[List[Dict[str, Any]]] = Field(None, min_items=1)
    escalation_rules: Optional[Dict[str, Any]] = None
    timeout_settings: Optional[Dict[str, Any]] = None
    conditions: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class WorkflowFromTemplateCreate(BaseModel):
    """Schema for creating workflow from template."""
    template_id: str
    contract_id: str
    overrides: Optional[Dict[str, Any]] = Field(default_factory=dict)


class TemplateRecommendationRequest(BaseModel):
    """Schema for template recommendation requests."""
    contract_value: Optional[float] = Field(default=0, ge=0)
    contract_type: Optional[str] = Field(default="")
    risk_score: Optional[float] = Field(default=0.5, ge=0, le=1)
    department: Optional[str] = Field(default="")
    custom_fields: Optional[Dict[str, Any]] = Field(default_factory=dict)


@router.post("/", response_model=Dict[str, Any])
async def create_workflow_template(
    template_data: WorkflowTemplateCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create a new workflow template."""
    try:
        # Add user context
        template_dict = template_data.dict()
        template_dict["workspace_id"] = current_user.get("workspace_id")
        
        result = await approval_workflow_service.create_workflow_template(
            template_dict, 
            current_user["user_id"]
        )
        
        if result.get("error"):
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "Workflow template created successfully",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"Error creating workflow template: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=Dict[str, Any])
async def get_workflow_templates(
    active_only: bool = Query(True, description="Return only active templates"),
    current_user: dict = Depends(get_current_user)
):
    """Get workflow templates for the current workspace."""
    try:
        workspace_id = current_user.get("workspace_id")
        templates = await approval_workflow_service.get_workflow_templates(
            workspace_id=workspace_id,
            active_only=active_only
        )
        
        return {
            "success": True,
            "data": templates,
            "count": len(templates)
        }
        
    except Exception as e:
        logger.error(f"Error getting workflow templates: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{template_id}", response_model=Dict[str, Any])
async def get_workflow_template(
    template_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get a specific workflow template."""
    try:
        templates = await approval_workflow_service.get_workflow_templates(
            workspace_id=current_user.get("workspace_id"),
            active_only=False
        )
        
        template = next((t for t in templates if t["id"] == template_id), None)
        
        if not template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        return {
            "success": True,
            "data": template
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting workflow template: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{template_id}", response_model=Dict[str, Any])
async def update_workflow_template(
    template_id: str,
    updates: WorkflowTemplateUpdate,
    current_user: dict = Depends(get_current_user)
):
    """Update a workflow template."""
    try:
        # Filter out None values
        update_data = {k: v for k, v in updates.dict().items() if v is not None}
        
        result = await approval_workflow_service.update_workflow_template(
            template_id,
            update_data,
            current_user["user_id"]
        )
        
        if result.get("error"):
            if "not found" in result["error"].lower():
                raise HTTPException(status_code=404, detail=result["error"])
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "Workflow template updated successfully",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating workflow template: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{template_id}", response_model=Dict[str, Any])
async def delete_workflow_template(
    template_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Delete (deactivate) a workflow template."""
    try:
        result = await approval_workflow_service.delete_workflow_template(template_id)
        
        if result.get("error"):
            if "not found" in result["error"].lower():
                raise HTTPException(status_code=404, detail=result["error"])
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "Workflow template deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting workflow template: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/create-workflow", response_model=Dict[str, Any])
async def create_workflow_from_template(
    workflow_data: WorkflowFromTemplateCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create a workflow instance from a template."""
    try:
        result = await approval_workflow_service.create_workflow_from_template(
            workflow_data.template_id,
            workflow_data.contract_id,
            current_user["user_id"],
            current_user.get("workspace_id"),
            workflow_data.overrides
        )
        
        if result.get("error"):
            if "not found" in result["error"].lower():
                raise HTTPException(status_code=404, detail=result["error"])
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "Workflow created from template successfully",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating workflow from template: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/recommendations", response_model=Dict[str, Any])
async def get_template_recommendations(
    request: TemplateRecommendationRequest,
    current_user: dict = Depends(get_current_user)
):
    """Get workflow template recommendations based on contract data."""
    try:
        # Get available templates
        templates = await approval_workflow_service.get_workflow_templates(
            workspace_id=current_user.get("workspace_id"),
            active_only=True
        )
        
        # Prepare contract data for analysis
        contract_data = {
            "value": request.contract_value,
            "type": request.contract_type,
            "risk_score": request.risk_score,
            "department": request.department,
            **request.custom_fields
        }
        
        # Get optimal workflow route
        recommendation = await approval_workflow_service.get_optimal_workflow_route(
            contract_data,
            templates
        )
        
        return {
            "success": True,
            "data": recommendation,
            "contract_data": contract_data
        }
        
    except Exception as e:
        logger.error(f"Error getting template recommendations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{template_id}/performance", response_model=Dict[str, Any])
async def get_template_performance(
    template_id: str,
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: dict = Depends(get_current_user)
):
    """Get performance metrics for a workflow template."""
    try:
        # This would typically query the workflow_performance_metrics table
        # For now, return a placeholder response
        return {
            "success": True,
            "data": {
                "template_id": template_id,
                "period_days": days,
                "total_workflows": 0,
                "success_rate": 0.0,
                "average_duration_hours": 0.0,
                "escalation_rate": 0.0,
                "timeout_rate": 0.0,
                "user_satisfaction": 0.0
            },
            "message": "Performance metrics feature coming soon"
        }
        
    except Exception as e:
        logger.error(f"Error getting template performance: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{template_id}/duplicate", response_model=Dict[str, Any])
async def duplicate_workflow_template(
    template_id: str,
    new_name: str = Query(..., min_length=1, max_length=255),
    current_user: dict = Depends(get_current_user)
):
    """Duplicate an existing workflow template."""
    try:
        # Get the original template
        templates = await approval_workflow_service.get_workflow_templates(
            workspace_id=current_user.get("workspace_id"),
            active_only=False
        )
        
        original_template = next((t for t in templates if t["id"] == template_id), None)
        
        if not original_template:
            raise HTTPException(status_code=404, detail="Template not found")
        
        # Create duplicate with new name
        duplicate_data = {
            "name": new_name,
            "description": f"Copy of {original_template['name']}",
            "workflow_type": original_template["workflow_type"],
            "steps": original_template["steps"],
            "escalation_rules": original_template.get("escalation_rules", {}),
            "timeout_settings": original_template.get("timeout_settings", {}),
            "conditions": original_template.get("conditions", {}),
            "workspace_id": current_user.get("workspace_id")
        }
        
        result = await approval_workflow_service.create_workflow_template(
            duplicate_data,
            current_user["user_id"]
        )
        
        if result.get("error"):
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "Template duplicated successfully",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error duplicating workflow template: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
