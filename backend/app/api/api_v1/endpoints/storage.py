"""
API endpoints for file storage operations.
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, Path
from typing import List, Optional
from app.core.auth import get_current_user
from app.services.storage import StorageService
from pydantic import BaseModel
from datetime import datetime

router = APIRouter()

class FileResponse(BaseModel):
    """Response model for file operations."""
    id: str
    filename: str
    content_type: str
    size: int
    path: str
    url: str
    uploaded_at: str

@router.post("/upload", response_model=FileResponse)
async def upload_file(
    file: UploadFile = File(...),
    workspace_id: str = Form(..., description="The ID of the workspace"),
    folder: Optional[str] = Form("", description="Optional folder path within the bucket"),
    current_user: dict = Depends(get_current_user)
):
    """
    Upload a file to the storage bucket.
    """
    try:
        # Validate that the user has access to this workspace
        from app.core.auth import validate_workspace_access
        from app.db.database import get_supabase_client
        supabase = get_supabase_client()
        await validate_workspace_access(current_user["id"], workspace_id, supabase)
        # Check file size (limit to 10MB for example)
        file_size_limit = 10 * 1024 * 1024  # 10MB
        file_content = await file.read()
        await file.seek(0)  # Reset file cursor
        
        if len(file_content) > file_size_limit:
            raise HTTPException(
                status_code=400, 
                detail=f"File size exceeds the limit of {file_size_limit / (1024 * 1024)}MB"
            )
        
        # Check file type (optional)
        allowed_types = [
            "application/pdf", 
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain",
            "text/html",
            "application/rtf",
            "image/jpeg",
            "image/png"
        ]
        
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file.content_type} not allowed. Allowed types: {', '.join(allowed_types)}"
            )
        
        # Upload file
        result = await StorageService.upload_file(
            file=file,
            folder=folder,
            workspace_id=workspace_id,
            metadata={
                "uploaded_by": current_user["id"],
                "uploaded_at": datetime.utcnow().isoformat()
            }
        )
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading file: {str(e)}")

@router.get("/files", response_model=List[dict])
async def list_files(
    workspace_id: str = Query(..., description="The ID of the workspace"),
    folder: Optional[str] = Query("", description="Optional folder path within the bucket"),
    current_user: dict = Depends(get_current_user)
):
    """
    List files in a folder.
    """
    try:
        # Validate that the user has access to this workspace
        from app.core.auth import validate_workspace_access
        from app.db.database import get_supabase_client
        supabase = get_supabase_client()
        await validate_workspace_access(current_user["id"], workspace_id, supabase)
        files = StorageService.list_files(folder=folder, workspace_id=workspace_id)
        return files
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing files: {str(e)}")

@router.get("/files/{file_path:path}")
async def get_file_url(
    file_path: str = Path(..., description="The path to the file in the bucket"),
    expires_in: Optional[int] = Query(3600, description="Number of seconds until the URL expires"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get a signed URL for a file that expires after a certain time.
    """
    try:
        signed_url = StorageService.get_file_url(file_path=file_path, expires_in=expires_in)
        return {"url": signed_url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting file URL: {str(e)}")

@router.delete("/files/{file_path:path}")
async def delete_file(
    file_path: str = Path(..., description="The path to the file in the bucket"),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a file from the storage bucket.
    """
    try:
        success = StorageService.delete_file(file_path=file_path)
        if not success:
            raise HTTPException(status_code=500, detail="Error deleting file")
        return {"message": "File deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting file: {str(e)}")
