from fastapi import APIRouter, Depends, HTTPException, Path, Query, Body, UploadFile, File, Form
from typing import List, Optional, Dict, Any
from app.schemas.document import Document, DocumentCreate, DocumentUpdate, SignatureData, FileInfo
from app.core.auth import get_current_user, validate_workspace_access, get_authenticated_db_client
from app.db.database import get_supabase_client
from app.services.storage import StorageService
from app.services.document_search_service import DocumentSearchService
from app.utils.response import handle_supabase_response, not_found_error
from datetime import datetime
import uuid
import json

router = APIRouter()

@router.get("/documents", response_model=List[Document])
async def get_documents(
    workspace_id: str = Query(..., description="Workspace ID (required for security)"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve documents for a specific workspace that the user has access to.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    from app.core.auth import validate_workspace_access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Always filter by workspace_id for security
    query = supabase.table("documents").select("*").eq("workspace_id", workspace_id)

    response = query.execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return response.data

@router.post("/documents", response_model=Document)
async def create_document(
    document: DocumentCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new document.
    """
    supabase = get_supabase_client()

    # Prepare the document data
    document_data = document.model_dump()
    document_data["id"] = f"doc-{str(uuid.uuid4())}"
    document_data["created_at"] = datetime.utcnow().isoformat()
    document_data["created_by"] = current_user["id"]
    document_data["status"] = "draft"

    # Insert the document
    response = supabase.table("documents").insert(document_data).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return response.data[0]

# Enhanced Search Endpoints

@router.get("/search/advanced")
async def advanced_document_search(
    workspace_id: str = Query(..., description="Workspace ID to search in"),
    query: str = Query(..., description="Search query"),
    search_type: str = Query("hybrid", description="Search type: text, semantic, or hybrid"),
    file_types: Optional[str] = Query(None, description="Comma-separated file types to filter"),
    folder: Optional[str] = Query(None, description="Folder to search in"),
    date_from: Optional[str] = Query(None, description="Start date filter (ISO format)"),
    date_to: Optional[str] = Query(None, description="End date filter (ISO format)"),
    limit: int = Query(50, description="Maximum number of results"),
    offset: int = Query(0, description="Pagination offset"),
    current_user: dict = Depends(get_current_user)
):
    """
    Perform advanced search across documents with AI-powered semantic search.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)

    # Validate workspace access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Initialize search service
    search_service = DocumentSearchService(supabase)

    # Prepare filters
    filters = {}
    if file_types:
        filters["file_type"] = [ft.strip() for ft in file_types.split(",")]
    if folder and folder != "all":
        filters["folder"] = folder
    if date_from or date_to:
        filters["date_range"] = {}
        if date_from:
            filters["date_range"]["start"] = date_from
        if date_to:
            filters["date_range"]["end"] = date_to

    # Perform search
    try:
        results = await search_service.advanced_search(
            workspace_id=workspace_id,
            query=query,
            filters=filters,
            search_type=search_type,
            limit=limit,
            offset=offset
        )

        return results

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@router.get("/search/suggestions")
async def get_search_suggestions(
    workspace_id: str = Query(..., description="Workspace ID"),
    query: str = Query(..., description="Partial search query"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get search suggestions based on workspace content and query.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)

    # Validate workspace access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Initialize search service
    search_service = DocumentSearchService(supabase)

    try:
        suggestions = await search_service._generate_search_suggestions(query, workspace_id)
        return {"suggestions": suggestions}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get suggestions: {str(e)}")

@router.post("/search/save-query")
async def save_search_query(
    workspace_id: str = Query(..., description="Workspace ID"),
    query_data: Dict[str, Any] = Body(..., description="Search query data to save"),
    current_user: dict = Depends(get_current_user)
):
    """
    Save a search query for future use.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)

    # Validate workspace access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    try:
        # Prepare saved query data
        saved_query = {
            "id": str(uuid.uuid4()),
            "name": query_data.get("name", "Untitled Search"),
            "query": query_data.get("query", ""),
            "filters": query_data.get("filters", {}),
            "search_type": query_data.get("search_type", "hybrid"),
            "workspace_id": workspace_id,
            "created_by": current_user["id"],
            "created_at": datetime.utcnow().isoformat()
        }

        # Save to database (you'll need to create a saved_searches table)
        response = supabase.table("saved_searches").insert(saved_query).execute()

        if response.data:
            return {"message": "Search query saved successfully", "id": saved_query["id"]}
        else:
            raise HTTPException(status_code=500, detail="Failed to save search query")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save search query: {str(e)}")

@router.get("/search/saved")
async def get_saved_searches(
    workspace_id: str = Query(..., description="Workspace ID"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get saved search queries for the workspace.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)

    # Validate workspace access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    try:
        response = supabase.table("saved_searches").select("*").eq("workspace_id", workspace_id).order("created_at", desc=True).execute()

        return {"saved_searches": response.data or []}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get saved searches: {str(e)}")

# Document Analytics Endpoints

@router.get("/analytics/usage")
async def get_document_usage_analytics(
    workspace_id: str = Query(..., description="Workspace ID"),
    days: int = Query(30, description="Number of days to analyze"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get document usage analytics for the workspace.
    """
    # Use authenticated client with RLS
    supabase = get_authenticated_db_client(current_user)

    # Validate workspace access
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    try:
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # Get document counts by type
        response = supabase.table("documents").select("filename, created_at").eq("workspace_id", workspace_id).gte("created_at", start_date.isoformat()).execute()

        documents = response.data or []

        # Analyze file types
        file_types = {}
        for doc in documents:
            filename = doc.get("filename", "")
            ext = filename.split(".")[-1].lower() if "." in filename else "unknown"
            file_types[ext] = file_types.get(ext, 0) + 1

        # Analyze upload trends
        upload_trends = {}
        for doc in documents:
            date = doc.get("created_at", "").split("T")[0]  # Get date part
            upload_trends[date] = upload_trends.get(date, 0) + 1

        return {
            "total_documents": len(documents),
            "file_types": file_types,
            "upload_trends": upload_trends,
            "period_days": days
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")

@router.post("/documents/upload", response_model=Document)
async def upload_document_with_file(
    file: UploadFile = File(...),
    title: str = Form(...),
    workspace_id: str = Form(...),
    folder: Optional[str] = Form(""),
    content: Optional[str] = Form(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Upload a document file and create a document record.
    """
    supabase = get_supabase_client()

    try:
        # Upload file to storage
        file_result = await StorageService.upload_file(
            file=file,
            folder=folder,
            workspace_id=workspace_id,
            metadata={
                "uploaded_by": current_user["id"],
                "title": title
            }
        )

        # Create file info
        file_info = {
            "filename": file_result["filename"],
            "content_type": file_result["content_type"],
            "size": file_result["size"],
            "path": file_result["path"],
            "url": file_result["url"],
            "uploaded_at": file_result["uploaded_at"]
        }

        # Prepare document data
        document_data = {
            "id": f"doc-{str(uuid.uuid4())}",
            "title": title,
            "filename": file.filename,
            "workspace_id": workspace_id,
            "content": content,
            "file_url": file_result["url"],
            "file_path": file_result["path"],
            "file_info": file_info,
            "folder": folder,
            "created_at": datetime.utcnow().isoformat(),
            "created_by": current_user["id"],
            "status": "draft"
        }

        # Insert document
        response = supabase.table("documents").insert(document_data).execute()

        if response.error:
            # If document creation fails, try to delete the uploaded file
            StorageService.delete_file(file_result["path"])
            raise HTTPException(status_code=400, detail=response.error.message)

        return response.data[0]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading document: {str(e)}")

@router.get("/documents/{document_id}", response_model=Document)
async def get_document(
    document_id: str = Path(..., description="The ID of the document"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve a specific document.
    """
    supabase = get_supabase_client()

    # Get the document
    response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    if not response.data:
        raise HTTPException(status_code=404, detail="Document not found")

    # Get the signers for this document
    signers_response = supabase.table("document_signers").select("*").eq("document_id", document_id).order("order").execute()

    if signers_response.error:
        raise HTTPException(status_code=400, detail=signers_response.error.message)

    # Add signers to the document
    document = response.data[0]
    document["signers"] = signers_response.data

    return document

@router.put("/documents/{document_id}", response_model=Document)
async def update_document(
    document_update: DocumentUpdate,
    document_id: str = Path(..., description="The ID of the document"),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a specific document.
    """
    supabase = get_supabase_client()

    # Check if the document exists
    check_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if check_response.error:
        raise HTTPException(status_code=400, detail=check_response.error.message)

    if not check_response.data:
        raise HTTPException(status_code=404, detail="Document not found")

    # Prepare the update data
    update_data = document_update.model_dump(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow().isoformat()

    # Update the document
    response = supabase.table("documents").update(update_data).eq("id", document_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    return response.data[0]

@router.post("/documents/{document_id}/sign", response_model=Document)
async def sign_document(
    signature_data: SignatureData,
    document_id: str = Path(..., description="The ID of the document"),
    current_user: dict = Depends(get_current_user)
):
    """
    Sign a document.
    """
    supabase = get_supabase_client()

    # Check if the document exists
    document_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if document_response.error:
        raise HTTPException(status_code=400, detail=document_response.error.message)

    if not document_response.data:
        raise HTTPException(status_code=404, detail="Document not found")

    # Check if the signer exists
    signer_response = supabase.table("document_signers").select("*").eq("id", signature_data.signer_id).execute()

    if signer_response.error:
        raise HTTPException(status_code=400, detail=signer_response.error.message)

    if not signer_response.data:
        raise HTTPException(status_code=404, detail="Signer not found")

    # Update the signer status
    signer_update = {
        "status": "completed",
        "completed_at": datetime.utcnow().isoformat(),
        "signature_data": signature_data.signature.model_dump()
    }

    signer_update_response = supabase.table("document_signers").update(signer_update).eq("id", signature_data.signer_id).execute()

    if signer_update_response.error:
        raise HTTPException(status_code=400, detail=signer_update_response.error.message)

    # Check if all signers have completed
    all_signers_response = supabase.table("document_signers").select("*").eq("document_id", document_id).execute()

    if all_signers_response.error:
        raise HTTPException(status_code=400, detail=all_signers_response.error.message)

    all_completed = all(signer["status"] == "completed" for signer in all_signers_response.data)

    # Update document status if all signers have completed
    if all_completed:
        document_update_response = supabase.table("documents").update({"status": "completed"}).eq("id", document_id).execute()

        if document_update_response.error:
            raise HTTPException(status_code=400, detail=document_update_response.error.message)

    # Get the updated document with signers
    updated_document_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if updated_document_response.error:
        raise HTTPException(status_code=400, detail=updated_document_response.error.message)

    updated_signers_response = supabase.table("document_signers").select("*").eq("document_id", document_id).order("order").execute()

    if updated_signers_response.error:
        raise HTTPException(status_code=400, detail=updated_signers_response.error.message)

    # Add signers to the document
    updated_document = updated_document_response.data[0]
    updated_document["signers"] = updated_signers_response.data

    return updated_document

@router.post("/documents/{document_id}/decline", response_model=Document)
async def decline_document(
    document_id: str = Path(..., description="The ID of the document"),
    decline_data: dict = Body(..., description="Decline data"),
    current_user: dict = Depends(get_current_user)
):
    """
    Decline to sign a document.
    """
    supabase = get_supabase_client()

    # Check if the document exists
    document_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if document_response.error:
        raise HTTPException(status_code=400, detail=document_response.error.message)

    if not document_response.data:
        raise HTTPException(status_code=404, detail="Document not found")

    # Check if the signer exists
    signer_response = supabase.table("document_signers").select("*").eq("id", decline_data["signer_id"]).execute()

    if signer_response.error:
        raise HTTPException(status_code=400, detail=signer_response.error.message)

    if not signer_response.data:
        raise HTTPException(status_code=404, detail="Signer not found")

    # Update the signer status
    signer_update = {
        "status": "declined",
        "decline_reason": decline_data["reason"],
        "declined_at": datetime.utcnow().isoformat()
    }

    signer_update_response = supabase.table("document_signers").update(signer_update).eq("id", decline_data["signer_id"]).execute()

    if signer_update_response.error:
        raise HTTPException(status_code=400, detail=signer_update_response.error.message)

    # Update document status
    document_update_response = supabase.table("documents").update({"status": "declined"}).eq("id", document_id).execute()

    if document_update_response.error:
        raise HTTPException(status_code=400, detail=document_update_response.error.message)

    # Get the updated document with signers
    updated_document_response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if updated_document_response.error:
        raise HTTPException(status_code=400, detail=updated_document_response.error.message)

    updated_signers_response = supabase.table("document_signers").select("*").eq("document_id", document_id).order("order").execute()

    if updated_signers_response.error:
        raise HTTPException(status_code=400, detail=updated_signers_response.error.message)

    # Add signers to the document
    updated_document = updated_document_response.data[0]
    updated_document["signers"] = updated_signers_response.data

    return updated_document

@router.get("/documents/{document_id}/file")
async def get_document_file_url(
    document_id: str = Path(..., description="The ID of the document"),
    expires_in: Optional[int] = Query(3600, description="Number of seconds until the URL expires"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get a signed URL for a document file that expires after a certain time.
    """
    supabase = get_supabase_client()

    # Get the document
    response = supabase.table("documents").select("*").eq("id", document_id).execute()

    if response.error:
        raise HTTPException(status_code=400, detail=response.error.message)

    if not response.data:
        raise HTTPException(status_code=404, detail="Document not found")

    document = response.data[0]

    # Check if document has a file path
    if not document.get("file_path"):
        raise HTTPException(status_code=404, detail="Document has no associated file")

    try:
        # Get signed URL
        signed_url = StorageService.get_file_url(
            file_path=document["file_path"],
            expires_in=expires_in
        )

        return {
            "url": signed_url,
            "expires_in": expires_in,
            "filename": document["filename"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting file URL: {str(e)}")
