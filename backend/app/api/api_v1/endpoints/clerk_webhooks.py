"""
Clerk webhook handlers for user synchronization
Handles user creation, updates, and deletion from <PERSON>
"""

from fastapi import APIRouter, Request, HTTPException, Header
from typing import Optional
import json
import hmac
import hashlib
from app.db.database import get_supabase_client
from app.core.config import settings

router = APIRouter()

def handle_supabase_response(response, error_message: str = "Database operation failed"):
    """Helper function to handle Supabase response objects consistently."""
    has_error = hasattr(response, 'error') and response.error
    has_data = hasattr(response, 'data') and response.data is not None

    if has_error:
        raise HTTPException(status_code=400, detail=f"{error_message}: {response.error}")

    return response.data if has_data else []

def verify_webhook_signature(payload: bytes, signature: str, secret: str) -> bool:
    """Verify the webhook signature from <PERSON>."""
    if not signature or not secret:
        return False
    
    # Clerk sends the signature in the format "v1,<signature>"
    if not signature.startswith("v1,"):
        return False
    
    signature = signature[3:]  # Remove "v1," prefix
    
    # Create the expected signature
    expected_signature = hmac.new(
        secret.encode(),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    # Compare signatures
    return hmac.compare_digest(signature, expected_signature)

@router.post("/user-created")
async def handle_user_created(
    request: Request,
    svix_id: Optional[str] = Header(None),
    svix_timestamp: Optional[str] = Header(None),
    svix_signature: Optional[str] = Header(None)
):
    """Handle user creation webhook from Clerk."""
    
    # Get the raw payload
    payload = await request.body()
    
    # Verify the webhook signature (optional but recommended for production)
    webhook_secret = getattr(settings, 'CLERK_WEBHOOK_SECRET', None)
    if webhook_secret and svix_signature:
        if not verify_webhook_signature(payload, svix_signature, webhook_secret):
            raise HTTPException(status_code=401, detail="Invalid webhook signature")
    
    try:
        # Parse the webhook payload
        data = json.loads(payload)
        
        # Extract user data from Clerk webhook
        user_data = data.get('data', {})
        event_type = data.get('type')
        
        if event_type != 'user.created':
            return {"message": "Event type not handled"}
        
        # Extract user information
        clerk_user_id = user_data.get('id')
        email_addresses = user_data.get('email_addresses', [])
        first_name = user_data.get('first_name', '')
        last_name = user_data.get('last_name', '')
        
        # Get primary email
        primary_email = None
        for email in email_addresses:
            if email.get('id') == user_data.get('primary_email_address_id'):
                primary_email = email.get('email_address')
                break
        
        if not primary_email and email_addresses:
            primary_email = email_addresses[0].get('email_address')
        
        if not primary_email:
            raise HTTPException(status_code=400, detail="No email address found for user")
        
        # Create user record in Supabase
        supabase = get_supabase_client()
        
        # Prepare user data for Supabase
        user_record = {
            "id": clerk_user_id,
            "email": primary_email,
            "first_name": first_name or "",
            "last_name": last_name or "",
            "title": "",  # User can update this later
            "company": "",  # User can update this later
            "timezone": "America/New_York",  # Default timezone
            "bio": "",
            "initials": f"{first_name[:1] if first_name else ''}{last_name[:1] if last_name else ''}".upper(),
            "notification_preferences": {
                "email_approvals": True,
                "email_updates": True,
                "email_reminders": True,
                "email_comments": True,
                "system_approvals": True,
                "browser_notifications": True,
                "email_digest_frequency": "daily"
            },
            "workspaces": [],
            "workspace_roles": {}
        }
        
        # Insert user into Supabase
        response = supabase.table("users").insert(user_record).execute()
        created_user = handle_supabase_response(response, "Failed to create user in database")
        
        # Add user to all demo workspaces so they can access demo data
        demo_workspaces = [
            "demo-workspace-tech",
            "demo-workspace-legal",
            "demo-workspace-hr",
            "demo-workspace-consulting"
        ]

        for workspace_id in demo_workspaces:
            try:
                # Check if the workspace exists
                workspace_response = supabase.table("workspaces").select("id").eq("id", workspace_id).execute()
                workspace_data = handle_supabase_response(workspace_response, "Failed to check workspace")

                if workspace_data:
                    # Add user to workspace
                    membership_data = {
                        "workspace_id": workspace_id,
                        "user_id": clerk_user_id,
                        "role_id": "role-viewer"  # Default role for demo access
                    }

                    membership_response = supabase.table("workspace_members").insert(membership_data).execute()
                    handle_supabase_response(membership_response, "Failed to add user to workspace")

                    print(f"Added user {primary_email} to demo workspace {workspace_id}")
            except Exception as workspace_error:
                print(f"Warning: Could not add user to workspace {workspace_id}: {workspace_error}")
                # Don't fail the user creation if workspace assignment fails
        
        print(f"Successfully created user: {primary_email} (ID: {clerk_user_id})")
        
        return {
            "message": "User created successfully",
            "user_id": clerk_user_id,
            "email": primary_email
        }
        
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON payload")
    except Exception as e:
        print(f"Error handling user creation webhook: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process webhook: {str(e)}")

@router.post("/user-updated")
async def handle_user_updated(
    request: Request,
    svix_id: Optional[str] = Header(None),
    svix_timestamp: Optional[str] = Header(None),
    svix_signature: Optional[str] = Header(None)
):
    """Handle user update webhook from Clerk."""
    
    payload = await request.body()
    
    # Verify webhook signature if secret is configured
    webhook_secret = getattr(settings, 'CLERK_WEBHOOK_SECRET', None)
    if webhook_secret and svix_signature:
        if not verify_webhook_signature(payload, svix_signature, webhook_secret):
            raise HTTPException(status_code=401, detail="Invalid webhook signature")
    
    try:
        data = json.loads(payload)
        user_data = data.get('data', {})
        event_type = data.get('type')
        
        if event_type != 'user.updated':
            return {"message": "Event type not handled"}
        
        clerk_user_id = user_data.get('id')
        email_addresses = user_data.get('email_addresses', [])
        first_name = user_data.get('first_name', '')
        last_name = user_data.get('last_name', '')
        
        # Get primary email
        primary_email = None
        for email in email_addresses:
            if email.get('id') == user_data.get('primary_email_address_id'):
                primary_email = email.get('email_address')
                break
        
        if not primary_email and email_addresses:
            primary_email = email_addresses[0].get('email_address')
        
        # Update user record in Supabase
        supabase = get_supabase_client()
        
        update_data = {
            "email": primary_email,
            "first_name": first_name or "",
            "last_name": last_name or "",
            "initials": f"{first_name[:1] if first_name else ''}{last_name[:1] if last_name else ''}".upper(),
        }
        
        response = supabase.table("users").update(update_data).eq("id", clerk_user_id).execute()
        handle_supabase_response(response, "Failed to update user in database")
        
        print(f"Successfully updated user: {primary_email} (ID: {clerk_user_id})")
        
        return {
            "message": "User updated successfully",
            "user_id": clerk_user_id,
            "email": primary_email
        }
        
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON payload")
    except Exception as e:
        print(f"Error handling user update webhook: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process webhook: {str(e)}")

@router.post("/user-deleted")
async def handle_user_deleted(
    request: Request,
    svix_id: Optional[str] = Header(None),
    svix_timestamp: Optional[str] = Header(None),
    svix_signature: Optional[str] = Header(None)
):
    """Handle user deletion webhook from Clerk."""
    
    payload = await request.body()
    
    # Verify webhook signature if secret is configured
    webhook_secret = getattr(settings, 'CLERK_WEBHOOK_SECRET', None)
    if webhook_secret and svix_signature:
        if not verify_webhook_signature(payload, svix_signature, webhook_secret):
            raise HTTPException(status_code=401, detail="Invalid webhook signature")
    
    try:
        data = json.loads(payload)
        user_data = data.get('data', {})
        event_type = data.get('type')
        
        if event_type != 'user.deleted':
            return {"message": "Event type not handled"}
        
        clerk_user_id = user_data.get('id')
        
        # Delete user record from Supabase
        supabase = get_supabase_client()
        
        # Note: This will cascade delete related records due to foreign key constraints
        response = supabase.table("users").delete().eq("id", clerk_user_id).execute()
        handle_supabase_response(response, "Failed to delete user from database")
        
        print(f"Successfully deleted user: {clerk_user_id}")
        
        return {
            "message": "User deleted successfully",
            "user_id": clerk_user_id
        }
        
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON payload")
    except Exception as e:
        print(f"Error handling user deletion webhook: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process webhook: {str(e)}")

@router.get("/test")
async def test_webhook_endpoint():
    """Test endpoint to verify webhook setup."""
    return {
        "message": "Clerk webhook endpoint is working",
        "webhook_secret_configured": bool(getattr(settings, 'CLERK_WEBHOOK_SECRET', None))
    }
