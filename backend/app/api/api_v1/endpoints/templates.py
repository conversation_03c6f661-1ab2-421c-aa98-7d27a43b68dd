from fastapi import APIRouter, Depends, HTTPException, Path, Query
from typing import List, Optional
from app.schemas.template import Template, TemplateCreate, TemplateUpdate
from app.core.auth import get_current_user, validate_workspace_access
from app.db.database import get_supabase_client
from datetime import datetime
import uuid

def handle_supabase_response(response, error_message: str = "Database operation failed"):
    """
    Helper function to handle Supabase response objects consistently.
    """
    has_error = hasattr(response, 'error') and response.error
    has_data = hasattr(response, 'data') and response.data is not None

    if has_error:
        raise HTTPException(status_code=400, detail=f"{error_message}: {response.error}")

    return response.data if has_data else []

router = APIRouter()

@router.get("/", response_model=List[Template])
async def get_templates(
    workspace_id: str = Query(..., description="Workspace ID (required for security)"),
    type: Optional[str] = Query(None, description="Filter by template type"),
    industry: Optional[str] = Query(None, description="Filter by industry"),
    complexity: Optional[str] = Query(None, description="Filter by complexity level"),
    search: Optional[str] = Query(None, description="Search in title and description"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve templates for a specific workspace that the user has access to.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Start building the query - always filter by workspace_id for security
    query = supabase.table("templates").select("*").eq("workspace_id", workspace_id)

    # Apply additional filters
    if type:
        query = query.eq("type", type)
    if industry:
        query = query.eq("industry", industry)
    if complexity:
        query = query.eq("complexity", complexity)
    if search:
        query = query.or_(f"title.ilike.%{search}%,description.ilike.%{search}%")

    # Apply pagination
    query = query.range(skip, skip + limit - 1)

    # Execute the query
    response = query.execute()

    # Handle response using helper function
    data = handle_supabase_response(response, "Failed to retrieve templates")

    return data

@router.post("/", response_model=Template)
async def create_template(
    template: TemplateCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new template in a workspace the user has access to.
    """
    supabase = get_supabase_client()

    # Validate that the user has access to the specified workspace
    workspace_id = template.workspace_id
    if workspace_id:
        await validate_workspace_access(current_user["id"], workspace_id, supabase)

    # Prepare the template data
    template_data = template.model_dump()
    template_data["id"] = str(uuid.uuid4())
    template_data["created_by"] = {
        "id": current_user["id"],
        "name": f"{current_user.get('first_name', '')} {current_user.get('last_name', '')}".strip() or "User"
    }
    template_data["created_at"] = datetime.utcnow().isoformat()
    template_data["usage_count"] = 0
    template_data["is_user_created"] = True

    # Insert the template
    response = supabase.table("templates").insert(template_data).execute()

    # Handle response using helper function
    data = handle_supabase_response(response, "Failed to create template")

    if not data:
        raise HTTPException(status_code=500, detail="Template creation failed - no data returned")

    return data[0]

@router.get("/{template_id}", response_model=Template)
async def get_template(
    template_id: str = Path(..., description="The ID of the template to retrieve"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve a specific template by ID with workspace access validation.
    """
    supabase = get_supabase_client()

    # First get the template to check its workspace
    response = supabase.table("templates").select("*").eq("id", template_id).execute()

    # Handle response using helper function
    data = handle_supabase_response(response, "Failed to retrieve template")

    if not data:
        raise HTTPException(status_code=404, detail="Template not found")

    template = data[0]
    workspace_id = template.get("workspace_id")

    if workspace_id:
        # Validate that the user has access to this template's workspace
        await validate_workspace_access(current_user["id"], workspace_id, supabase)

    return template

@router.put("/{template_id}", response_model=Template)
async def update_template(
    template: TemplateUpdate,
    template_id: str = Path(..., description="The ID of the template to update"),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a specific template.
    """
    supabase = get_supabase_client()

    # Check if template exists and user has permission
    check_response = supabase.table("templates").select("created_by, workspace_id").eq("id", template_id).execute()

    # Handle check response
    check_data = handle_supabase_response(check_response, "Failed to check template permissions")

    if not check_data:
        raise HTTPException(status_code=404, detail="Template not found")

    template_data = check_data[0]

    # Check if user is the creator or has admin rights in the workspace
    if template_data.get("created_by", {}).get("id") != current_user["id"]:
        # Check if user is admin in this workspace
        workspace_id = template_data.get("workspace_id")
        if workspace_id:
            member_check = supabase.table("workspace_members").select("role_id").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()
            member_data = handle_supabase_response(member_check, "Failed to check workspace permissions")

            if not member_data or member_data[0].get("role_id") != "role-admin":
                raise HTTPException(status_code=403, detail="You don't have permission to update this template")

    # Prepare update data
    update_data = template.model_dump(exclude_unset=True)
    update_data["updated_at"] = datetime.utcnow().isoformat()

    # Update the template
    response = supabase.table("templates").update(update_data).eq("id", template_id).execute()

    # Handle update response
    data = handle_supabase_response(response, "Failed to update template")

    if not data:
        raise HTTPException(status_code=500, detail="Template update failed - no data returned")

    return data[0]

@router.delete("/{template_id}")
async def delete_template(
    template_id: str = Path(..., description="The ID of the template to delete"),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a specific template.
    """
    supabase = get_supabase_client()

    # Check if template exists and user has permission
    check_response = supabase.table("templates").select("created_by, workspace_id").eq("id", template_id).execute()

    # Handle check response
    check_data = handle_supabase_response(check_response, "Failed to check template permissions")

    if not check_data:
        raise HTTPException(status_code=404, detail="Template not found")

    template_data = check_data[0]

    # Check if user is the creator or has admin rights in the workspace
    if template_data.get("created_by", {}).get("id") != current_user["id"]:
        # Check if user is admin in this workspace
        workspace_id = template_data.get("workspace_id")
        if workspace_id:
            member_check = supabase.table("workspace_members").select("role_id").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()
            member_data = handle_supabase_response(member_check, "Failed to check workspace permissions")

            if not member_data or member_data[0].get("role_id") != "role-admin":
                raise HTTPException(status_code=403, detail="You don't have permission to delete this template")

    # Delete the template
    response = supabase.table("templates").delete().eq("id", template_id).execute()

    # Handle delete response
    handle_supabase_response(response, "Failed to delete template")

    return {"message": "Template deleted successfully"}
