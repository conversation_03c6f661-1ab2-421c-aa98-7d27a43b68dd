"""
Monitoring and health check endpoints for LegalAI API.
"""

from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException
from typing import Dict, Any
from datetime import datetime
import psutil
import os
from app.middleware.monitoring import performance_monitor
from app.db.database import get_supabase_client
from app.core.auth import get_current_user

router = APIRouter()

@router.get("/health")
async def health_check():
    """
    Comprehensive health check endpoint.
    Returns the overall health status of the application.
    """
    try:
        # Get basic health status
        health_status = performance_monitor.get_health_status()
        
        # Check database connectivity
        try:
            supabase = get_supabase_client()
            # Simple query to test database connection
            response = supabase.table("users").select("id").limit(1).execute()
            database_status = "healthy" if not (hasattr(response, 'error') and response.error) else "unhealthy"
        except Exception as e:
            database_status = "unhealthy"
            health_status["database_error"] = str(e)
        
        # Get system metrics
        try:
            system_metrics = {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent,
                "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
            }
        except Exception:
            system_metrics = {"error": "Unable to get system metrics"}
        
        # Determine overall status
        overall_status = "healthy"
        if health_status["status"] == "unhealthy" or database_status == "unhealthy":
            overall_status = "unhealthy"
        elif health_status["status"] == "degraded":
            overall_status = "degraded"
        
        return {
            "status": overall_status,
            "timestamp": datetime.utcnow().isoformat(),
            "application": health_status,
            "database": {"status": database_status},
            "system": system_metrics,
            "version": "1.0.0",
            "environment": os.getenv("ENVIRONMENT", "development")
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }

@router.get("/health/ready")
async def readiness_check():
    """
    Readiness check for Kubernetes/container orchestration.
    Returns 200 if the application is ready to serve traffic.
    """
    try:
        # Check if database is accessible
        supabase = get_supabase_client()
        response = supabase.table("users").select("id").limit(1).execute()
        
        if hasattr(response, 'error') and response.error:
            raise HTTPException(status_code=503, detail="Database not ready")
        
        return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
        
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Application not ready: {str(e)}")

@router.get("/health/live")
async def liveness_check():
    """
    Liveness check for Kubernetes/container orchestration.
    Returns 200 if the application is alive (basic functionality working).
    """
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime_seconds": performance_monitor.get_health_status()["uptime_seconds"]
    }

@router.get("/metrics")
async def get_metrics(current_user: dict = Depends(get_current_user)):
    """
    Get application performance metrics.
    Requires authentication.
    """
    try:
        metrics_summary = performance_monitor.get_metrics_summary()
        
        # Add additional metrics
        metrics_summary.update({
            "timestamp": datetime.utcnow().isoformat(),
            "system": {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent
            }
        })
        
        return metrics_summary
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")

@router.get("/metrics/detailed")
async def get_detailed_metrics(current_user: dict = Depends(get_current_user)):
    """
    Get detailed performance metrics including recent request history.
    Requires authentication.
    """
    try:
        # Get basic metrics
        metrics = performance_monitor.get_metrics_summary()
        
        # Add detailed information
        with performance_monitor.lock:
            recent_requests = list(performance_monitor.metrics_history)[-100:]  # Last 100 requests
            
            detailed_metrics = {
                "summary": metrics,
                "recent_requests": [
                    {
                        "request_id": req.request_id,
                        "method": req.method,
                        "path": req.path,
                        "duration_ms": req.duration_ms,
                        "status_code": req.status_code,
                        "timestamp": datetime.fromtimestamp(req.start_time).isoformat(),
                        "user_id": req.user_id,
                        "workspace_id": req.workspace_id,
                        "error": req.error
                    }
                    for req in recent_requests
                ],
                "endpoint_stats": dict(performance_monitor.endpoint_stats),
                "active_requests_count": len(performance_monitor.active_requests)
            }
        
        return detailed_metrics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get detailed metrics: {str(e)}")

@router.post("/metrics/reset")
async def reset_metrics(current_user: dict = Depends(get_current_user)):
    """
    Reset performance metrics.
    Requires authentication. Use with caution.
    """
    try:
        with performance_monitor.lock:
            performance_monitor.metrics_history.clear()
            performance_monitor.error_counts.clear()
            performance_monitor.endpoint_stats.clear()
        
        return {
            "status": "success",
            "message": "Metrics reset successfully",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reset metrics: {str(e)}")

@router.get("/status")
async def get_application_status():
    """
    Get basic application status (public endpoint).
    """
    return {
        "status": "running",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "development"),
        "api_prefix": "/api"
    }
