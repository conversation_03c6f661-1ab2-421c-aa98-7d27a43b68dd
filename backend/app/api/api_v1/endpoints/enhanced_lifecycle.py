"""
Enhanced Contract Lifecycle Management API endpoints for Averum Contracts
Provides advanced version control with branching, merging, and comprehensive diff tracking
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.enhanced_lifecycle_service import enhanced_lifecycle_service, BranchType, MergeStrategy

router = APIRouter()


class CreateBranchRequest(BaseModel):
    """Request model for creating a branch."""
    branch_name: str
    branch_type: BranchType
    parent_branch_id: Optional[str] = None
    description: Optional[str] = None


class MergeBranchesRequest(BaseModel):
    """Request model for merging branches."""
    source_branch_id: str
    target_branch_id: str
    merge_strategy: MergeStrategy
    commit_message: Optional[str] = None


class CreateMergeRequestRequest(BaseModel):
    """Request model for creating a merge request."""
    source_branch_id: str
    target_branch_id: str
    title: str
    description: Optional[str] = None


@router.post("/contracts/{contract_id}/branches")
async def create_branch(
    contract_id: str,
    request_data: CreateBranchRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new branch for a contract.
    
    Args:
        contract_id: Contract ID
        request_data: Branch creation request
        current_user: Current authenticated user
        
    Returns:
        Created branch information
    """
    try:
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id or not workspace_id:
            raise HTTPException(status_code=401, detail="User ID or workspace ID not found in token")
        
        branch = await enhanced_lifecycle_service.create_branch(
            contract_id=contract_id,
            branch_name=request_data.branch_name,
            branch_type=request_data.branch_type,
            parent_branch_id=request_data.parent_branch_id,
            user_id=user_id,
            workspace_id=workspace_id,
            description=request_data.description
        )
        
        return {
            "success": True,
            "branch": branch.dict(),
            "message": f"Branch '{request_data.branch_name}' created successfully"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create branch: {str(e)}")


@router.get("/contracts/{contract_id}/branches")
async def list_contract_branches(
    contract_id: str,
    include_inactive: bool = False,
    current_user: dict = Depends(get_current_user)
):
    """
    List all branches for a contract.
    
    Args:
        contract_id: Contract ID
        include_inactive: Whether to include inactive branches
        current_user: Current authenticated user
        
    Returns:
        List of contract branches
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        # Get branches from database
        query = enhanced_lifecycle_service.supabase.table("contract_branches").select("*").eq("contract_id", contract_id).eq("workspace_id", workspace_id)
        
        if not include_inactive:
            query = query.eq("is_active", True)
        
        result = query.order("created_at", desc=True).execute()
        
        return {
            "success": True,
            "branches": result.data or [],
            "total_branches": len(result.data or [])
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list branches: {str(e)}")


@router.post("/merge-branches")
async def merge_branches(
    request_data: MergeBranchesRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Merge one branch into another.
    
    Args:
        request_data: Merge request data
        current_user: Current authenticated user
        
    Returns:
        Merge result
    """
    try:
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        result = await enhanced_lifecycle_service.merge_branches(
            source_branch_id=request_data.source_branch_id,
            target_branch_id=request_data.target_branch_id,
            merge_strategy=request_data.merge_strategy,
            user_id=user_id,
            commit_message=request_data.commit_message
        )
        
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to merge branches: {str(e)}")


@router.get("/versions/{version1_id}/diff/{version2_id}")
async def get_advanced_diff(
    version1_id: str,
    version2_id: str,
    include_metadata: bool = True,
    current_user: dict = Depends(get_current_user)
):
    """
    Get advanced diff between two versions.
    
    Args:
        version1_id: First version ID
        version2_id: Second version ID
        include_metadata: Whether to include metadata
        current_user: Current authenticated user
        
    Returns:
        Advanced diff information
    """
    try:
        diff_result = await enhanced_lifecycle_service.generate_advanced_diff(
            version1_id=version1_id,
            version2_id=version2_id,
            include_metadata=include_metadata
        )
        
        return {
            "success": True,
            "diff": diff_result
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate diff: {str(e)}")


@router.post("/merge-requests")
async def create_merge_request(
    request_data: CreateMergeRequestRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a merge request.
    
    Args:
        request_data: Merge request creation data
        current_user: Current authenticated user
        
    Returns:
        Created merge request
    """
    try:
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id or not workspace_id:
            raise HTTPException(status_code=401, detail="User ID or workspace ID not found in token")
        
        # Create merge request in database
        merge_request_data = {
            "id": str(__import__("uuid").uuid4()),
            "source_branch_id": request_data.source_branch_id,
            "target_branch_id": request_data.target_branch_id,
            "title": request_data.title,
            "description": request_data.description,
            "status": "open",
            "created_by": user_id,
            "created_at": __import__("datetime").datetime.utcnow().isoformat(),
            "workspace_id": workspace_id,
            "conflicts": []
        }
        
        result = enhanced_lifecycle_service.supabase.table("merge_requests").insert(merge_request_data).execute()
        
        if result.data:
            return {
                "success": True,
                "merge_request": result.data[0],
                "message": "Merge request created successfully"
            }
        
        raise Exception("Failed to create merge request")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create merge request: {str(e)}")


@router.get("/merge-requests")
async def list_merge_requests(
    status: Optional[str] = None,
    limit: int = 50,
    current_user: dict = Depends(get_current_user)
):
    """
    List merge requests for the workspace.
    
    Args:
        status: Optional status filter
        limit: Maximum number of results
        current_user: Current authenticated user
        
    Returns:
        List of merge requests
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        query = enhanced_lifecycle_service.supabase.table("merge_requests").select("*").eq("workspace_id", workspace_id)
        
        if status:
            query = query.eq("status", status)
        
        result = query.order("created_at", desc=True).limit(limit).execute()
        
        return {
            "success": True,
            "merge_requests": result.data or [],
            "total_requests": len(result.data or [])
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list merge requests: {str(e)}")


@router.get("/branches/{branch_id}")
async def get_branch_details(
    branch_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Get detailed information about a branch.
    
    Args:
        branch_id: Branch ID
        current_user: Current authenticated user
        
    Returns:
        Branch details
    """
    try:
        workspace_id = current_user.get("workspace_id")
        
        if not workspace_id:
            raise HTTPException(status_code=401, detail="Workspace ID not found in token")
        
        # Get branch details
        result = enhanced_lifecycle_service.supabase.table("contract_branches").select("*").eq("id", branch_id).eq("workspace_id", workspace_id).single().execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Branch not found")
        
        branch = result.data
        
        # Get head version details
        version_result = enhanced_lifecycle_service.supabase.table("contract_versions").select("*").eq("id", branch["head_version_id"]).single().execute()
        
        branch["head_version"] = version_result.data if version_result.data else None
        
        return {
            "success": True,
            "branch": branch
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get branch details: {str(e)}")


@router.delete("/branches/{branch_id}")
async def delete_branch(
    branch_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a branch (mark as inactive).
    
    Args:
        branch_id: Branch ID
        current_user: Current authenticated user
        
    Returns:
        Deletion status
    """
    try:
        workspace_id = current_user.get("workspace_id")
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        if not workspace_id or not user_id:
            raise HTTPException(status_code=401, detail="User ID or workspace ID not found in token")
        
        # Check if branch exists and user has permission
        branch_result = enhanced_lifecycle_service.supabase.table("contract_branches").select("*").eq("id", branch_id).eq("workspace_id", workspace_id).single().execute()
        
        if not branch_result.data:
            raise HTTPException(status_code=404, detail="Branch not found")
        
        branch = branch_result.data
        
        # Check if user is branch creator or admin
        if branch["created_by"] != user_id:
            # Check if user is workspace admin (simplified check)
            user_role = current_user.get("role", "user")
            if user_role not in ["admin", "workspace_admin"]:
                raise HTTPException(status_code=403, detail="Permission denied")
        
        # Don't allow deletion of main branch
        if branch["branch_name"] == "main":
            raise HTTPException(status_code=400, detail="Cannot delete main branch")
        
        # Mark branch as inactive
        result = enhanced_lifecycle_service.supabase.table("contract_branches").update({
            "is_active": False
        }).eq("id", branch_id).execute()
        
        if result.data:
            return {
                "success": True,
                "message": f"Branch '{branch['branch_name']}' deleted successfully"
            }
        
        raise Exception("Failed to delete branch")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete branch: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for enhanced lifecycle service.
    
    Returns:
        Service health status
    """
    try:
        return {
            "status": "healthy",
            "service": "enhanced_lifecycle",
            "features": [
                "branching",
                "merging",
                "advanced_diff",
                "merge_requests",
                "version_control"
            ]
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "enhanced_lifecycle",
            "error": str(e)
        }
