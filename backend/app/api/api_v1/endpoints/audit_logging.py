"""
Audit Logging API endpoints for Averum Contracts
Provides audit trail access, compliance reporting, and security monitoring
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.audit_logging_service import audit_logging_service, AuditEventType, AuditSeverity, AuditOutcome

router = APIRouter()


class AuditSearchRequest(BaseModel):
    """Request model for audit log searches."""
    user_id: Optional[str] = None
    workspace_id: Optional[str] = None
    event_type: Optional[AuditEventType] = None
    action: Optional[str] = None
    resource_type: Optional[str] = None
    severity: Optional[AuditSeverity] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: int = 100
    offset: int = 0


class ComplianceReportRequest(BaseModel):
    """Request model for generating compliance reports."""
    report_type: str
    period_start: datetime
    period_end: datetime
    include_details: bool = True


@router.get("/search")
async def search_audit_logs(
    user_id: Optional[str] = Query(None),
    workspace_id: Optional[str] = Query(None),
    event_type: Optional[AuditEventType] = Query(None),
    action: Optional[str] = Query(None),
    resource_type: Optional[str] = Query(None),
    severity: Optional[AuditSeverity] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    current_user: dict = Depends(get_current_user)
):
    """
    Search audit logs with comprehensive filtering.
    
    Args:
        user_id: Filter by user ID
        workspace_id: Filter by workspace ID
        event_type: Filter by event type
        action: Filter by action (partial match)
        resource_type: Filter by resource type
        severity: Filter by severity level
        start_date: Filter by start date
        end_date: Filter by end date
        limit: Maximum results to return
        offset: Pagination offset
        current_user: Current authenticated user
        
    Returns:
        Filtered audit log results
    """
    try:
        # Check permissions
        user_role = current_user.get("role", "user")
        current_user_id = current_user.get("sub") or current_user.get("user_id")
        current_workspace_id = current_user.get("workspace_id")
        
        # Restrict access based on user role
        if user_role == "admin":
            # System admins can search all logs
            pass
        elif user_role in ["workspace_admin"]:
            # Workspace admins can only search their workspace
            if workspace_id and workspace_id != current_workspace_id:
                raise HTTPException(status_code=403, detail="Access denied to other workspace audit logs")
            workspace_id = current_workspace_id
        else:
            # Regular users can only search their own logs
            if user_id and user_id != current_user_id:
                raise HTTPException(status_code=403, detail="Access denied to other user audit logs")
            user_id = current_user_id
            workspace_id = current_workspace_id
        
        # Search audit logs
        search_results = await audit_logging_service.search_audit_logs(
            user_id=user_id,
            workspace_id=workspace_id,
            event_type=event_type,
            action=action,
            resource_type=resource_type,
            start_date=start_date,
            end_date=end_date,
            severity=severity,
            limit=limit,
            offset=offset
        )
        
        return {
            "success": True,
            "search_results": search_results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to search audit logs: {str(e)}")


@router.get("/user/{user_id}")
async def get_user_audit_trail(
    user_id: str,
    days: int = Query(30, le=365),
    event_type: Optional[AuditEventType] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get audit trail for a specific user.
    
    Args:
        user_id: User ID to get audit trail for
        days: Number of days to look back
        event_type: Optional event type filter
        current_user: Current authenticated user
        
    Returns:
        User's audit trail
    """
    try:
        # Check permissions
        user_role = current_user.get("role", "user")
        current_user_id = current_user.get("sub") or current_user.get("user_id")
        
        if user_role not in ["admin", "workspace_admin"] and user_id != current_user_id:
            raise HTTPException(status_code=403, detail="Access denied to other user audit trails")
        
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get user audit trail
        search_results = await audit_logging_service.search_audit_logs(
            user_id=user_id,
            event_type=event_type,
            start_date=start_date,
            end_date=end_date,
            limit=1000
        )
        
        # Generate summary statistics
        logs = search_results["audit_logs"]
        summary = {
            "total_events": len(logs),
            "event_types": {},
            "severity_breakdown": {},
            "outcome_breakdown": {},
            "most_common_actions": {}
        }
        
        for log in logs:
            # Count event types
            event_type = log.get("event_type", "unknown")
            summary["event_types"][event_type] = summary["event_types"].get(event_type, 0) + 1
            
            # Count severities
            severity = log.get("severity", "unknown")
            summary["severity_breakdown"][severity] = summary["severity_breakdown"].get(severity, 0) + 1
            
            # Count outcomes
            outcome = log.get("outcome", "unknown")
            summary["outcome_breakdown"][outcome] = summary["outcome_breakdown"].get(outcome, 0) + 1
            
            # Count actions
            action = log.get("action", "unknown")
            summary["most_common_actions"][action] = summary["most_common_actions"].get(action, 0) + 1
        
        # Get top 10 most common actions
        summary["most_common_actions"] = dict(
            sorted(summary["most_common_actions"].items(), key=lambda x: x[1], reverse=True)[:10]
        )
        
        return {
            "success": True,
            "user_id": user_id,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days
            },
            "summary": summary,
            "audit_trail": search_results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get user audit trail: {str(e)}")


@router.get("/security-alerts")
async def get_security_alerts(
    acknowledged: Optional[bool] = Query(None),
    severity: Optional[str] = Query(None),
    limit: int = Query(50, le=200),
    current_user: dict = Depends(get_current_user)
):
    """
    Get security alerts from audit logs.
    
    Args:
        acknowledged: Filter by acknowledgment status
        severity: Filter by severity level
        limit: Maximum alerts to return
        current_user: Current authenticated user
        
    Returns:
        Security alerts
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for security alerts")
        
        # Build query
        query = audit_logging_service.supabase.table("security_alerts").select("*")
        
        if acknowledged is not None:
            query = query.eq("acknowledged", acknowledged)
        
        if severity:
            query = query.eq("severity", severity)
        
        result = query.order("created_at", desc=True).limit(limit).execute()
        
        return {
            "success": True,
            "security_alerts": result.data or [],
            "total_alerts": len(result.data or []),
            "filters": {
                "acknowledged": acknowledged,
                "severity": severity
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get security alerts: {str(e)}")


@router.post("/security-alerts/{alert_id}/acknowledge")
async def acknowledge_security_alert(
    alert_id: str,
    current_user: dict = Depends(get_current_user)
):
    """
    Acknowledge a security alert.
    
    Args:
        alert_id: Alert ID to acknowledge
        current_user: Current authenticated user
        
    Returns:
        Acknowledgment status
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required")
        
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        # Update alert
        result = audit_logging_service.supabase.table("security_alerts").update({
            "acknowledged": True,
            "acknowledged_by": user_id,
            "acknowledged_at": datetime.utcnow().isoformat()
        }).eq("alert_id", alert_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Security alert not found")
        
        # Log the acknowledgment
        await audit_logging_service.log_event(
            event_type=AuditEventType.SECURITY_EVENT,
            action="security_alert_acknowledged",
            user_id=user_id,
            resource_type="security_alert",
            resource_id=alert_id,
            outcome=AuditOutcome.SUCCESS,
            severity=AuditSeverity.MEDIUM,
            details={"alert_id": alert_id}
        )
        
        return {
            "success": True,
            "message": f"Security alert {alert_id} acknowledged successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to acknowledge security alert: {str(e)}")


@router.post("/compliance-report")
async def generate_compliance_report(
    request_data: ComplianceReportRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate compliance report for audit logs.
    
    Args:
        request_data: Compliance report request
        current_user: Current authenticated user
        
    Returns:
        Generated compliance report
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for compliance reports")
        
        user_id = current_user.get("sub") or current_user.get("user_id")
        
        # Generate compliance report
        search_results = await audit_logging_service.search_audit_logs(
            start_date=request_data.period_start,
            end_date=request_data.period_end,
            limit=10000  # Large limit for comprehensive report
        )
        
        logs = search_results["audit_logs"]
        
        # Calculate compliance metrics
        compliance_metrics = {
            "total_events": len(logs),
            "period": {
                "start": request_data.period_start.isoformat(),
                "end": request_data.period_end.isoformat()
            },
            "event_type_breakdown": {},
            "severity_distribution": {},
            "outcome_analysis": {},
            "security_events": 0,
            "failed_events": 0,
            "compliance_violations": 0
        }
        
        for log in logs:
            event_type = log.get("event_type", "unknown")
            compliance_metrics["event_type_breakdown"][event_type] = compliance_metrics["event_type_breakdown"].get(event_type, 0) + 1
            
            severity = log.get("severity", "unknown")
            compliance_metrics["severity_distribution"][severity] = compliance_metrics["severity_distribution"].get(severity, 0) + 1
            
            outcome = log.get("outcome", "unknown")
            compliance_metrics["outcome_analysis"][outcome] = compliance_metrics["outcome_analysis"].get(outcome, 0) + 1
            
            if event_type == "security_event":
                compliance_metrics["security_events"] += 1
            
            if outcome == "failure":
                compliance_metrics["failed_events"] += 1
            
            if event_type == "compliance_event":
                compliance_metrics["compliance_violations"] += 1
        
        # Calculate compliance score
        total_events = compliance_metrics["total_events"]
        if total_events > 0:
            compliance_score = max(0, 100 - (
                (compliance_metrics["failed_events"] / total_events * 20) +
                (compliance_metrics["security_events"] / total_events * 30) +
                (compliance_metrics["compliance_violations"] / total_events * 50)
            ))
        else:
            compliance_score = 100
        
        # Store compliance report
        report_data = {
            "report_id": str(__import__("uuid").uuid4()),
            "report_type": request_data.report_type,
            "period_start": request_data.period_start.isoformat(),
            "period_end": request_data.period_end.isoformat(),
            "generated_by": user_id,
            "generated_at": datetime.utcnow().isoformat(),
            "report_data": compliance_metrics,
            "compliance_score": compliance_score,
            "violations_count": compliance_metrics["compliance_violations"],
            "status": "generated"
        }
        
        result = audit_logging_service.supabase.table("compliance_reports").insert(report_data).execute()
        
        if result.data:
            return {
                "success": True,
                "compliance_report": result.data[0],
                "message": "Compliance report generated successfully"
            }
        
        raise Exception("Failed to store compliance report")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate compliance report: {str(e)}")


@router.get("/integrity-check")
async def verify_audit_integrity(
    start_date: datetime = Query(...),
    end_date: datetime = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Verify integrity of audit log chain.
    
    Args:
        start_date: Start date for integrity check
        end_date: End date for integrity check
        current_user: Current authenticated user
        
    Returns:
        Integrity verification results
    """
    try:
        # Check if user has admin privileges
        user_role = current_user.get("role", "user")
        if user_role not in ["admin"]:
            raise HTTPException(status_code=403, detail="System admin access required for integrity checks")
        
        # Verify audit integrity
        integrity_results = await audit_logging_service.verify_audit_integrity(start_date, end_date)
        
        return {
            "success": True,
            "integrity_check": integrity_results,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to verify audit integrity: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for audit logging service.
    
    Returns:
        Service health status
    """
    try:
        return {
            "status": "healthy",
            "service": "audit_logging",
            "features": [
                "comprehensive_audit_trail",
                "immutable_storage",
                "security_alerts",
                "compliance_reporting",
                "integrity_verification",
                "structured_logging"
            ]
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "audit_logging",
            "error": str(e)
        }
