"""
Advanced Clause Analysis API endpoints for Averum Contracts
Provides comprehensive clause extraction, missing clause detection, and risk assessment
"""

import tempfile
import os
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.clause_analysis_service import clause_analysis_service
from app.services.secure_ai_service import secure_ai_service

router = APIRouter()


class ClauseAnalysisRequest(BaseModel):
    """Request model for clause analysis."""
    text: str
    contract_id: Optional[str] = None
    include_missing_clauses: bool = True
    include_risk_assessment: bool = True


class ClauseExtractionRequest(BaseModel):
    """Request model for clause extraction only."""
    text: str
    contract_type: Optional[str] = None


class MissingClauseDetectionRequest(BaseModel):
    """Request model for missing clause detection."""
    text: str
    contract_type: Optional[str] = None
    extracted_clauses: Optional[List[str]] = None


def get_client_info(request: Request) -> Dict[str, Optional[str]]:
    """Extract client information for audit logging."""
    return {
        "ip_address": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent"),
        "session_id": request.headers.get("x-session-id")
    }


@router.post("/analyze-clauses")
async def analyze_clauses_comprehensive(
    request_data: ClauseAnalysisRequest,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    Perform comprehensive clause analysis including extraction, missing clause detection, and risk assessment.
    
    Args:
        request_data: Clause analysis request data
        request: FastAPI request object for client info
        current_user: Current authenticated user
        
    Returns:
        Comprehensive clause analysis results
    """
    try:
        client_info = get_client_info(request)
        
        # Get user and workspace info
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # Log the analysis request
        await secure_ai_service.security_service.log_audit_event(
            event_type="clause_analysis",
            user_id=user_id,
            workspace_id=workspace_id,
            action="comprehensive_analysis_requested",
            resource_type="contract",
            resource_id=request_data.contract_id,
            details={
                "text_length": len(request_data.text),
                "include_missing_clauses": request_data.include_missing_clauses,
                "include_risk_assessment": request_data.include_risk_assessment
            },
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            session_id=client_info["session_id"]
        )
        
        # Perform comprehensive clause analysis
        analysis_result = await clause_analysis_service.analyze_contract_comprehensively(request_data.text)
        
        # Prepare response
        response_data = {
            "success": True,
            "contract_type": analysis_result["contract_type"],
            "extracted_clauses": analysis_result["extracted_clauses"],
            "total_clauses_found": analysis_result["total_clauses_found"],
            "overall_risk_score": analysis_result["overall_risk_score"],
            "completeness_score": analysis_result["completeness_score"],
            "high_risk_clauses": analysis_result["high_risk_clauses"],
            "critical_missing_clauses": analysis_result["critical_missing_clauses"]
        }
        
        # Add missing clauses if requested
        if request_data.include_missing_clauses:
            response_data["missing_clauses"] = analysis_result["missing_clauses"]
        
        # Add risk assessments if requested
        if request_data.include_risk_assessment:
            response_data["risk_assessments"] = analysis_result["risk_assessments"]
        
        # Log successful completion
        await secure_ai_service.security_service.log_audit_event(
            event_type="clause_analysis",
            user_id=user_id,
            workspace_id=workspace_id,
            action="comprehensive_analysis_completed",
            resource_type="contract",
            resource_id=request_data.contract_id,
            details={
                "clauses_found": analysis_result["total_clauses_found"],
                "missing_clauses": len(analysis_result["missing_clauses"]),
                "overall_risk_score": analysis_result["overall_risk_score"],
                "completeness_score": analysis_result["completeness_score"]
            },
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            session_id=client_info["session_id"]
        )
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        # Log error
        await secure_ai_service.security_service.log_audit_event(
            event_type="clause_analysis",
            user_id=user_id,
            workspace_id=workspace_id,
            action="analysis_error",
            resource_type="contract",
            resource_id=request_data.contract_id,
            details={
                "error_type": type(e).__name__,
                "error_message": str(e)
            },
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            session_id=client_info["session_id"]
        )
        
        raise HTTPException(status_code=500, detail=f"Clause analysis failed: {str(e)}")


@router.post("/extract-clauses")
async def extract_clauses_only(
    request_data: ClauseExtractionRequest,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    Extract clauses from contract text without risk assessment.
    
    Args:
        request_data: Clause extraction request
        request: FastAPI request object for client info
        current_user: Current authenticated user
        
    Returns:
        Extracted clauses
    """
    try:
        client_info = get_client_info(request)
        
        # Get user and workspace info
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # Extract clauses
        extracted_clauses = await clause_analysis_service.extract_clauses(request_data.text)
        
        # Convert to dict format
        clauses_data = [clause.dict() for clause in extracted_clauses]
        
        # Log the extraction
        await secure_ai_service.security_service.log_audit_event(
            event_type="clause_extraction",
            user_id=user_id,
            workspace_id=workspace_id,
            action="clauses_extracted",
            resource_type="contract",
            details={
                "text_length": len(request_data.text),
                "clauses_found": len(extracted_clauses),
                "contract_type": request_data.contract_type
            },
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            session_id=client_info["session_id"]
        )
        
        return {
            "success": True,
            "clauses_found": len(extracted_clauses),
            "extracted_clauses": clauses_data,
            "contract_type": request_data.contract_type
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Clause extraction failed: {str(e)}")


@router.post("/detect-missing-clauses")
async def detect_missing_clauses(
    request_data: MissingClauseDetectionRequest,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """
    Detect missing standard clauses in contract text.
    
    Args:
        request_data: Missing clause detection request
        request: FastAPI request object for client info
        current_user: Current authenticated user
        
    Returns:
        Missing clauses information
    """
    try:
        client_info = get_client_info(request)
        
        # Get user and workspace info
        user_id = current_user.get("sub") or current_user.get("user_id")
        workspace_id = current_user.get("workspace_id")
        
        if not user_id:
            raise HTTPException(status_code=401, detail="User ID not found in token")
        
        # First extract clauses if not provided
        if request_data.extracted_clauses:
            # Convert clause type names to ExtractedClause objects (simplified)
            from app.services.clause_analysis_service import ExtractedClause
            extracted_clauses = [
                ExtractedClause(
                    type=clause_type,
                    title=f"{clause_type.replace('_', ' ').title()} Clause",
                    content="",
                    importance="medium",
                    risk_level="medium",
                    confidence=1.0,
                    position=i,
                    start_char=0,
                    end_char=0,
                    category="general",
                    keywords=[],
                    risk_factors=[]
                )
                for i, clause_type in enumerate(request_data.extracted_clauses)
            ]
        else:
            extracted_clauses = await clause_analysis_service.extract_clauses(request_data.text)
        
        # Detect missing clauses
        missing_clauses = await clause_analysis_service.detect_missing_clauses(
            request_data.text, 
            extracted_clauses
        )
        
        # Convert to dict format
        missing_data = [clause.dict() for clause in missing_clauses]
        
        # Log the detection
        await secure_ai_service.security_service.log_audit_event(
            event_type="missing_clause_detection",
            user_id=user_id,
            workspace_id=workspace_id,
            action="missing_clauses_detected",
            resource_type="contract",
            details={
                "text_length": len(request_data.text),
                "missing_clauses_found": len(missing_clauses),
                "contract_type": request_data.contract_type
            },
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            session_id=client_info["session_id"]
        )
        
        return {
            "success": True,
            "missing_clauses_found": len(missing_clauses),
            "missing_clauses": missing_data,
            "contract_type": request_data.contract_type,
            "critical_missing": len([c for c in missing_clauses if c.importance == "critical"]),
            "high_importance_missing": len([c for c in missing_clauses if c.importance == "high"])
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Missing clause detection failed: {str(e)}")


@router.get("/clause-patterns")
async def get_clause_patterns(
    current_user: dict = Depends(get_current_user)
):
    """
    Get available clause patterns and categories.
    
    Returns:
        Available clause patterns and categories
    """
    try:
        patterns = []
        for pattern in clause_analysis_service.clause_patterns:
            patterns.append({
                "name": pattern.name,
                "category": pattern.category,
                "importance": pattern.importance,
                "risk_level": pattern.risk_level,
                "description": pattern.description
            })
        
        return {
            "success": True,
            "clause_patterns": patterns,
            "standard_clauses": clause_analysis_service.standard_clauses,
            "supported_categories": list(set(p.category for p in clause_analysis_service.clause_patterns))
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get clause patterns: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for clause analysis service.
    
    Returns:
        Service health status
    """
    try:
        # Test basic functionality
        test_text = "This agreement shall terminate upon 30 days written notice."
        clauses = await clause_analysis_service.extract_clauses(test_text)
        
        return {
            "status": "healthy",
            "service": "clause_analysis",
            "patterns_loaded": len(clause_analysis_service.clause_patterns),
            "standard_clauses": len(clause_analysis_service.standard_clauses),
            "test_extraction_successful": len(clauses) > 0
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "clause_analysis",
            "error": str(e)
        }
