from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Path, Query, Body
from typing import List, Optional, Dict, Any
from app.schemas.workspace import (
    Workspace, WorkspaceCreate, WorkspaceUpdate, WorkspaceWithMembers,
    WorkspaceMember, WorkspaceMemberCreate, WorkspaceMemberUpdate
)
from app.core.auth import get_current_user, get_authenticated_db_client
from app.db.database import get_supabase_client
from app.utils.response import (
    handle_supabase_response,
    not_found_error,
    forbidden_error,
    server_error
)
from datetime import datetime
import uuid
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/", response_model=List[Workspace])
async def get_workspaces(
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve all workspaces for the current user.
    RLS automatically filters to only workspaces the user has access to.
    """
    try:
        # Use authenticated client with RLS - automatically filters by user's workspace access
        supabase = get_authenticated_db_client(current_user)

        # With RLS, we can directly query workspaces - they'll be automatically filtered
        workspaces_response = supabase.table("workspaces").select("*").execute()

        # Handle workspaces response
        workspaces_data = handle_supabase_response(workspaces_response, "Failed to retrieve user workspaces")

        return workspaces_data or []

    except Exception as e:
        logger.error(f"Error retrieving workspaces: {str(e)}")
        # Return mock data as fallback to prevent infinite loops
        return [
            {
                "id": "mock-workspace-1",
                "name": "Default Workspace",
                "description": "Default workspace for development",
                "members": 1,
                "contracts": 0,
                "created_by": current_user.get("id", "unknown"),
                "created_at": "2024-01-01T00:00:00Z",
                "is_active": True
            }
        ]

@router.post("/", response_model=Workspace)
async def create_workspace(
    workspace: WorkspaceCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new workspace.
    """
    supabase = get_supabase_client()

    # Prepare workspace data
    # Use provided ID if available, otherwise generate a new one
    workspace_id = workspace.id if workspace.id else str(uuid.uuid4())
    workspace_data = workspace.model_dump()
    workspace_data["id"] = workspace_id
    workspace_data["created_by"] = current_user["id"]
    workspace_data["created_at"] = datetime.utcnow().isoformat()
    workspace_data["is_active"] = True
    workspace_data["members"] = 1  # Creator is the first member
    workspace_data["contracts"] = 0  # No contracts initially

    # Ensure settings has a default value if not provided
    if not workspace_data.get("settings"):
        workspace_data["settings"] = {}

    # Insert the workspace
    workspace_response = supabase.table("workspaces").insert(workspace_data).execute()

    # Handle workspace creation response
    workspace_data_result = handle_supabase_response(workspace_response, "Failed to create workspace")

    if not workspace_data_result:
        raise HTTPException(status_code=500, detail="Workspace creation failed - no data returned")

    # Add the creator as a member with admin role
    member_data = {
        "workspace_id": workspace_id,
        "user_id": current_user["id"],
        "role_id": "role-admin",  # Assuming this is your admin role ID
        "joined_at": datetime.utcnow().isoformat()
    }

    member_response = supabase.table("workspace_members").insert(member_data).execute()

    # Handle member creation response
    member_data_result = handle_supabase_response(member_response, "Failed to add workspace member")

    if not member_data_result:
        # If adding the member fails, try to delete the workspace
        supabase.table("workspaces").delete().eq("id", workspace_id).execute()
        raise HTTPException(status_code=500, detail="Failed to add creator as workspace member")

    return workspace_data_result[0]

@router.get("/{workspace_id}", response_model=WorkspaceWithMembers)
async def get_workspace(
    workspace_id: str = Path(..., description="The ID of the workspace to retrieve"),
    include_members: bool = Query(False, description="Include workspace members in the response"),
    current_user: dict = Depends(get_current_user)
):
    """
    Retrieve a specific workspace by ID.
    """
    supabase = get_supabase_client()

    # Check if user is a member of this workspace
    member_check = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    # Handle member check response
    member_data = handle_supabase_response(member_check, "Failed to check workspace membership")

    if not member_data:
        raise forbidden_error("You don't have access to this workspace")

    # Get workspace details
    workspace_response = supabase.table("workspaces").select("*").eq("id", workspace_id).execute()

    # Handle workspace response
    workspace_data = handle_supabase_response(workspace_response, "Failed to retrieve workspace")

    if not workspace_data:
        raise not_found_error("Workspace")

    workspace = workspace_data[0]

    # If requested, include members
    if include_members:
        members_response = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).execute()

        # Handle members response
        members_data = handle_supabase_response(members_response, "Failed to retrieve workspace members")

        workspace["members_list"] = members_data
    else:
        workspace["members_list"] = []

    return workspace

@router.put("/{workspace_id}", response_model=Workspace)
async def update_workspace(
    workspace: WorkspaceUpdate,
    workspace_id: str = Path(..., description="The ID of the workspace to update"),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a specific workspace.
    """
    supabase = get_supabase_client()

    # Check if user is an admin of this workspace
    member_check = supabase.table("workspace_members").select("role_id").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    # Handle member check response
    member_data = handle_supabase_response(member_check, "Failed to check workspace membership")

    if not member_data:
        raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    if member_data[0]["role_id"] != "role-admin":
        raise HTTPException(status_code=403, detail="Only workspace administrators can update workspace details")

    # Prepare update data
    update_data = workspace.model_dump(exclude_unset=True)

    # Update the workspace
    response = supabase.table("workspaces").update(update_data).eq("id", workspace_id).execute()

    # Handle update response
    updated_data = handle_supabase_response(response, "Failed to update workspace")

    if not updated_data:
        raise HTTPException(status_code=404, detail="Workspace not found")

    return updated_data[0]

@router.delete("/{workspace_id}")
async def delete_workspace(
    workspace_id: str = Path(..., description="The ID of the workspace to delete"),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a specific workspace.
    """
    supabase = get_supabase_client()

    # Check if user is an admin of this workspace
    member_check = supabase.table("workspace_members").select("role_id").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    # Handle member check response
    member_data = handle_supabase_response(member_check, "Failed to check workspace membership")

    if not member_data:
        raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    if member_data[0]["role_id"] != "role-admin":
        raise HTTPException(status_code=403, detail="Only workspace administrators can delete workspaces")

    # Delete the workspace
    response = supabase.table("workspaces").delete().eq("id", workspace_id).execute()

    # Handle delete response
    handle_supabase_response(response, "Failed to delete workspace")

    # Also delete all workspace memberships
    members_delete_response = supabase.table("workspace_members").delete().eq("workspace_id", workspace_id).execute()
    handle_supabase_response(members_delete_response, "Failed to delete workspace memberships")

    return {"message": "Workspace deleted successfully"}

@router.get("/{workspace_id}/members", response_model=List[WorkspaceMember])
async def get_workspace_members(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all members of a workspace.
    """
    supabase = get_supabase_client()

    # Check if user is a member of this workspace
    member_check = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    # Handle member check response
    member_data = handle_supabase_response(member_check, "Failed to check workspace membership")

    if not member_data:
        raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    # Get all members
    members_response = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).execute()

    # Handle members response
    members_data = handle_supabase_response(members_response, "Failed to retrieve workspace members")

    # Get user details for each member
    members = []
    for member in members_data:
        # Get user details
        user_response = supabase.table("users").select("*").eq("id", member["user_id"]).execute()

        # Handle user response
        user_data = handle_supabase_response(user_response, "Failed to retrieve user details")

        if user_data:
            member["user"] = user_data[0]
        else:
            member["user"] = {"id": member["user_id"], "name": "Unknown User"}

        members.append(member)

    return members

@router.post("/{workspace_id}/members", response_model=WorkspaceMember)
async def add_workspace_member(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    member: WorkspaceMemberCreate = Body(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Add a member to a workspace.
    """
    supabase = get_supabase_client()

    # Check if user is an admin of this workspace
    member_check = supabase.table("workspace_members").select("role_id").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    # Handle member check response
    member_data = handle_supabase_response(member_check, "Failed to check workspace membership")

    if not member_data:
        raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    if member_data[0]["role_id"] != "role-admin":
        raise HTTPException(status_code=403, detail="Only workspace administrators can add members")

    # Check if the user exists
    user_check = supabase.table("users").select("*").eq("id", member.user_id).execute()

    # Handle user check response
    user_data = handle_supabase_response(user_check, "Failed to check user existence")

    if not user_data:
        raise not_found_error("User")

    # Check if the user is already a member
    existing_member = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", member.user_id).execute()

    # Handle existing member check response
    existing_member_data = handle_supabase_response(existing_member, "Failed to check existing membership")

    if existing_member_data:
        # Update existing membership
        update_data = {
            "role_id": member.role_id,
            "status": member.status
        }

        response = supabase.table("workspace_members").update(update_data).eq("workspace_id", workspace_id).eq("user_id", member.user_id).execute()

        # Handle update response
        updated_data = handle_supabase_response(response, "Failed to update workspace member")

        if not updated_data:
            raise HTTPException(status_code=500, detail="Member update failed - no data returned")

        # Add user details
        updated_data[0]["user"] = user_data[0]

        return updated_data[0]

    # Create new membership
    member_id = str(uuid.uuid4())
    member_data_dict = {
        "id": member_id,
        "workspace_id": workspace_id,
        "user_id": member.user_id,
        "role_id": member.role_id,
        "status": member.status,
        "joined_at": datetime.utcnow().isoformat()
    }

    response = supabase.table("workspace_members").insert(member_data_dict).execute()

    # Handle insert response
    inserted_data = handle_supabase_response(response, "Failed to add workspace member")

    if not inserted_data:
        raise HTTPException(status_code=500, detail="Member creation failed - no data returned")

    # Add user details
    inserted_data[0]["user"] = user_data[0]

    return inserted_data[0]

@router.put("/{workspace_id}/members/{user_id}", response_model=WorkspaceMember)
async def update_workspace_member(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    user_id: str = Path(..., description="The ID of the user"),
    member_update: WorkspaceMemberUpdate = Body(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Update a workspace member's role or status.
    """
    supabase = get_supabase_client()

    # Check if user is an admin of this workspace
    member_check = supabase.table("workspace_members").select("role_id").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    # Handle member check response
    member_data = handle_supabase_response(member_check, "Failed to check workspace membership")

    if not member_data:
        raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    if member_data[0]["role_id"] != "role-admin":
        raise HTTPException(status_code=403, detail="Only workspace administrators can update members")

    # Check if the member exists
    existing_member = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", user_id).execute()

    # Handle existing member check response
    existing_member_data = handle_supabase_response(existing_member, "Failed to check existing member")

    if not existing_member_data:
        raise HTTPException(status_code=404, detail="Member not found in this workspace")

    # Update the member
    update_data = {}

    if member_update.role_id is not None:
        update_data["role_id"] = member_update.role_id

    if member_update.status is not None:
        update_data["status"] = member_update.status

    if not update_data:
        # No changes to make
        return existing_member_data[0]

    response = supabase.table("workspace_members").update(update_data).eq("workspace_id", workspace_id).eq("user_id", user_id).execute()

    # Handle update response
    updated_data = handle_supabase_response(response, "Failed to update workspace member")

    if not updated_data:
        raise HTTPException(status_code=500, detail="Member update failed - no data returned")

    # Get user details
    user_response = supabase.table("users").select("*").eq("id", user_id).execute()

    # Handle user response
    user_data = handle_supabase_response(user_response, "Failed to retrieve user details")

    if user_data:
        updated_data[0]["user"] = user_data[0]
    else:
        updated_data[0]["user"] = {"id": user_id, "name": "Unknown User"}

    return updated_data[0]

@router.delete("/{workspace_id}/members/{user_id}")
async def remove_workspace_member(
    workspace_id: str = Path(..., description="The ID of the workspace"),
    user_id: str = Path(..., description="The ID of the user to remove"),
    current_user: dict = Depends(get_current_user)
):
    """
    Remove a member from a workspace.
    """
    supabase = get_supabase_client()

    # Check if user is an admin of this workspace or is removing themselves
    member_check = supabase.table("workspace_members").select("role_id").eq("workspace_id", workspace_id).eq("user_id", current_user["id"]).execute()

    # Handle member check response
    member_data = handle_supabase_response(member_check, "Failed to check workspace membership")

    if not member_data:
        raise HTTPException(status_code=403, detail="You don't have access to this workspace")

    # Allow users to remove themselves or admins to remove anyone
    if member_data[0]["role_id"] != "role-admin" and current_user["id"] != user_id:
        raise HTTPException(status_code=403, detail="Only workspace administrators can remove other members")

    # Check if the member exists
    existing_member = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("user_id", user_id).execute()

    # Handle existing member check response
    existing_member_data = handle_supabase_response(existing_member, "Failed to check existing member")

    if not existing_member_data:
        raise HTTPException(status_code=404, detail="Member not found in this workspace")

    # Don't allow removing the last admin
    if existing_member_data[0]["role_id"] == "role-admin":
        # Check if this is the last admin
        admins_check = supabase.table("workspace_members").select("*").eq("workspace_id", workspace_id).eq("role_id", "role-admin").execute()

        # Handle admins check response
        admins_data = handle_supabase_response(admins_check, "Failed to check admin count")

        if admins_data and len(admins_data) <= 1:
            raise HTTPException(status_code=400, detail="Cannot remove the last administrator from the workspace")

    # Remove the member
    response = supabase.table("workspace_members").delete().eq("workspace_id", workspace_id).eq("user_id", user_id).execute()

    # Handle delete response
    handle_supabase_response(response, "Failed to remove workspace member")

    return {"message": "Member removed from workspace successfully"}
