"""
Document Processing API endpoints for Averum Contracts
"""

import tempfile
import os
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.auth import get_current_user
from app.services.document_processor import document_processor, DocumentProcessingError
from app.services.storage import StorageService

router = APIRouter()


class DocumentProcessingRequest(BaseModel):
    """Request model for document processing."""
    extract_entities: bool = True
    chunk_document: bool = True
    validate_content: bool = True
    workspace_id: Optional[str] = None


class TextProcessingRequest(BaseModel):
    """Request model for text processing."""
    text: str
    file_name: str = "text_input"
    extract_entities: bool = True
    chunk_document: bool = True
    validate_content: bool = True


@router.post("/process-document")
async def process_document(
    file: UploadFile = File(...),
    extract_entities: bool = Form(True),
    chunk_document: bool = Form(True),
    validate_content: bool = Form(True),
    workspace_id: Optional[str] = Form(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Process an uploaded document and extract structured information.
    
    Args:
        file: Uploaded document file
        extract_entities: Whether to extract named entities
        chunk_document: Whether to chunk the document
        validate_content: Whether to validate content
        workspace_id: Workspace ID for the document
        current_user: Current authenticated user
        
    Returns:
        Processed document information
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
        try:
            # Write uploaded file to temporary location
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            
            # Process the document
            processed_doc = await document_processor.process_document(
                file_path=temp_file.name,
                file_name=file.filename,
                extract_entities=extract_entities,
                chunk_document=chunk_document,
                validate_content=validate_content
            )
            
            # Convert to dict for JSON response
            result = {
                "success": True,
                "file_name": file.filename,
                "metadata": processed_doc.metadata.dict(),
                "text_length": len(processed_doc.cleaned_text),
                "chunk_count": len(processed_doc.chunks),
                "entity_count": len(processed_doc.extracted_entities),
                "section_count": len(processed_doc.sections),
                "processing_time": processed_doc.processing_time,
                "processing_errors": processed_doc.processing_errors,
                "extracted_text": processed_doc.cleaned_text[:1000] + "..." if len(processed_doc.cleaned_text) > 1000 else processed_doc.cleaned_text,
                "entities": processed_doc.extracted_entities,
                "sections": [{"id": s["id"], "title": s["title"], "word_count": s["word_count"]} for s in processed_doc.sections],
                "chunks": [{"chunk_id": c.chunk_id, "word_count": c.word_count, "start_position": c.start_position} for c in processed_doc.chunks]
            }
            
            return JSONResponse(content=result)
            
        except DocumentProcessingError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)


@router.post("/process-text")
async def process_text(
    request: TextProcessingRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Process raw text input and extract structured information.
    
    Args:
        request: Text processing request
        current_user: Current authenticated user
        
    Returns:
        Processed text information
    """
    try:
        # Process the text
        processed_doc = await document_processor.process_text(
            text=request.text,
            file_name=request.file_name,
            extract_entities=request.extract_entities,
            chunk_document=request.chunk_document,
            validate_content=request.validate_content
        )
        
        # Convert to dict for JSON response
        result = {
            "success": True,
            "file_name": request.file_name,
            "metadata": processed_doc.metadata.dict(),
            "text_length": len(processed_doc.cleaned_text),
            "chunk_count": len(processed_doc.chunks),
            "entity_count": len(processed_doc.extracted_entities),
            "section_count": len(processed_doc.sections),
            "processing_time": processed_doc.processing_time,
            "processing_errors": processed_doc.processing_errors,
            "extracted_text": processed_doc.cleaned_text,
            "entities": processed_doc.extracted_entities,
            "sections": processed_doc.sections,
            "chunks": [c.dict() for c in processed_doc.chunks]
        }
        
        return JSONResponse(content=result)
        
    except DocumentProcessingError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/supported-formats")
async def get_supported_formats(
    current_user: dict = Depends(get_current_user)
):
    """
    Get list of supported document formats.
    
    Returns:
        List of supported file formats
    """
    return {
        "supported_formats": document_processor.supported_formats,
        "max_file_size": document_processor.max_file_size,
        "chunk_size": document_processor.chunk_size,
        "chunk_overlap": document_processor.chunk_overlap
    }


@router.post("/extract-entities")
async def extract_entities_from_text(
    text: str = Form(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Extract named entities from text.
    
    Args:
        text: Text to extract entities from
        current_user: Current authenticated user
        
    Returns:
        Extracted entities
    """
    try:
        entities = await document_processor._extract_entities(text)
        
        return {
            "success": True,
            "entity_count": len(entities),
            "entities": entities
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to extract entities: {str(e)}")


@router.post("/chunk-text")
async def chunk_text(
    text: str = Form(...),
    chunk_size: Optional[int] = Form(None),
    chunk_overlap: Optional[int] = Form(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Chunk text into smaller pieces.
    
    Args:
        text: Text to chunk
        chunk_size: Custom chunk size (words)
        chunk_overlap: Custom chunk overlap (words)
        current_user: Current authenticated user
        
    Returns:
        Text chunks
    """
    try:
        # Temporarily override chunk settings if provided
        original_chunk_size = document_processor.chunk_size
        original_chunk_overlap = document_processor.chunk_overlap
        
        if chunk_size:
            document_processor.chunk_size = chunk_size
        if chunk_overlap:
            document_processor.chunk_overlap = chunk_overlap
        
        try:
            chunks = await document_processor._chunk_document(text)
            
            return {
                "success": True,
                "chunk_count": len(chunks),
                "chunks": [c.dict() for c in chunks],
                "settings": {
                    "chunk_size": document_processor.chunk_size,
                    "chunk_overlap": document_processor.chunk_overlap
                }
            }
        finally:
            # Restore original settings
            document_processor.chunk_size = original_chunk_size
            document_processor.chunk_overlap = original_chunk_overlap
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to chunk text: {str(e)}")


@router.get("/health")
async def health_check():
    """
    Health check for document processing service.
    
    Returns:
        Service health status
    """
    try:
        # Test basic functionality
        test_text = "This is a test document for health check."
        processed = await document_processor.process_text(
            text=test_text,
            file_name="health_check",
            extract_entities=False,
            chunk_document=False,
            validate_content=False
        )
        
        return {
            "status": "healthy",
            "service": "document_processing",
            "supported_formats": document_processor.supported_formats,
            "test_processing_time": processed.processing_time
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "document_processing",
            "error": str(e)
        }
