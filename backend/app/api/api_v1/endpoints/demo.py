"""
Demo endpoints for testing data without authentication
These endpoints are for development/demo purposes only
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
from app.db.database import get_supabase_client
from app.core.auth import get_current_user, validate_workspace_access

def handle_supabase_response(response, error_message: str = "Database operation failed"):
    """
    Helper function to handle Supabase response objects consistently.
    """
    has_error = hasattr(response, 'error') and response.error
    has_data = hasattr(response, 'data') and response.data is not None

    if has_error:
        raise HTTPException(status_code=400, detail=f"{error_message}: {response.error}")

    return response.data if has_data else []

router = APIRouter()

@router.get("/users")
async def get_demo_users():
    """Get all demo users."""
    supabase = get_supabase_client()

    response = supabase.table("users").select("*").like("email", "%@demo.legalai.com").execute()
    data = handle_supabase_response(response, "Failed to retrieve demo users")

    return {
        "count": len(data),
        "users": data
    }

@router.get("/workspaces")
async def get_demo_workspaces():
    """Get all demo workspaces."""
    supabase = get_supabase_client()

    response = supabase.table("workspaces").select("*").like("name", "Demo %").execute()
    data = handle_supabase_response(response, "Failed to retrieve demo workspaces")

    return {
        "count": len(data),
        "workspaces": data
    }

@router.get("/contracts")
async def get_demo_contracts():
    """Get all demo contracts."""
    supabase = get_supabase_client()

    response = supabase.table("contracts").select("*").like("id", "demo-%").execute()
    data = handle_supabase_response(response, "Failed to retrieve demo contracts")

    return {
        "count": len(data),
        "contracts": data
    }

@router.get("/templates")
async def get_demo_templates():
    """Get all demo templates."""
    supabase = get_supabase_client()

    response = supabase.table("templates").select("*").like("id", "demo-%").execute()
    data = handle_supabase_response(response, "Failed to retrieve demo templates")

    return {
        "count": len(data),
        "templates": data
    }

@router.get("/all")
async def get_all_demo_data():
    """Get all demo data in one response."""
    supabase = get_supabase_client()

    # Get demo users
    users_response = supabase.table("users").select("*").like("email", "%@demo.legalai.com").execute()
    users = handle_supabase_response(users_response, "Failed to retrieve demo users")

    # Get demo workspaces
    workspaces_response = supabase.table("workspaces").select("*").like("name", "Demo %").execute()
    workspaces = handle_supabase_response(workspaces_response, "Failed to retrieve demo workspaces")

    # Get demo contracts
    contracts_response = supabase.table("contracts").select("*").like("id", "demo-%").execute()
    contracts = handle_supabase_response(contracts_response, "Failed to retrieve demo contracts")

    # Get demo templates
    templates_response = supabase.table("templates").select("*").like("id", "demo-%").execute()
    templates = handle_supabase_response(templates_response, "Failed to retrieve demo templates")

    # Get workspace members for demo workspaces
    workspace_ids = [ws["id"] for ws in workspaces]
    members_response = supabase.table("workspace_members").select("*").in_("workspace_id", workspace_ids).execute()
    members = handle_supabase_response(members_response, "Failed to retrieve workspace members")

    return {
        "summary": {
            "users_count": len(users),
            "workspaces_count": len(workspaces),
            "contracts_count": len(contracts),
            "templates_count": len(templates),
            "members_count": len(members)
        },
        "data": {
            "users": users,
            "workspaces": workspaces,
            "contracts": contracts,
            "templates": templates,
            "workspace_members": members
        }
    }

@router.get("/workspace/{workspace_id}/contracts")
async def get_workspace_contracts(
    workspace_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get contracts for a specific workspace."""
    supabase = get_supabase_client()

    # Validate that the user has access to this workspace
    await validate_workspace_access(current_user["id"], workspace_id, supabase)

    response = supabase.table("contracts").select("*").eq("workspace_id", workspace_id).execute()
    data = handle_supabase_response(response, "Failed to retrieve workspace contracts")

    return {
        "workspace_id": workspace_id,
        "count": len(data),
        "contracts": data
    }

@router.get("/user/{user_id}/workspaces")
async def get_user_workspaces(
    user_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get workspaces for a specific user."""
    # Only allow users to view their own workspaces or admins to view any
    if current_user["id"] != user_id:
        raise HTTPException(status_code=403, detail="Access denied: You can only view your own workspaces")

    supabase = get_supabase_client()

    # Get workspace memberships
    members_response = supabase.table("workspace_members").select("*").eq("user_id", user_id).execute()
    members = handle_supabase_response(members_response, "Failed to retrieve user workspace memberships")

    if not members:
        return {
            "user_id": user_id,
            "count": 0,
            "workspaces": []
        }

    # Get workspace details
    workspace_ids = [member["workspace_id"] for member in members]
    workspaces_response = supabase.table("workspaces").select("*").in_("id", workspace_ids).execute()
    workspaces = handle_supabase_response(workspaces_response, "Failed to retrieve workspaces")

    # Combine workspace data with role information
    workspace_data = []
    for workspace in workspaces:
        member_info = next((m for m in members if m["workspace_id"] == workspace["id"]), None)
        workspace_with_role = {
            **workspace,
            "user_role": member_info["role_id"] if member_info else None,
            "joined_at": member_info["joined_at"] if member_info else None
        }
        workspace_data.append(workspace_with_role)

    return {
        "user_id": user_id,
        "count": len(workspace_data),
        "workspaces": workspace_data
    }

@router.post("/simulate-user/{user_id}")
async def simulate_user_session(
    user_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Simulate a user session for demo purposes."""
    # Only allow users to simulate their own session or admins to simulate any
    if current_user["id"] != user_id:
        raise HTTPException(status_code=403, detail="Access denied: You can only simulate your own session")

    supabase = get_supabase_client()

    # Get user data
    user_response = supabase.table("users").select("*").eq("id", user_id).execute()
    user_data = handle_supabase_response(user_response, "Failed to retrieve user")

    if not user_data:
        raise HTTPException(status_code=404, detail="User not found")

    user = user_data[0]

    # Get user's workspaces
    members_response = supabase.table("workspace_members").select("*").eq("user_id", user_id).execute()
    members = handle_supabase_response(members_response, "Failed to retrieve user memberships")

    workspace_ids = [member["workspace_id"] for member in members]

    # Get contracts from user's workspaces
    contracts_response = supabase.table("contracts").select("*").in_("workspace_id", workspace_ids).execute()
    contracts = handle_supabase_response(contracts_response, "Failed to retrieve user contracts")

    # Get templates from user's workspaces
    templates_response = supabase.table("templates").select("*").in_("workspace_id", workspace_ids).execute()
    templates = handle_supabase_response(templates_response, "Failed to retrieve user templates")

    return {
        "user": user,
        "accessible_data": {
            "contracts_count": len(contracts),
            "templates_count": len(templates),
            "workspaces_count": len(workspace_ids),
            "contracts": contracts,
            "templates": templates
        }
    }
