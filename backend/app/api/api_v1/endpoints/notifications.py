"""
Notification endpoints for the Averum Contracts API.
"""

from fastapi import APIRout<PERSON>, Depends, HTTPException, Query, Path
from typing import List, Optional
from datetime import datetime

from app.schemas.notification import (
    Notification,
    NotificationCreate,
    NotificationUpdate,
    NotificationFilters,
    NotificationSummary,
    NotificationBulkUpdate,
    NotificationBulkDelete,
    NotificationPreferencesUpdate,
    NotificationType,
    NotificationStatus
)
from app.schemas.user import NotificationPreferences
from app.services.notification_service import NotificationService
from app.core.auth import get_current_user, get_authenticated_db_client
from app.db.database import get_supabase_client
from app.core.notification_errors import (
    handle_notification_errors,
    NotificationErrorHandler,
    NotificationNotFoundError,
    NotificationPermissionError
)

router = APIRouter()

@router.get("/", response_model=List[Notification])
@handle_notification_errors("get_notifications")
async def get_notifications(
    workspace_id: str = Query(..., description="Workspace ID to filter notifications"),
    status: Optional[NotificationStatus] = Query(None, description="Filter by notification status"),
    type: Optional[NotificationType] = Query(None, description="Filter by notification type"),
    sender_id: Optional[str] = Query(None, description="Filter by sender ID"),
    entity_type: Optional[str] = Query(None, description="Filter by entity type"),
    start_date: Optional[datetime] = Query(None, description="Filter notifications created after this date"),
    end_date: Optional[datetime] = Query(None, description="Filter notifications created before this date"),
    skip: int = Query(0, description="Number of notifications to skip for pagination"),
    limit: int = Query(50, description="Maximum number of notifications to return"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get notifications for the current user with filtering and pagination.
    """
    with NotificationErrorHandler("get_notifications", current_user["id"], workspace_id) as handler:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)

        # Create filters
        filters = NotificationFilters(
            workspace_id=workspace_id,
            status=status,
            type=type,
            sender_id=sender_id,
            entity_type=entity_type,
            start_date=start_date,
            end_date=end_date,
            skip=skip,
            limit=limit
        )

        # Get notifications
        notifications = await notification_service.get_notifications(
            user_id=current_user["id"],
            filters=filters
        )

        handler.log_success({"count": len(notifications), "filters": filters.dict()})
        return notifications

@router.get("/summary", response_model=NotificationSummary)
async def get_notification_summary(
    workspace_id: str = Query(..., description="Workspace ID to get summary for"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get notification summary for the current user in a workspace.
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        # Get summary
        summary = await notification_service.get_notification_summary(
            user_id=current_user["id"],
            workspace_id=workspace_id
        )
        
        return summary
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get notification summary: {str(e)}")

@router.get("/{notification_id}", response_model=Notification)
async def get_notification(
    notification_id: str = Path(..., description="The ID of the notification to retrieve"),
    current_user: dict = Depends(get_current_user)
):
    """
    Get a specific notification by ID.
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        # Get notification
        notification = await notification_service.get_notification_by_id(notification_id)
        
        # Verify user has access to this notification
        if notification.user_id != current_user["id"]:
            raise HTTPException(status_code=403, detail="Access denied to this notification")
        
        return notification
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get notification: {str(e)}")

@router.post("/", response_model=Notification)
async def create_notification(
    notification_data: NotificationCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Create a new notification.
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        # Create notification
        notification = await notification_service.create_notification(notification_data)
        
        return notification
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create notification: {str(e)}")

@router.patch("/{notification_id}", response_model=Notification)
async def update_notification(
    notification_id: str = Path(..., description="The ID of the notification to update"),
    update_data: NotificationUpdate = ...,
    current_user: dict = Depends(get_current_user)
):
    """
    Update a notification (typically to mark as read/unread).
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        # Update notification
        notification = await notification_service.update_notification(
            notification_id=notification_id,
            user_id=current_user["id"],
            update_data=update_data
        )
        
        return notification
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update notification: {str(e)}")

@router.patch("/{notification_id}/read", response_model=Notification)
async def mark_notification_as_read(
    notification_id: str = Path(..., description="The ID of the notification to mark as read"),
    current_user: dict = Depends(get_current_user)
):
    """
    Mark a specific notification as read.
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        # Mark as read
        notification = await notification_service.mark_as_read(
            notification_id=notification_id,
            user_id=current_user["id"]
        )
        
        return notification
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to mark notification as read: {str(e)}")

@router.patch("/mark-all-read")
async def mark_all_notifications_as_read(
    workspace_id: str = Query(..., description="Workspace ID to mark all notifications as read"),
    current_user: dict = Depends(get_current_user)
):
    """
    Mark all notifications as read for the current user in a workspace.
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        # Mark all as read
        count = await notification_service.mark_all_as_read(
            user_id=current_user["id"],
            workspace_id=workspace_id
        )
        
        return {"message": f"Marked {count} notifications as read"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to mark all notifications as read: {str(e)}")

@router.delete("/{notification_id}")
async def delete_notification(
    notification_id: str = Path(..., description="The ID of the notification to delete"),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete a specific notification.
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        # Delete notification
        success = await notification_service.delete_notification(
            notification_id=notification_id,
            user_id=current_user["id"]
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Notification not found")
        
        return {"message": "Notification deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete notification: {str(e)}")

@router.delete("/")
async def delete_all_notifications(
    workspace_id: str = Query(..., description="Workspace ID to delete all notifications"),
    current_user: dict = Depends(get_current_user)
):
    """
    Delete all notifications for the current user in a workspace.
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        # Delete all notifications
        count = await notification_service.delete_all_notifications(
            user_id=current_user["id"],
            workspace_id=workspace_id
        )
        
        return {"message": f"Deleted {count} notifications"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete all notifications: {str(e)}")

@router.patch("/bulk-update")
async def bulk_update_notifications(
    bulk_update: NotificationBulkUpdate,
    current_user: dict = Depends(get_current_user)
):
    """
    Bulk update multiple notifications.
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        updated_notifications = []
        for notification_id in bulk_update.notification_ids:
            try:
                update_data = NotificationUpdate(status=bulk_update.status)
                notification = await notification_service.update_notification(
                    notification_id=notification_id,
                    user_id=current_user["id"],
                    update_data=update_data
                )
                updated_notifications.append(notification)
            except Exception as e:
                # Log error but continue with other notifications
                continue
        
        return {
            "message": f"Updated {len(updated_notifications)} notifications",
            "updated_count": len(updated_notifications),
            "requested_count": len(bulk_update.notification_ids)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to bulk update notifications: {str(e)}")

@router.delete("/bulk-delete")
async def bulk_delete_notifications(
    bulk_delete: NotificationBulkDelete,
    current_user: dict = Depends(get_current_user)
):
    """
    Bulk delete multiple notifications.
    """
    try:
        # Create notification service with authenticated client
        supabase = get_authenticated_db_client(current_user)
        notification_service = NotificationService(supabase)
        
        deleted_count = 0
        for notification_id in bulk_delete.notification_ids:
            try:
                success = await notification_service.delete_notification(
                    notification_id=notification_id,
                    user_id=current_user["id"]
                )
                if success:
                    deleted_count += 1
            except Exception as e:
                # Log error but continue with other notifications
                continue
        
        return {
            "message": f"Deleted {deleted_count} notifications",
            "deleted_count": deleted_count,
            "requested_count": len(bulk_delete.notification_ids)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to bulk delete notifications: {str(e)}")
