"""
AI Health Check API endpoints for monitoring AI service status
"""

from fastapi import APIRouter, Depends
from datetime import datetime
from app.core.auth import get_current_user
from app.services.ai_service import ai_service
from app.services.cache_service import ai_cache
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/health", response_model=dict)
async def get_ai_health(current_user: dict = Depends(get_current_user)):
    """
    Get the health status of the AI service and its providers.
    """
    try:
        # Check provider availability
        huggingface_available = bool(settings.HUGGINGFACE_API_KEY)
        gemini_available = bool(settings.GEMINI_API_KEY)
        
        # Determine overall status
        if huggingface_available and gemini_available:
            status = "healthy"
        elif huggingface_available or gemini_available:
            status = "degraded"
        else:
            status = "limited"  # Fallback mode only
        
        # Get cache statistics
        cache_stats = ai_cache.get_stats()
        
        # Test basic AI functionality with a simple contract
        test_contract = "This is a simple test contract for health check purposes."
        
        try:
            test_start = datetime.now()
            test_result = await ai_service.analyze_contract(test_contract, "contract_analysis")
            test_duration = (datetime.now() - test_start).total_seconds()
            
            analysis_working = True
            last_successful_analysis = datetime.now()
            average_response_time = test_duration
            
        except Exception as e:
            logger.warning(f"AI health check analysis failed: {e}")
            analysis_working = False
            last_successful_analysis = None
            average_response_time = None
        
        return {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "providers": {
                "huggingface": {
                    "available": huggingface_available,
                    "configured": huggingface_available,
                    "status": "ready" if huggingface_available else "not_configured"
                },
                "gemini": {
                    "available": gemini_available,
                    "configured": gemini_available,
                    "status": "ready" if gemini_available else "not_configured"
                }
            },
            "cache": {
                "status": "operational",
                "stats": cache_stats,
                "type": type(ai_cache).__name__
            },
            "analysis": {
                "working": analysis_working,
                "last_successful": last_successful_analysis.isoformat() if last_successful_analysis else None,
                "average_response_time": average_response_time
            },
            "configuration": {
                "environment": settings.ENVIRONMENT,
                "cache_ttl": settings.AI_CACHE_TTL,
                "max_retries": settings.AI_MAX_RETRIES,
                "timeout": settings.AI_TIMEOUT
            }
        }
        
    except Exception as e:
        logger.error(f"AI health check failed: {e}")
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "providers": {
                "huggingface": {"status": "unknown"},
                "gemini": {"status": "unknown"}
            },
            "cache": {"status": "unknown"},
            "analysis": {"working": False}
        }


@router.post("/test", response_model=dict)
async def test_ai_analysis(
    test_text: str = "This is a test contract for AI analysis verification.",
    current_user: dict = Depends(get_current_user)
):
    """
    Test AI analysis with custom text.
    """
    try:
        start_time = datetime.now()
        result = await ai_service.analyze_contract(test_text, "contract_analysis")
        end_time = datetime.now()
        
        processing_time = (end_time - start_time).total_seconds()
        
        return {
            "success": True,
            "processing_time": processing_time,
            "provider_used": result.provider,
            "confidence": result.confidence,
            "contract_type": result.contract_type,
            "risk_score": result.risk_score,
            "test_text_length": len(test_text),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"AI test analysis failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.get("/stats", response_model=dict)
async def get_ai_stats(current_user: dict = Depends(get_current_user)):
    """
    Get AI service usage statistics.
    """
    try:
        cache_stats = ai_cache.get_stats()
        
        return {
            "cache_statistics": cache_stats,
            "service_configuration": {
                "primary_provider": "HuggingFace",
                "fallback_provider": "Gemini",
                "cache_ttl": settings.AI_CACHE_TTL,
                "max_retries": settings.AI_MAX_RETRIES,
                "timeout": settings.AI_TIMEOUT
            },
            "provider_status": {
                "huggingface_configured": bool(settings.HUGGINGFACE_API_KEY),
                "gemini_configured": bool(settings.GEMINI_API_KEY)
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get AI stats: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.post("/cache/clear", response_model=dict)
async def clear_ai_cache(current_user: dict = Depends(get_current_user)):
    """
    Clear the AI analysis cache.
    """
    try:
        await ai_cache.clear()
        
        return {
            "success": True,
            "message": "AI cache cleared successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to clear AI cache: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.post("/cache/cleanup", response_model=dict)
async def cleanup_ai_cache(current_user: dict = Depends(get_current_user)):
    """
    Clean up expired entries from the AI cache.
    """
    try:
        if hasattr(ai_cache, 'cleanup_expired'):
            cleaned_count = await ai_cache.cleanup_expired()
            return {
                "success": True,
                "cleaned_entries": cleaned_count,
                "message": f"Cleaned up {cleaned_count} expired cache entries",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": True,
                "message": "Cache cleanup not needed for this cache type",
                "timestamp": datetime.now().isoformat()
            }
        
    except Exception as e:
        logger.error(f"Failed to cleanup AI cache: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
