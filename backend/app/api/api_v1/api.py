from fastapi import APIRouter
from app.api.api_v1.endpoints import contracts, workspaces, users, templates, signers, clauses, documents, storage, analytics, demo, clerk_webhooks, export_history, monitoring, notifications, folders, ai_health, approval_workflows, contract_lifecycle, document_processing, secure_ai, clause_analysis, batch_ai, performance, enhanced_lifecycle, enhanced_repository, websocket, contract_analytics, rate_limiting, rls_audit, audit_logging, api_testing, workflow_templates, workflow_escalations

api_router = APIRouter()

api_router.include_router(contracts.router, prefix="/contracts", tags=["contracts"])
api_router.include_router(workspaces.router, prefix="/workspaces", tags=["workspaces"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(templates.router, prefix="/templates", tags=["templates"])
api_router.include_router(documents.router, prefix="/documents", tags=["documents"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
api_router.include_router(signers.router, tags=["signers"])
api_router.include_router(clauses.router, tags=["clauses"])
api_router.include_router(storage.router, prefix="/storage", tags=["storage"])
api_router.include_router(notifications.router, prefix="/notifications", tags=["notifications"])
api_router.include_router(demo.router, prefix="/demo", tags=["demo"])
api_router.include_router(clerk_webhooks.router, prefix="/clerk-webhooks", tags=["clerk-webhooks"])
api_router.include_router(export_history.router, prefix="/export-history", tags=["export-history"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["monitoring"])
api_router.include_router(folders.router, prefix="/folders", tags=["folders"])
api_router.include_router(ai_health.router, prefix="/ai", tags=["ai-health"])
api_router.include_router(approval_workflows.router, prefix="/approval-workflows", tags=["approval-workflows"])
api_router.include_router(contract_lifecycle.router, prefix="/lifecycle", tags=["contract-lifecycle"])
api_router.include_router(document_processing.router, prefix="/document-processing", tags=["document-processing"])
api_router.include_router(secure_ai.router, prefix="/secure-ai", tags=["secure-ai"])
api_router.include_router(clause_analysis.router, prefix="/clause-analysis", tags=["clause-analysis"])
api_router.include_router(batch_ai.router, prefix="/batch-ai", tags=["batch-ai"])
api_router.include_router(performance.router, prefix="/performance", tags=["performance"])
api_router.include_router(enhanced_lifecycle.router, prefix="/enhanced-lifecycle", tags=["enhanced-lifecycle"])
api_router.include_router(enhanced_repository.router, prefix="/enhanced-repository", tags=["enhanced-repository"])
api_router.include_router(websocket.router, prefix="/realtime", tags=["realtime"])
api_router.include_router(contract_analytics.router, prefix="/contract-analytics", tags=["contract-analytics"])
api_router.include_router(rate_limiting.router, prefix="/rate-limiting", tags=["rate-limiting"])
api_router.include_router(rls_audit.router, prefix="/rls-audit", tags=["rls-audit"])
api_router.include_router(audit_logging.router, prefix="/audit-logging", tags=["audit-logging"])
api_router.include_router(api_testing.router, prefix="/api-testing", tags=["api-testing"])
api_router.include_router(workflow_templates.router, prefix="/workflow-templates", tags=["workflow-templates"])
api_router.include_router(workflow_escalations.router, prefix="/workflow-escalations", tags=["workflow-escalations"])
