"""
Core module for Averum Contracts backend application.
Contains authentication, configuration, and security utilities.
"""

from .config import settings
from .auth import get_current_user, validate_workspace_access, get_optional_current_user, get_authenticated_db_client

__all__ = [
    "settings",
    "get_current_user",
    "validate_workspace_access",
    "get_optional_current_user",
    "get_authenticated_db_client"
]