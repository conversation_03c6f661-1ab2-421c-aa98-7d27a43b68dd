"""
Custom exceptions and error handling for the notification system.
"""

import logging
from typing import Op<PERSON>, Dict, Any
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)

class NotificationError(Exception):
    """Base exception for notification-related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)

class NotificationNotFoundError(NotificationError):
    """Raised when a notification is not found."""
    pass

class NotificationPermissionError(NotificationError):
    """Raised when user doesn't have permission to access a notification."""
    pass

class NotificationValidationError(NotificationError):
    """Raised when notification data validation fails."""
    pass

class NotificationServiceError(NotificationError):
    """Raised when notification service operations fail."""
    pass

class NotificationWebSocketError(NotificationError):
    """Raised when WebSocket notification operations fail."""
    pass

class NotificationRateLimitError(NotificationError):
    """Raised when notification rate limits are exceeded."""
    pass

def handle_notification_error(error: Exception, operation: str, context: Optional[Dict[str, Any]] = None) -> HTTPException:
    """
    Convert notification errors to appropriate HTTP exceptions.
    """
    context = context or {}
    
    # Log the error with context
    logger.error(f"Notification {operation} error: {str(error)}", extra={
        "operation": operation,
        "error_type": type(error).__name__,
        "context": context
    })
    
    if isinstance(error, NotificationNotFoundError):
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "error": "Notification not found",
                "message": error.message,
                "operation": operation,
                **error.details
            }
        )
    
    elif isinstance(error, NotificationPermissionError):
        return HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={
                "error": "Permission denied",
                "message": error.message,
                "operation": operation,
                **error.details
            }
        )
    
    elif isinstance(error, NotificationValidationError):
        return HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail={
                "error": "Validation error",
                "message": error.message,
                "operation": operation,
                **error.details
            }
        )
    
    elif isinstance(error, NotificationRateLimitError):
        return HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail={
                "error": "Rate limit exceeded",
                "message": error.message,
                "operation": operation,
                **error.details
            }
        )
    
    elif isinstance(error, NotificationWebSocketError):
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "WebSocket error",
                "message": error.message,
                "operation": operation,
                **error.details
            }
        )
    
    elif isinstance(error, NotificationServiceError):
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Service error",
                "message": error.message,
                "operation": operation,
                **error.details
            }
        )
    
    else:
        # Generic error handling
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "Internal server error",
                "message": "An unexpected error occurred",
                "operation": operation
            }
        )

def log_notification_operation(operation: str, user_id: str, workspace_id: str, details: Optional[Dict[str, Any]] = None):
    """
    Log notification operations for monitoring and debugging.
    """
    logger.info(f"Notification {operation}", extra={
        "operation": operation,
        "user_id": user_id,
        "workspace_id": workspace_id,
        "details": details or {}
    })

def log_notification_error(operation: str, error: Exception, user_id: Optional[str] = None, workspace_id: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
    """
    Log notification errors with context.
    """
    logger.error(f"Notification {operation} failed: {str(error)}", extra={
        "operation": operation,
        "error_type": type(error).__name__,
        "user_id": user_id,
        "workspace_id": workspace_id,
        "details": details or {}
    })

def validate_notification_data(data: Dict[str, Any]) -> None:
    """
    Validate notification data and raise ValidationError if invalid.
    """
    required_fields = ["title", "message", "type", "user_id", "workspace_id"]
    
    for field in required_fields:
        if field not in data or not data[field]:
            raise NotificationValidationError(
                f"Missing required field: {field}",
                {"field": field, "data": data}
            )
    
    # Validate notification type
    valid_types = ["approval", "contract", "system", "mention"]
    if data["type"] not in valid_types:
        raise NotificationValidationError(
            f"Invalid notification type: {data['type']}",
            {"valid_types": valid_types, "provided_type": data["type"]}
        )
    
    # Validate title and message length
    if len(data["title"]) > 255:
        raise NotificationValidationError(
            "Title too long (max 255 characters)",
            {"title_length": len(data["title"]), "max_length": 255}
        )
    
    if len(data["message"]) > 1000:
        raise NotificationValidationError(
            "Message too long (max 1000 characters)",
            {"message_length": len(data["message"]), "max_length": 1000}
        )

def check_notification_permissions(user_id: str, notification_user_id: str, workspace_id: str, user_workspaces: list) -> None:
    """
    Check if user has permission to access a notification.
    """
    # User can only access their own notifications
    if user_id != notification_user_id:
        raise NotificationPermissionError(
            "You can only access your own notifications",
            {"user_id": user_id, "notification_user_id": notification_user_id}
        )
    
    # User must be a member of the workspace
    if workspace_id not in user_workspaces:
        raise NotificationPermissionError(
            "You don't have access to this workspace",
            {"user_id": user_id, "workspace_id": workspace_id, "user_workspaces": user_workspaces}
        )

class NotificationErrorHandler:
    """
    Context manager for handling notification operations with proper error handling.
    """
    
    def __init__(self, operation: str, user_id: Optional[str] = None, workspace_id: Optional[str] = None):
        self.operation = operation
        self.user_id = user_id
        self.workspace_id = workspace_id
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            log_notification_error(
                self.operation,
                exc_val,
                self.user_id,
                self.workspace_id
            )
        return False  # Don't suppress exceptions
    
    def log_success(self, details: Optional[Dict[str, Any]] = None):
        """Log successful operation."""
        if self.user_id and self.workspace_id:
            log_notification_operation(
                self.operation,
                self.user_id,
                self.workspace_id,
                details
            )

# Decorator for notification endpoint error handling
def handle_notification_errors(operation: str):
    """
    Decorator to handle errors in notification endpoints.
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except NotificationError as e:
                raise handle_notification_error(e, operation)
            except Exception as e:
                logger.error(f"Unexpected error in {operation}: {str(e)}")
                raise handle_notification_error(e, operation)
        return wrapper
    return decorator
