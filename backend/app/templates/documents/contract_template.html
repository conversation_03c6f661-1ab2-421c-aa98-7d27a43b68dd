<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ contract.title }}</title>
    <style>
        @page {
            size: A4;
            margin: 1in;
            @top-center {
                content: "{{ branding.company_name }}";
                font-size: 10pt;
                color: {{ branding.color_scheme.secondary }};
            }
            @bottom-center {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 10pt;
                color: {{ branding.color_scheme.secondary }};
            }
        }
        
        body {
            font-family: {{ branding.fonts.body }};
            font-size: 12pt;
            line-height: 1.6;
            color: {{ branding.color_scheme.primary }};
            margin: 0;
            padding: 0;
        }
        
        .letterhead {
            text-align: center;
            margin-bottom: 2em;
            border-bottom: 2px solid {{ branding.color_scheme.primary }};
            padding-bottom: 1em;
        }
        
        .company-name {
            font-size: 18pt;
            font-weight: bold;
            color: {{ branding.color_scheme.primary }};
            margin: 0;
        }
        
        h1 {
            font-family: {{ branding.fonts.heading }};
            font-size: 20pt;
            font-weight: bold;
            text-align: center;
            color: {{ branding.color_scheme.primary }};
            margin: 1em 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        h2 {
            font-family: {{ branding.fonts.heading }};
            font-size: 16pt;
            font-weight: bold;
            color: {{ branding.color_scheme.primary }};
            margin: 1.5em 0 0.5em 0;
            border-bottom: 1px solid {{ branding.color_scheme.secondary }};
            padding-bottom: 0.2em;
        }
        
        h3, h4 {
            font-family: {{ branding.fonts.heading }};
            color: {{ branding.color_scheme.primary }};
            margin: 1em 0 0.5em 0;
        }
        
        .contract-meta {
            text-align: center;
            margin: 1em 0;
            padding: 1em;
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
        }
        
        .party-block {
            margin: 1em 0;
            padding: 1em;
            border: 1px solid #e2e8f0;
            background-color: #f8fafc;
        }
        
        .clause {
            margin: 1.5em 0;
            padding: 1em;
            border-left: 3px solid {{ branding.color_scheme.accent }};
            padding-left: 1.5em;
        }
        
        .signature-section {
            margin-top: 3em;
            page-break-inside: avoid;
        }
        
        .signature-block {
            margin: 2em 0;
            padding: 1em;
            border: 1px solid #e2e8f0;
            display: inline-block;
            width: 45%;
            vertical-align: top;
        }
        
        .signature-line p {
            margin: 0.5em 0;
            border-bottom: 1px solid #000;
            padding-bottom: 0.2em;
            min-height: 1.5em;
        }
        
        .footer {
            margin-top: 2em;
            text-align: center;
            font-size: 10pt;
            color: {{ branding.color_scheme.secondary }};
            border-top: 1px solid #e2e8f0;
            padding-top: 1em;
        }
    </style>
</head>
<body>
    <div class="contract-document">
        {% if branding.letterhead %}
        <div class="letterhead">
            {% if branding.company_logo %}
            <img src="{{ branding.company_logo }}" alt="Company Logo" class="logo" style="max-height: 60px; margin-bottom: 10px;">
            {% endif %}
            <h2 class="company-name">{{ branding.company_name }}</h2>
        </div>
        {% endif %}
        
        <div class="contract-header">
            <h1>{{ contract.title }}</h1>
            <div class="contract-meta">
                <p><strong>Contract Type:</strong> {{ contract.type or 'N/A' }}</p>
                <p><strong>Effective Date:</strong> {{ contract.effective_date_formatted or 'N/A' }}</p>
                <p><strong>Contract Number:</strong> {{ contract.contract_number or 'N/A' }}</p>
            </div>
        </div>
        
        <div class="parties-section">
            <h2>Parties</h2>
            {% for party in contract.formatted_parties %}
            <div class="party-block">
                <h3>Party {{ party.letter }}: {{ party.name }}</h3>
                <p><strong>Type:</strong> {{ party.type.title() }}</p>
                <p><strong>Address:</strong> {{ party.address or 'N/A' }}</p>
                {% if party.representative %}
                <p><strong>Representative:</strong> {{ party.representative }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="terms-section">
            <h2>Terms and Conditions</h2>
            {% if contract.description %}
            <div class="description">
                <p>{{ contract.description }}</p>
            </div>
            {% endif %}
            
            {% for clause in contract.formatted_clauses %}
            <div class="clause">
                <h4>{{ clause.number }}. {{ clause.title }}</h4>
                <p>{{ clause.content }}</p>
            </div>
            {% endfor %}
        </div>
        
        <div class="signature-section">
            <h2>Signatures</h2>
            <p>IN WITNESS WHEREOF, the parties hereto have executed this Agreement as of the Effective Date first written above.</p>
            
            <div class="signature-blocks">
                {% for party in contract.formatted_parties %}
                <div class="signature-block">
                    <h4>Party {{ party.letter }}: {{ party.name }}</h4>
                    <div class="signature-line">
                        <p>Signature: _________________________</p>
                        <p>Date: _________________________</p>
                        {% if party.name %}
                        <p>Name: {{ party.name }}</p>
                        {% endif %}
                        {% if party.title %}
                        <p>Title: {{ party.title }}</p>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="footer">
            <p>{{ branding.footer_text }}</p>
            <p>Generated on: {{ generation.formatted_timestamp }}</p>
        </div>
    </div>
</body>
</html>
