"""
Standardized response utilities for consistent API responses.
"""

from typing import Any, Optional, List, Dict
from fastapi import HTTPException
from pydantic import BaseModel


class ApiResponse(BaseModel):
    """Standardized API response format."""
    data: Any
    status: int = 200
    message: str = "Success"
    meta: Optional[Dict[str, Any]] = None


class ApiError(BaseModel):
    """Standardized API error format."""
    detail: str
    status: int
    message: str
    code: Optional[str] = None
    timestamp: Optional[str] = None


def handle_supabase_response(
    response, 
    error_message: str = "Database operation failed",
    success_message: str = "Success"
) -> Any:
    """
    Helper function to handle Supabase response objects consistently.
    Returns the raw data for direct use in endpoints.
    """
    has_error = hasattr(response, 'error') and response.error
    has_data = hasattr(response, 'data') and response.data is not None

    if has_error:
        raise HTTPException(
            status_code=400, 
            detail=f"{error_message}: {response.error}"
        )

    return response.data if has_data else []


def create_response(
    data: Any,
    status: int = 200,
    message: str = "Success",
    meta: Optional[Dict[str, Any]] = None
) -> ApiResponse:
    """
    Create a standardized API response.
    
    Args:
        data: The response data
        status: HTTP status code
        message: Success message
        meta: Optional metadata (pagination, etc.)
    
    Returns:
        ApiResponse object
    """
    return ApiResponse(
        data=data,
        status=status,
        message=message,
        meta=meta
    )


def create_success_response(
    data: Any,
    message: str = "Success",
    meta: Optional[Dict[str, Any]] = None
) -> ApiResponse:
    """Create a success response (200)."""
    return create_response(data, 200, message, meta)


def create_created_response(
    data: Any,
    message: str = "Created successfully",
    meta: Optional[Dict[str, Any]] = None
) -> ApiResponse:
    """Create a created response (201)."""
    return create_response(data, 201, message, meta)


def create_paginated_response(
    data: List[Any],
    total: int,
    page: int = 1,
    limit: int = 100,
    message: str = "Success"
) -> ApiResponse:
    """
    Create a paginated response with metadata.
    
    Args:
        data: List of items
        total: Total number of items
        page: Current page number
        limit: Items per page
        message: Success message
    
    Returns:
        ApiResponse with pagination metadata
    """
    meta = {
        "pagination": {
            "total": total,
            "page": page,
            "limit": limit,
            "pages": (total + limit - 1) // limit,  # Ceiling division
            "has_next": page * limit < total,
            "has_prev": page > 1
        }
    }
    
    return create_response(data, 200, message, meta)


def create_error_response(
    message: str,
    status: int = 400,
    code: Optional[str] = None,
    details: Optional[str] = None
) -> HTTPException:
    """
    Create a standardized error response.
    
    Args:
        message: Error message
        status: HTTP status code
        code: Optional error code
        details: Optional error details
    
    Returns:
        HTTPException with standardized format
    """
    error_detail = {
        "detail": message,
        "status": status,
        "message": message,
        "code": code,
        "timestamp": None  # Will be set by middleware if needed
    }
    
    if details:
        error_detail["details"] = details
    
    return HTTPException(status_code=status, detail=error_detail)


# Common error responses
def not_found_error(resource: str = "Resource") -> HTTPException:
    """Create a 404 not found error."""
    return create_error_response(
        f"{resource} not found",
        404,
        "NOT_FOUND"
    )


def unauthorized_error(message: str = "Unauthorized") -> HTTPException:
    """Create a 401 unauthorized error."""
    return create_error_response(
        message,
        401,
        "UNAUTHORIZED"
    )


def forbidden_error(message: str = "Forbidden") -> HTTPException:
    """Create a 403 forbidden error."""
    return create_error_response(
        message,
        403,
        "FORBIDDEN"
    )


def validation_error(message: str = "Validation failed") -> HTTPException:
    """Create a 422 validation error."""
    return create_error_response(
        message,
        422,
        "VALIDATION_ERROR"
    )


def server_error(message: str = "Internal server error") -> HTTPException:
    """Create a 500 server error."""
    return create_error_response(
        message,
        500,
        "INTERNAL_ERROR"
    )
