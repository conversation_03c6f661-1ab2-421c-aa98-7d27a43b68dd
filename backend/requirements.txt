# Core FastAPI Dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.23.2
pydantic>=2.4.2
python-dotenv>=1.0.0
httpx>=0.25.0
python-jose[cryptography]>=3.3.0
python-multipart>=0.0.6
pydantic-settings>=2.0.3

# Database and Storage
supabase>=2.0.0

# Rate Limiting and Monitoring
slowapi>=0.1.9
psutil>=5.9.0

# Basic Document Generation (lightweight alternatives)
reportlab>=4.0.0
jinja2>=3.1.2
markdown>=3.5.1

# Document Processing (essential only)
PyPDF2>=3.0.1

# Security
cryptography>=41.0.0

# Optional Dependencies (commented out for Railway deployment)
# redis>=5.0.0  # Enable if you add Redis service
# weasyprint>=60.0  # Requires system dependencies
# python-docx>=1.1.0  # Heavy dependency
# pdfplumber>=0.9.0  # Heavy dependency
# nltk>=3.8.1  # Heavy dependency

# Testing Dependencies (only for development)
# pytest>=7.4.2
# pytest-asyncio>=0.21.1
