# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# Clerk Configuration
CLERK_SECRET_KEY=your_clerk_secret_key
CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key

# API Configuration
API_PREFIX=/api
BACKEND_CORS_ORIGINS=["http://localhost:5173", "http://localhost:5174", "http://localhost:3000"]

# Environment
ENVIRONMENT=development

# Clerk Webhook Configuration (optional)
CLERK_WEBHOOK_SECRET=your_clerk_webhook_secret

# AI Service Configuration
GEMINI_API_KEY=your_gemini_api_key
HUGGINGFACE_API_KEY=your_huggingface_api_key

# Redis Configuration (for rate limiting and caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# WebSocket Configuration
VITE_WS_BASE_URL=ws://localhost:8000

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_LEVEL=INFO

# Rate Limiting Configuration
RATE_LIMITING_ENABLED=true
DEFAULT_RATE_LIMIT=100
DEFAULT_RATE_WINDOW=3600

# Audit Logging Configuration
AUDIT_LOGGING_ENABLED=true
AUDIT_RETENTION_DAYS=2555

# Testing Configuration
TEST_BASE_URL=http://localhost:8000
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=.r3l+_Ajj
