#!/usr/bin/env python3
"""
Setup script for development user
"""
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.db.database import get_supabase_client

def setup_development_user():
    """Setup the development user and add them to workspaces"""
    try:
        supabase = get_supabase_client()
        user_id = 'user_2qKJKJKJKJKJKJKJKJKJKJKJKJKJ'
        email = '<EMAIL>'
        
        print("Setting up development user...")
        
        # First, delete any existing user with the same email
        try:
            delete_result = supabase.table('users').delete().eq('email', email).execute()
            print(f"Deleted existing users with email {email}")
        except Exception as e:
            print(f"No existing user to delete: {e}")
        
        # Create the development user
        user_data = {
            'id': user_id,
            'email': email,
            'first_name': '<PERSON>',
            'last_name': '<PERSON>oma<PERSON>',
            'initials': 'KW',
            'notification_preferences': {
                'email_approvals': True,
                'email_updates': True,
                'email_reminders': True,
                'email_comments': True,
                'system_approvals': True,
                'browser_notifications': True,
                'email_digest_frequency': 'daily'
            }
        }
        
        user_result = supabase.table('users').insert(user_data).execute()
        print(f"Created development user: {user_result.data}")
        
        # Get all workspaces
        workspaces_result = supabase.table('workspaces').select('id, name').execute()
        workspaces = workspaces_result.data
        
        print(f"Found {len(workspaces)} workspaces")
        
        # Add user to all workspaces as administrator
        for workspace in workspaces:
            workspace_id = workspace['id']
            workspace_name = workspace['name']
            
            try:
                # Check if membership already exists
                existing = supabase.table('workspace_members').select('*').eq('workspace_id', workspace_id).eq('user_id', user_id).execute()
                
                if not existing.data:
                    membership_data = {
                        'workspace_id': workspace_id,
                        'user_id': user_id,
                        'role_id': 'administrator'
                    }
                    
                    membership_result = supabase.table('workspace_members').insert(membership_data).execute()
                    print(f"Added user to workspace '{workspace_name}' ({workspace_id})")
                else:
                    print(f"User already member of workspace '{workspace_name}' ({workspace_id})")
                    
            except Exception as e:
                print(f"Error adding user to workspace '{workspace_name}': {e}")
        
        print("Development user setup completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error setting up development user: {e}")
        return False

if __name__ == "__main__":
    success = setup_development_user()
    sys.exit(0 if success else 1)
