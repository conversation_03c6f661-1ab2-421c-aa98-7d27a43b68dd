#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the FastAPI backend with the correct environment variables.
"""

import os
import sys
import subprocess
import platform

def kill_process_on_port(port):
    """Kill the process running on the specified port."""
    system = platform.system()
    
    try:
        if system == "Windows":
            # Windows
            result = subprocess.run(
                f"netstat -ano | findstr :{port}",
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                # Extract PID from the output
                for line in result.stdout.strip().split('\n'):
                    if f":{port}" in line:
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            pid = parts[4]
                            print(f"Killing process {pid} running on port {port}...")
                            subprocess.run(f"taskkill /F /PID {pid}", shell=True)
                            print(f"Process {pid} killed.")
                            return True
            
            print(f"No process found running on port {port}.")
            return False
            
        else:
            # macOS/Linux
            result = subprocess.run(
                f"lsof -ti:{port}",
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.stdout:
                pid = result.stdout.strip()
                print(f"Killing process {pid} running on port {port}...")
                subprocess.run(f"kill -9 {pid}", shell=True)
                print(f"Process {pid} killed.")
                return True
            
            print(f"No process found running on port {port}.")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False

def run_backend():
    """Run the FastAPI backend with the correct environment variables."""
    # Kill any process running on port 8000
    kill_process_on_port(8000)
    
    # Set environment variables
    env = os.environ.copy()
    
    # Required environment variables for the backend
    env["SUPABASE_URL"] = "https://kdcjdbufciuvvznqnotx.supabase.co"
    env["SUPABASE_KEY"] = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtkY2pkYnVmY2l1dnZ6bnFub3R4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjEwMjE2OSwiZXhwIjoyMDYxNjc4MTY5fQ.3uqZ5bsCPA1R-T42hREnRQ6oO5U39yipnUGghANd83E"
    env["CLERK_SECRET_KEY"] = "test_clerk_secret_key"
    env["CLERK_PUBLISHABLE_KEY"] = "pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ"
    env["API_PREFIX"] = "/api"
    env["BACKEND_CORS_ORIGINS"] = '["http://localhost:5173", "http://localhost:3000"]'
    env["ENVIRONMENT"] = "development"
    
    # Run the backend
    print("Starting the backend...")
    
    # Use the correct Python command based on the system
    python_cmd = "python" if platform.system() == "Windows" else "python3"
    
    # Run the backend
    subprocess.run([python_cmd, "run.py"], env=env)

if __name__ == "__main__":
    run_backend()
