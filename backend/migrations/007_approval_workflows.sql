-- Migration: 007_approval_workflows.sql
-- Description: Create approval workflow system tables
-- Date: 2024-01-01

-- Create approval_workflows table
CREATE TABLE IF NOT EXISTS approval_workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    workflow_type VARCHAR(20) NOT NULL CHECK (workflow_type IN ('sequential', 'parallel', 'conditional', 'hybrid')),
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'completed', 'rejected', 'cancelled', 'escalated')),
    priority VARCHAR(10) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    created_by UUID NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    due_date TIMESTAMPTZ,
    escalation_rules JSONB DEFAULT '{}',
    conditions JSONB DEFAULT '{}',
    template_id UUID,
    rejection_reason TEXT,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create approvals table
CREATE TABLE IF NOT EXISTS approvals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES approval_workflows(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    order_index INTEGER NOT NULL DEFAULT 1,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'escalated', 'expired', 'skipped')),
    required BOOLEAN NOT NULL DEFAULT true,
    role VARCHAR(50) NOT NULL DEFAULT 'approver',
    conditions JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    approved_at TIMESTAMPTZ,
    rejected_at TIMESTAMPTZ,
    comments TEXT,
    approved_by UUID,
    rejected_by UUID,
    escalated_to UUID,
    escalated_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create workflow_templates table
CREATE TABLE IF NOT EXISTS workflow_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    workflow_type VARCHAR(20) NOT NULL CHECK (workflow_type IN ('sequential', 'parallel', 'conditional', 'hybrid')),
    approvers JSONB NOT NULL DEFAULT '[]',
    escalation_rules JSONB DEFAULT '{}',
    conditions JSONB DEFAULT '[]',
    default_due_hours INTEGER DEFAULT 48,
    created_by UUID NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- Create approval_history table for audit trail
CREATE TABLE IF NOT EXISTS approval_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    approval_id UUID NOT NULL REFERENCES approvals(id) ON DELETE CASCADE,
    workflow_id UUID NOT NULL REFERENCES approval_workflows(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL,
    performed_by UUID NOT NULL,
    performed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    previous_status VARCHAR(20),
    new_status VARCHAR(20),
    comments TEXT,
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_approval_workflows_contract_id ON approval_workflows(contract_id);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_workspace_id ON approval_workflows(workspace_id);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_status ON approval_workflows(status);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_created_by ON approval_workflows(created_by);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_due_date ON approval_workflows(due_date);

CREATE INDEX IF NOT EXISTS idx_approvals_workflow_id ON approvals(workflow_id);
CREATE INDEX IF NOT EXISTS idx_approvals_user_id ON approvals(user_id);
CREATE INDEX IF NOT EXISTS idx_approvals_status ON approvals(status);
CREATE INDEX IF NOT EXISTS idx_approvals_order ON approvals(workflow_id, order_index);

CREATE INDEX IF NOT EXISTS idx_workflow_templates_workspace_id ON workflow_templates(workspace_id);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_active ON workflow_templates(is_active);

CREATE INDEX IF NOT EXISTS idx_approval_history_approval_id ON approval_history(approval_id);
CREATE INDEX IF NOT EXISTS idx_approval_history_workflow_id ON approval_history(workflow_id);
CREATE INDEX IF NOT EXISTS idx_approval_history_performed_by ON approval_history(performed_by);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_approval_workflows_updated_at 
    BEFORE UPDATE ON approval_workflows 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_approvals_updated_at 
    BEFORE UPDATE ON approvals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflow_templates_updated_at 
    BEFORE UPDATE ON workflow_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create RLS policies for security
ALTER TABLE approval_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE approval_history ENABLE ROW LEVEL SECURITY;

-- RLS policies for approval_workflows
CREATE POLICY "Users can view workflows in their workspaces" ON approval_workflows
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create workflows in their workspaces" ON approval_workflows
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update workflows they created or are assigned to" ON approval_workflows
    FOR UPDATE USING (
        created_by = auth.uid() OR
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid() AND role IN ('admin', 'manager')
        )
    );

-- RLS policies for approvals
CREATE POLICY "Users can view approvals in their workspaces" ON approvals
    FOR SELECT USING (
        workflow_id IN (
            SELECT id FROM approval_workflows 
            WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members 
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can update their own approvals" ON approvals
    FOR UPDATE USING (user_id = auth.uid());

-- RLS policies for workflow_templates
CREATE POLICY "Users can view templates in their workspaces" ON workflow_templates
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage templates in their workspaces" ON workflow_templates
    FOR ALL USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid() AND role IN ('admin', 'manager')
        )
    );

-- RLS policies for approval_history
CREATE POLICY "Users can view approval history in their workspaces" ON approval_history
    FOR SELECT USING (
        workflow_id IN (
            SELECT id FROM approval_workflows 
            WHERE workspace_id IN (
                SELECT workspace_id FROM workspace_members 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Create function to automatically create approval history entries
CREATE OR REPLACE FUNCTION create_approval_history()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create history for status changes
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO approval_history (
            approval_id,
            workflow_id,
            action,
            performed_by,
            previous_status,
            new_status,
            comments
        ) VALUES (
            NEW.id,
            NEW.workflow_id,
            CASE 
                WHEN NEW.status = 'approved' THEN 'approve'
                WHEN NEW.status = 'rejected' THEN 'reject'
                WHEN NEW.status = 'escalated' THEN 'escalate'
                ELSE 'update'
            END,
            COALESCE(NEW.approved_by, NEW.rejected_by, auth.uid()),
            OLD.status,
            NEW.status,
            NEW.comments
        );
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for approval history
CREATE TRIGGER create_approval_history_trigger
    AFTER UPDATE ON approvals
    FOR EACH ROW EXECUTE FUNCTION create_approval_history();

-- Create function to check workflow completion
CREATE OR REPLACE FUNCTION check_workflow_completion()
RETURNS TRIGGER AS $$
DECLARE
    workflow_record approval_workflows%ROWTYPE;
    required_approvals_count INTEGER;
    completed_approvals_count INTEGER;
BEGIN
    -- Get the workflow
    SELECT * INTO workflow_record 
    FROM approval_workflows 
    WHERE id = NEW.workflow_id;
    
    -- Count required approvals
    SELECT COUNT(*) INTO required_approvals_count
    FROM approvals 
    WHERE workflow_id = NEW.workflow_id AND required = true;
    
    -- Count completed approvals
    SELECT COUNT(*) INTO completed_approvals_count
    FROM approvals 
    WHERE workflow_id = NEW.workflow_id 
    AND required = true 
    AND status = 'approved';
    
    -- Check if workflow is complete
    IF completed_approvals_count = required_approvals_count THEN
        UPDATE approval_workflows 
        SET status = 'completed', completed_at = NOW()
        WHERE id = NEW.workflow_id;
        
        -- Update contract status
        UPDATE contracts 
        SET status = 'approved'
        WHERE id = workflow_record.contract_id;
    END IF;
    
    -- Check if workflow is rejected
    IF NEW.status = 'rejected' THEN
        UPDATE approval_workflows 
        SET status = 'rejected', completed_at = NOW()
        WHERE id = NEW.workflow_id;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for workflow completion check
CREATE TRIGGER check_workflow_completion_trigger
    AFTER UPDATE ON approvals
    FOR EACH ROW EXECUTE FUNCTION check_workflow_completion();

-- Insert some default workflow templates
INSERT INTO workflow_templates (name, description, workspace_id, workflow_type, approvers, default_due_hours, created_by, is_active)
SELECT 
    'Standard Contract Approval',
    'Standard approval workflow for most contracts requiring legal and finance approval',
    w.id,
    'sequential',
    '[
        {"user_id": "", "order": 1, "required": true, "role": "legal_reviewer"},
        {"user_id": "", "order": 2, "required": true, "role": "finance_approver"}
    ]'::jsonb,
    48,
    '00000000-0000-0000-0000-000000000000'::uuid,
    true
FROM workspaces w
WHERE NOT EXISTS (
    SELECT 1 FROM workflow_templates 
    WHERE name = 'Standard Contract Approval' AND workspace_id = w.id
);

-- Add comments for documentation
COMMENT ON TABLE approval_workflows IS 'Stores approval workflow instances for contracts';
COMMENT ON TABLE approvals IS 'Stores individual approval steps within workflows';
COMMENT ON TABLE workflow_templates IS 'Stores reusable workflow templates';
COMMENT ON TABLE approval_history IS 'Audit trail for approval actions';

COMMENT ON COLUMN approval_workflows.workflow_type IS 'Type of workflow: sequential, parallel, conditional, or hybrid';
COMMENT ON COLUMN approval_workflows.status IS 'Current status of the workflow';
COMMENT ON COLUMN approval_workflows.escalation_rules IS 'JSON configuration for escalation rules';
COMMENT ON COLUMN approval_workflows.conditions IS 'JSON configuration for conditional logic';

COMMENT ON COLUMN approvals.order_index IS 'Order of approval in sequential workflows';
COMMENT ON COLUMN approvals.required IS 'Whether this approval is required for workflow completion';
COMMENT ON COLUMN approvals.conditions IS 'JSON configuration for approval conditions';
