-- Advanced Workflow Features Migration
-- Adds escalation, timeout handling, and workflow templates

-- Create workflow_escalations table
CREATE TABLE IF NOT EXISTS workflow_escalations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES approval_workflows(id) ON DELETE CASCADE,
    escalation_type VARCHAR(50) NOT NULL CHECK (escalation_type IN ('timeout', 'manual', 'automatic', 'conditional')),
    reason TEXT,
    escalated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    escalated_by VARCHAR(255),
    original_approvers JSONB DEFAULT '[]'::jsonb,
    new_approvers JSONB DEFAULT '[]'::jsonb,
    resolved_at TIMESTAMPTZ,
    resolved_by VARCHAR(255),
    resolution_notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create workflow_templates table
CREATE TABLE IF NOT EXISTS workflow_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    workflow_type VARCHAR(50) NOT NULL CHECK (workflow_type IN ('sequential', 'parallel', 'conditional', 'hybrid')),
    steps JSONB NOT NULL DEFAULT '[]'::jsonb,
    escalation_rules JSONB DEFAULT '{}'::jsonb,
    timeout_settings JSONB DEFAULT '{}'::jsonb,
    conditions JSONB DEFAULT '{}'::jsonb,
    workspace_id UUID,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- Add new columns to approval_workflows table
ALTER TABLE approval_workflows 
ADD COLUMN IF NOT EXISTS template_id UUID REFERENCES workflow_templates(id),
ADD COLUMN IF NOT EXISTS escalated_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS escalation_reason TEXT,
ADD COLUMN IF NOT EXISTS escalation_rules JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS timeout_settings JSONB DEFAULT '{}'::jsonb;

-- Add new columns to approval_steps table
ALTER TABLE approval_steps 
ADD COLUMN IF NOT EXISTS timeout_hours INTEGER DEFAULT 24,
ADD COLUMN IF NOT EXISTS expired_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS timeout_reason TEXT,
ADD COLUMN IF NOT EXISTS escalated BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS escalation_level INTEGER DEFAULT 0;

-- Create workflow_performance_metrics table for analytics
CREATE TABLE IF NOT EXISTS workflow_performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID NOT NULL REFERENCES approval_workflows(id) ON DELETE CASCADE,
    template_id UUID REFERENCES workflow_templates(id),
    contract_id UUID NOT NULL,
    workspace_id UUID NOT NULL,
    
    -- Performance metrics
    total_duration_hours DECIMAL(10,2),
    approval_duration_hours DECIMAL(10,2),
    escalation_count INTEGER DEFAULT 0,
    timeout_count INTEGER DEFAULT 0,
    
    -- Success metrics
    completed_successfully BOOLEAN DEFAULT false,
    completion_rate DECIMAL(5,4), -- 0.0 to 1.0
    user_satisfaction_score DECIMAL(3,2), -- 1.0 to 5.0
    
    -- Efficiency metrics
    steps_completed INTEGER DEFAULT 0,
    steps_skipped INTEGER DEFAULT 0,
    steps_escalated INTEGER DEFAULT 0,
    
    -- Timestamps
    workflow_started_at TIMESTAMPTZ,
    workflow_completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_workflow_escalations_workflow_id ON workflow_escalations(workflow_id);
CREATE INDEX IF NOT EXISTS idx_workflow_escalations_type ON workflow_escalations(escalation_type);
CREATE INDEX IF NOT EXISTS idx_workflow_escalations_created_at ON workflow_escalations(created_at);

CREATE INDEX IF NOT EXISTS idx_workflow_templates_workspace_id ON workflow_templates(workspace_id);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_active ON workflow_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_type ON workflow_templates(workflow_type);

CREATE INDEX IF NOT EXISTS idx_approval_workflows_template_id ON approval_workflows(template_id);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_escalated_at ON approval_workflows(escalated_at);

CREATE INDEX IF NOT EXISTS idx_approval_steps_timeout ON approval_steps(timeout_hours);
CREATE INDEX IF NOT EXISTS idx_approval_steps_escalated ON approval_steps(escalated);

CREATE INDEX IF NOT EXISTS idx_workflow_performance_workspace_id ON workflow_performance_metrics(workspace_id);
CREATE INDEX IF NOT EXISTS idx_workflow_performance_template_id ON workflow_performance_metrics(template_id);
CREATE INDEX IF NOT EXISTS idx_workflow_performance_completed_at ON workflow_performance_metrics(workflow_completed_at);

-- Enable RLS on new tables
ALTER TABLE workflow_escalations ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_performance_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for workflow_escalations
CREATE POLICY "Users can view escalations in their workspace" ON workflow_escalations
    FOR SELECT USING (
        workflow_id IN (
            SELECT id FROM approval_workflows 
            WHERE workspace_id = (auth.jwt() ->> 'workspace_id')::uuid
        )
    );

CREATE POLICY "Users can create escalations in their workspace" ON workflow_escalations
    FOR INSERT WITH CHECK (
        workflow_id IN (
            SELECT id FROM approval_workflows 
            WHERE workspace_id = (auth.jwt() ->> 'workspace_id')::uuid
        )
    );

CREATE POLICY "Users can update escalations in their workspace" ON workflow_escalations
    FOR UPDATE USING (
        workflow_id IN (
            SELECT id FROM approval_workflows 
            WHERE workspace_id = (auth.jwt() ->> 'workspace_id')::uuid
        )
    );

-- RLS Policies for workflow_templates
CREATE POLICY "Users can view templates in their workspace" ON workflow_templates
    FOR SELECT USING (
        workspace_id = (auth.jwt() ->> 'workspace_id')::uuid OR workspace_id IS NULL
    );

CREATE POLICY "Users can create templates in their workspace" ON workflow_templates
    FOR INSERT WITH CHECK (
        workspace_id = (auth.jwt() ->> 'workspace_id')::uuid OR workspace_id IS NULL
    );

CREATE POLICY "Users can update templates in their workspace" ON workflow_templates
    FOR UPDATE USING (
        workspace_id = (auth.jwt() ->> 'workspace_id')::uuid OR workspace_id IS NULL
    );

-- RLS Policies for workflow_performance_metrics
CREATE POLICY "Users can view performance metrics in their workspace" ON workflow_performance_metrics
    FOR SELECT USING (
        workspace_id = (auth.jwt() ->> 'workspace_id')::uuid
    );

CREATE POLICY "Users can create performance metrics in their workspace" ON workflow_performance_metrics
    FOR INSERT WITH CHECK (
        workspace_id = (auth.jwt() ->> 'workspace_id')::uuid
    );

CREATE POLICY "Users can update performance metrics in their workspace" ON workflow_performance_metrics
    FOR UPDATE USING (
        workspace_id = (auth.jwt() ->> 'workspace_id')::uuid
    );

-- Create function to automatically track workflow performance
CREATE OR REPLACE FUNCTION track_workflow_performance()
RETURNS TRIGGER AS $$
BEGIN
    -- When workflow status changes to completed or rejected
    IF NEW.status IN ('completed', 'rejected') AND OLD.status != NEW.status THEN
        INSERT INTO workflow_performance_metrics (
            workflow_id,
            template_id,
            contract_id,
            workspace_id,
            completed_successfully,
            workflow_started_at,
            workflow_completed_at,
            escalation_count,
            timeout_count
        )
        VALUES (
            NEW.id,
            NEW.template_id,
            NEW.contract_id,
            NEW.workspace_id,
            NEW.status = 'completed',
            NEW.created_at,
            NOW(),
            (SELECT COUNT(*) FROM workflow_escalations WHERE workflow_id = NEW.id),
            (SELECT COUNT(*) FROM approval_steps WHERE workflow_id = NEW.id AND status = 'expired')
        )
        ON CONFLICT (workflow_id) DO UPDATE SET
            completed_successfully = EXCLUDED.completed_successfully,
            workflow_completed_at = EXCLUDED.workflow_completed_at,
            escalation_count = EXCLUDED.escalation_count,
            timeout_count = EXCLUDED.timeout_count,
            updated_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for workflow performance tracking
DROP TRIGGER IF EXISTS trigger_track_workflow_performance ON approval_workflows;
CREATE TRIGGER trigger_track_workflow_performance
    AFTER UPDATE ON approval_workflows
    FOR EACH ROW
    EXECUTE FUNCTION track_workflow_performance();

-- Create function to handle automatic timeout checking
CREATE OR REPLACE FUNCTION check_approval_timeouts()
RETURNS INTEGER AS $$
DECLARE
    timeout_count INTEGER := 0;
    step_record RECORD;
BEGIN
    -- Find pending approval steps that have timed out
    FOR step_record IN
        SELECT id, workflow_id, timeout_hours, created_at
        FROM approval_steps
        WHERE status = 'pending'
        AND timeout_hours IS NOT NULL
        AND created_at + (timeout_hours || ' hours')::INTERVAL < NOW()
        AND expired_at IS NULL
    LOOP
        -- Mark step as expired
        UPDATE approval_steps
        SET 
            status = 'expired',
            expired_at = NOW(),
            timeout_reason = 'Approval timeout exceeded',
            updated_at = NOW()
        WHERE id = step_record.id;
        
        timeout_count := timeout_count + 1;
        
        -- Log the timeout
        RAISE NOTICE 'Approval step % timed out after % hours', step_record.id, step_record.timeout_hours;
    END LOOP;
    
    RETURN timeout_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to get workflow template recommendations
CREATE OR REPLACE FUNCTION get_template_recommendations(
    contract_value DECIMAL DEFAULT 0,
    contract_type VARCHAR DEFAULT '',
    risk_score DECIMAL DEFAULT 0.5,
    department VARCHAR DEFAULT ''
)
RETURNS TABLE(
    template_id UUID,
    template_name VARCHAR,
    match_score DECIMAL,
    recommendation_reason TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        wt.id,
        wt.name,
        -- Calculate match score based on conditions
        CASE 
            WHEN wt.conditions IS NULL OR wt.conditions = '{}'::jsonb THEN 0.5
            ELSE (
                -- Contract value match
                CASE 
                    WHEN wt.conditions ? 'contract_value' THEN
                        CASE 
                            WHEN contract_value BETWEEN 
                                COALESCE((wt.conditions->'contract_value'->>'min_value')::DECIMAL, 0) AND
                                COALESCE((wt.conditions->'contract_value'->>'max_value')::DECIMAL, 999999999)
                            THEN 0.3 ELSE 0.0
                        END
                    ELSE 0.1
                END +
                -- Contract type match
                CASE 
                    WHEN wt.conditions ? 'contract_types' AND 
                         wt.conditions->'contract_types' ? contract_type THEN 0.3
                    ELSE 0.1
                END +
                -- Risk score match
                CASE 
                    WHEN wt.conditions ? 'risk_score' THEN
                        CASE 
                            WHEN risk_score BETWEEN 
                                COALESCE((wt.conditions->'risk_score'->>'min_risk')::DECIMAL, 0) AND
                                COALESCE((wt.conditions->'risk_score'->>'max_risk')::DECIMAL, 1)
                            THEN 0.3 ELSE 0.0
                        END
                    ELSE 0.1
                END +
                -- Department match
                CASE 
                    WHEN wt.conditions ? 'departments' AND 
                         wt.conditions->'departments' ? department THEN 0.1
                    ELSE 0.05
                END
            )
        END AS match_score,
        CONCAT(
            'Template matches: ',
            CASE WHEN wt.conditions ? 'contract_value' THEN 'value range, ' ELSE '' END,
            CASE WHEN wt.conditions ? 'contract_types' THEN 'contract type, ' ELSE '' END,
            CASE WHEN wt.conditions ? 'risk_score' THEN 'risk level, ' ELSE '' END,
            CASE WHEN wt.conditions ? 'departments' THEN 'department' ELSE '' END
        ) AS recommendation_reason
    FROM workflow_templates wt
    WHERE wt.is_active = true
    ORDER BY match_score DESC;
END;
$$ LANGUAGE plpgsql;

-- Insert default workflow templates
INSERT INTO workflow_templates (name, description, workflow_type, steps, escalation_rules, timeout_settings, conditions, created_by) VALUES
(
    'Standard Contract Approval',
    'Standard approval workflow for most contracts',
    'sequential',
    '[
        {"step": 1, "role": "manager", "required": true},
        {"step": 2, "role": "legal_counsel", "required": true},
        {"step": 3, "role": "department_head", "required": false}
    ]'::jsonb,
    '{
        "auto_escalate_on_timeout": true,
        "escalation_timeout_hours": 48,
        "manual_escalation_approvers": ["director", "ceo"]
    }'::jsonb,
    '{
        "default_timeout_hours": 24,
        "escalation_timeout_hours": 48,
        "final_timeout_hours": 72
    }'::jsonb,
    '{
        "contract_value": {"min_value": 0, "max_value": 50000},
        "risk_score": {"min_risk": 0, "max_risk": 0.7}
    }'::jsonb,
    'system'
),
(
    'High-Value Contract Approval',
    'Enhanced approval workflow for high-value contracts',
    'sequential',
    '[
        {"step": 1, "role": "manager", "required": true},
        {"step": 2, "role": "legal_counsel", "required": true},
        {"step": 3, "role": "department_head", "required": true},
        {"step": 4, "role": "director", "required": true},
        {"step": 5, "role": "cfo", "required": true}
    ]'::jsonb,
    '{
        "auto_escalate_on_timeout": true,
        "escalation_timeout_hours": 24,
        "manual_escalation_approvers": ["ceo", "board"]
    }'::jsonb,
    '{
        "default_timeout_hours": 12,
        "escalation_timeout_hours": 24,
        "final_timeout_hours": 48
    }'::jsonb,
    '{
        "contract_value": {"min_value": 50000},
        "risk_score": {"min_risk": 0.7}
    }'::jsonb,
    'system'
),
(
    'Express Approval',
    'Fast-track approval for low-risk contracts',
    'parallel',
    '[
        {"step": 1, "role": "manager", "required": true},
        {"step": 1, "role": "legal_counsel", "required": false}
    ]'::jsonb,
    '{
        "auto_escalate_on_timeout": false,
        "escalation_timeout_hours": 72
    }'::jsonb,
    '{
        "default_timeout_hours": 8,
        "escalation_timeout_hours": 24
    }'::jsonb,
    '{
        "contract_value": {"max_value": 10000},
        "risk_score": {"max_risk": 0.3},
        "contract_types": ["nda", "service_agreement", "purchase_order"]
    }'::jsonb,
    'system'
);

-- Add helpful comments
COMMENT ON TABLE workflow_escalations IS 'Tracks escalation events for approval workflows';
COMMENT ON TABLE workflow_templates IS 'Reusable workflow templates with conditions and rules';
COMMENT ON TABLE workflow_performance_metrics IS 'Performance analytics for workflow optimization';
COMMENT ON FUNCTION check_approval_timeouts() IS 'Automatically checks and handles approval timeouts';
COMMENT ON FUNCTION get_template_recommendations(DECIMAL, VARCHAR, DECIMAL, VARCHAR) IS 'Recommends workflow templates based on contract attributes';
