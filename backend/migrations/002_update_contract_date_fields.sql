-- Migration: Update contract date fields to <PERSON>IMESTA<PERSON> WITH TIME ZONE
-- Date: 2024-01-15
-- Description: Convert effective_date and expiry_date from DATE to TIMESTAMP WITH TIME ZONE for consistency

-- Update contracts table to use TIMESTAMP WITH TIME ZONE for date fields
ALTER TABLE contracts 
ALTER COLUMN effective_date TYPE TIMESTAMP WITH TIME ZONE,
ALTER COLUMN expiry_date TYPE TIMESTAMP WITH TIME ZONE;

-- Add comment to track this migration
COMMENT ON TABLE contracts IS 'Updated date fields to TIMESTAMP WITH TIME ZONE for ISO string compatibility - Migration 002';
