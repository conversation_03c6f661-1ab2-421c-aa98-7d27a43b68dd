-- Contract Lifecycle Management Migration
-- This migration adds version control, renewal tracking, and lifecycle automation

-- Contract Versions table for version control
CREATE TABLE IF NOT EXISTS contract_versions (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    content JSONB NOT NULL, -- Full contract data snapshot
    changes_summary TEXT, -- Summary of changes made in this version
    change_details JSONB, -- Detailed change tracking
    created_by JSONB NOT NULL, -- User who created this version
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_current BOOLEAN DEFAULT FALSE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
    
    -- Ensure unique version numbers per contract
    UNIQUE(contract_id, version_number)
);

-- Contract Renewals table for renewal tracking
CREATE TABLE IF NOT EXISTS contract_renewals (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    original_expiry_date TIMESTAMP WITH TIME ZONE NOT NULL,
    new_expiry_date TIMESTAMP WITH TIME ZONE NOT NULL,
    renewal_period TEXT, -- e.g., "1 year", "6 months"
    auto_renewal BOOLEAN DEFAULT FALSE,
    renewal_notice_period INTEGER DEFAULT 30, -- Days before expiry to send notice
    renewal_status TEXT DEFAULT 'pending', -- pending, approved, rejected, completed
    renewal_terms JSONB, -- Any changes to terms during renewal
    requested_by JSONB NOT NULL,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    approved_by JSONB,
    approved_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE
);

-- Contract Lifecycle Events table for tracking all lifecycle events
CREATE TABLE IF NOT EXISTS contract_lifecycle_events (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL, -- created, updated, approved, activated, renewed, expired, terminated, etc.
    event_description TEXT NOT NULL,
    event_data JSONB, -- Additional event-specific data
    triggered_by TEXT, -- user_id or 'system' for automated events
    triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE
);

-- Contract Reminders table for automated notifications
CREATE TABLE IF NOT EXISTS contract_reminders (
    id TEXT PRIMARY KEY,
    contract_id TEXT NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    reminder_type TEXT NOT NULL, -- expiry_warning, renewal_due, review_required, etc.
    reminder_date TIMESTAMP WITH TIME ZONE NOT NULL,
    days_before_event INTEGER, -- How many days before the event this reminder is for
    message TEXT NOT NULL,
    recipients JSONB NOT NULL, -- Array of user IDs to notify
    sent BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP WITH TIME ZONE,
    workspace_id TEXT NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_contract_versions_contract_id ON contract_versions(contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_versions_current ON contract_versions(contract_id, is_current) WHERE is_current = TRUE;
CREATE INDEX IF NOT EXISTS idx_contract_renewals_contract_id ON contract_renewals(contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_renewals_status ON contract_renewals(renewal_status);
CREATE INDEX IF NOT EXISTS idx_contract_lifecycle_events_contract_id ON contract_lifecycle_events(contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_lifecycle_events_type ON contract_lifecycle_events(event_type);
CREATE INDEX IF NOT EXISTS idx_contract_reminders_contract_id ON contract_reminders(contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_reminders_date ON contract_reminders(reminder_date) WHERE sent = FALSE;

-- RLS Policies for contract_versions
ALTER TABLE contract_versions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view contract versions in their workspaces" ON contract_versions
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create contract versions in their workspaces" ON contract_versions
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for contract_renewals
ALTER TABLE contract_renewals ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view contract renewals in their workspaces" ON contract_renewals
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage contract renewals in their workspaces" ON contract_renewals
    FOR ALL USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for contract_lifecycle_events
ALTER TABLE contract_lifecycle_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view lifecycle events in their workspaces" ON contract_lifecycle_events
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create lifecycle events in their workspaces" ON contract_lifecycle_events
    FOR INSERT WITH CHECK (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for contract_reminders
ALTER TABLE contract_reminders ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view contract reminders in their workspaces" ON contract_reminders
    FOR SELECT USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage contract reminders in their workspaces" ON contract_reminders
    FOR ALL USING (
        workspace_id IN (
            SELECT workspace_id FROM workspace_members
            WHERE user_id = auth.uid()
        )
    );

-- Functions for automatic lifecycle tracking

-- Function to create a new contract version
CREATE OR REPLACE FUNCTION create_contract_version()
RETURNS TRIGGER AS $$
DECLARE
    next_version INTEGER;
BEGIN
    -- Get the next version number
    SELECT COALESCE(MAX(version_number), 0) + 1
    INTO next_version
    FROM contract_versions
    WHERE contract_id = NEW.id;

    -- Mark all previous versions as not current
    UPDATE contract_versions
    SET is_current = FALSE
    WHERE contract_id = NEW.id;

    -- Create new version record
    INSERT INTO contract_versions (
        id,
        contract_id,
        version_number,
        title,
        content,
        changes_summary,
        created_by,
        is_current,
        workspace_id
    ) VALUES (
        gen_random_uuid()::text,
        NEW.id,
        next_version,
        NEW.title,
        row_to_json(NEW),
        CASE
            WHEN TG_OP = 'INSERT' THEN 'Initial version'
            ELSE 'Contract updated'
        END,
        NEW.created_by,
        TRUE,
        NEW.workspace_id
    );

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to log lifecycle events
CREATE OR REPLACE FUNCTION log_contract_lifecycle_event()
RETURNS TRIGGER AS $$
BEGIN
    -- Log the appropriate lifecycle event
    INSERT INTO contract_lifecycle_events (
        id,
        contract_id,
        event_type,
        event_description,
        event_data,
        triggered_by,
        workspace_id
    ) VALUES (
        gen_random_uuid()::text,
        COALESCE(NEW.id, OLD.id),
        CASE
            WHEN TG_OP = 'INSERT' THEN 'created'
            WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN 'status_changed'
            WHEN TG_OP = 'UPDATE' THEN 'updated'
            WHEN TG_OP = 'DELETE' THEN 'deleted'
        END,
        CASE
            WHEN TG_OP = 'INSERT' THEN 'Contract created'
            WHEN TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
                'Status changed from ' || OLD.status || ' to ' || NEW.status
            WHEN TG_OP = 'UPDATE' THEN 'Contract updated'
            WHEN TG_OP = 'DELETE' THEN 'Contract deleted'
        END,
        CASE
            WHEN TG_OP = 'DELETE' THEN row_to_json(OLD)
            ELSE row_to_json(NEW)
        END,
        auth.uid()::text,
        COALESCE(NEW.workspace_id, OLD.workspace_id)
    );

    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Function to create automatic reminders
CREATE OR REPLACE FUNCTION create_contract_reminders()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create reminders for contracts with expiry dates
    IF NEW.expiry_date IS NOT NULL THEN
        -- Delete existing reminders for this contract
        DELETE FROM contract_reminders WHERE contract_id = NEW.id;

        -- Create 30-day warning reminder
        INSERT INTO contract_reminders (
            id,
            contract_id,
            reminder_type,
            reminder_date,
            days_before_event,
            message,
            recipients,
            workspace_id
        ) VALUES (
            gen_random_uuid()::text,
            NEW.id,
            'expiry_warning',
            NEW.expiry_date - INTERVAL '30 days',
            30,
            'Contract "' || NEW.title || '" expires in 30 days',
            jsonb_build_array(NEW.created_by->>'id'),
            NEW.workspace_id
        );

        -- Create 7-day warning reminder
        INSERT INTO contract_reminders (
            id,
            contract_id,
            reminder_type,
            reminder_date,
            days_before_event,
            message,
            recipients,
            workspace_id
        ) VALUES (
            gen_random_uuid()::text,
            NEW.id,
            'expiry_warning',
            NEW.expiry_date - INTERVAL '7 days',
            7,
            'Contract "' || NEW.title || '" expires in 7 days',
            jsonb_build_array(NEW.created_by->>'id'),
            NEW.workspace_id
        );

        -- Create expiry day reminder
        INSERT INTO contract_reminders (
            id,
            contract_id,
            reminder_type,
            reminder_date,
            days_before_event,
            message,
            recipients,
            workspace_id
        ) VALUES (
            gen_random_uuid()::text,
            NEW.id,
            'expiry_warning',
            NEW.expiry_date,
            0,
            'Contract "' || NEW.title || '" expires today',
            jsonb_build_array(NEW.created_by->>'id'),
            NEW.workspace_id
        );
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers

-- Trigger to create contract versions on insert/update
CREATE TRIGGER contract_version_trigger
    AFTER INSERT OR UPDATE ON contracts
    FOR EACH ROW
    EXECUTE FUNCTION create_contract_version();

-- Trigger to log lifecycle events
CREATE TRIGGER contract_lifecycle_trigger
    AFTER INSERT OR UPDATE OR DELETE ON contracts
    FOR EACH ROW
    EXECUTE FUNCTION log_contract_lifecycle_event();

-- Trigger to create automatic reminders
CREATE TRIGGER contract_reminders_trigger
    AFTER INSERT OR UPDATE OF expiry_date ON contracts
    FOR EACH ROW
    EXECUTE FUNCTION create_contract_reminders();

-- Function to automatically update contract status based on dates
CREATE OR REPLACE FUNCTION update_contract_status_by_date()
RETURNS void AS $$
BEGIN
    -- Update contracts to expired status
    UPDATE contracts
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'active'
    AND expiry_date IS NOT NULL
    AND expiry_date < NOW();

    -- Update contracts to active status if effective date has passed
    UPDATE contracts
    SET status = 'active', updated_at = NOW()
    WHERE status = 'draft'
    AND effective_date IS NOT NULL
    AND effective_date <= NOW()
    AND (expiry_date IS NULL OR expiry_date > NOW());
END;
$$ language 'plpgsql';

-- Create a scheduled job to run status updates (this would typically be handled by a cron job or background task)
-- For now, we'll create the function and it can be called manually or by the application

-- Add version tracking to existing contracts (one-time migration)
DO $$
DECLARE
    contract_record RECORD;
BEGIN
    FOR contract_record IN SELECT * FROM contracts LOOP
        -- Create initial version for existing contracts
        INSERT INTO contract_versions (
            id,
            contract_id,
            version_number,
            title,
            content,
            changes_summary,
            created_by,
            created_at,
            is_current,
            workspace_id
        ) VALUES (
            gen_random_uuid()::text,
            contract_record.id,
            1,
            contract_record.title,
            row_to_json(contract_record),
            'Initial version (migrated)',
            contract_record.created_by,
            contract_record.created_at,
            TRUE,
            contract_record.workspace_id
        );

        -- Create initial lifecycle event
        INSERT INTO contract_lifecycle_events (
            id,
            contract_id,
            event_type,
            event_description,
            event_data,
            triggered_by,
            triggered_at,
            workspace_id
        ) VALUES (
            gen_random_uuid()::text,
            contract_record.id,
            'migrated',
            'Contract migrated to lifecycle management system',
            row_to_json(contract_record),
            'system',
            contract_record.created_at,
            contract_record.workspace_id
        );
    END LOOP;
END $$;
