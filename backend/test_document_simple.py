"""
Simple test for document generation dependencies
"""

import sys
import os

print("🔧 Testing Document Generation Dependencies")
print("=" * 50)

# Test WeasyPrint
try:
    from weasyprint import HTML, CSS
    print("✅ WeasyPrint: Available")
    weasyprint_available = True
except ImportError as e:
    print(f"❌ WeasyPrint: Not available - {e}")
    weasyprint_available = False

# Test ReportLab
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet
    print("✅ ReportLab: Available")
    reportlab_available = True
except ImportError as e:
    print(f"❌ ReportLab: Not available - {e}")
    reportlab_available = False

# Test python-docx
try:
    from docx import Document as DocxDocument
    print("✅ python-docx: Available")
    docx_available = True
except ImportError as e:
    print(f"❌ python-docx: Not available - {e}")
    docx_available = False

# Test Jinja2
try:
    from jinja2 import Template, Environment, FileSystemLoader
    print("✅ Jinja2: Available")
    jinja2_available = True
except ImportError as e:
    print(f"❌ Jinja2: Not available - {e}")
    jinja2_available = False

print("\n" + "=" * 50)
print("📋 Dependency Summary:")
print(f"   WeasyPrint: {'✅' if weasyprint_available else '❌'}")
print(f"   ReportLab: {'✅' if reportlab_available else '❌'}")
print(f"   python-docx: {'✅' if docx_available else '❌'}")
print(f"   Jinja2: {'✅' if jinja2_available else '❌'}")

# Test basic PDF generation with ReportLab
if reportlab_available:
    print("\n🔄 Testing Basic PDF Generation with ReportLab...")
    try:
        import tempfile
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
            doc = SimpleDocTemplate(temp_file.name, pagesize=letter)
            styles = getSampleStyleSheet()
            
            story = []
            story.append(Paragraph("Test Contract Document", styles['Title']))
            story.append(Paragraph("This is a test document generated by ReportLab.", styles['Normal']))
            
            doc.build(story)
            
            # Check file size
            file_size = os.path.getsize(temp_file.name)
            print(f"✅ PDF Generation Test: Success ({file_size:,} bytes)")
            
            # Clean up
            os.unlink(temp_file.name)
            
    except Exception as e:
        print(f"❌ PDF Generation Test: Failed - {e}")

# Test basic DOCX generation
if docx_available:
    print("\n🔄 Testing Basic DOCX Generation...")
    try:
        import tempfile
        from docx import Document
        
        with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as temp_file:
            doc = Document()
            doc.add_heading('Test Contract Document', 0)
            doc.add_paragraph('This is a test document generated by python-docx.')
            
            doc.save(temp_file.name)
            
            # Check file size
            file_size = os.path.getsize(temp_file.name)
            print(f"✅ DOCX Generation Test: Success ({file_size:,} bytes)")
            
            # Clean up
            os.unlink(temp_file.name)
            
    except Exception as e:
        print(f"❌ DOCX Generation Test: Failed - {e}")

# Test Jinja2 template rendering
if jinja2_available:
    print("\n🔄 Testing Template Rendering...")
    try:
        from jinja2 import Template
        
        template_content = """
        <h1>{{ title }}</h1>
        <p>Contract between {{ party1 }} and {{ party2 }}</p>
        <ul>
        {% for clause in clauses %}
            <li>{{ clause }}</li>
        {% endfor %}
        </ul>
        """
        
        template = Template(template_content)
        rendered = template.render(
            title="Test Contract",
            party1="Company A",
            party2="Company B",
            clauses=["Payment Terms", "Confidentiality", "Termination"]
        )
        
        if "Test Contract" in rendered and "Company A" in rendered:
            print("✅ Template Rendering Test: Success")
        else:
            print("❌ Template Rendering Test: Failed - Content not rendered correctly")
            
    except Exception as e:
        print(f"❌ Template Rendering Test: Failed - {e}")

print("\n" + "=" * 50)

# Overall assessment
working_deps = sum([reportlab_available, docx_available, jinja2_available])
total_deps = 3  # Core dependencies (excluding WeasyPrint which is optional)

if working_deps == total_deps:
    print("🎉 All core dependencies are working! Document generation is ready.")
    print("📝 Note: WeasyPrint is optional - ReportLab will be used for PDF generation.")
elif working_deps >= 2:
    print("⚠️ Most dependencies are working. Some features may be limited.")
else:
    print("❌ Critical dependencies are missing. Document generation may not work properly.")

print(f"\n📊 Status: {working_deps}/{total_deps} core dependencies working")
