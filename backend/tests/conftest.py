"""
Test configuration and fixtures for Averum Contracts API.
"""

import pytest
import asyncio
import os
import uuid
from typing import Dict, Any, AsyncGenerator
from unittest.mock import Mock, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Set test environment
os.environ["ENVIRONMENT"] = "test"
os.environ["TESTING"] = "true"

from app.main import app
from app.core.config import settings
from app.db.database import get_supabase_client


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
async def async_client():
    """Create an async test client for the FastAPI application."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def mock_supabase():
    """Mock Supabase client for testing."""
    mock_client = Mock()
    
    # Mock table operations
    mock_table = Mock()
    mock_client.table.return_value = mock_table
    
    # Mock query operations
    mock_table.select.return_value = mock_table
    mock_table.insert.return_value = mock_table
    mock_table.update.return_value = mock_table
    mock_table.delete.return_value = mock_table
    mock_table.eq.return_value = mock_table
    mock_table.neq.return_value = mock_table
    mock_table.gt.return_value = mock_table
    mock_table.gte.return_value = mock_table
    mock_table.lt.return_value = mock_table
    mock_table.lte.return_value = mock_table
    mock_table.like.return_value = mock_table
    mock_table.ilike.return_value = mock_table
    mock_table.is_.return_value = mock_table
    mock_table.in_.return_value = mock_table
    mock_table.contains.return_value = mock_table
    mock_table.contained_by.return_value = mock_table
    mock_table.range.return_value = mock_table
    mock_table.match.return_value = mock_table
    mock_table.order.return_value = mock_table
    mock_table.limit.return_value = mock_table
    mock_table.offset.return_value = mock_table
    mock_table.single.return_value = mock_table
    
    # Mock execute method
    mock_response = Mock()
    mock_response.data = []
    mock_response.error = None
    mock_table.execute.return_value = mock_response
    
    # Mock auth operations
    mock_auth = Mock()
    mock_client.auth = mock_auth
    
    # Mock storage operations
    mock_storage = Mock()
    mock_client.storage = mock_storage
    
    return mock_client


@pytest.fixture
def mock_user():
    """Mock authenticated user for testing."""
    return {
        "id": "test-user-id",
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "User",
        "username": "testuser",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def mock_workspace():
    """Mock workspace for testing."""
    return {
        "id": "test-workspace-id",
        "name": "Test Workspace",
        "description": "A test workspace",
        "created_by": "test-user-id",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def mock_contract():
    """Mock contract for testing."""
    return {
        "id": "test-contract-id",
        "title": "Test Contract",
        "type": "Service Agreement",
        "status": "draft",
        "content": {
            "title": "Test Contract",
            "parties": ["Party A", "Party B"],
            "terms": "Test terms and conditions"
        },
        "workspace_id": "test-workspace-id",
        "created_by": {
            "id": "test-user-id",
            "name": "Test User"
        },
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def sample_contract_data():
    """Sample contract data for testing."""
    return {
        "title": "Software Development Agreement",
        "type": "Service Agreement",
        "status": "draft",
        "content": {
            "title": "Software Development Agreement",
            "parties": {
                "client": "Acme Corporation",
                "contractor": "Tech Solutions Inc."
            },
            "scope": "Development of custom web application",
            "timeline": "6 months",
            "payment": {
                "amount": 150000,
                "currency": "USD",
                "schedule": "Monthly"
            },
            "terms": [
                "Intellectual property rights",
                "Confidentiality agreement",
                "Termination clauses"
            ]
        },
        "workspace_id": "test-workspace-id"
    }


@pytest.fixture
def auth_headers(mock_user):
    """Authentication headers for testing."""
    # In a real test, you'd generate a proper JWT token
    # For now, we'll use a mock token
    return {
        "Authorization": "Bearer mock-jwt-token",
        "Content-Type": "application/json"
    }


@pytest.fixture
def mock_ai_service():
    """Mock AI service for testing."""
    mock_service = AsyncMock()
    
    # Mock analysis result
    mock_result = Mock()
    mock_result.contract_type = "Service Agreement"
    mock_result.risk_score = 0.3
    mock_result.compliance_score = 0.8
    mock_result.language_clarity = 0.9
    mock_result.confidence = 0.85
    mock_result.provider = "mock"
    mock_result.processing_time = 0.1
    mock_result.key_terms = ["payment", "termination", "intellectual property"]
    mock_result.potential_issues = ["Missing liability clause"]
    mock_result.recommendations = ["Add liability limitations"]
    
    mock_service.analyze_contract.return_value = mock_result
    
    return mock_service


@pytest.fixture
def mock_document_generator():
    """Mock document generator service for testing."""
    mock_generator = AsyncMock()
    
    # Mock generation result
    mock_result = {
        "file_path": "/tmp/test-contract.pdf",
        "file_size": 1024,
        "format": "pdf",
        "generation_time": 0.5
    }
    
    mock_generator.generate_contract_document.return_value = mock_result
    
    return mock_generator


@pytest.fixture
def mock_notification_service():
    """Mock notification service for testing."""
    mock_service = AsyncMock()
    
    mock_service.send_notification.return_value = True
    mock_service.broadcast_to_workspace.return_value = True
    mock_service.send_email_notification.return_value = True
    
    return mock_service


@pytest.fixture
def security_test_data():
    """Test data for security testing."""
    return {
        "xss_payloads": [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "<svg onload=alert('xss')>",
            "';alert('xss');//"
        ],
        "sql_injection_payloads": [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM users --",
            "1; DELETE FROM contracts; --"
        ],
        "path_traversal_payloads": [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "....//....//....//etc/passwd"
        ]
    }


@pytest.fixture
def performance_test_data():
    """Test data for performance testing."""
    return {
        "large_contract": {
            "title": "Large Contract Document",
            "content": "Lorem ipsum " * 10000,  # Large content
            "type": "Service Agreement",
            "workspace_id": "test-workspace-id"
        },
        "bulk_contracts": [
            {
                "title": f"Contract {i}",
                "type": "Service Agreement",
                "content": f"Contract content {i}",
                "workspace_id": "test-workspace-id"
            }
            for i in range(100)
        ]
    }


# Test database setup and teardown
@pytest.fixture(scope="function")
async def test_db():
    """Set up and tear down test database state."""
    # Setup: Create test data if needed
    test_data = {}
    
    yield test_data
    
    # Teardown: Clean up test data
    # In a real implementation, you'd clean up test records
    pass


# Mock external services
@pytest.fixture(autouse=True)
def mock_external_services(monkeypatch):
    """Mock external services to avoid real API calls during testing."""

    # Mock Clerk authentication
    def mock_verify_token(token: str):
        return {
            "sub": "test-user-id",
            "email": "<EMAIL>",
            "first_name": "Test",
            "last_name": "User"
        }

    # Mock OpenAI/Hugging Face API calls
    async def mock_ai_api_call(*args, **kwargs):
        return {
            "analysis": "Mock AI analysis result",
            "confidence": 0.85
        }

    # Only apply mocks if the functions exist
    try:
        import app.core.auth
        if hasattr(app.core.auth, 'verify_clerk_token'):
            monkeypatch.setattr("app.core.auth.verify_clerk_token", mock_verify_token)
    except (ImportError, AttributeError):
        pass

    yield


# Utility functions for tests
def create_test_id():
    """Generate a test ID."""
    return f"test-{uuid.uuid4()}"


def assert_valid_uuid(value: str):
    """Assert that a value is a valid UUID."""
    try:
        uuid.UUID(value)
        return True
    except ValueError:
        return False


def assert_valid_timestamp(value: str):
    """Assert that a value is a valid ISO timestamp."""
    try:
        from datetime import datetime
        datetime.fromisoformat(value.replace('Z', '+00:00'))
        return True
    except ValueError:
        return False
