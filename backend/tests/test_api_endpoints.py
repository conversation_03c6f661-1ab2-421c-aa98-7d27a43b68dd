"""
API endpoint tests for Averum Contracts API.
Tests core functionality of contract management endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock
from app.main import app


class TestHealthEndpoint:
    """Test health check endpoint."""
    
    def test_health_check(self):
        """Test health check endpoint returns success."""
        client = TestClient(app)
        response = client.get("/api/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "database" in data
        assert "counts" in data


class TestContractEndpoints:
    """Test contract management endpoints."""
    
    @patch('app.core.auth.get_current_user')
    @patch('app.db.database.get_supabase_client')
    def test_get_contracts(self, mock_supabase, mock_auth, mock_user, mock_workspace):
        """Test getting contracts list."""
        # Setup mocks
        mock_auth.return_value = mock_user
        
        mock_client = Mock()
        mock_supabase.return_value = mock_client
        
        # Mock successful response
        mock_response = Mock()
        mock_response.data = [
            {
                "id": "contract-1",
                "title": "Test Contract 1",
                "type": "Service Agreement",
                "status": "draft",
                "workspace_id": mock_workspace["id"],
                "created_at": "2024-01-01T00:00:00Z"
            }
        ]
        mock_response.error = None
        
        mock_client.table.return_value.select.return_value.execute.return_value = mock_response
        
        client = TestClient(app)
        response = client.get(
            "/api/contracts/",
            headers={"Authorization": "Bearer mock-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    @patch('app.core.auth.get_current_user')
    @patch('app.db.database.get_supabase_client')
    def test_create_contract(self, mock_supabase, mock_auth, mock_user, sample_contract_data):
        """Test creating a new contract."""
        # Setup mocks
        mock_auth.return_value = mock_user
        
        mock_client = Mock()
        mock_supabase.return_value = mock_client
        
        # Mock workspace validation
        mock_workspace_response = Mock()
        mock_workspace_response.data = [{"id": "test-workspace-id"}]
        mock_workspace_response.error = None
        
        # Mock contract creation
        mock_create_response = Mock()
        mock_create_response.data = [{
            **sample_contract_data,
            "id": "new-contract-id",
            "created_by": {"id": mock_user["id"], "name": "Test User"},
            "created_at": "2024-01-01T00:00:00Z"
        }]
        mock_create_response.error = None
        
        mock_client.table.return_value.select.return_value.eq.return_value.execute.return_value = mock_workspace_response
        mock_client.table.return_value.insert.return_value.execute.return_value = mock_create_response
        
        client = TestClient(app)
        response = client.post(
            "/api/contracts/",
            json=sample_contract_data,
            headers={"Authorization": "Bearer mock-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == sample_contract_data["title"]
        assert "id" in data
        assert "created_at" in data
    
    @patch('app.core.auth.get_current_user')
    @patch('app.db.database.get_supabase_client')
    def test_get_contract_by_id(self, mock_supabase, mock_auth, mock_user, mock_contract):
        """Test getting a specific contract by ID."""
        # Setup mocks
        mock_auth.return_value = mock_user
        
        mock_client = Mock()
        mock_supabase.return_value = mock_client
        
        mock_response = Mock()
        mock_response.data = [mock_contract]
        mock_response.error = None
        
        mock_client.table.return_value.select.return_value.eq.return_value.execute.return_value = mock_response
        
        client = TestClient(app)
        response = client.get(
            f"/api/contracts/{mock_contract['id']}",
            headers={"Authorization": "Bearer mock-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == mock_contract["id"]
        assert data["title"] == mock_contract["title"]
    
    @patch('app.core.auth.get_current_user')
    @patch('app.db.database.get_supabase_client')
    def test_update_contract(self, mock_supabase, mock_auth, mock_user, mock_contract):
        """Test updating a contract."""
        # Setup mocks
        mock_auth.return_value = mock_user
        
        mock_client = Mock()
        mock_supabase.return_value = mock_client
        
        # Mock contract existence check
        mock_check_response = Mock()
        mock_check_response.data = [{"id": mock_contract["id"]}]
        mock_check_response.error = None
        
        # Mock update response
        updated_contract = {**mock_contract, "title": "Updated Contract Title"}
        mock_update_response = Mock()
        mock_update_response.data = [updated_contract]
        mock_update_response.error = None
        
        mock_client.table.return_value.select.return_value.eq.return_value.execute.return_value = mock_check_response
        mock_client.table.return_value.update.return_value.eq.return_value.execute.return_value = mock_update_response
        
        update_data = {"title": "Updated Contract Title"}
        
        client = TestClient(app)
        response = client.put(
            f"/api/contracts/{mock_contract['id']}",
            json=update_data,
            headers={"Authorization": "Bearer mock-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "Updated Contract Title"
    
    def test_contract_not_found(self):
        """Test handling of non-existent contract."""
        client = TestClient(app)
        response = client.get(
            "/api/contracts/non-existent-id",
            headers={"Authorization": "Bearer mock-token"}
        )
        
        # Should return 404 or handle gracefully
        assert response.status_code in [404, 401, 403]


class TestContractLifecycleEndpoints:
    """Test contract lifecycle management endpoints."""
    
    @patch('app.core.auth.get_current_user')
    @patch('app.db.database.get_supabase_client')
    def test_get_contract_versions(self, mock_supabase, mock_auth, mock_user):
        """Test getting contract versions."""
        # Setup mocks
        mock_auth.return_value = mock_user
        
        mock_client = Mock()
        mock_supabase.return_value = mock_client
        
        mock_response = Mock()
        mock_response.data = [
            {
                "id": "version-1",
                "contract_id": "contract-1",
                "version_number": 1,
                "title": "Contract v1",
                "is_current": True,
                "created_at": "2024-01-01T00:00:00Z"
            }
        ]
        mock_response.error = None
        
        mock_client.table.return_value.select.return_value.eq.return_value.order.return_value.execute.return_value = mock_response
        
        client = TestClient(app)
        response = client.get(
            "/api/lifecycle/contracts/contract-1/versions",
            headers={"Authorization": "Bearer mock-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    @patch('app.core.auth.get_current_user')
    @patch('app.db.database.get_supabase_client')
    def test_create_contract_version(self, mock_supabase, mock_auth, mock_user):
        """Test creating a new contract version."""
        # Setup mocks
        mock_auth.return_value = mock_user
        
        mock_client = Mock()
        mock_supabase.return_value = mock_client
        
        # Mock version creation
        mock_response = Mock()
        mock_response.data = [{
            "id": "new-version-id",
            "contract_id": "contract-1",
            "version_number": 2,
            "title": "Contract v2",
            "is_current": True,
            "created_at": "2024-01-01T00:00:00Z"
        }]
        mock_response.error = None
        
        mock_client.table.return_value.insert.return_value.execute.return_value = mock_response
        
        version_data = {
            "contract_id": "contract-1",
            "title": "Contract v2",
            "content": {"updated": "content"},
            "changes_summary": "Updated terms",
            "workspace_id": "test-workspace-id"
        }
        
        client = TestClient(app)
        response = client.post(
            "/api/lifecycle/versions",
            json=version_data,
            headers={"Authorization": "Bearer mock-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["version_number"] == 2


class TestWorkspaceEndpoints:
    """Test workspace management endpoints."""
    
    @patch('app.core.auth.get_current_user')
    @patch('app.db.database.get_supabase_client')
    def test_get_user_workspaces(self, mock_supabase, mock_auth, mock_user):
        """Test getting user's workspaces."""
        # Setup mocks
        mock_auth.return_value = mock_user
        
        mock_client = Mock()
        mock_supabase.return_value = mock_client
        
        mock_response = Mock()
        mock_response.data = [
            {
                "id": "workspace-1",
                "name": "Test Workspace",
                "description": "A test workspace",
                "created_at": "2024-01-01T00:00:00Z"
            }
        ]
        mock_response.error = None
        
        mock_client.table.return_value.select.return_value.eq.return_value.execute.return_value = mock_response
        
        client = TestClient(app)
        response = client.get(
            "/api/workspaces/",
            headers={"Authorization": "Bearer mock-token"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)


class TestErrorHandling:
    """Test error handling in API endpoints."""
    
    def test_invalid_json(self):
        """Test handling of invalid JSON in request body."""
        client = TestClient(app)
        response = client.post(
            "/api/contracts/",
            data="invalid json",
            headers={
                "Authorization": "Bearer mock-token",
                "Content-Type": "application/json"
            }
        )
        
        assert response.status_code == 422
    
    def test_missing_required_fields(self):
        """Test handling of missing required fields."""
        client = TestClient(app)
        response = client.post(
            "/api/contracts/",
            json={"title": "Test"},  # Missing required fields
            headers={"Authorization": "Bearer mock-token"}
        )
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_unauthorized_access(self):
        """Test handling of unauthorized access."""
        client = TestClient(app)
        response = client.get("/api/contracts/")
        
        # Should require authentication
        assert response.status_code == 401


class TestInputValidation:
    """Test input validation and sanitization."""
    
    @patch('app.core.auth.get_current_user')
    def test_input_sanitization_in_contract_creation(self, mock_auth, mock_user):
        """Test that malicious input is sanitized in contract creation."""
        mock_auth.return_value = mock_user
        
        malicious_data = {
            "title": "<script>alert('xss')</script>Malicious Contract",
            "type": "Service Agreement",
            "content": {
                "description": "'; DROP TABLE contracts; --"
            },
            "workspace_id": "test-workspace-id"
        }
        
        client = TestClient(app)
        response = client.post(
            "/api/contracts/",
            json=malicious_data,
            headers={"Authorization": "Bearer mock-token"}
        )
        
        # Should not crash due to malicious input
        assert response.status_code in [200, 201, 401, 403, 422, 500]
        
        # Response should not contain dangerous content
        response_text = response.text
        assert "<script>" not in response_text
        assert "DROP TABLE" not in response_text
