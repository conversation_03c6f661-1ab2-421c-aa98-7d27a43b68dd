"""
Comprehensive API Testing Framework for Averum Contracts
Provides API contract testing, OpenAPI validation, performance testing, and load testing
"""

import asyncio
import logging
import json
import time
import statistics
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass
from enum import Enum
import uuid

import httpx
import pytest
from pydantic import BaseModel, ValidationError
from fastapi.testclient import TestClient
from fastapi.openapi.utils import get_openapi

logger = logging.getLogger(__name__)


class TestType(str, Enum):
    """API test types."""
    CONTRACT = "contract"
    PERFORMANCE = "performance"
    LOAD = "load"
    SECURITY = "security"
    INTEGRATION = "integration"
    SMOKE = "smoke"


class TestStatus(str, Enum):
    """Test execution status."""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class TestResult:
    """Test execution result."""
    test_id: str
    test_name: str
    test_type: TestType
    status: TestStatus
    duration: float
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class PerformanceMetrics:
    """Performance test metrics."""
    response_time_avg: float
    response_time_min: float
    response_time_max: float
    response_time_p95: float
    response_time_p99: float
    throughput: float
    error_rate: float
    total_requests: int
    successful_requests: int
    failed_requests: int


class APITestingService:
    """Comprehensive API testing service."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = TestClient
        self.test_results: List[TestResult] = []
        self.test_suites = self._load_test_suites()
        
    def _load_test_suites(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load test suites configuration."""
        return {
            "contract_tests": [
                {
                    "name": "Authentication Contract",
                    "endpoint": "/api/v1/auth/login",
                    "method": "POST",
                    "expected_schema": {
                        "type": "object",
                        "properties": {
                            "access_token": {"type": "string"},
                            "token_type": {"type": "string"},
                            "expires_in": {"type": "integer"}
                        },
                        "required": ["access_token", "token_type"]
                    }
                },
                {
                    "name": "Contracts List Contract",
                    "endpoint": "/api/v1/contracts/",
                    "method": "GET",
                    "expected_schema": {
                        "type": "object",
                        "properties": {
                            "contracts": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "id": {"type": "string"},
                                        "title": {"type": "string"},
                                        "status": {"type": "string"}
                                    }
                                }
                            },
                            "total": {"type": "integer"}
                        }
                    }
                },
                {
                    "name": "AI Analysis Contract",
                    "endpoint": "/api/v1/secure-ai/analyze",
                    "method": "POST",
                    "expected_schema": {
                        "type": "object",
                        "properties": {
                            "analysis_id": {"type": "string"},
                            "status": {"type": "string"},
                            "results": {"type": "object"}
                        }
                    }
                }
            ],
            "performance_tests": [
                {
                    "name": "Contracts List Performance",
                    "endpoint": "/api/v1/contracts/",
                    "method": "GET",
                    "max_response_time": 500,  # ms
                    "concurrent_users": 10,
                    "duration": 30  # seconds
                },
                {
                    "name": "Document Upload Performance",
                    "endpoint": "/api/v1/documents/upload",
                    "method": "POST",
                    "max_response_time": 2000,  # ms
                    "concurrent_users": 5,
                    "duration": 60
                },
                {
                    "name": "AI Analysis Performance",
                    "endpoint": "/api/v1/secure-ai/analyze",
                    "method": "POST",
                    "max_response_time": 5000,  # ms
                    "concurrent_users": 3,
                    "duration": 120
                }
            ],
            "load_tests": [
                {
                    "name": "High Load Contracts API",
                    "endpoint": "/api/v1/contracts/",
                    "method": "GET",
                    "concurrent_users": 100,
                    "duration": 300,  # 5 minutes
                    "ramp_up_time": 60
                },
                {
                    "name": "Stress Test Analytics",
                    "endpoint": "/api/v1/contract-analytics/overview",
                    "method": "GET",
                    "concurrent_users": 50,
                    "duration": 180,
                    "ramp_up_time": 30
                }
            ]
        }
    
    async def run_contract_tests(self, auth_token: Optional[str] = None) -> List[TestResult]:
        """Run API contract tests to validate response schemas."""
        results = []
        
        for test_config in self.test_suites["contract_tests"]:
            test_id = str(uuid.uuid4())
            test_name = test_config["name"]
            
            logger.info(f"Running contract test: {test_name}")
            start_time = time.time()
            
            try:
                # Prepare request
                headers = {}
                if auth_token:
                    headers["Authorization"] = f"Bearer {auth_token}"
                
                # Make request
                async with httpx.AsyncClient() as client:
                    response = await client.request(
                        method=test_config["method"],
                        url=f"{self.base_url}{test_config['endpoint']}",
                        headers=headers,
                        timeout=30.0
                    )
                
                duration = time.time() - start_time
                
                # Validate response
                if response.status_code == 200:
                    # Validate schema
                    response_data = response.json()
                    schema_valid = self._validate_schema(response_data, test_config["expected_schema"])
                    
                    if schema_valid:
                        result = TestResult(
                            test_id=test_id,
                            test_name=test_name,
                            test_type=TestType.CONTRACT,
                            status=TestStatus.PASSED,
                            duration=duration,
                            details={"response_size": len(response.content)}
                        )
                    else:
                        result = TestResult(
                            test_id=test_id,
                            test_name=test_name,
                            test_type=TestType.CONTRACT,
                            status=TestStatus.FAILED,
                            duration=duration,
                            error_message="Response schema validation failed",
                            details={"response_data": response_data}
                        )
                else:
                    result = TestResult(
                        test_id=test_id,
                        test_name=test_name,
                        test_type=TestType.CONTRACT,
                        status=TestStatus.FAILED,
                        duration=duration,
                        error_message=f"HTTP {response.status_code}: {response.text}",
                        details={"status_code": response.status_code}
                    )
                
            except Exception as e:
                duration = time.time() - start_time
                result = TestResult(
                    test_id=test_id,
                    test_name=test_name,
                    test_type=TestType.CONTRACT,
                    status=TestStatus.ERROR,
                    duration=duration,
                    error_message=str(e)
                )
            
            results.append(result)
            self.test_results.append(result)
        
        return results
    
    async def run_performance_tests(self, auth_token: Optional[str] = None) -> List[TestResult]:
        """Run performance tests to validate response times and throughput."""
        results = []
        
        for test_config in self.test_suites["performance_tests"]:
            test_id = str(uuid.uuid4())
            test_name = test_config["name"]
            
            logger.info(f"Running performance test: {test_name}")
            start_time = time.time()
            
            try:
                # Run performance test
                metrics = await self._run_performance_test(test_config, auth_token)
                duration = time.time() - start_time
                
                # Check if performance meets requirements
                max_response_time = test_config.get("max_response_time", 1000)
                if metrics.response_time_avg <= max_response_time and metrics.error_rate < 0.05:
                    status = TestStatus.PASSED
                    error_message = None
                else:
                    status = TestStatus.FAILED
                    error_message = f"Performance requirements not met: avg_time={metrics.response_time_avg}ms, error_rate={metrics.error_rate}"
                
                result = TestResult(
                    test_id=test_id,
                    test_name=test_name,
                    test_type=TestType.PERFORMANCE,
                    status=status,
                    duration=duration,
                    error_message=error_message,
                    details={
                        "metrics": {
                            "avg_response_time": metrics.response_time_avg,
                            "p95_response_time": metrics.response_time_p95,
                            "throughput": metrics.throughput,
                            "error_rate": metrics.error_rate,
                            "total_requests": metrics.total_requests
                        }
                    }
                )
                
            except Exception as e:
                duration = time.time() - start_time
                result = TestResult(
                    test_id=test_id,
                    test_name=test_name,
                    test_type=TestType.PERFORMANCE,
                    status=TestStatus.ERROR,
                    duration=duration,
                    error_message=str(e)
                )
            
            results.append(result)
            self.test_results.append(result)
        
        return results
    
    async def run_load_tests(self, auth_token: Optional[str] = None) -> List[TestResult]:
        """Run load tests to validate system behavior under high load."""
        results = []
        
        for test_config in self.test_suites["load_tests"]:
            test_id = str(uuid.uuid4())
            test_name = test_config["name"]
            
            logger.info(f"Running load test: {test_name}")
            start_time = time.time()
            
            try:
                # Run load test
                metrics = await self._run_load_test(test_config, auth_token)
                duration = time.time() - start_time
                
                # Evaluate load test results
                if metrics.error_rate < 0.1 and metrics.response_time_p95 < 5000:
                    status = TestStatus.PASSED
                    error_message = None
                else:
                    status = TestStatus.FAILED
                    error_message = f"Load test failed: p95_time={metrics.response_time_p95}ms, error_rate={metrics.error_rate}"
                
                result = TestResult(
                    test_id=test_id,
                    test_name=test_name,
                    test_type=TestType.LOAD,
                    status=status,
                    duration=duration,
                    error_message=error_message,
                    details={
                        "load_metrics": {
                            "concurrent_users": test_config["concurrent_users"],
                            "duration": test_config["duration"],
                            "avg_response_time": metrics.response_time_avg,
                            "p95_response_time": metrics.response_time_p95,
                            "p99_response_time": metrics.response_time_p99,
                            "throughput": metrics.throughput,
                            "error_rate": metrics.error_rate,
                            "total_requests": metrics.total_requests
                        }
                    }
                )
                
            except Exception as e:
                duration = time.time() - start_time
                result = TestResult(
                    test_id=test_id,
                    test_name=test_name,
                    test_type=TestType.LOAD,
                    status=TestStatus.ERROR,
                    duration=duration,
                    error_message=str(e)
                )
            
            results.append(result)
            self.test_results.append(result)
        
        return results
    
    def _validate_schema(self, data: Any, schema: Dict[str, Any]) -> bool:
        """Validate data against JSON schema."""
        try:
            # Simple schema validation - in production, use jsonschema library
            if schema["type"] == "object":
                if not isinstance(data, dict):
                    return False
                
                # Check required properties
                required = schema.get("required", [])
                for prop in required:
                    if prop not in data:
                        return False
                
                # Check property types
                properties = schema.get("properties", {})
                for prop, prop_schema in properties.items():
                    if prop in data:
                        if not self._validate_type(data[prop], prop_schema):
                            return False
            
            return True
            
        except Exception as e:
            logger.error(f"Schema validation error: {e}")
            return False
    
    def _validate_type(self, value: Any, type_schema: Dict[str, Any]) -> bool:
        """Validate value type against schema."""
        expected_type = type_schema["type"]
        
        if expected_type == "string":
            return isinstance(value, str)
        elif expected_type == "integer":
            return isinstance(value, int)
        elif expected_type == "number":
            return isinstance(value, (int, float))
        elif expected_type == "boolean":
            return isinstance(value, bool)
        elif expected_type == "array":
            return isinstance(value, list)
        elif expected_type == "object":
            return isinstance(value, dict)
        
        return True
    
    async def _run_performance_test(self, test_config: Dict[str, Any], auth_token: Optional[str]) -> PerformanceMetrics:
        """Run individual performance test."""
        concurrent_users = test_config.get("concurrent_users", 10)
        duration = test_config.get("duration", 30)
        endpoint = test_config["endpoint"]
        method = test_config["method"]
        
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        async def make_request():
            nonlocal successful_requests, failed_requests
            
            headers = {}
            if auth_token:
                headers["Authorization"] = f"Bearer {auth_token}"
            
            start_time = time.time()
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.request(
                        method=method,
                        url=f"{self.base_url}{endpoint}",
                        headers=headers,
                        timeout=10.0
                    )
                
                response_time = (time.time() - start_time) * 1000  # Convert to ms
                response_times.append(response_time)
                
                if response.status_code < 400:
                    successful_requests += 1
                else:
                    failed_requests += 1
                    
            except Exception:
                failed_requests += 1
                response_times.append(10000)  # Timeout value
        
        # Run concurrent requests for specified duration
        end_time = time.time() + duration
        tasks = []
        
        while time.time() < end_time:
            # Maintain concurrent users
            while len(tasks) < concurrent_users and time.time() < end_time:
                task = asyncio.create_task(make_request())
                tasks.append(task)
            
            # Wait for some tasks to complete
            if tasks:
                done, pending = await asyncio.wait(tasks, timeout=1.0, return_when=asyncio.FIRST_COMPLETED)
                tasks = list(pending)
        
        # Wait for remaining tasks
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        # Calculate metrics
        total_requests = successful_requests + failed_requests
        error_rate = failed_requests / total_requests if total_requests > 0 else 0
        throughput = total_requests / duration
        
        if response_times:
            response_times.sort()
            avg_time = statistics.mean(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            p95_time = response_times[int(len(response_times) * 0.95)]
            p99_time = response_times[int(len(response_times) * 0.99)]
        else:
            avg_time = min_time = max_time = p95_time = p99_time = 0
        
        return PerformanceMetrics(
            response_time_avg=avg_time,
            response_time_min=min_time,
            response_time_max=max_time,
            response_time_p95=p95_time,
            response_time_p99=p99_time,
            throughput=throughput,
            error_rate=error_rate,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests
        )
    
    async def _run_load_test(self, test_config: Dict[str, Any], auth_token: Optional[str]) -> PerformanceMetrics:
        """Run load test with gradual ramp-up."""
        # Similar to performance test but with ramp-up
        concurrent_users = test_config.get("concurrent_users", 100)
        duration = test_config.get("duration", 300)
        ramp_up_time = test_config.get("ramp_up_time", 60)
        
        # For simplicity, run as performance test with higher load
        # In production, implement proper ramp-up logic
        return await self._run_performance_test(test_config, auth_token)
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        if not self.test_results:
            return {"message": "No test results available"}
        
        # Group results by type
        results_by_type = {}
        for result in self.test_results:
            test_type = result.test_type.value
            if test_type not in results_by_type:
                results_by_type[test_type] = []
            results_by_type[test_type].append(result)
        
        # Calculate summary statistics
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r.status == TestStatus.PASSED])
        failed_tests = len([r for r in self.test_results if r.status == TestStatus.FAILED])
        error_tests = len([r for r in self.test_results if r.status == TestStatus.ERROR])
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        return {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "error_tests": error_tests,
                "success_rate": success_rate,
                "generated_at": datetime.utcnow().isoformat()
            },
            "results_by_type": {
                test_type: [
                    {
                        "test_id": r.test_id,
                        "test_name": r.test_name,
                        "status": r.status.value,
                        "duration": r.duration,
                        "error_message": r.error_message,
                        "details": r.details,
                        "timestamp": r.timestamp.isoformat()
                    }
                    for r in results
                ]
                for test_type, results in results_by_type.items()
            },
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []
        
        failed_tests = [r for r in self.test_results if r.status == TestStatus.FAILED]
        error_tests = [r for r in self.test_results if r.status == TestStatus.ERROR]
        
        if failed_tests:
            recommendations.append(f"Address {len(failed_tests)} failed tests to improve API reliability")
        
        if error_tests:
            recommendations.append(f"Fix {len(error_tests)} test errors to ensure proper test coverage")
        
        # Performance-specific recommendations
        perf_tests = [r for r in self.test_results if r.test_type == TestType.PERFORMANCE]
        slow_tests = [r for r in perf_tests if r.details and r.details.get("metrics", {}).get("avg_response_time", 0) > 1000]
        
        if slow_tests:
            recommendations.append("Optimize slow endpoints to improve response times")
        
        if not recommendations:
            recommendations.append("All tests passing - maintain current quality standards")
        
        return recommendations


# Global API testing service instance
api_testing_service = APITestingService()
