"""
Security tests for Averum Contracts API.
Tests input sanitization, XSS prevention, SQL injection prevention, and security headers.
"""

import pytest
from fastapi.testclient import TestClient
from app.middleware.security import InputSanitizer, sanitize_input_data, SecurityConfig
from app.main import app


class TestInputSanitization:
    """Test input sanitization functionality."""
    
    def test_xss_prevention(self, security_test_data):
        """Test XSS payload sanitization."""
        sanitizer = InputSanitizer()
        
        for payload in security_test_data["xss_payloads"]:
            sanitized = sanitizer.sanitize_string(payload, allow_html=False)
            
            # Should not contain dangerous patterns
            assert "<script" not in sanitized.lower()
            assert "javascript:" not in sanitized.lower()
            assert "onerror=" not in sanitized.lower()
            assert "onload=" not in sanitized.lower()
            
            # Should be HTML encoded
            assert "&lt;" in sanitized or "&gt;" in sanitized or "[FILTERED]" in sanitized
    
    def test_sql_injection_prevention(self, security_test_data):
        """Test SQL injection payload sanitization."""
        sanitizer = InputSanitizer()
        
        for payload in security_test_data["sql_injection_payloads"]:
            sanitized = sanitizer.sanitize_string(payload, allow_html=False)
            
            # Should filter dangerous SQL patterns
            assert "[FILTERED]" in sanitized or sanitized != payload
    
    def test_html_preservation_in_allowed_fields(self):
        """Test that HTML is preserved in allowed fields."""
        test_data = {
            "title": "<script>alert('xss')</script>Safe Title",
            "content": "<p>This is <strong>valid</strong> HTML content</p>",
            "description": "<em>Valid emphasis</em> text"
        }
        
        sanitized = sanitize_input_data(test_data, allow_html_fields=['content', 'description'])
        
        # Title should be sanitized (not in allow list)
        assert "<script>" not in sanitized["title"]
        
        # Content should preserve valid HTML
        assert "<p>" in sanitized["content"]
        assert "<strong>" in sanitized["content"]
        
        # Description should preserve valid HTML
        assert "<em>" in sanitized["description"]
    
    def test_nested_object_sanitization(self):
        """Test sanitization of nested objects."""
        test_data = {
            "contract": {
                "title": "<script>alert('xss')</script>",
                "content": "<p>Valid content</p>",
                "metadata": {
                    "author": "'; DROP TABLE users; --",
                    "notes": "<strong>Important notes</strong>"
                }
            }
        }
        
        sanitized = sanitize_input_data(test_data, allow_html_fields=['content', 'notes'])
        
        # Nested sanitization should work
        assert "<script>" not in sanitized["contract"]["title"]
        assert "<p>" in sanitized["contract"]["content"]
        assert "[FILTERED]" in sanitized["contract"]["metadata"]["author"]
        assert "<strong>" in sanitized["contract"]["metadata"]["notes"]
    
    def test_list_sanitization(self):
        """Test sanitization of lists."""
        test_data = {
            "tags": ["<script>alert('xss')</script>", "normal tag", "'; DROP TABLE users; --"],
            "content": "<p>Valid HTML</p>"
        }
        
        sanitized = sanitize_input_data(test_data, allow_html_fields=['content'])
        
        # List items should be sanitized
        assert all("<script>" not in tag for tag in sanitized["tags"])
        assert any("[FILTERED]" in tag for tag in sanitized["tags"])
        
        # Content should preserve HTML
        assert "<p>" in sanitized["content"]


class TestSecurityHeaders:
    """Test security headers middleware."""
    
    def test_security_headers_present(self):
        """Test that security headers are added to responses."""
        client = TestClient(app)
        
        response = client.get("/api/health")
        
        # Check for key security headers
        assert "X-Content-Type-Options" in response.headers
        assert response.headers["X-Content-Type-Options"] == "nosniff"
        
        assert "X-Frame-Options" in response.headers
        assert response.headers["X-Frame-Options"] == "DENY"
        
        assert "X-XSS-Protection" in response.headers
        assert "Strict-Transport-Security" in response.headers
        assert "Content-Security-Policy" in response.headers
        assert "Referrer-Policy" in response.headers
    
    def test_csp_header_configuration(self):
        """Test Content Security Policy header configuration."""
        client = TestClient(app)
        
        response = client.get("/api/health")
        csp = response.headers.get("Content-Security-Policy", "")
        
        # Check key CSP directives
        assert "default-src 'self'" in csp
        assert "script-src" in csp
        assert "style-src" in csp
        assert "frame-ancestors 'none'" in csp
    
    def test_server_header_obfuscation(self):
        """Test that server header is obfuscated."""
        client = TestClient(app)
        
        response = client.get("/api/health")
        
        # Should have custom server header
        assert response.headers.get("Server") == "Averum-API"


class TestFileUploadSecurity:
    """Test file upload security validation."""
    
    def test_allowed_file_types(self):
        """Test that allowed file types pass validation."""
        from app.middleware.security import validate_file_upload
        
        allowed_files = [
            ("document.pdf", "application/pdf", 1024),
            ("contract.docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", 2048),
            ("image.png", "image/png", 512)
        ]
        
        for filename, content_type, size in allowed_files:
            is_valid, message = validate_file_upload(filename, content_type, size)
            assert is_valid, f"File {filename} should be valid: {message}"
    
    def test_blocked_file_types(self):
        """Test that dangerous file types are blocked."""
        from app.middleware.security import validate_file_upload
        
        dangerous_files = [
            ("malware.exe", "application/octet-stream", 1024),
            ("script.js", "application/javascript", 512),
            ("batch.bat", "application/x-msdos-program", 256)
        ]
        
        for filename, content_type, size in dangerous_files:
            is_valid, message = validate_file_upload(filename, content_type, size)
            assert not is_valid, f"File {filename} should be blocked"
    
    def test_file_size_limits(self):
        """Test file size validation."""
        from app.middleware.security import validate_file_upload
        
        # Test oversized file
        is_valid, message = validate_file_upload(
            "large.pdf", 
            "application/pdf", 
            SecurityConfig.MAX_FILE_SIZE + 1
        )
        assert not is_valid
        assert "size exceeds" in message.lower()
    
    def test_path_traversal_prevention(self):
        """Test path traversal prevention in filenames."""
        from app.middleware.security import validate_file_upload
        
        dangerous_filenames = [
            "../../../etc/passwd",
            "..\\..\\windows\\system32\\config\\sam",
            "normal/../../../etc/passwd",
            "file/with/path.pdf"
        ]
        
        for filename in dangerous_filenames:
            is_valid, message = validate_file_upload(filename, "application/pdf", 1024)
            assert not is_valid, f"Filename {filename} should be blocked"
            assert "path traversal" in message.lower()


class TestAPIEndpointSecurity:
    """Test security of API endpoints."""
    
    def test_contract_creation_sanitization(self, auth_headers):
        """Test that contract creation sanitizes input."""
        client = TestClient(app)
        
        malicious_contract = {
            "title": "<script>alert('xss')</script>Malicious Contract",
            "type": "Service Agreement",
            "content": {
                "description": "'; DROP TABLE contracts; --",
                "terms": "<p>Valid HTML terms</p>"
            },
            "workspace_id": "test-workspace-id"
        }
        
        # Note: This would normally require proper authentication
        # In a real test, you'd mock the authentication
        response = client.post(
            "/api/contracts/",
            json=malicious_contract,
            headers=auth_headers
        )
        
        # The endpoint should handle the request (even if it fails due to auth)
        # The important thing is that it doesn't crash due to malicious input
        assert response.status_code in [200, 201, 401, 403, 422]
    
    def test_rate_limiting_headers(self):
        """Test that rate limiting headers are present."""
        client = TestClient(app)
        
        response = client.get("/api/health")
        
        # Should have rate limiting headers
        assert "X-RateLimit-Limit" in response.headers
        assert "X-RateLimit-Remaining" in response.headers
    
    def test_cors_headers(self):
        """Test CORS headers configuration."""
        client = TestClient(app)
        
        # Test preflight request
        response = client.options(
            "/api/health",
            headers={"Origin": "http://localhost:5173"}
        )
        
        # Should have CORS headers
        assert "Access-Control-Allow-Origin" in response.headers
        assert "Access-Control-Allow-Methods" in response.headers
        assert "Access-Control-Allow-Headers" in response.headers


class TestSecurityConfiguration:
    """Test security configuration and constants."""
    
    def test_security_config_constants(self):
        """Test that security configuration constants are properly set."""
        # Test CSP policy
        assert SecurityConfig.CSP_POLICY is not None
        assert "default-src 'self'" in SecurityConfig.CSP_POLICY
        
        # Test security headers
        assert len(SecurityConfig.SECURITY_HEADERS) > 0
        assert "X-Content-Type-Options" in SecurityConfig.SECURITY_HEADERS
        
        # Test XSS patterns
        assert len(SecurityConfig.XSS_PATTERNS) > 0
        assert any("script" in pattern for pattern in SecurityConfig.XSS_PATTERNS)
        
        # Test SQL patterns
        assert len(SecurityConfig.SQL_PATTERNS) > 0
        assert any("SELECT" in pattern for pattern in SecurityConfig.SQL_PATTERNS)
        
        # Test file restrictions
        assert SecurityConfig.MAX_FILE_SIZE > 0
        assert len(SecurityConfig.ALLOWED_MIME_TYPES) > 0
    
    def test_allowed_mime_types(self):
        """Test that allowed MIME types are reasonable."""
        allowed_types = SecurityConfig.ALLOWED_MIME_TYPES
        
        # Should include common document types
        assert "application/pdf" in allowed_types
        assert "application/msword" in allowed_types
        assert "text/plain" in allowed_types
        
        # Should not include dangerous types
        assert "application/x-executable" not in allowed_types
        assert "application/javascript" not in allowed_types


@pytest.mark.integration
class TestSecurityIntegration:
    """Integration tests for security features."""
    
    def test_end_to_end_security_flow(self, auth_headers):
        """Test complete security flow from request to response."""
        client = TestClient(app)
        
        # Send request with potential security issues
        test_data = {
            "title": "<script>alert('xss')</script>Test Contract",
            "content": "'; DROP TABLE users; --",
            "description": "<p>Valid HTML description</p>"
        }
        
        response = client.post(
            "/api/contracts/",
            json=test_data,
            headers=auth_headers
        )
        
        # Check security headers are present
        assert "X-Content-Type-Options" in response.headers
        assert "Content-Security-Policy" in response.headers
        
        # Check that response doesn't contain dangerous content
        response_text = response.text
        assert "<script>" not in response_text
        assert "DROP TABLE" not in response_text
    
    def test_security_middleware_performance(self):
        """Test that security middleware doesn't significantly impact performance."""
        client = TestClient(app)
        
        import time
        
        # Measure response time with security middleware
        start_time = time.time()
        response = client.get("/api/health")
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # Should respond quickly (less than 1 second for health check)
        assert response_time < 1.0
        assert response.status_code == 200
        
        # Should have process time header
        assert "X-Process-Time" in response.headers
