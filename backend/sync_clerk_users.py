#!/usr/bin/env python3
"""
Manual script to sync users from <PERSON> to Supabase database
Use this when webhooks aren't working in development
"""

import asyncio
import requests
from dotenv import load_dotenv
import os
from supabase import create_client

# Load environment variables
load_dotenv('.env')

CLERK_SECRET_KEY = os.getenv('CLERK_SECRET_KEY')
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

def get_clerk_users():
    """Get all users from Clerk"""
    headers = {
        'Authorization': f'Bearer {CLERK_SECRET_KEY}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get('https://api.clerk.com/v1/users', headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"Error fetching users from Clerk: {e}")
        return []

def sync_user_to_supabase(user_data):
    """Sync a single user to Su<PERSON>base"""
    supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Extract user information
    user_id = user_data.get('id')
    email_addresses = user_data.get('email_addresses', [])
    first_name = user_data.get('first_name', '')
    last_name = user_data.get('last_name', '')
    
    # Get primary email
    primary_email = None
    for email in email_addresses:
        if email.get('id') == user_data.get('primary_email_address_id'):
            primary_email = email.get('email_address')
            break
    
    if not primary_email and email_addresses:
        primary_email = email_addresses[0].get('email_address')
    
    if not primary_email:
        print(f"No email found for user {user_id}")
        return False
    
    # Check if user already exists
    existing_user = supabase.table("users").select("*").eq("id", user_id).execute()
    if existing_user.data:
        print(f"User {primary_email} already exists in database")
        return True
    
    # Create user record
    user_record = {
        "id": user_id,
        "email": primary_email,
        "first_name": first_name or "",
        "last_name": last_name or "",
        "title": "",
        "company": "",
        "timezone": "America/New_York",
        "bio": "",
        "initials": f"{first_name[:1] if first_name else ''}{last_name[:1] if last_name else ''}".upper(),
        "notification_preferences": {
            "email_approvals": True,
            "email_updates": True,
            "email_reminders": True,
            "email_comments": True,
            "system_approvals": True,
            "browser_notifications": True,
            "email_digest_frequency": "daily"
        },
        "workspaces": [],
        "workspace_roles": {}
    }
    
    try:
        # Insert user
        response = supabase.table("users").insert(user_record).execute()
        if response.data:
            print(f"✅ Created user: {primary_email} (ID: {user_id})")
            
            # Add to default workspace
            membership_data = {
                "workspace_id": "demo-workspace-tech",
                "user_id": user_id,
                "role_id": "role-viewer"
            }
            
            membership_response = supabase.table("workspace_members").insert(membership_data).execute()
            if membership_response.data:
                print(f"✅ Added user to default workspace: demo-workspace-tech")
            else:
                print(f"⚠️ Failed to add user to workspace")
            
            return True
        else:
            print(f"❌ Failed to create user: {primary_email}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating user {primary_email}: {e}")
        return False

def main():
    """Main sync function"""
    print("🔄 Syncing users from Clerk to Supabase...")
    
    # Get users from Clerk
    clerk_users = get_clerk_users()
    if not clerk_users:
        print("❌ No users found in Clerk or failed to fetch")
        return
    
    print(f"📊 Found {len(clerk_users)} users in Clerk")
    
    # Sync each user
    synced_count = 0
    for user in clerk_users:
        if sync_user_to_supabase(user):
            synced_count += 1
    
    print(f"\n🎉 Sync complete! {synced_count}/{len(clerk_users)} users synced")

if __name__ == "__main__":
    main()
