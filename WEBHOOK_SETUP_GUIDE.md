# 🔗 Clerk Webhook Setup Guide

## Current Status

✅ **Backend Running**: http://127.0.0.1:8000  
✅ **Clerk Secret Key**: Configured  
✅ **Demo Data**: 7 users, 12 workspaces, 6 contracts  
✅ **Webhook Endpoints**: Ready and tested  
⚠️ **Webhook Secret**: Not configured yet  

## 🎯 Webhook Purpose

Webhooks automatically synchronize users between Clerk and your Supabase database:
- **User Created** → Automatically add to your database + assign to default workspace
- **User Updated** → Update user information in your database  
- **User Deleted** → Remove user from your database

## 🔧 Step-by-Step Webhook Setup

### Step 1: Configure Webhook Endpoints in Clerk

1. **Go to Clerk Dashboard**: https://dashboard.clerk.com/
2. **Select your application**
3. **Navigate to "Webhooks"** in the sidebar
4. **Click "Add Endpoint"**

### Step 2: Add Webhook URLs

Add these three webhook endpoints:

#### User Created Webhook
- **URL**: `http://127.0.0.1:8000/api/clerk-webhooks/user-created`
- **Events**: Select `user.created`
- **Description**: "Sync new users to database"

#### User Updated Webhook  
- **URL**: `http://127.0.0.1:8000/api/clerk-webhooks/user-updated`
- **Events**: Select `user.updated`
- **Description**: "Sync user updates to database"

#### User Deleted Webhook
- **URL**: `http://127.0.0.1:8000/api/clerk-webhooks/user-deleted`
- **Events**: Select `user.deleted`
- **Description**: "Remove users from database"

### Step 3: Get Webhook Secret

1. **After creating each webhook**, Clerk will provide a **Signing Secret**
2. **Copy the signing secret** (starts with `whsec_`)
3. **Add to your backend/.env file**:

```env
# Add this line to backend/.env
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### Step 4: Test Webhook Setup

1. **Restart your backend server** to pick up the webhook secret
2. **Test the webhook endpoint**:

```bash
curl http://127.0.0.1:8000/api/clerk-webhooks/test
```

Should return:
```json
{
  "message": "Clerk webhook endpoint is working",
  "webhook_secret_configured": true
}
```

## 🧪 Testing the Webhooks

### Option 1: Create a Test User in Clerk

1. **Go to Clerk Dashboard** → **Users**
2. **Click "Create User"**
3. **Fill in test user details**
4. **Check your backend logs** for webhook processing
5. **Verify user appears in Supabase** database

### Option 2: Use the Test Page

1. **Open the test page**: `test_clerk_setup.html`
2. **Click "Sign Up"** to create a new account
3. **Complete the signup process**
4. **Check backend logs** for webhook activity
5. **Verify user synchronization**

## 🔍 Webhook Payload Examples

### User Created Payload
```json
{
  "type": "user.created",
  "data": {
    "id": "user_2abc123def456",
    "email_addresses": [
      {
        "id": "idn_2abc123def456",
        "email_address": "<EMAIL>"
      }
    ],
    "primary_email_address_id": "idn_2abc123def456",
    "first_name": "John",
    "last_name": "Doe",
    "created_at": *************
  }
}
```

### What Happens When Webhook Fires

1. **User Created** → 
   - Creates user record in `users` table
   - Assigns user to default workspace (`demo-workspace-tech`)
   - Sets default role (`role-viewer`)
   - Logs success/failure

2. **User Updated** →
   - Updates user information in database
   - Syncs name and email changes
   - Updates initials

3. **User Deleted** →
   - Removes user from database
   - Cascades to remove workspace memberships
   - Logs deletion

## 🚨 Troubleshooting

### Common Issues

1. **Webhook Secret Not Configured**
   ```
   "webhook_secret_configured": false
   ```
   **Solution**: Add `CLERK_WEBHOOK_SECRET` to backend/.env

2. **Webhook URL Not Reachable**
   ```
   Clerk shows "Failed" status
   ```
   **Solution**: 
   - Ensure backend is running on http://127.0.0.1:8000
   - For production, use your actual domain
   - Consider using ngrok for local testing

3. **User Not Created in Database**
   ```
   User exists in Clerk but not in Supabase
   ```
   **Solution**:
   - Check backend logs for webhook errors
   - Verify Supabase credentials
   - Check database permissions

### Debug Steps

1. **Check Backend Logs**:
   ```bash
   # Watch the backend terminal for webhook activity
   # Look for "Successfully created user" messages
   ```

2. **Test Webhook Endpoint**:
   ```bash
   curl http://127.0.0.1:8000/api/clerk-webhooks/test
   ```

3. **Check Database**:
   ```bash
   # In backend directory
   python3 -c "
   from dotenv import load_dotenv
   import os
   from supabase import create_client
   load_dotenv('.env')
   supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
   users = supabase.table('users').select('*').execute()
   print(f'Total users: {len(users.data)}')
   for user in users.data[-3:]:  # Show last 3 users
       print(f'  - {user[\"first_name\"]} {user[\"last_name\"]} ({user[\"email\"]})')
   "
   ```

## 🌐 Production Considerations

### For Production Deployment

1. **Use HTTPS URLs** for webhook endpoints
2. **Configure proper domain** instead of localhost
3. **Set up webhook secret** in production environment
4. **Monitor webhook failures** in Clerk dashboard
5. **Implement retry logic** for failed webhook processing
6. **Add webhook signature verification** for security

### Example Production URLs
```
https://your-domain.com/api/clerk-webhooks/user-created
https://your-domain.com/api/clerk-webhooks/user-updated  
https://your-domain.com/api/clerk-webhooks/user-deleted
```

## ✅ Success Indicators

When webhooks are working correctly, you should see:

1. **In Clerk Dashboard**: Webhook status shows "Success" ✅
2. **In Backend Logs**: "Successfully created user" messages
3. **In Database**: New users appear automatically
4. **In Application**: Users can sign up and immediately access the app

## 🔗 Next Steps

After webhook setup is complete:

1. **Test user signup flow** end-to-end
2. **Verify workspace assignments** work correctly  
3. **Test user onboarding** with real authentication
4. **Configure frontend** to use real authentication
5. **Deploy to production** with proper webhook URLs

Your LegalAI V4 application will then have complete user synchronization between Clerk and your database! 🎉
