[build]
builder = "nixpacks"
buildCommand = "cd backend && pip install -r requirements.txt"

[deploy]
startCommand = "cd backend && python run.py"
healthcheckPath = "/api/health"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

[env]
ENVIRONMENT = "production"
API_PREFIX = "/api"
SUPABASE_URL = "https://kdcjdbufciuvvznqnotx.supabase.co"
SUPABASE_KEY = "${{SUPABASE_SERVICE_KEY}}"
CLERK_SECRET_KEY = "${{CLERK_SECRET_KEY}}"
CLERK_PUBLISHABLE_KEY = "pk_test_ZGVmaW5pdGUtb3Bvc3N1bS0xLmNsZXJrLmFjY291bnRzLmRldiQ"
BACKEND_CORS_ORIGINS = '["${{RAILWAY_STATIC_URL}}", "https://*.railway.app"]'
