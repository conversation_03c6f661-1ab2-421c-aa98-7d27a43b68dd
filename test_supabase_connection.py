#!/usr/bin/env python3
"""
Test script to verify Supabase connection.
This script attempts to connect to Supabase and perform a simple query.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from supabase import create_client, Client

# Add the backend directory to the path so we can import from app
backend_path = Path(__file__).parent / "backend"
sys.path.append(str(backend_path))

def test_supabase_connection():
    """
    Test the connection to Supabase.
    """
    # Load environment variables from backend/.env
    env_path = backend_path / ".env"
    load_dotenv(dotenv_path=env_path)

    # Get Supabase credentials from environment variables
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")

    if not supabase_url or not supabase_key:
        print("Error: SUPABASE_URL and SUPABASE_KEY environment variables must be set.")
        print(f"Current values: URL={supabase_url}, KEY={'*****' if supabase_key else None}")
        return False

    try:
        print(f"Attempting to connect to Supabase at: {supabase_url}")
        print(f"Using key: {supabase_key[:5]}...{supabase_key[-5:]}")

        # Create Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)

        # Try a simple query to verify connection
        print("Testing connection with a simple query...")

        try:
            # Check all tables defined in the schema
            tables = [
                "users", "workspaces", "workspace_members", "contracts", "templates", "folders",
                "contract_signers", "clauses", "documents", "document_signers"
            ]
            print("Connection successful! Checking tables...")

            for table in tables:
                try:
                    response = supabase.table(table).select("*").execute()
                    count = len(response.data)
                    print(f"  - Table '{table}': {count} records")
                except Exception as table_error:
                    print(f"  - Table '{table}': Error - {table_error}")

            # Try to get database version info
            try:
                # This is a raw SQL query to get PostgreSQL version
                # Note: This might not work depending on Supabase permissions
                print("\nAttempting to get database version...")
                # Uncomment the following if your Supabase instance allows raw SQL queries
                # version = supabase.rpc("pg_version").execute()
                # print(f"Database version: {version.data}")
                print("Note: Raw SQL queries might be restricted in your Supabase instance.")
            except Exception as version_error:
                print(f"Could not get database version: {version_error}")

        except Exception as query_error:
            print(f"Error executing query: {query_error}")
            return False

        return True

    except Exception as e:
        print(f"Error connecting to Supabase: {e}")
        return False

if __name__ == "__main__":
    print("Testing Supabase connection...")
    success = test_supabase_connection()

    if success:
        print("Supabase connection test completed successfully.")
        sys.exit(0)
    else:
        print("Supabase connection test failed.")
        sys.exit(1)
